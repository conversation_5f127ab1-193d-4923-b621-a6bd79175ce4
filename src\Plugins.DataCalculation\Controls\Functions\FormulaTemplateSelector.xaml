<UserControl
    Background="{DynamicResource Brush.Surface}"
    d:DesignHeight="600"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Plugins.DataCalculation.Controls.Functions.FormulaTemplateSelector"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:local="clr-namespace:Plugins.DataCalculation.Controls.Functions"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <local:FormulaComplexityToStringConverter x:Key="ComplexityToString" />
        <Style TargetType="TextBlock" x:Key="CategoryHeaderStyle">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="{DynamicResource Brush.Text}" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <Style TargetType="Border" x:Key="FormulaItemStyle">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="{DynamicResource Brush.Divider}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="Padding" Value="12" />
            <Setter Property="Margin" Value="0,2" />
            <Setter Property="Cursor" Value="Hand" />
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource Brush.Primary/08}" />
                    <Setter Property="BorderBrush" Value="{DynamicResource Brush.Primary}" />
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="Border" x:Key="ComplexityIndicatorStyle">
            <Setter Property="CornerRadius" Value="2" />
            <Setter Property="Padding" Value="4,2" />
            <Setter Property="Margin" Value="4,0,0,0" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- <Border
            Background="{DynamicResource Brush.Primary}"
            Grid.Row="0"
            Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock
                        FontSize="18"
                        FontWeight="Medium"
                        Foreground="White"
                        Margin="8,0,0,0"
                        Text="选择公式模板"
                        VerticalAlignment="Center" />
                </StackPanel>
                <Button
                    Click="CloseButton_Click"
                    Grid.Column="1"
                    Style="{StaticResource Button.Text}"
                    ToolTip="关闭">
                    <ipack:PackIconMaterial Foreground="White" Kind="Close" />
                </Button>
            </Grid>
        </Border> -->

        <DockPanel Grid.Row="1" Margin="10">
            <ui:TextBox
                DockPanel.Dock="Right"
                Margin="0,0,8,0"
                Width="240"
                TextChanged="SearchTextBox_TextChanged"
                x:Name="SearchTextBox" />
            <ComboBox
                Margin="0,0,8,0"
                MinWidth="140"
                SelectionChanged="CategoryFilterComboBox_SelectionChanged"
                x:Name="CategoryFilterComboBox" />
            <ComboBox
                MinWidth="120"
                SelectionChanged="ComplexityFilterComboBox_SelectionChanged"
                x:Name="ComplexityFilterComboBox" />
        </DockPanel>

        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <ItemsControl x:Name="CategoriesItemsControl">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <StackPanel>
                                <TextBlock Style="{StaticResource CategoryHeaderStyle}" Text="{Binding Name}" />
                                <ItemsControl ItemsSource="{Binding Templates}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border
                                                MouseLeftButtonUp="FormulaItem_Click"
                                                Style="{StaticResource FormulaItemStyle}"
                                                Tag="{Binding}">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto" />
                                                        <RowDefinition Height="Auto" />
                                                        <RowDefinition Height="Auto" />
                                                    </Grid.RowDefinitions>
                                                    <Grid Grid.Row="0">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*" />
                                                            <ColumnDefinition Width="Auto" />
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock
                                                            FontSize="14"
                                                            FontWeight="Medium"
                                                            Grid.Column="0"
                                                            Text="{Binding Name}" />
                                                        <Border Grid.Column="1">
                                                            <Border.Style>
                                                                <Style BasedOn="{StaticResource ComplexityIndicatorStyle}" TargetType="Border">
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding Complexity}" Value="Simple">
                                                                            <Setter Property="Background" Value="#E8F5E8" />
                                                                            <Setter Property="BorderBrush" Value="#4CAF50" />
                                                                            <Setter Property="BorderThickness" Value="1" />
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Complexity}" Value="Medium">
                                                                            <Setter Property="Background" Value="#FFF3E0" />
                                                                            <Setter Property="BorderBrush" Value="#FF9800" />
                                                                            <Setter Property="BorderThickness" Value="1" />
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Complexity}" Value="Complex">
                                                                            <Setter Property="Background" Value="#FFEBEE" />
                                                                            <Setter Property="BorderBrush" Value="#F44336" />
                                                                            <Setter Property="BorderThickness" Value="1" />
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Complexity}" Value="Advanced">
                                                                            <Setter Property="Background" Value="#F3E5F5" />
                                                                            <Setter Property="BorderBrush" Value="#9C27B0" />
                                                                            <Setter Property="BorderThickness" Value="1" />
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <TextBlock
                                                                FontSize="10"
                                                                FontWeight="Medium"
                                                                Text="{Binding Complexity, Converter={StaticResource ComplexityToString}}" />
                                                        </Border>
                                                    </Grid>
                                                    <TextBlock
                                                        FontSize="12"
                                                        Foreground="{DynamicResource Brush.TextSecondary}"
                                                        Grid.Row="1"
                                                        Margin="0,4,0,0"
                                                        Text="{Binding Description}"
                                                        TextWrapping="Wrap" />
                                                    <StackPanel
                                                        Grid.Row="2"
                                                        Margin="0,6,0,0"
                                                        Orientation="Horizontal">
                                                        <TextBlock
                                                            FontSize="11"
                                                            Foreground="{DynamicResource Brush.TextSecondary}"
                                                            Text="示例: " />
                                                        <TextBlock
                                                            FontFamily="Consolas"
                                                            FontSize="11"
                                                            Foreground="{DynamicResource Brush.TextSecondary}"
                                                            Text="{Binding Expression}" />
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </StackPanel>
        </ScrollViewer>

        <StackPanel
            Grid.Row="3"
            HorizontalAlignment="Right"
            Margin="10"
            Orientation="Horizontal">
            <ui:Button
                Click="ConfirmButton_Click"
                IsDefault="True"
                Margin="0,0,8,0"
                Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="Check"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock Text="确定" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
            <ui:Button
                Click="CancelButton_Click"
                Width="80">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="Close"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock Text="取消" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
        </StackPanel>
    </Grid>
</UserControl>

