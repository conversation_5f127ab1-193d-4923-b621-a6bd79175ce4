using System.ComponentModel.DataAnnotations;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 日志配置类
    /// </summary>
    public class LoggingSettings
    {
        /// <summary>
        /// 默认日志级别
        /// </summary>
        [Required]
        public LogLevel LogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogPath { get; set; } = "logs/app-.log";

        /// <summary>
        /// 是否启用结构化日志记录
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;

        /// <summary>
        /// 是否启用控制台日志
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = true;

        /// <summary>
        /// 是否启用文件日志
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;

        /// <summary>
        /// 日志文件滚动间隔
        /// </summary>
        public RollingInterval RollingInterval { get; set; } = RollingInterval.Day;

        /// <summary>
        /// 保留日志文件数量
        /// </summary>
        [Range(1, 365)]
        public int RetainedFileCountLimit { get; set; } = 7;

        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        [Range(1, 1024)]
        public int MaxFileSizeMB { get; set; } = 100;

        /// <summary>
        /// 特定命名空间的日志级别覆盖
        /// </summary>
        public Dictionary<string, LogLevel> LogLevelOverrides { get; set; } = new()
        {
            { "Microsoft", LogLevel.Warning },
            { "System", LogLevel.Warning },
            { "Microsoft.Hosting.Lifetime", LogLevel.Information }
        };

        /// <summary>
        /// 性能日志配置
        /// </summary>
        public PerformanceLoggingSettings Performance { get; set; } = new();
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Trace = 0,
        Debug = 1,
        Information = 2,
        Warning = 3,
        Error = 4,
        Critical = 5,
        None = 6
    }

    /// <summary>
    /// 日志文件滚动间隔枚举
    /// </summary>
    public enum RollingInterval
    {
        Infinite,
        Year,
        Month,
        Day,
        Hour,
        Minute
    }

    /// <summary>
    /// 性能日志配置
    /// </summary>
    public class PerformanceLoggingSettings
    {
        /// <summary>
        /// 是否启用性能日志
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 慢查询阈值（毫秒）
        /// </summary>
        [Range(1, 60000)]
        public int SlowQueryThresholdMs { get; set; } = 1000;

        /// <summary>
        /// 慢操作阈值（毫秒）
        /// </summary>
        [Range(1, 60000)]
        public int SlowOperationThresholdMs { get; set; } = 2000;
    }
}
