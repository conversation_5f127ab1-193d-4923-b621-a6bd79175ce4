using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ProjectDigitizer.Application.Interfaces
{
    /// <summary>
    /// 异步操作管理器接口
    /// 提供统一的异步操作管理、进度报告和取消机制
    /// </summary>
    public interface IAsyncOperationManager
    {
        /// <summary>
        /// 启动异步操作
        /// </summary>
        /// <typeparam name="T">操作结果类型</typeparam>
        /// <param name="operationName">操作名称</param>
        /// <param name="operation">异步操作函数</param>
        /// <param name="progressReporter">进度报告器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步操作句柄</returns>
        Task<IAsyncOperationHandle<T>> StartOperationAsync<T>(
            string operationName,
            Func<IProgress<OperationProgress>, CancellationToken, Task<T>> operation,
            IProgress<OperationProgress>? progressReporter = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 启动无返回值的异步操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="operation">异步操作函数</param>
        /// <param name="progressReporter">进度报告器</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步操作句柄</returns>
        Task<IAsyncOperationHandle> StartOperationAsync(
            string operationName,
            Func<IProgress<OperationProgress>, CancellationToken, Task> operation,
            IProgress<OperationProgress>? progressReporter = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取所有活动操作
        /// </summary>
        /// <returns>活动操作列表</returns>
        IEnumerable<IAsyncOperationHandle> GetActiveOperations();

        /// <summary>
        /// 根据ID获取操作句柄
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>操作句柄，如果不存在则返回null</returns>
        IAsyncOperationHandle? GetOperation(string operationId);

        /// <summary>
        /// 取消所有活动操作
        /// </summary>
        /// <returns>取消任务</returns>
        Task CancelAllOperationsAsync();

        /// <summary>
        /// 等待所有活动操作完成
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <returns>等待任务</returns>
        Task WaitForAllOperationsAsync(TimeSpan? timeout = null);

        /// <summary>
        /// 操作状态变更事件
        /// </summary>
        event EventHandler<OperationStatusChangedEventArgs> OperationStatusChanged;

        /// <summary>
        /// 操作进度更新事件
        /// </summary>
        event EventHandler<OperationProgressEventArgs> OperationProgressUpdated;
    }

    /// <summary>
    /// 异步操作句柄接口
    /// </summary>
    public interface IAsyncOperationHandle : IDisposable
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        string OperationId { get; }

        /// <summary>
        /// 操作名称
        /// </summary>
        string OperationName { get; }

        /// <summary>
        /// 操作状态
        /// </summary>
        OperationStatus Status { get; }

        /// <summary>
        /// 当前进度
        /// </summary>
        OperationProgress? CurrentProgress { get; }

        /// <summary>
        /// 开始时间
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// 结束时间（如果已完成）
        /// </summary>
        DateTime? EndTime { get; }

        /// <summary>
        /// 操作异常（如果失败）
        /// </summary>
        Exception? Exception { get; }

        /// <summary>
        /// 取消令牌源
        /// </summary>
        CancellationTokenSource CancellationTokenSource { get; }

        /// <summary>
        /// 操作任务
        /// </summary>
        Task Task { get; }

        /// <summary>
        /// 取消操作
        /// </summary>
        void Cancel();

        /// <summary>
        /// 等待操作完成
        /// </summary>
        /// <returns>等待任务</returns>
        Task WaitAsync();
    }

    /// <summary>
    /// 带返回值的异步操作句柄接口
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    public interface IAsyncOperationHandle<T> : IAsyncOperationHandle
    {
        /// <summary>
        /// 操作结果（如果成功完成）
        /// </summary>
        T? Result { get; }

        /// <summary>
        /// 等待操作完成并获取结果
        /// </summary>
        /// <returns>操作结果</returns>
        new Task<T> WaitAsync();
    }

    /// <summary>
    /// 操作进度信息
    /// </summary>
    public class OperationProgress
    {
        /// <summary>
        /// 进度百分比 (0-100)
        /// </summary>
        public double Percentage { get; set; }

        /// <summary>
        /// 进度消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 当前步骤
        /// </summary>
        public int CurrentStep { get; set; }

        /// <summary>
        /// 总步骤数
        /// </summary>
        public int TotalSteps { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public Dictionary<string, object>? Data { get; set; }

        public OperationProgress(double percentage, string? message = null)
        {
            Percentage = Math.Max(0, Math.Min(100, percentage));
            Message = message;
        }

        public OperationProgress(int currentStep, int totalSteps, string? message = null)
        {
            CurrentStep = currentStep;
            TotalSteps = totalSteps;
            Percentage = totalSteps > 0 ? (double)currentStep / totalSteps * 100 : 0;
            Message = message;
        }
    }

    /// <summary>
    /// 操作状态枚举
    /// </summary>
    public enum OperationStatus
    {
        /// <summary>
        /// 等待中
        /// </summary>
        Pending,

        /// <summary>
        /// 运行中
        /// </summary>
        Running,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 失败
        /// </summary>
        Failed
    }

    /// <summary>
    /// 操作状态变更事件参数
    /// </summary>
    public class OperationStatusChangedEventArgs : EventArgs
    {
        public string OperationId { get; }
        public string OperationName { get; }
        public OperationStatus OldStatus { get; }
        public OperationStatus NewStatus { get; }
        public Exception? Exception { get; }

        public OperationStatusChangedEventArgs(string operationId, string operationName,
            OperationStatus oldStatus, OperationStatus newStatus, Exception? exception = null)
        {
            OperationId = operationId;
            OperationName = operationName;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Exception = exception;
        }
    }

    /// <summary>
    /// 操作进度事件参数
    /// </summary>
    public class OperationProgressEventArgs : EventArgs
    {
        public string OperationId { get; }
        public string OperationName { get; }
        public OperationProgress Progress { get; }

        public OperationProgressEventArgs(string operationId, string operationName, OperationProgress progress)
        {
            OperationId = operationId;
            OperationName = operationName;
            Progress = progress;
        }
    }
}
