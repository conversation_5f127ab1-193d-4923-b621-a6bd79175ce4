using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using FluentAssertions;

using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Core.Exceptions;

using Xunit;

namespace ProjectDigitizer.Application.Tests.Services;

public class ExceptionHandlingServiceTests
{
    private readonly Mock<IGlobalExceptionHandler> _mockGlobalExceptionHandler;
    private readonly Mock<IStructuredLogger> _mockStructuredLogger;
    private readonly Mock<ErrorRecoveryManager> _mockRecoveryManager;
    private readonly Mock<ILogger<ExceptionHandlingService>> _mockLogger;
    private readonly ExceptionHandlingService _service;

    public ExceptionHandlingServiceTests()
    {
        _mockGlobalExceptionHandler = new Mock<IGlobalExceptionHandler>();
        _mockStructuredLogger = new Mock<IStructuredLogger>();
        _mockRecoveryManager = new Mock<ErrorRecoveryManager>(
            new List<IErrorRecoveryStrategy>(),
            Mock.Of<ILogger<ErrorRecoveryManager>>());
        _mockLogger = new Mock<ILogger<ExceptionHandlingService>>();

        _service = new ExceptionHandlingService(
            _mockGlobalExceptionHandler.Object,
            _mockStructuredLogger.Object,
            _mockRecoveryManager.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void ThrowBusinessRuleViolation_ShouldCreateAndThrowBusinessRuleViolationException()
    {
        // Arrange
        var ruleName = "TestRule";
        var message = "Test rule violation";
        var operationName = "TestOperation";

        // Act & Assert
        var exception = Assert.Throws<BusinessRuleViolationException>(() =>
            _service.ThrowBusinessRuleViolation(ruleName, message, operationName));

        exception.RuleName.Should().Be(ruleName);
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be("BUSINESS_RULE_VIOLATION");
    }

    [Fact]
    public void ThrowEntityNotFound_ShouldCreateAndThrowEntityNotFoundException()
    {
        // Arrange
        var entityType = "TestEntity";
        var entityId = 123;
        var operationName = "TestOperation";

        // Act & Assert
        var exception = Assert.Throws<EntityNotFoundException>(() =>
            _service.ThrowEntityNotFound(entityType, entityId, operationName));

        exception.EntityType.Should().Be(entityType);
        exception.EntityId.Should().Be(entityId);
        exception.ErrorCode.Should().Be("ENTITY_NOT_FOUND");
    }

    [Fact]
    public void ThrowServiceException_ShouldCreateAndThrowServiceException()
    {
        // Arrange
        var serviceName = "TestService";
        var message = "Service error";
        var operationName = "TestOperation";

        // Act & Assert
        var exception = Assert.Throws<ServiceException>(() =>
            _service.ThrowServiceException(serviceName, message, operationName));

        exception.ServiceName.Should().Be(serviceName);
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be("SERVICE_ERROR");
    }

    [Fact]
    public void ValidateAndThrow_WithValidationErrors_ShouldThrowValidationException()
    {
        // Arrange
        var validationErrors = new List<ValidationError>
        {
            new() { PropertyName = "Property1", ErrorMessage = "Error 1" },
            new() { PropertyName = "Property2", ErrorMessage = "Error 2" }
        };
        var operationName = "TestOperation";

        // Act & Assert
        var exception = Assert.Throws<ValidationException>(() =>
            _service.ValidateAndThrow(validationErrors, operationName));

        exception.ValidationErrors.Should().HaveCount(2);
        exception.ErrorCode.Should().Be("VALIDATION_ERROR");
    }

    [Fact]
    public void ValidateAndThrow_WithNoValidationErrors_ShouldNotThrow()
    {
        // Arrange
        var validationErrors = new List<ValidationError>();
        var operationName = "TestOperation";

        // Act & Assert
        _service.Invoking(s => s.ValidateAndThrow(validationErrors, operationName))
            .Should().NotThrow();
    }
}
