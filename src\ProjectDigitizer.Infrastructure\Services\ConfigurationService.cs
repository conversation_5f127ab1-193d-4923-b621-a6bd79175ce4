using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 配置管理服务
/// 提供强类型配置访问和验证功能
/// </summary>
/// <typeparam name="T">配置类型</typeparam>
public class ConfigurationService<T> where T : class, new()
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService<T>> _logger;
    private readonly string _sectionName;
    private T? _cachedConfiguration;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService<T>> logger, string? sectionName = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _sectionName = sectionName ?? typeof(T).Name;
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    /// <returns>配置对象</returns>
    public T GetConfiguration()
    {
        try
        {
            if (_cachedConfiguration != null)
                return _cachedConfiguration;

            _logger.LogDebug("加载配置节: {SectionName}", _sectionName);

            var section = _configuration.GetSection(_sectionName);
            if (!section.Exists())
            {
                _logger.LogWarning("配置节不存在: {SectionName}，使用默认配置", _sectionName);
                _cachedConfiguration = new T();
                return _cachedConfiguration;
            }

            _cachedConfiguration = section.Get<T>();
            if (_cachedConfiguration == null)
            {
                _logger.LogWarning("无法绑定配置节: {SectionName}，使用默认配置", _sectionName);
                _cachedConfiguration = new T();
            }

            _logger.LogDebug("配置加载成功: {SectionName}", _sectionName);
            return _cachedConfiguration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置时发生错误: {SectionName}", _sectionName);
            throw new ConfigurationException($"加载配置失败: {ex.Message}", _sectionName, ex);
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <returns>验证结果</returns>
    public bool ValidateConfiguration()
    {
        try
        {
            _logger.LogDebug("验证配置: {SectionName}", _sectionName);

            var configuration = GetConfiguration();

            // 基本验证：确保配置对象不为空
            if (configuration == null)
            {
                _logger.LogError("配置对象为空: {SectionName}", _sectionName);
                return false;
            }

            // TODO: 添加更多具体的验证逻辑
            // 可以通过实现IValidatableObject接口或使用数据注解来进行验证

            _logger.LogDebug("配置验证通过: {SectionName}", _sectionName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证配置时发生错误: {SectionName}", _sectionName);
            return false;
        }
    }

    /// <summary>
    /// 重新加载配置
    /// </summary>
    public void ReloadConfiguration()
    {
        try
        {
            _logger.LogDebug("重新加载配置: {SectionName}", _sectionName);

            _cachedConfiguration = null;
            GetConfiguration();

            _logger.LogDebug("配置重新加载完成: {SectionName}", _sectionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载配置时发生错误: {SectionName}", _sectionName);
            throw new ConfigurationException($"重新加载配置失败: {ex.Message}", _sectionName, ex);
        }
    }

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>配置值</returns>
    public string? GetValue(string key)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            var fullKey = $"{_sectionName}:{key}";
            var value = _configuration[fullKey];

            _logger.LogDebug("获取配置值: {Key} = {Value}", fullKey, value);

            return value;
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            _logger.LogError(ex, "获取配置值时发生错误: {Key}", key);
            throw new ConfigurationException($"获取配置值失败: {ex.Message}", key, ex);
        }
    }
}
