using System;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Services
{
    /// <summary>
    /// 项目文件适配器
    /// 负责项目文件的保存和加载操作
    /// </summary>
    public class ProjectFileAdapter : IProjectFileAdapter
    {
        private readonly IProjectFileService _projectFileService;
        private readonly ILogger<ProjectFileAdapter> _logger;

        public ProjectFileAdapter(IProjectFileService projectFileService, ILogger<ProjectFileAdapter> logger)
        {
            _projectFileService = projectFileService ?? throw new ArgumentNullException(nameof(projectFileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 保存项目文件
        /// </summary>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="filePath">文件路径</param>
        public async Task SaveProjectAsync(ProjectFile projectFile, string filePath)
        {
            try
            {
                _logger.LogDebug("开始保存项目文件: {FilePath}", filePath);
                await _projectFileService.SaveProjectAsync(projectFile, filePath);
                _logger.LogDebug("项目文件保存成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存项目文件失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 加载项目文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>项目文件数据</returns>
        public async Task<ProjectFile> LoadProjectAsync(string filePath)
        {
            try
            {
                _logger.LogDebug("开始加载项目文件: {FilePath}", filePath);
                var projectFile = await _projectFileService.LoadProjectAsync(filePath);
                _logger.LogDebug("项目文件加载成功: {FilePath}", filePath);
                return projectFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载项目文件失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 创建新的项目文件
        /// </summary>
        /// <param name="projectName">项目名称</param>
        /// <param name="description">项目描述</param>
        /// <returns>新的项目文件</returns>
        public ProjectFile CreateNewProject(string projectName, string? description = null)
        {
            try
            {
                _logger.LogDebug("创建新项目: {ProjectName}", projectName);
                var projectFile = _projectFileService.CreateNewProject(projectName, description);
                _logger.LogDebug("新项目创建成功: {ProjectName}", projectName);
                return projectFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建新项目失败: {ProjectName}", projectName);
                throw;
            }
        }
    }
}
