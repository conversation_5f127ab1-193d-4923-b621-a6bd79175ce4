<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:nodify="https://miroiu.github.io/nodify"
                    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels">

    <!-- ========== 电路连接模板 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的电路连接相关模板 -->

    <!-- 电路连接模板 -->
    <DataTemplate x:Key="CircuitConnectionTemplate" DataType="{x:Type viewmodels:ConnectionViewModel}">
        <!-- 使用Grid来叠加多层效果 -->
        <Grid>
            <!-- 主连接线 -->
            <nodify:CircuitConnection x:Name="MainCircuitConnection"
                                      Source="{Binding Source.Anchor}"
                                      Target="{Binding Target.Anchor}"
                                      Stroke="{Binding ConnectionColor}"
                                      StrokeThickness="2.5"
                                      IsEnabled="{Binding IsEnabled}"
                                      Cursor="Hand"/>

            <!-- 光效流动层 -->
            <nodify:CircuitConnection x:Name="CircuitFlowEffect"
                                      Source="{Binding Source.Anchor}"
                                      Target="{Binding Target.Anchor}"
                                      StrokeThickness="4"
                                      IsEnabled="{Binding IsEnabled}"
                                      IsHitTestVisible="False"
                                      Opacity="0"
                                      StrokeDashArray="12 30"
                                      StrokeDashOffset="0">
                <!-- 光效渐变画刷 -->
                <nodify:CircuitConnection.Stroke>
                    <SolidColorBrush Color="#C0FFFFFF" x:Name="CircuitFlowBrush"/>
                </nodify:CircuitConnection.Stroke>
            </nodify:CircuitConnection>
        </Grid>

        <DataTemplate.Triggers>
            <!-- 禁用状态 -->
            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                <Setter TargetName="MainCircuitConnection" Property="Opacity" Value="0.5"/>
                <Setter TargetName="MainCircuitConnection" Property="StrokeDashArray" Value="5,5"/>
            </DataTrigger>

            <!-- 鼠标悬停效果 -->
            <DataTrigger Binding="{Binding ElementName=MainCircuitConnection, Path=IsMouseOver}" Value="True">
                <Setter TargetName="MainCircuitConnection" Property="StrokeThickness" Value="3"/>
            </DataTrigger>

            <!-- 慢速光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Slow">
                <Setter TargetName="CircuitFlowEffect" Property="Opacity" Value="0.8"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="CircuitFlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:2"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 正常速度光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Normal">
                <Setter TargetName="CircuitFlowEffect" Property="Opacity" Value="0.9"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="CircuitFlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:1"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 快速光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Fast">
                <Setter TargetName="CircuitFlowEffect" Property="Opacity" Value="1.0"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="CircuitFlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:0.5"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

</ResourceDictionary>
