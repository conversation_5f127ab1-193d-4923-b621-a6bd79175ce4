<UserControl x:Class="ProjectDigitizer.Studio.Controls.Common.ConnectionPoint"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="20"
             d:DesignWidth="20">

    <UserControl.Resources>
        <!-- 连接点脉冲动画 -->
        <Storyboard x:Key="ConnectionPulseAnimation"
                    RepeatBehavior="Forever">
            <!-- 脉冲环动画 -->
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PulseRing"
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                                      Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5"
                                      Value="0.8"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5"
                                      Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PulseRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                                      Value="0.8"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5"
                                      Value="1.8">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="PulseRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                                      Value="0.8"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5"
                                      Value="1.8">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>

            <!-- 主连接点轻微脉动 -->
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="InnerDot"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                                      Value="1.0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.75"
                                      Value="1.05"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5"
                                      Value="1.0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="InnerDot"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                                      Value="1.0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.75"
                                      Value="1.05"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5"
                                      Value="1.0"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>

        <!-- 连接成功动画 -->
        <Storyboard x:Key="ConnectionSuccessAnimation">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ConnectionIndicator"
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2"
                        Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.8"
                        Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1"
                        Value="0.7"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.1"
                        Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5"
                        Value="0.8"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0.5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                        Value="1.3">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <BackEase EasingMode="EaseOut"
                                Amplitude="0.3"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5"
                        Value="1.1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0.5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                        Value="1.3">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <BackEase EasingMode="EaseOut"
                                Amplitude="0.3"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5"
                        Value="1.1"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>

        <!-- 连接断开动画 -->
        <Storyboard x:Key="ConnectionDisconnectAnimation">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="ConnectionIndicator"
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0.7"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3"
                        Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="0.8"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4"
                        Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="1.1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4"
                        Value="0.8">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="OuterRing"
                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="0:0:0"
                        Value="1.1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4"
                        Value="0.8">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </UserControl.Resources>

    <Grid Width="24"
          Height="24"
          Cursor="Hand">
        <!-- 背景光晕效果 -->
        <Ellipse x:Name="GlowRing"
                 Width="20"
                 Height="20"
                 Fill="Transparent"
                 Stroke="{Binding AccentColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="2"
                 Opacity="0"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1"
                                ScaleY="1"/>
            </Ellipse.RenderTransform>
            <Ellipse.Effect>
                <BlurEffect Radius="3"/>
            </Ellipse.Effect>
        </Ellipse>

        <!-- 外圈指示器 - 用于hover和连接状态 -->
        <Ellipse x:Name="OuterRing"
                 Width="16"
                 Height="16"
                 Fill="Transparent"
                 Stroke="{Binding AccentColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="1.5"
                 Opacity="0"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1"
                                ScaleY="1"/>
            </Ellipse.RenderTransform>
        </Ellipse>

        <!-- 主连接点 -->
        <Ellipse x:Name="InnerDot"
                 Width="10"
                 Height="10"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Opacity="0.9"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1"
                                ScaleY="1"/>
            </Ellipse.RenderTransform>
            <Ellipse.Fill>
                <RadialGradientBrush x:Name="InnerDotBrush">
                    <GradientStop Color="#4A90E2"
                                  Offset="0"/>
                    <GradientStop Color="#2E5BBA"
                                  Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
            <Ellipse.Stroke>
                <LinearGradientBrush StartPoint="0,0"
                                     EndPoint="1,1">
                    <GradientStop Color="White"
                                  Offset="0"/>
                    <GradientStop Color="#E0E0E0"
                                  Offset="1"/>
                </LinearGradientBrush>
            </Ellipse.Stroke>
            <Ellipse.StrokeThickness>1</Ellipse.StrokeThickness>
            <Ellipse.Effect>
                <DropShadowEffect x:Name="InnerShadow"
                                  Color="#40000000"
                                  Opacity="0.3"
                                  ShadowDepth="0"
                                  BlurRadius="3"/>
            </Ellipse.Effect>
        </Ellipse>

        <!-- 连接状态指示器 -->
        <Ellipse x:Name="ConnectionIndicator"
                 Width="4"
                 Height="4"
                 Fill="White"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Opacity="0">
            <Ellipse.Effect>
                <DropShadowEffect Color="Black"
                                  Opacity="0.5"
                                  ShadowDepth="0"
                                  BlurRadius="2"/>
            </Ellipse.Effect>
        </Ellipse>

        <!-- 脉冲效果圆环 -->
        <Ellipse x:Name="PulseRing"
                 Width="12"
                 Height="12"
                 Fill="Transparent"
                 Stroke="{Binding AccentColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                 StrokeThickness="1"
                 Opacity="0"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1"
                                ScaleY="1"/>
            </Ellipse.RenderTransform>
        </Ellipse>
    </Grid>

    <UserControl.Triggers>
        <!-- 鼠标进入动画 -->
        <EventTrigger RoutedEvent="MouseEnter">
            <BeginStoryboard>
                <Storyboard>
                    <!-- 外圈显示 -->
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0.8"
                                     Duration="0:0:0.25"/>
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                     To="1.1"
                                     Duration="0:0:0.25"/>
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                     To="1.1"
                                     Duration="0:0:0.25"/>

                    <!-- 光晕效果 -->
                    <DoubleAnimation Storyboard.TargetName="GlowRing"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0.4"
                                     Duration="0:0:0.25"/>

                    <!-- 主连接点放大 -->
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                     To="1.2"
                                     Duration="0:0:0.25"/>
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                     To="1.2"
                                     Duration="0:0:0.25"/>
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="Opacity"
                                     To="1.0"
                                     Duration="0:0:0.25"/>

                    <!-- 阴影增强 -->
                    <DoubleAnimation Storyboard.TargetName="InnerShadow"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0.6"
                                     Duration="0:0:0.25"/>
                    <DoubleAnimation Storyboard.TargetName="InnerShadow"
                                     Storyboard.TargetProperty="BlurRadius"
                                     To="6"
                                     Duration="0:0:0.25"/>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>

        <!-- 鼠标离开动画 -->
        <EventTrigger RoutedEvent="MouseLeave">
            <BeginStoryboard>
                <Storyboard>
                    <!-- 外圈隐藏 -->
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0"
                                     Duration="0:0:0.3"/>
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                     To="1.0"
                                     Duration="0:0:0.3"/>
                    <DoubleAnimation Storyboard.TargetName="OuterRing"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                     To="1.0"
                                     Duration="0:0:0.3"/>

                    <!-- 光晕隐藏 -->
                    <DoubleAnimation Storyboard.TargetName="GlowRing"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0"
                                     Duration="0:0:0.3"/>

                    <!-- 主连接点恢复 -->
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                     To="1.0"
                                     Duration="0:0:0.3"/>
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                     To="1.0"
                                     Duration="0:0:0.3"/>
                    <DoubleAnimation Storyboard.TargetName="InnerDot"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0.9"
                                     Duration="0:0:0.3"/>

                    <!-- 阴影恢复 -->
                    <DoubleAnimation Storyboard.TargetName="InnerShadow"
                                     Storyboard.TargetProperty="Opacity"
                                     To="0.3"
                                     Duration="0:0:0.3"/>
                    <DoubleAnimation Storyboard.TargetName="InnerShadow"
                                     Storyboard.TargetProperty="BlurRadius"
                                     To="3"
                                     Duration="0:0:0.3"/>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </UserControl.Triggers>
</UserControl>
