using System;
using System.Collections.Generic;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 画布虚拟化管理器接口
    /// </summary>
    public interface ICanvasVirtualizationManager : IDisposable
    {
        /// <summary>
        /// 当前可见的节点集合
        /// </summary>
        IEnumerable<T> GetVisibleNodes<T>() where T : class;

        /// <summary>
        /// 更新视口信息
        /// </summary>
        void UpdateViewport(ViewportRect viewport, double zoom);

        /// <summary>
        /// 添加节点到管理器
        /// </summary>
        void AddNode<T>(T node) where T : class;

        /// <summary>
        /// 从管理器移除节点
        /// </summary>
        void RemoveNode<T>(T node) where T : class;

        /// <summary>
        /// 清空所有节点
        /// </summary>
        void Clear();

        /// <summary>
        /// 强制更新节点可见性
        /// </summary>
        void ForceUpdateVisibility();

        /// <summary>
        /// 可见性变化事件：newVisible, hidden, shown
        /// </summary>
        event Action<IEnumerable<object>, IEnumerable<object>, IEnumerable<object>>? OnVisibilityChanged;
    }
}
