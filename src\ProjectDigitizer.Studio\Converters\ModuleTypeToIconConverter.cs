using System;
using System.Globalization;
using System.Windows.Data;

using MahApps.Metro.IconPacks;

using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 模块类型到图标的转换器
    /// </summary>
    public class ModuleTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value is ModuleType moduleType)
                {
                    return GetIconForModuleType(moduleType);
                }
            }
            catch (Exception)
            {
                // 如果转换失败，返回默认图标
            }
            return PackIconMaterialKind.Cog; // 默认图标
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 根据模块类型获取对应的图标
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>图标类型</returns>
        private static PackIconMaterialKind GetIconForModuleType(ModuleType moduleType)
        {
            return moduleType switch
            {
                // 常规数据类 - 管道和基础设施图标
                ModuleType.PipeLine => PackIconMaterialKind.Pipe,
                ModuleType.RiserPipe => PackIconMaterialKind.PipeDisconnected,
                ModuleType.PressureBox => PackIconMaterialKind.Gauge,
                ModuleType.Excavation => PackIconMaterialKind.Shovel,
                ModuleType.Demolition => PackIconMaterialKind.Hammer,
                ModuleType.AntiCorrosion => PackIconMaterialKind.Shield,
                ModuleType.LightningProtection => PackIconMaterialKind.Flash,

                // 数据衍生关联类 - 连接和关联图标
                ModuleType.WarningBand => PackIconMaterialKind.Alert,
                ModuleType.WeldInspection => PackIconMaterialKind.Magnify,
                ModuleType.InstallationTeam => PackIconMaterialKind.AccountGroup,
                ModuleType.Measures => PackIconMaterialKind.Ruler,

                // 触发器类 - 触发和启动图标
                ModuleType.ClickTrigger => PackIconMaterialKind.CursorDefaultClick,
                ModuleType.AssociationTrigger => PackIconMaterialKind.Link,
                ModuleType.TimedTrigger => PackIconMaterialKind.Timer,
                ModuleType.FileChangeTrigger => PackIconMaterialKind.FileAlert,
                ModuleType.EnvironmentTrigger => PackIconMaterialKind.Earth,

                // 处理类 - 数据处理图标
                ModuleType.DataFilter => PackIconMaterialKind.Filter,
                ModuleType.TagSearch => PackIconMaterialKind.TagSearch,
                ModuleType.DataCalculation => PackIconMaterialKind.Calculator,
                ModuleType.DataValidation => PackIconMaterialKind.CheckCircle,
                ModuleType.DataTransform => PackIconMaterialKind.SwapHorizontal,
                ModuleType.DataCondition => PackIconMaterialKind.CodeBraces,
                ModuleType.DataMerge => PackIconMaterialKind.Merge,
                ModuleType.DataSort => PackIconMaterialKind.Sort,
                ModuleType.DataGroup => PackIconMaterialKind.Group,
                ModuleType.ArrayExpansion => PackIconMaterialKind.UnfoldMoreHorizontal,
                ModuleType.Other => PackIconMaterialKind.Cog,

                // 输入类 - 数据源图标
                ModuleType.FileInput => PackIconMaterialKind.File,
                ModuleType.DatabaseInput => PackIconMaterialKind.Database,
                ModuleType.APIInput => PackIconMaterialKind.Api,
                ModuleType.CADInput => PackIconMaterialKind.DrawingBox,
                ModuleType.ExcelInput => PackIconMaterialKind.FileExcel,
                ModuleType.CSVInput => PackIconMaterialKind.FileDelimited,
                ModuleType.XMLInput => PackIconMaterialKind.FileCode,
                ModuleType.JSONInput => PackIconMaterialKind.CodeJson,
                ModuleType.ManualDataInput => PackIconMaterialKind.Keyboard,

                // 智能体和控制类
                ModuleType.AIAgent => PackIconMaterialKind.Robot,
                ModuleType.ConditionalBranch => PackIconMaterialKind.SourceBranch,
                ModuleType.LoopProcessor => PackIconMaterialKind.Repeat,
                ModuleType.ErrorHandler => PackIconMaterialKind.AlertCircle,
                ModuleType.FlowControl => PackIconMaterialKind.TrafficLight,
                ModuleType.ScriptExecutor => PackIconMaterialKind.Script,
                ModuleType.VariableManager => PackIconMaterialKind.Variable,
                ModuleType.StateManager => PackIconMaterialKind.StateMachine,

                // 整理类 - 组织和管理图标
                ModuleType.TableManager => PackIconMaterialKind.Table,
                ModuleType.GraphicsAPI => PackIconMaterialKind.VectorTriangle,
                ModuleType.ExcelCSV => PackIconMaterialKind.FileExcel,
                ModuleType.WordProcessor => PackIconMaterialKind.FileWord,

                // 输出类 - 导出和输出图标
                ModuleType.FileGeneration => PackIconMaterialKind.FileDocument,
                ModuleType.ManualLocation => PackIconMaterialKind.MapMarker,
                ModuleType.SpecifiedPath => PackIconMaterialKind.FolderOpen,
                ModuleType.ThirdPartyAPI => PackIconMaterialKind.Api,
                ModuleType.CADExport => PackIconMaterialKind.FileOutline,
                ModuleType.ExcelExport => PackIconMaterialKind.FileExcel,
                ModuleType.CSVExport => PackIconMaterialKind.FileDelimited,
                ModuleType.WordExport => PackIconMaterialKind.FileWord,
                ModuleType.PPTExport => PackIconMaterialKind.FilePowerpoint,
                ModuleType.ImageExport => PackIconMaterialKind.FileImage,
                ModuleType.PublishRelease => PackIconMaterialKind.Publish,
                ModuleType.NotificationAlert => PackIconMaterialKind.Bell,
                ModuleType.DialogChat => PackIconMaterialKind.MessageText,
                ModuleType.OtherOutput => PackIconMaterialKind.Export,

                // 默认图标
                _ => PackIconMaterialKind.Cog
            };
        }
    }
}
