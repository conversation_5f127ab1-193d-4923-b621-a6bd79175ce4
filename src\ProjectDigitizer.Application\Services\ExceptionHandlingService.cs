using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Exceptions;

namespace ProjectDigitizer.Application.Services;

/// <summary>
/// 异常处理服务
/// 提供应用层的统一异常处理机制
/// </summary>
public class ExceptionHandlingService : IExceptionHandlingService
{
    private readonly IGlobalExceptionHandler _globalExceptionHandler;
    private readonly IStructuredLogger _structuredLogger;
    private readonly ErrorRecoveryManager _recoveryManager;
    private readonly ILogger<ExceptionHandlingService> _logger;
    private readonly Dictionary<Type, Func<Exception, string, Task<ExceptionHandlingResult>>> _handlers;

    public ExceptionHandlingService(
        IGlobalExceptionHandler globalExceptionHandler,
        IStructuredLogger structuredLogger,
        ErrorRecoveryManager recoveryManager,
        ILogger<ExceptionHandlingService> logger)
    {
        _globalExceptionHandler = globalExceptionHandler ?? throw new ArgumentNullException(nameof(globalExceptionHandler));
        _structuredLogger = structuredLogger ?? throw new ArgumentNullException(nameof(structuredLogger));
        _recoveryManager = recoveryManager ?? throw new ArgumentNullException(nameof(recoveryManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _handlers = new Dictionary<Type, Func<Exception, string, Task<ExceptionHandlingResult>>>();
    }

    /// <inheritdoc />
    public async Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, string context)
    {
        var exceptionType = exception.GetType();

        // 尝试使用注册的处理器
        if (_handlers.TryGetValue(exceptionType, out var handler))
        {
            return await handler(exception, context);
        }

        // 使用默认处理逻辑
        await _globalExceptionHandler.HandleExceptionAsync(exception, context);

        // 尝试恢复
        var recoveryResult = await _recoveryManager.TryRecoverAsync(exception, context);

        return new ExceptionHandlingResult
        {
            IsHandled = true,
            UserMessage = GetUserFriendlyMessage(exception),
            ShouldRetry = recoveryResult.IsSuccessful,
            RetryDelay = TimeSpan.FromSeconds(1),
            AdditionalData = new Dictionary<string, object>
            {
                ["RecoveryAttempted"] = true,
                ["RecoverySuccessful"] = recoveryResult.IsSuccessful
            }
        };
    }

    /// <inheritdoc />
    public void RegisterHandler<T>(Func<T, string, Task<ExceptionHandlingResult>> handler) where T : Exception
    {
        _handlers[typeof(T)] = (ex, context) => handler((T)ex, context);
    }

    /// <summary>
    /// 执行操作并处理异常
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="context">操作上下文</param>
    /// <returns>操作结果</returns>
    public async Task<T> ExecuteWithExceptionHandlingAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        object? context = null)
    {
        using var tracker = _structuredLogger.StartOperation(operationName, new Dictionary<string, object>
        {
            ["Context"] = context?.ToString() ?? "N/A"
        });

        try
        {
            var result = await operation();
            tracker.Complete(result);
            return result;
        }
        catch (Exception ex)
        {
            tracker.Fail(ex);
            await _globalExceptionHandler.HandleExceptionAsync(ex, operationName);

            // 尝试恢复
            var recoveryResult = await _recoveryManager.TryRecoverAsync(ex, context);
            if (!recoveryResult.IsSuccessful && !recoveryResult.RequiresUserIntervention)
            {
                throw; // 重新抛出不可恢复的异常
            }

            // 返回默认值或重新抛出
            if (typeof(T).IsValueType)
            {
                return default(T)!;
            }

            throw; // 对于引用类型，重新抛出异常
        }
    }

    /// <summary>
    /// 执行操作并处理异常（无返回值）
    /// </summary>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="context">操作上下文</param>
    public async Task ExecuteWithExceptionHandlingAsync(
        Func<Task> operation,
        string operationName,
        object? context = null)
    {
        using var tracker = _structuredLogger.StartOperation(operationName, new Dictionary<string, object>
        {
            ["Context"] = context?.ToString() ?? "N/A"
        });

        try
        {
            await operation();
            tracker.Complete();
        }
        catch (Exception ex)
        {
            tracker.Fail(ex);
            await _globalExceptionHandler.HandleExceptionAsync(ex, operationName);

            // 尝试恢复
            var recoveryResult = await _recoveryManager.TryRecoverAsync(ex, context);
            if (!recoveryResult.IsSuccessful && !recoveryResult.RequiresUserIntervention)
            {
                throw; // 重新抛出不可恢复的异常
            }
        }
    }

    /// <summary>
    /// 同步执行操作并处理异常
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="context">操作上下文</param>
    /// <returns>操作结果</returns>
    public T ExecuteWithExceptionHandling<T>(
        Func<T> operation,
        string operationName,
        object? context = null)
    {
        using var tracker = _structuredLogger.StartOperation(operationName, new Dictionary<string, object>
        {
            ["Context"] = context?.ToString() ?? "N/A"
        });

        try
        {
            var result = operation();
            tracker.Complete(result);
            return result;
        }
        catch (Exception ex)
        {
            tracker.Fail(ex);
            _globalExceptionHandler.HandleException(ex, operationName);

            // 对于同步操作，直接重新抛出异常
            throw;
        }
    }

    /// <summary>
    /// 验证并抛出验证异常
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    /// <param name="operationName">操作名称</param>
    public void ValidateAndThrow(List<ValidationError> validationErrors, string operationName)
    {
        if (validationErrors.Count > 0)
        {
            var validationException = new ValidationException(validationErrors);
            _structuredLogger.LogError(validationException, operationName, new { ValidationErrors = validationErrors });
            throw validationException;
        }
    }

    /// <summary>
    /// 创建并抛出业务规则违反异常
    /// </summary>
    /// <param name="ruleName">规则名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="operationName">操作名称</param>
    public void ThrowBusinessRuleViolation(string ruleName, string message, string operationName)
    {
        var exception = new BusinessRuleViolationException(ruleName, message);
        _structuredLogger.LogError(exception, operationName, new { RuleName = ruleName });
        throw exception;
    }

    /// <summary>
    /// 创建并抛出实体未找到异常
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="entityId">实体ID</param>
    /// <param name="operationName">操作名称</param>
    public void ThrowEntityNotFound(string entityType, object entityId, string operationName)
    {
        var exception = new EntityNotFoundException(entityType, entityId);
        _structuredLogger.LogError(exception, operationName, new { EntityType = entityType, EntityId = entityId });
        throw exception;
    }

    /// <summary>
    /// 创建并抛出服务异常
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="message">错误消息</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="innerException">内部异常</param>
    public void ThrowServiceException(string serviceName, string message, string operationName, Exception? innerException = null)
    {
        var exception = innerException != null
            ? new ServiceException(serviceName, message, innerException)
            : new ServiceException(serviceName, message);

        _structuredLogger.LogError(exception, operationName, new { ServiceName = serviceName });
        throw exception;
    }

    /// <summary>
    /// 获取用户友好的错误消息
    /// </summary>
    private string GetUserFriendlyMessage(Exception exception)
    {
        return exception switch
        {
            ValidationException => "输入数据验证失败，请检查输入内容",
            BusinessRuleViolationException => "操作违反了业务规则",
            EntityNotFoundException => "未找到指定的数据",
            ServiceException => "服务执行过程中发生错误",
            _ => "系统发生了未知错误，请稍后重试"
        };
    }
}
