<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:nodify="https://miroiu.github.io/nodify">

    <!-- ========== 输出连接器样式 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的输出连接器相关样式 -->

    <!-- 现代化输出连接器样式 -->
    <Style x:Key="ModernOutputConnectorStyle" TargetType="nodify:NodeOutput">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="nodify:NodeOutput">
                    <Ellipse Width="10"
                             Height="10"
                             Fill="{StaticResource Brush.Primary}"
                             Stroke="{StaticResource Brush.Surface}"
                             StrokeThickness="1"
                             Cursor="Hand">
                        <Ellipse.Style>
                            <Style TargetType="Ellipse">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Fill" Value="{StaticResource Brush.PrimaryVariant}"/>
                                        <Setter Property="StrokeThickness" Value="2"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Ellipse.Style>
                    </Ellipse>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HTML原型输出连接器样式 -->
    <Style x:Key="HtmlPrototypeOutputConnectorStyle" TargetType="nodify:NodeOutput">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="nodify:NodeOutput">
                    <Ellipse Width="12"
                             Height="12"
                             Fill="{StaticResource Brush.Primary}"
                             Stroke="{StaticResource Brush.Surface}"
                             StrokeThickness="1"
                             Cursor="Hand"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 高级输出连接器模板 - 支持多连接和状态指示 -->
    <DataTemplate x:Key="AdvancedOutputConnectorTemplate">
        <nodify:NodeOutput Header=""
                           IsConnected="{Binding IsConnected}"
                           Anchor="{Binding Anchor, Mode=OneWayToSource}"
                           ToolTip="{Binding Title}">
            <nodify:NodeOutput.Template>
                <ControlTemplate TargetType="nodify:NodeOutput">
                    <Grid Width="20" Height="20" Cursor="Hand">
                        <!-- 外圈指示器 - 用于hover和连接状态 -->
                        <Ellipse x:Name="OuterRing"
                                 Width="18"
                                 Height="18"
                                 Fill="Transparent"
                                 Stroke="{StaticResource OutputConnectionAccentBrush}"
                                 StrokeThickness="1"
                                 Opacity="0"
                                 StrokeDashArray="2,2"
                                 RenderTransformOrigin="0.5,0.5">
                            <Ellipse.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Ellipse.RenderTransform>
                        </Ellipse>

                        <!-- 主连接器 - 优化设计 -->
                        <Ellipse x:Name="MainConnector"
                                 Width="10"
                                 Height="10"
                                 Fill="{StaticResource OutputConnectionBrush}"
                                 Stroke="{StaticResource ConnectionPointBorderBrush}"
                                 StrokeThickness="1.5"
                                 Opacity="0.8"
                                 RenderTransformOrigin="0.5,0.5">
                            <Ellipse.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                            </Ellipse.RenderTransform>
                            <Ellipse.Effect>
                                <DropShadowEffect Color="{StaticResource Color.Shadow}"
                                                  Opacity="0.2"
                                                  ShadowDepth="1"
                                                  BlurRadius="2"/>
                            </Ellipse.Effect>
                        </Ellipse>

                        <!-- 连接状态指示器 -->
                        <Ellipse x:Name="ConnectionIndicator"
                                 Width="3"
                                 Height="3"
                                 Fill="White"
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center"
                                 Opacity="0"/>

                        <!-- 多连接数量徽章 -->
                        <Border x:Name="ConnectionCountBadge"
                                CornerRadius="10"
                                MinWidth="20"
                                Height="20"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                Margin="0,-10,-10,0"
                                Background="{StaticResource Brush.Danger}"
                                Visibility="Collapsed">
                            <Border.Effect>
                                <DropShadowEffect Color="#000000"
                                                  Opacity="0.25"
                                                  ShadowDepth="2"
                                                  BlurRadius="4"/>
                            </Border.Effect>
                            <TextBlock Text="{Binding ConnectionCount, FallbackValue=0}"
                                       Foreground="White"
                                       FontSize="10"
                                       FontWeight="Bold"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Margin="6,2"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 鼠标悬停效果 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="OuterRing" Property="Opacity" Value="0.6"/>
                            <Setter TargetName="MainConnector" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="1.3" ScaleY="1.3"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="MainConnector" Property="Opacity" Value="1.0"/>
                            <Setter TargetName="MainConnector" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{StaticResource Color.Primary}"
                                                      Opacity="0.4"
                                                      ShadowDepth="0"
                                                      BlurRadius="4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <!-- 已连接状态 -->
                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                            <Setter TargetName="ConnectionIndicator" Property="Opacity" Value="1.0"/>
                            <Setter TargetName="MainConnector" Property="Fill" Value="{StaticResource Brush.Primary}"/>
                        </DataTrigger>

                        <!-- 多连接徽章触发器 -->
                        <DataTrigger Binding="{Binding HasMultipleConnections}" Value="True">
                            <Setter TargetName="ConnectionCountBadge" Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </nodify:NodeOutput.Template>
        </nodify:NodeOutput>
    </DataTemplate>

</ResourceDictionary>
