using System.Windows;
using System.Windows.Input;

using Microsoft.Xaml.Behaviors;

using Nodify;

namespace ProjectDigitizer.Studio.Behaviors
{
    /// <summary>
    /// 画布拖拽行为
    /// </summary>
    public class CanvasDragBehavior : Behavior<NodifyEditor>
    {
        /// <summary>
        /// 附加到对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.Drop += OnDrop;
            AssociatedObject.DragOver += OnDragOver;
            AssociatedObject.AllowDrop = true;
        }

        /// <summary>
        /// 从对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            base.OnDetaching();
            AssociatedObject.Drop -= OnDrop;
            AssociatedObject.DragOver -= OnDragOver;
        }

        /// <summary>
        /// 拖拽悬停事件处理
        /// </summary>
        private void OnDragOver(object sender, DragEventArgs e)
        {
            e.Effects = e.Data.GetDataPresent("TemplateItem") ? DragDropEffects.Copy : DragDropEffects.None;
            e.Handled = true;
        }

        /// <summary>
        /// 拖拽放置事件处理
        /// </summary>
        private void OnDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("TemplateItem"))
            {
                var position = e.GetPosition(AssociatedObject);
                // 这里可以添加具体的拖拽处理逻辑
                e.Handled = true;
            }
        }
    }

    /// <summary>
    /// 键盘快捷键行为
    /// </summary>
    public class KeyboardShortcutBehavior : Behavior<FrameworkElement>
    {
        /// <summary>
        /// 附加到对象时调用
        /// </summary>
        protected override void OnAttached()
        {
            base.OnAttached();
            AssociatedObject.KeyDown += OnKeyDown;
        }

        /// <summary>
        /// 从对象分离时调用
        /// </summary>
        protected override void OnDetaching()
        {
            base.OnDetaching();
            AssociatedObject.KeyDown -= OnKeyDown;
        }

        /// <summary>
        /// 键盘按下事件处理
        /// </summary>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            // 处理常用快捷键
            if (Keyboard.Modifiers == ModifierKeys.Control)
            {
                switch (e.Key)
                {
                    case Key.S:
                        // 保存快捷键
                        e.Handled = true;
                        break;
                    case Key.O:
                        // 打开快捷键
                        e.Handled = true;
                        break;
                    case Key.N:
                        // 新建快捷键
                        e.Handled = true;
                        break;
                }
            }
        }
    }
}
