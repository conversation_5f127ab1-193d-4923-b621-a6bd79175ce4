using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Core.Entities
{
    /// <summary>
    /// 函数显示项 - 用于在节点的 ExpandedContent 中显示函数信息
    /// </summary>
    public class FunctionDisplayItem : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _expression = string.Empty;
        private bool _isEnabled = true;
        private string _result = string.Empty;
        private string _status = "就绪";

        /// <summary>函数名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>函数表达式</summary>
        public string Expression
        {
            get => _expression;
            set => SetProperty(ref _expression, value);
        }

        /// <summary>是否启用</summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>计算结果</summary>
        public string Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        /// <summary>状态</summary>
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>显示文本（用于节点内容显示）</summary>
        public string DisplayText => string.IsNullOrEmpty(Expression) ? Name : $"{Name}: {Expression}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

            // 当名称或表达式变化时，通知显示文本也变化了
            if (propertyName == nameof(Name) || propertyName == nameof(Expression))
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(DisplayText)));
            }
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
