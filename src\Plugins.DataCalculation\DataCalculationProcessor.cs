using System.Collections;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace Plugins.DataCalculation;

/// <summary>
/// 数据计算处理器：读取节点配置中的表达式，注入输入变量后求值；
/// 若未配置表达式，则对输入做数值求和作为降级处理。
/// </summary>
/// <summary>
/// 数据计算节点的运行时处理器。
/// - 当节点属性中存在表达式时，按 CalcEngine 计算（变量来自上游 inputData）。
/// - 当表达式为空时，降级为对输入数据求和（支持集合递归）。
/// </summary>
public class DataCalculationProcessor : INodeProcessor
{
    public ModuleType SupportedModuleType => ModuleType.DataCalculation;

    /// <summary>
    /// 执行当前节点。
    /// </summary>
    /// <param name="node">节点实例。</param>
    /// <param name="inputData">来自上游的输入数据。</param>
    /// <returns>节点执行结果。</returns>
    public NodeExecutionResult Execute(INode node, Dictionary<string, object> inputData)
    {
        var sw = System.Diagnostics.Stopwatch.StartNew();
        double sum = 0d;
        try
        {
            // 从节点属性读取表达式（兼容以 '=' 开头的样式）
            var expr = (node.Properties as TransformNodeProperties)?.Expression?.Trim();

            if (!string.IsNullOrWhiteSpace(expr))
            {
                // 通过门面统一注入变量与文化、并做错误转换
                var value = Services.CalcEngineFacade.Evaluate(expr!, inputData);

                var result = new NodeExecutionResult
                {
                    IsSuccess = true,
                    ExecutionTime = sw.Elapsed
                };
                result.OutputData["Result"] = value ?? 0d;
                result.Metadata["processor"] = nameof(DataCalculationProcessor);
                result.Metadata["mode"] = "CalcEngine";
                result.Metadata["timestamp"] = DateTimeOffset.UtcNow;
                return result;
            }
            else
            {
                foreach (var kv in inputData)
                {
                    sum += SumObject(kv.Value);
                }

                var result = new NodeExecutionResult
                {
                    IsSuccess = true,
                    ExecutionTime = sw.Elapsed
                };
                result.OutputData["Result"] = sum;
                result.Metadata["processor"] = nameof(DataCalculationProcessor);
                result.Metadata["mode"] = "SumFallback";
                result.Metadata["timestamp"] = DateTimeOffset.UtcNow;
                return result;
            }
        }
        catch (Exception ex)
        {
            return new NodeExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ExecutionTime = sw.Elapsed
            };
        }
        finally { sw.Stop(); }
    }

    private static double SumObject(object? value)
    {
        if (value is null) return 0d;
        if (value is byte b) return b;
        if (value is sbyte sb) return sb;
        if (value is short s) return s;
        if (value is ushort uss) return uss;
        if (value is int i) return i;
        if (value is uint ui) return ui;
        if (value is long l) return l;
        if (value is ulong ul) return ul;
        if (value is float f) return f;
        if (value is double d) return d;
        if (value is decimal m) return (double)m;
        if (value is string str && double.TryParse(str, out var parsed)) return parsed;
        if (value is IEnumerable enumerable)
        {
            double total = 0d;
            foreach (var item in enumerable) total += SumObject(item);
            return total;
        }
        return 0d;
    }

    /// <summary>
    /// 定义输入端口。
    /// </summary>
    public IEnumerable<IPortDefinition> GetInputPorts()
    {
        return new[]
        {
            new PortDefinition
            {
                Id = "in",
                Name = "输入",
                Description = "要参与计算的数据（标量/数值集合）",
                DataType = "number",
                IsRequired = false,
                AllowMultipleConnections = true,
                Position = PortPosition.Left
            }
        };
    }

    /// <summary>
    /// 定义输出端口。
    /// </summary>
    public IEnumerable<IPortDefinition> GetOutputPorts()
    {
        return new[]
        {
            new PortDefinition
            {
                Id = "result",
                Name = "结果",
                Description = "表达式计算得到的数值结果",
                DataType = "number",
                IsRequired = false,
                AllowMultipleConnections = true,
                Position = PortPosition.Right
            }
        };
    }

    /// <summary>
    /// 校验节点配置（此处简单返回通过，复杂校验由 Inspector 完成）。
    /// </summary>
    public ValidationResult ValidateConfiguration(INode node) => new();
}

internal sealed class PortDefinition : IPortDefinition
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool AllowMultipleConnections { get; set; } = true;
    public PortPosition Position { get; set; }
}


