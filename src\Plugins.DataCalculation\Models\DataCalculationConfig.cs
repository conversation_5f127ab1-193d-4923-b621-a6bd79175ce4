using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Plugins.DataCalculation.Models
{
    /// <summary>
    /// 计算触发与执行模式。
    /// </summary>
    public enum CalculationMode
    {
        Realtime,
        Manual,
        Scheduled
    }

    /// <summary>
    /// 执行出错时的处理策略。
    /// </summary>
    public enum ErrorHandling
    {
        ReturnError,
        UseDefault,
        LogOnly,
        Stop
    }

    /// <summary>
    /// 输出结果的格式。
    /// </summary>
    public enum OutputFormat
    {
        Number,
        Text,
        Json,
        Table
    }

    /// <summary>
    /// 变量映射：将变量名绑定到上游节点及其属性。
    /// </summary>
    public sealed class VariableMapping
    {
        /// <summary>变量名（用于公式中的占位：{name}）。</summary>
        [JsonPropertyName("name")] public string Name { get; set; } = string.Empty;
        /// <summary>上游源节点 Id（可选）。</summary>
        [JsonPropertyName("sourceNodeId")] public string? SourceNodeId { get; set; }
        /// <summary>从上游节点读取的属性名（如 Result、Value 等）。</summary>
        [JsonPropertyName("propertyName")] public string? PropertyName { get; set; }
    }

    /// <summary>
    /// 函数定义：命名的公式片段，便于在节点上组织多条计算。
    /// </summary>
    public sealed class FunctionDef
    {
        /// <summary>函数名称（用于节点展开视图显示）。</summary>
        [JsonPropertyName("name")] public string Name { get; set; } = string.Empty;
        /// <summary>公式表达式，支持 {变量} 占位与 ExcelLike 语法。</summary>
        [JsonPropertyName("expression")] public string Expression { get; set; } = string.Empty;
    }

    /// <summary>
    /// 数据计算配置的强类型模型，持久化于 Module.Parameters["dataCalculation"] 的 JSON 中。
    /// </summary>
    public sealed class DataCalculationConfig
    {
        /// <summary>计算模式。</summary>
        [JsonPropertyName("calculationMode")] public CalculationMode CalculationMode { get; set; } = CalculationMode.Realtime;
        /// <summary>小数精度（四舍五入）。</summary>
        [JsonPropertyName("precision")] public int Precision { get; set; } = 2;
        /// <summary>错误处理策略。</summary>
        [JsonPropertyName("errorHandling")] public ErrorHandling ErrorHandling { get; set; } = ErrorHandling.ReturnError;
        /// <summary>输出格式。</summary>
        [JsonPropertyName("outputFormat")] public OutputFormat OutputFormat { get; set; } = OutputFormat.Number;
        /// <summary>是否缓存结果（保留最近一次计算）。</summary>
        [JsonPropertyName("cacheResults")] public bool CacheResults { get; set; } = false;
        /// <summary>变量映射集合。</summary>
        [JsonPropertyName("variables")] public List<VariableMapping> Variables { get; set; } = new();
        /// <summary>函数定义集合。</summary>
        [JsonPropertyName("functions")] public List<FunctionDef> Functions { get; set; } = new();

        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            WriteIndented = false,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.Never
        };

        /// <summary>
        /// 尝试从节点参数字典读取配置（优先 JSON，其次兼容旧的扁平键）。
        /// </summary>
        /// <param name="parameters">节点参数字典（Module.Parameters）。</param>
        /// <param name="config">输出的强类型配置。</param>
        /// <returns>成功返回 true；失败返回 false 并输出默认配置。</returns>
        public static bool TryLoadFromParameters(IDictionary<string, object?> parameters, out DataCalculationConfig config)
        {
            config = new DataCalculationConfig();
            try
            {
                if (parameters.TryGetValue("dataCalculation", out var jsonObj))
                {
                    var json = Convert.ToString(jsonObj) ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(json))
                    {
                        config = JsonSerializer.Deserialize<DataCalculationConfig>(json, JsonOptions) ?? new DataCalculationConfig();
                        return true;
                    }
                }

                // 兼容旧版的扁平参数键
                if (parameters.TryGetValue("calculationMode", out var modeStr)
                    && Enum.TryParse(Convert.ToString(modeStr), true, out CalculationMode mode))
                {
                    config.CalculationMode = mode;
                }
                if (parameters.TryGetValue("precision", out var precStr)
                    && int.TryParse(Convert.ToString(precStr), out var prec))
                {
                    config.Precision = prec;
                }
                if (parameters.TryGetValue("errorHandling", out var errStr)
                    && Enum.TryParse(Convert.ToString(errStr), true, out ErrorHandling eh))
                {
                    config.ErrorHandling = eh;
                }
                if (parameters.TryGetValue("outputFormat", out var fmtStr)
                    && Enum.TryParse(Convert.ToString(fmtStr), true, out OutputFormat fmt))
                {
                    config.OutputFormat = fmt;
                }
                if (parameters.TryGetValue("cacheResults", out var cacheStr)
                    && bool.TryParse(Convert.ToString(cacheStr), out var cache))
                {
                    config.CacheResults = cache;
                }

                if (parameters.TryGetValue("function_count", out var fcntObj)
                    && int.TryParse(Convert.ToString(fcntObj), out var fcnt))
                {
                    for (int i = 0; i < fcnt; i++)
                    {
                        var name = parameters.TryGetValue($"function_{i}_name", out var n) ? Convert.ToString(n) ?? string.Empty : string.Empty;
                        var expr = parameters.TryGetValue($"function_{i}_expression", out var e) ? Convert.ToString(e) ?? string.Empty : string.Empty;
                        if (!string.IsNullOrWhiteSpace(name) || !string.IsNullOrWhiteSpace(expr))
                            config.Functions.Add(new FunctionDef { Name = name, Expression = expr });
                    }
                }

                if (parameters.TryGetValue("variable_count", out var vcntObj)
                    && int.TryParse(Convert.ToString(vcntObj), out var vcnt))
                {
                    for (int i = 0; i < vcnt; i++)
                    {
                        var name = parameters.TryGetValue($"variable_{i}_name", out var n) ? Convert.ToString(n) ?? string.Empty : string.Empty;
                        var srcId = parameters.TryGetValue($"variable_{i}_sourceNodeId", out var s) ? Convert.ToString(s) : null;
                        var prop = parameters.TryGetValue($"variable_{i}_propertyName", out var p) ? Convert.ToString(p) : null;
                        if (!string.IsNullOrWhiteSpace(name))
                            config.Variables.Add(new VariableMapping { Name = name, SourceNodeId = srcId, PropertyName = prop });
                    }
                }

                return true;
            }
            catch
            {
                config = new DataCalculationConfig();
                return false;
            }
        }

        /// <summary>
        /// 将配置写回节点参数（写入 JSON，并可选同步旧的扁平键，便于平滑迁移）。
        /// </summary>
        /// <param name="parameters">节点参数字典（Module.Parameters）。</param>
        /// <param name="alsoWriteLegacy">是否同步写入扁平键。</param>
        public void SaveToParameters(IDictionary<string, object?> parameters, bool alsoWriteLegacy = true)
        {
            var json = JsonSerializer.Serialize(this, JsonOptions);
            parameters["dataCalculation"] = json;

            if (!alsoWriteLegacy)
                return;

            parameters["calculationMode"] = CalculationMode.ToString();
            parameters["precision"] = Precision.ToString();
            parameters["errorHandling"] = ErrorHandling.ToString();
            parameters["outputFormat"] = OutputFormat.ToString();
            parameters["cacheResults"] = CacheResults.ToString();

            for (int i = 0; i < Functions.Count; i++)
            {
                parameters[$"function_{i}_name"] = Functions[i].Name;
                parameters[$"function_{i}_expression"] = Functions[i].Expression;
            }
            parameters["function_count"] = Functions.Count.ToString();

            for (int i = 0; i < Variables.Count; i++)
            {
                parameters[$"variable_{i}_name"] = Variables[i].Name;
                parameters[$"variable_{i}_sourceNodeId"] = Variables[i].SourceNodeId ?? string.Empty;
                parameters[$"variable_{i}_propertyName"] = Variables[i].PropertyName ?? string.Empty;
            }
            parameters["variable_count"] = Variables.Count.ToString();
        }
    }
}

