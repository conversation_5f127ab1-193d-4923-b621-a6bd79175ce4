using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Threading;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Studio.Commands;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 进度指示器视图模型
    /// 用于显示异步操作的进度和状态
    /// </summary>
    public class ProgressIndicatorViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly IAsyncOperationManager _operationManager;
        private readonly Dispatcher _dispatcher;
        private bool _isVisible;
        private bool _disposed;

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 活动操作集合
        /// </summary>
        public ObservableCollection<OperationProgressViewModel> ActiveOperations { get; }

        /// <summary>
        /// 是否显示进度指示器
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            private set
            {
                if (_isVisible != value)
                {
                    _isVisible = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否有活动操作
        /// </summary>
        public bool HasActiveOperations => ActiveOperations.Any();

        /// <summary>
        /// 总体进度百分比
        /// </summary>
        public double OverallProgress
        {
            get
            {
                if (!ActiveOperations.Any())
                    return 0;

                return ActiveOperations.Average(op => op.Progress);
            }
        }

        /// <summary>
        /// 当前主要操作的消息
        /// </summary>
        public string? CurrentMessage
        {
            get
            {
                var runningOperation = ActiveOperations.FirstOrDefault(op => op.Status == OperationStatus.Running);
                return runningOperation?.Message ?? ActiveOperations.FirstOrDefault()?.Message;
            }
        }

        /// <summary>
        /// 取消所有操作命令
        /// </summary>
        public ICommand CancelAllCommand { get; }

        public ProgressIndicatorViewModel(IAsyncOperationManager operationManager)
        {
            _operationManager = operationManager ?? throw new ArgumentNullException(nameof(operationManager));
            _dispatcher = Dispatcher.CurrentDispatcher;
            ActiveOperations = new ObservableCollection<OperationProgressViewModel>();

            // 初始化命令
            CancelAllCommand = new RelayCommand(CancelAllOperations, () => HasActiveOperations);

            // 订阅操作管理器事件
            _operationManager.OperationStatusChanged += OnOperationStatusChanged;
            _operationManager.OperationProgressUpdated += OnOperationProgressUpdated;

            // 初始化现有的活动操作
            InitializeActiveOperations();
        }

        private void InitializeActiveOperations()
        {
            var activeOperations = _operationManager.GetActiveOperations();
            foreach (var operation in activeOperations)
            {
                var viewModel = new OperationProgressViewModel(operation);
                ActiveOperations.Add(viewModel);
            }

            UpdateVisibility();
        }

        private void OnOperationStatusChanged(object? sender, OperationStatusChangedEventArgs e)
        {
            _dispatcher.BeginInvoke(() =>
            {
                var existingOperation = ActiveOperations.FirstOrDefault(op => op.OperationId == e.OperationId);

                if (e.NewStatus == OperationStatus.Pending || e.NewStatus == OperationStatus.Running)
                {
                    if (existingOperation == null)
                    {
                        var operation = _operationManager.GetOperation(e.OperationId);
                        if (operation != null)
                        {
                            var viewModel = new OperationProgressViewModel(operation);
                            ActiveOperations.Add(viewModel);
                        }
                    }
                    else
                    {
                        existingOperation.UpdateStatus(e.NewStatus);
                    }
                }
                else if (e.NewStatus == OperationStatus.Completed ||
                         e.NewStatus == OperationStatus.Cancelled ||
                         e.NewStatus == OperationStatus.Failed)
                {
                    if (existingOperation != null)
                    {
                        existingOperation.UpdateStatus(e.NewStatus);

                        // 延迟移除已完成的操作，让用户能看到完成状态
                        _dispatcher.BeginInvoke(() =>
                        {
                            ActiveOperations.Remove(existingOperation);
                            existingOperation.Dispose();
                            UpdateVisibility();
                            OnPropertyChanged(nameof(OverallProgress));
                            OnPropertyChanged(nameof(CurrentMessage));
                        }, DispatcherPriority.Background);
                    }
                }

                UpdateVisibility();
                OnPropertyChanged(nameof(HasActiveOperations));
                OnPropertyChanged(nameof(OverallProgress));
                OnPropertyChanged(nameof(CurrentMessage));

                // 触发命令状态更新
                ((RelayCommand)CancelAllCommand).RaiseCanExecuteChanged();
            });
        }

        private void OnOperationProgressUpdated(object? sender, OperationProgressEventArgs e)
        {
            _dispatcher.BeginInvoke(() =>
            {
                var operation = ActiveOperations.FirstOrDefault(op => op.OperationId == e.OperationId);
                if (operation != null)
                {
                    operation.UpdateProgress(e.Progress);
                    OnPropertyChanged(nameof(OverallProgress));
                    OnPropertyChanged(nameof(CurrentMessage));
                }
            });
        }

        private void UpdateVisibility()
        {
            IsVisible = HasActiveOperations;
        }

        /// <summary>
        /// 取消指定操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        public void CancelOperation(string operationId)
        {
            var operation = _operationManager.GetOperation(operationId);
            operation?.Cancel();
        }

        /// <summary>
        /// 取消所有操作
        /// </summary>
        public async void CancelAllOperations()
        {
            try
            {
                await _operationManager.CancelAllOperationsAsync();
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，避免影响UI
                System.Diagnostics.Debug.WriteLine($"取消所有操作时发生错误: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                // 取消订阅事件
                _operationManager.OperationStatusChanged -= OnOperationStatusChanged;
                _operationManager.OperationProgressUpdated -= OnOperationProgressUpdated;

                // 清理操作视图模型
                foreach (var operation in ActiveOperations)
                {
                    operation.Dispose();
                }
                ActiveOperations.Clear();

                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 单个操作的进度视图模型
    /// </summary>
    public class OperationProgressViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly IAsyncOperationHandle _operationHandle;
        private double _progress;
        private string? _message;
        private OperationStatus _status;
        private bool _disposed;

        public event PropertyChangedEventHandler? PropertyChanged;

        public string OperationId => _operationHandle.OperationId;
        public string OperationName => _operationHandle.OperationName;
        public DateTime StartTime => _operationHandle.StartTime;

        public double Progress
        {
            get => _progress;
            private set
            {
                if (Math.Abs(_progress - value) > 0.01)
                {
                    _progress = value;
                    OnPropertyChanged();
                }
            }
        }

        public string? Message
        {
            get => _message;
            private set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged();
                }
            }
        }

        public OperationStatus Status
        {
            get => _status;
            private set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsRunning));
                    OnPropertyChanged(nameof(IsCompleted));
                    OnPropertyChanged(nameof(IsFailed));
                    OnPropertyChanged(nameof(IsCancelled));
                }
            }
        }

        public bool IsRunning => Status == OperationStatus.Running;
        public bool IsCompleted => Status == OperationStatus.Completed;
        public bool IsFailed => Status == OperationStatus.Failed;
        public bool IsCancelled => Status == OperationStatus.Cancelled;

        /// <summary>
        /// 取消操作命令
        /// </summary>
        public ICommand CancelCommand { get; }

        public OperationProgressViewModel(IAsyncOperationHandle operationHandle)
        {
            _operationHandle = operationHandle ?? throw new ArgumentNullException(nameof(operationHandle));

            // 初始化命令
            CancelCommand = new RelayCommand(Cancel, () => IsRunning);

            // 初始化状态
            Status = operationHandle.Status;

            if (operationHandle.CurrentProgress != null)
            {
                Progress = operationHandle.CurrentProgress.Percentage;
                Message = operationHandle.CurrentProgress.Message;
            }
        }

        public void UpdateProgress(OperationProgress progress)
        {
            Progress = progress.Percentage;
            Message = progress.Message;
        }

        public void UpdateStatus(OperationStatus status)
        {
            Status = status;
        }

        public void Cancel()
        {
            _operationHandle.Cancel();
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                // 这里不需要释放_operationHandle，因为它由AsyncOperationManager管理
                _disposed = true;
            }
        }
    }
}
