<UserControl
    d:DesignHeight="300"
    d:DesignWidth="500"
    mc:Ignorable="d"
    x:Class="ProjectDigitizer.Studio.Controls.Functions.FunctionExpressionEditor"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:functions="clr-namespace:ProjectDigitizer.Studio.Controls.Functions"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  函数标签页样式  -->
        <Style TargetType="TabItem" x:Key="FunctionTabStyle">
            <Setter Property="MinWidth" Value="120" />
            <Setter Property="MaxWidth" Value="200" />
        </Style>

        <!--  工具按钮样式  -->
        <Style
            TargetType="ui:Button"
            x:Key="ToolButtonStyle">
            <Setter Property="Width" Value="28" />
            <Setter Property="Height" Value="28" />
            <Setter Property="Margin" Value="2" />
        </Style>

        <!--  函数类型样式  -->
        <Style TargetType="ComboBox" x:Key="FunctionTypeStyle">
            <Setter Property="MinWidth" Value="100" />
            <Setter Property="Margin" Value="4,0" />
        </Style>

        <!--  表达式编辑器样式  -->
        <Style TargetType="TextBox" x:Key="ExpressionTextBoxStyle">
            <Setter Property="FontFamily" Value="Consolas" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="AcceptsReturn" Value="True" />
            <Setter Property="TextWrapping" Value="Wrap" />
            <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
        </Style>

        <!--  验证错误样式  -->
        <Style TargetType="TextBlock" x:Key="ValidationErrorStyle">
            <Setter Property="Foreground" Value="{StaticResource Brush.Danger}" />
            <Setter Property="FontSize" Value="11" />
            <Setter Property="Margin" Value="0,4,0,0" />
            <Setter Property="TextWrapping" Value="Wrap" />
        </Style>

        <!--  字段引用提示样式  -->
        <Style TargetType="Border" x:Key="FieldReferenceStyle">
            <Setter Property="Background" Value="{StaticResource Brush.Primary/16}" />
            <Setter Property="BorderBrush" Value="{StaticResource Brush.Primary}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="3" />
            <Setter Property="Padding" Value="4,2" />
            <Setter Property="Margin" Value="2" />
        </Style>
    </UserControl.Resources>

    <DockPanel>
        <!--  顶部工具栏  -->
        <StackPanel
            DockPanel.Dock="Top"
            Margin="0,0,0,8"
            Orientation="Horizontal">
            <TextBlock
                FontSize="14"
                FontWeight="Medium"
                Foreground="{DynamicResource Brush.Text}"
                Text="函数表达式"
                VerticalAlignment="Center" />

            <ui:Button
                Margin="8,0,0,0"
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="添加函数表达式"
                x:Name="AddFunctionButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="Plus" />
                </ui:Button.Content>
            </ui:Button>

            <ui:Button
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="导入模板"
                x:Name="ImportTemplateButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="Import" />
                </ui:Button.Content>
            </ui:Button>

            <ui:Button
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="验证所有表达式"
                x:Name="ValidateAllButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="CheckCircle" />
                </ui:Button.Content>
            </ui:Button>

            <ui:Button
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="格式化表达式"
                x:Name="FormatButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="FormatAlignLeft" />
                </ui:Button.Content>
            </ui:Button>

            <ui:Button
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="函数帮助"
                x:Name="HelpButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="Help" />
                </ui:Button.Content>
            </ui:Button>
            <!--  显示全部函数  -->
            <ui:Button
                Click="OnInsertFunctionClick"
                Style="{StaticResource ToolButtonStyle}"
                ToolTip="显示全部函数"
                x:Name="ShowAllFunctionsButton">
                <ui:Button.Content>
                    <ipack:PackIconMaterial Kind="FunctionVariant" />
                </ui:Button.Content>
            </ui:Button>
        </StackPanel>

        <!--  函数标签页  -->
        <TabControl
            ItemsSource="{Binding Functions}"
            x:Name="FunctionTabControl">

            <!--  标签页头部模板  -->
                    <TabControl.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                        <!--  函数类型图标  -->
                        <ipack:PackIconMaterial
                            Height="14"
                            Margin="0,0,4,0"
                            VerticalAlignment="Center"
                            Width="14">
                            <ipack:PackIconMaterial.Style>
                                <Style TargetType="ipack:PackIconMaterial">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Type}" Value="Math">
                                            <Setter Property="Kind" Value="Calculator" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.Primary}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Statistical">
                                            <Setter Property="Kind" Value="ChartLine" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.Success}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="String">
                                            <Setter Property="Kind" Value="FormatText" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.Warning}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="DateTime">
                                            <Setter Property="Kind" Value="Calendar" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.Secondary}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Logical">
                                            <Setter Property="Kind" Value="SourceBranch" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.PrimaryVariant}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Aggregation">
                                            <Setter Property="Kind" Value="Sigma" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.TextSecondary}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Custom">
                                            <Setter Property="Kind" Value="Cog" />
                                            <Setter Property="Foreground" Value="{StaticResource Brush.TextSecondary}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ipack:PackIconMaterial.Style>
                        </ipack:PackIconMaterial>

                        <!--  函数名称  -->
                        <TextBlock
                            FontSize="12"
                            Text="{Binding Name}"
                            VerticalAlignment="Center" />

                        <!--  验证状态指示器  -->
                        <ipack:PackIconMaterial
                            Height="12"
                            Margin="4,0,0,0"
                            VerticalAlignment="Center"
                            Width="12">
                            <ipack:PackIconMaterial.Style>
                                <Style TargetType="ipack:PackIconMaterial">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ValidationResult.IsValid}" Value="True">
                                            <Setter Property="Kind" Value="Check" />
                                            <Setter Property="Foreground" Value="{DynamicResource Brush.Success}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding ValidationResult.IsValid}" Value="False">
                                            <Setter Property="Kind" Value="Alert" />
                                            <Setter Property="Foreground" Value="{DynamicResource Brush.Danger}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ipack:PackIconMaterial.Style>
                        </ipack:PackIconMaterial>

                        <!--  删除按钮  -->
                        <ui:Button
                            Height="16"
                            Margin="4,0,0,0"
                            Padding="0"
                            Style="{StaticResource ToolButtonStyle}"
                            Width="16">
                            <ui:Button.Content>
                                <ipack:PackIconMaterial
                                    Height="10"
                                    Kind="Close"
                                    Width="10" />
                            </ui:Button.Content>
                        </ui:Button>
                    </StackPanel>
                </DataTemplate>
            </TabControl.ItemTemplate>

            <!--  标签页内容模板  -->
            <TabControl.ContentTemplate>
                <DataTemplate>
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  函数基本信息  -->
                        <StackPanel
                            Grid.Row="0"
                            Margin="0,0,0,8"
                            Orientation="Horizontal">
                            <TextBox
                                Margin="0,0,8,0"
                                Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                Width="120" />

                            <ComboBox
                                ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=AvailableFunctionTypes}"
                                SelectedItem="{Binding Type}"
                                Style="{StaticResource FunctionTypeStyle}" />

                            <CheckBox
                                Content="启用"
                                IsChecked="{Binding IsEnabled}"
                                Margin="8,0,0,0"
                                VerticalAlignment="Center" />
                        </StackPanel>

                        <!--  字段引用提示区  -->
                        <Border
                            Background="{DynamicResource Brush.SurfaceVariant}"
                            CornerRadius="4"
                            Grid.Row="1"
                            Margin="0,0,0,8"
                            Padding="8,4"
                            Visibility="{Binding HasReferencedFields, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock
                                    FontSize="11"
                                    FontWeight="Medium"
                                    Foreground="{DynamicResource Brush.TextSecondary}"
                                    Margin="0,0,0,4"
                                    Text="引用字段:" />

                                <ItemsControl ItemsSource="{Binding ReferencedFields}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel Orientation="Horizontal" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource FieldReferenceStyle}">
                                                <TextBlock
                                                    FontSize="10"
                                                    Foreground="{DynamicResource Brush.Primary}"
                                                    Text="{Binding}" />
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!--  表达式编辑器  -->
                        <Grid Grid.Row="2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  主编辑区  -->
                            <TextBox
                                Grid.Column="0"
                                MinHeight="80"
                                PreviewKeyDown="OnExpressionKeyDown"
                                Style="{StaticResource ExpressionTextBoxStyle}"
                                Text="{Binding Expression, UpdateSourceTrigger=PropertyChanged}"
                                TextChanged="OnExpressionTextChanged"
                                x:Name="ExpressionTextBox" />

                            <!--  侧边工具栏  -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <ui:Button Style="{StaticResource ToolButtonStyle}">
                                    <ui:Button.Content>
                                        <ipack:PackIconMaterial Kind="Function" />
                                    </ui:Button.Content>
                                </ui:Button>
                                <ui:Button Style="{StaticResource ToolButtonStyle}">
                                    <ui:Button.Content>
                                        <ipack:PackIconMaterial Kind="Variable" />
                                    </ui:Button.Content>
                                </ui:Button>
                                <ui:Button Style="{StaticResource ToolButtonStyle}">
                                    <ui:Button.Content>
                                        <ipack:PackIconMaterial Kind="CodeBraces" />
                                    </ui:Button.Content>
                                </ui:Button>
                                <ui:Button Style="{StaticResource ToolButtonStyle}">
                                    <ui:Button.Content>
                                        <ipack:PackIconMaterial Kind="Play" />
                                    </ui:Button.Content>
                                </ui:Button>
                            </StackPanel>
                        </Grid>

                        <!--  验证结果  -->
                        <TextBlock
                            Grid.Row="3"
                            Style="{StaticResource ValidationErrorStyle}"
                            Text="{Binding ValidationResult}"
                            Visibility="{Binding ValidationResult.IsValid, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}" />

                        <!--  函数描述  -->
                        <TextBox
                            FontSize="11"
                            Grid.Row="4"
                            Margin="0,4,0,0"
                            MaxHeight="40"
                            Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}" />
                    </Grid>
                </DataTemplate>
            </TabControl.ContentTemplate>
        </TabControl>

        <!--  空状态提示  -->
        <StackPanel
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Visibility="{Binding HasFunctions, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}"
            x:Name="EmptyStatePanel">
            <ipack:PackIconMaterial
                Foreground="{DynamicResource Brush.TextSecondary}"
                Height="48"
                HorizontalAlignment="Center"
                Kind="Function"
                Margin="0,0,0,16"
                Width="48" />
            <TextBlock
                FontSize="14"
                Foreground="{DynamicResource Brush.TextSecondary}"
                Text="点击 + 按钮添加函数表达式"
                TextAlignment="Center" />
        </StackPanel>

        <!--  函数智能提示弹出框  -->
        <Popup
            AllowsTransparency="True"
            Placement="Bottom"
            PlacementTarget="{Binding ElementName=FunctionTabControl}"
            StaysOpen="True"
            x:Name="IntelliSensePopup">
            <Border
                Background="{DynamicResource Brush.Surface}"
                BorderBrush="{DynamicResource Brush.Divider}"
                BorderThickness="1"
                CornerRadius="4"
                Effect="{StaticResource Shadow.M}">
                <ListBox
                    ItemsSource="{Binding IntelliSenseItems}"
                    KeyDown="OnIntelliSenseKeyDown"
                    MaxHeight="200"
                    MinWidth="200"
                    MouseDoubleClick="OnIntelliSenseMouseDoubleClick"
                    PreviewMouseLeftButtonUp="OnIntelliSenseMouseUp"
                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                    SelectedItem="{Binding SelectedIntelliSenseItem}"
                    x:Name="IntelliSenseListBox">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <ipack:PackIconMaterial
                                    Height="16"
                                    Kind="{Binding Icon}"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Width="16" />
                                <StackPanel>
                                    <TextBlock FontWeight="SemiBold" Text="{Binding DisplayName}" />
                                    <TextBlock
                                        FontSize="10"
                                        Foreground="{DynamicResource Brush.TextSecondary}"
                                        Text="{Binding Name}" />
                                    <TextBlock
                                        FontSize="11"
                                        Foreground="{DynamicResource Brush.TextSecondary}"
                                        Text="{Binding Description}" />
                                </StackPanel>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Border>
        </Popup>
    </DockPanel>
</UserControl>









