<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 触发器控制节点模板集合 ==========  -->
    <!--  用于 ClickTrigger、AssociationTrigger、TimedTrigger、FileChangeTrigger、EnvironmentTrigger 等触发器节点  -->

    <!--  通用触发器节点内容模板  -->
    <DataTemplate x:Key="TriggerNodeContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  触发器图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="Play" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ClickTrigger}">
                                                <Setter Property="Kind" Value="CursorDefaultClick" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AssociationTrigger}">
                                                <Setter Property="Kind" Value="Link" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TimedTrigger}">
                                                <Setter Property="Kind" Value="Clock" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FileChangeTrigger}">
                                                <Setter Property="Kind" Value="FileAlert" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EnvironmentTrigger}">
                                                <Setter Property="Kind" Value="Earth" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：触发器类型信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  触发器类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    Background="#9C27B0"
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="触发" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ClickTrigger}">
                                                        <Setter Property="Text" Value="点击" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AssociationTrigger}">
                                                        <Setter Property="Text" Value="关联" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TimedTrigger}">
                                                        <Setter Property="Text" Value="定时" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FileChangeTrigger}">
                                                        <Setter Property="Text" Value="文件" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EnvironmentTrigger}">
                                                        <Setter Property="Text" Value="环境" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    Opacity="0.8"
                                    VerticalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.TriggerCondition, FallbackValue='条件未设置'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TimedTrigger}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.Schedule, FallbackValue='每天 09:00'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FileChangeTrigger}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.WatchPath, FallbackValue='监控路径'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  触发器配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="触发器配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  启用/禁用按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="启用/禁用触发器">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Opacity="0.9"
                                        Width="14">
                                        <ipack:PackIconMaterial.Style>
                                            <Style TargetType="ipack:PackIconMaterial">
                                                <Setter Property="Kind" Value="Play" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding NodeProperties.IsEnabled}" Value="False">
                                                        <Setter Property="Kind" Value="Pause" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ipack:PackIconMaterial.Style>
                                    </ipack:PackIconMaterial>
                                </Button>

                                <!--  触发状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="触发状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Waiting">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Triggered">
                                                    <Setter Property="Fill" Value="#2196F3" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#F3E5F5"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  触发条件  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="条件:"
                                VerticalAlignment="Top" />

                            <TextBlock
                                FontFamily="Consolas"
                                FontSize="9"
                                Foreground="#9C27B0"
                                Grid.Column="1"
                                MaxHeight="30"
                                TextTrimming="CharacterEllipsis"
                                TextWrapping="Wrap">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="{Binding NodeProperties.TriggerExpression, FallbackValue='未设置触发条件'}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TimedTrigger}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.CronExpression, FallbackValue='0 9 * * *'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FileChangeTrigger}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.FilePattern, FallbackValue='*.txt'}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  触发频率/模式  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="模式:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#9C27B0"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.TriggerMode, FallbackValue='单次触发'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TimedTrigger}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.RepeatMode, FallbackValue='重复执行'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FileChangeTrigger}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.WatchMode, FallbackValue='文件变化'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Border>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  触发次数  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="次数" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#9C27B0"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.TriggerCount, FallbackValue=0}" />
                            </StackPanel>

                            <!--  最后触发时间  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="最后" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#9C27B0"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.LastTriggerTime, FallbackValue='--:--', StringFormat=HH:mm}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Width="12">
                                    <ipack:PackIconMaterial.Style>
                                        <Style TargetType="ipack:PackIconMaterial">
                                            <Setter Property="Kind" Value="CheckCircle" />
                                            <Setter Property="Foreground" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding NodeProperties.IsEnabled}" Value="False">
                                                    <Setter Property="Kind" Value="PauseCircle" />
                                                    <Setter Property="Foreground" Value="#999" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ipack:PackIconMaterial.Style>
                                </ipack:PackIconMaterial>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  点击触发器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ClickTriggerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource TriggerNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  关联触发器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="AssociationTriggerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource TriggerNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  定时触发器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="TimedTriggerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource TriggerNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  文件变化触发器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="FileChangeTriggerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource TriggerNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  环境触发器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="EnvironmentTriggerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource TriggerNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
