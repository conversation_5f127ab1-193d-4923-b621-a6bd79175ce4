<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 流程控制节点模板集合 ==========  -->
    <!--  用于 ConditionalBranch、LoopProcessor、ErrorHandler、FlowControl、ScriptExecutor、VariableManager、StateManager 等流程控制节点  -->

    <!--  通用流程控制节点内容模板  -->
    <DataTemplate x:Key="FlowControlNodeContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  流程控制图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="CallSplit" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                <Setter Property="Kind" Value="CallSplit" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                <Setter Property="Kind" Value="Repeat" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ErrorHandler}">
                                                <Setter Property="Kind" Value="AlertCircle" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FlowControl}">
                                                <Setter Property="Kind" Value="VectorPolyline" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ScriptExecutor}">
                                                <Setter Property="Kind" Value="CodeBraces" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.VariableManager}">
                                                <Setter Property="Kind" Value="Variable" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.StateManager}">
                                                <Setter Property="Kind" Value="StateMachine" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：控制类型信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  控制类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    Background="#9C27B0"
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="控制" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                        <Setter Property="Text" Value="分支" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                        <Setter Property="Text" Value="循环" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ErrorHandler}">
                                                        <Setter Property="Text" Value="异常" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ScriptExecutor}">
                                                        <Setter Property="Text" Value="脚本" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.VariableManager}">
                                                        <Setter Property="Text" Value="变量" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.StateManager}">
                                                        <Setter Property="Text" Value="状态" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    Opacity="0.8"
                                    VerticalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.ControlType, FallbackValue='标准控制'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.BranchCondition, FallbackValue='if-else'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.LoopType, FallbackValue='for循环'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ScriptExecutor}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.ScriptLanguage, FallbackValue='JavaScript'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="控制配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  调试按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="调试模式">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Bug"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Fill="#4CAF50"
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="控制状态"
                                    VerticalAlignment="Center"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#F3E5F5"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  控制表达式  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="表达式:"
                                VerticalAlignment="Top" />

                            <TextBlock
                                FontFamily="Consolas"
                                FontSize="9"
                                Foreground="#9C27B0"
                                Grid.Column="1"
                                MaxHeight="30"
                                TextTrimming="CharacterEllipsis"
                                TextWrapping="Wrap">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="{Binding NodeProperties.ControlExpression, FallbackValue='未设置控制表达式'}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.Condition, FallbackValue='if (condition) { ... }'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.LoopCondition, FallbackValue='for (i=0; i&lt;10; i++)'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ScriptExecutor}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.ScriptCode, FallbackValue='// JavaScript代码'}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  执行模式  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="模式:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#9C27B0"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.ExecutionMode, FallbackValue='同步执行'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ErrorHandler}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.ErrorHandlingMode, FallbackValue='捕获异常'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Border>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  执行次数/分支数  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="执行" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                    <Setter Property="Text" Value="分支" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                    <Setter Property="Text" Value="循环" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ErrorHandler}">
                                                    <Setter Property="Text" Value="异常" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#9C27B0"
                                    HorizontalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.ExecutionCount, FallbackValue=0}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ConditionalBranch}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.BranchCount, FallbackValue=2}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LoopProcessor}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.IterationCount, FallbackValue=0}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ErrorHandler}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.ErrorCount, FallbackValue=0}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  超时时间  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="超时" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#9C27B0"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.TimeoutSeconds, FallbackValue=30, StringFormat={}{0}s}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  条件分支节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ConditionalBranchNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  循环处理器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="LoopProcessorNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  错误处理器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ErrorHandlerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  流程控制节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="FlowControlNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  脚本执行器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ScriptExecutorNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  变量管理器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="VariableManagerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  状态管理器节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="StateManagerNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FlowControlNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
