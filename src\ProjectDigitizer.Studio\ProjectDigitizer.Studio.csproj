﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <TargetPlatformVersion>7.0</TargetPlatformVersion>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>

    <!-- XAML 完整校验配置 -->
    <EnableXamlGeneratedFileDebugging>true</EnableXamlGeneratedFileDebugging>
    <XamlDebuggingInformation>true</XamlDebuggingInformation>
    <MarkupCompilePass2>true</MarkupCompilePass2>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
  </PropertyGroup>

  <ItemGroup>
    
    <PackageReference Include="wpf-ui" />
    <PackageReference Include="MahApps.Metro.IconPacks.Material" />
    <PackageReference Include="Nodify" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" />
    <PackageReference Include="CommunityToolkit.Mvvm" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Extensions.Hosting" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="AvalonEdit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectDigitizer.Application\ProjectDigitizer.Application.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Infrastructure\ProjectDigitizer.Infrastructure.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Core\ProjectDigitizer.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Page Remove="Views\TelerikTemplateDesignerWindow.xaml" />
    <None Include="Views\TelerikTemplateDesignerWindow.xaml" />
    <Compile Remove="Views\TelerikTemplateDesignerWindow.xaml.cs" />
    <None Include="Views\TelerikTemplateDesignerWindow.xaml.cs" />
    <Compile Remove="Controls\Functions\**" />
    <Page Remove="Controls\Functions\**" />
    <None Remove="Controls\Functions\**" />
    <Page Remove="Controls\Inspector\Components\FunctionEditorComponent.xaml" />
    <Compile Remove="Controls\Inspector\Components\FunctionEditorComponent.xaml.cs" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controls\Inspector\Properties\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="Controls\Functions\**" />
  </ItemGroup>

</Project>
