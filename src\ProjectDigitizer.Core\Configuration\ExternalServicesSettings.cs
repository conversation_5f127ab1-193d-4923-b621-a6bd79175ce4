using System.ComponentModel.DataAnnotations;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 外部服务配置类
    /// </summary>
    public class ExternalServicesSettings
    {
        /// <summary>
        /// API基础URL
        /// </summary>
        [Url]
        public string BaseUrl { get; set; } = "https://api.example.com";

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        [Range(1, 300)]
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用重试机制
        /// </summary>
        public bool EnableRetry { get; set; } = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 10)]
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 重试延迟（毫秒）
        /// </summary>
        [Range(100, 10000)]
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// 是否启用缓存
        /// </summary>
        public bool EnableCaching { get; set; } = true;

        /// <summary>
        /// 缓存过期时间（分钟）
        /// </summary>
        [Range(1, 1440)] // 1分钟到24小时
        public int CacheExpirationMinutes { get; set; } = 60;

        /// <summary>
        /// 用户代理字符串
        /// </summary>
        public string UserAgent { get; set; } = "ProjectDigitizer/1.0";
    }
}
