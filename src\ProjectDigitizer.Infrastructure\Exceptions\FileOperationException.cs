namespace ProjectDigitizer.Infrastructure.Exceptions;

/// <summary>
/// 文件操作异常
/// </summary>
public class FileOperationException : InfrastructureException
{
    public string? FilePath { get; }

    public FileOperationException(string message) : base(message)
    {
    }

    public FileOperationException(string message, string filePath) : base(message)
    {
        FilePath = filePath;
    }

    public FileOperationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public FileOperationException(string message, string filePath, Exception innerException) : base(message, innerException)
    {
        FilePath = filePath;
    }
}
