namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 异常处理服务接口
/// </summary>
public interface IExceptionHandlingService
{
    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文信息</param>
    /// <returns>处理结果</returns>
    Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, string context);

    /// <summary>
    /// 注册异常处理器
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    /// <param name="handler">处理器</param>
    void RegisterHandler<T>(Func<T, string, Task<ExceptionHandlingResult>> handler) where T : Exception;
}

/// <summary>
/// 异常处理结果
/// </summary>
public class ExceptionHandlingResult
{
    /// <summary>
    /// 是否已处理
    /// </summary>
    public bool IsHandled { get; set; }

    /// <summary>
    /// 用户友好的错误消息
    /// </summary>
    public string UserMessage { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要重试
    /// </summary>
    public bool ShouldRetry { get; set; }

    /// <summary>
    /// 重试延迟
    /// </summary>
    public TimeSpan RetryDelay { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
