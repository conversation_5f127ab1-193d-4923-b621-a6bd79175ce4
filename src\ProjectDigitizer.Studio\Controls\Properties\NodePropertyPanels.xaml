<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:models="clr-namespace:ProjectDigitizer.Studio.Models"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  输入节点属性面板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="InputNodePropertyPanel">

        <StackPanel Margin="0">
            <!--  基本信息组  -->
            <GroupBox
                Header="基本信息"
                Margin="0,0,0,20"
                Padding="12">
                <StackPanel Margin="0">
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="节点名称:"
                            VerticalAlignment="Center" />
                        <TextBox
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Padding="8,6"
                            Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}" />
                    </Grid>

                    <Grid Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="节点类型:"
                            VerticalAlignment="Center" />
                        <TextBlock
                            FontWeight="SemiBold"
                            Foreground="{DynamicResource Brush.Primary}"
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Text="{Binding NodeTypeMetadata.Name}"
                            VerticalAlignment="Center" />
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!--  数据源配置组  -->
            <GroupBox
                Header="数据源配置"
                Margin="0,0,0,20"
                Padding="12">
                <StackPanel Margin="0">
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="数据源:"
                            VerticalAlignment="Center" />
                        <ComboBox
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Padding="8,6">
                            <ComboBoxItem Content="文件" IsSelected="True" />
                            <ComboBoxItem Content="数据库" />
                            <ComboBoxItem Content="API接口" />
                            <ComboBoxItem Content="CAD图纸" />
                        </ComboBox>
                    </Grid>

                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="文件路径:"
                            VerticalAlignment="Center" />
                        <TextBox
                            Grid.Column="1"
                            Margin="8,0,8,0"
                            Padding="8,6" />
                        <Button
                            Content="浏览..."
                            Grid.Column="2"
                            Padding="8,6"
                            Width="65" />
                    </Grid>

                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="文件格式:"
                            VerticalAlignment="Center" />
                        <ComboBox
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Padding="8,6">
                            <ComboBoxItem Content="自动检测" IsSelected="True" />
                            <ComboBoxItem Content="Excel (.xlsx)" />
                            <ComboBoxItem Content="CSV (.csv)" />
                            <ComboBoxItem Content="JSON (.json)" />
                            <ComboBoxItem Content="XML (.xml)" />
                        </ComboBox>
                    </Grid>

                    <CheckBox
                        Content="包含标题行"
                        IsChecked="True"
                        Margin="85,0,0,0"
                        Padding="8,6" />
                </StackPanel>
            </GroupBox>

            <!--  高级选项组  -->
            <GroupBox Header="高级选项" Padding="12">
                <StackPanel Margin="0">
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="编码格式:"
                            VerticalAlignment="Center" />
                        <ComboBox
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Padding="8,6">
                            <ComboBoxItem Content="UTF-8" IsSelected="True" />
                            <ComboBoxItem Content="GBK" />
                            <ComboBoxItem Content="ASCII" />
                        </ComboBox>
                    </Grid>

                    <CheckBox
                        Content="启用数据缓存"
                        IsChecked="True"
                        Margin="85,0,0,8"
                        Padding="8,6" />
                    <CheckBox
                        Content="自动刷新数据"
                        Margin="85,0,0,0"
                        Padding="8,6" />
                </StackPanel>
            </GroupBox>
        </StackPanel>

    </DataTemplate>

    <!--  转换节点属性面板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="TransformNodePropertyPanel">

        <StackPanel Margin="0">
            <!--  基本信息组  -->
            <GroupBox Header="基本信息" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点名称:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}" />
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点类型:" VerticalAlignment="Center" />
                        <TextBlock
                            FontWeight="Medium"
                            Grid.Column="1"
                            Text="{Binding NodeTypeMetadata.Name}"
                            VerticalAlignment="Center" />
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!--  转换配置组  -->
            <GroupBox Header="转换配置" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="转换类型:" VerticalAlignment="Center" />
                        <ComboBox Grid.Column="1">
                            <ComboBoxItem Content="数据过滤" IsSelected="True" />
                            <ComboBoxItem Content="字段映射" />
                            <ComboBoxItem Content="数据计算" />
                            <ComboBoxItem Content="格式转换" />
                        </ComboBox>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Margin="0,4,0,0"
                            Text="表达式:"
                            VerticalAlignment="Top" />
                        <TextBox
                            AcceptsReturn="True"
                            Grid.Column="1"
                            Height="60"
                            TextWrapping="Wrap"
                            VerticalScrollBarVisibility="Auto" />
                    </Grid>

                    <CheckBox Content="启用数据验证" IsChecked="True" />
                </StackPanel>
            </GroupBox>

            <!--  字段映射组  -->
            <GroupBox Header="字段映射">
                <StackPanel Margin="8">
                    <Grid Height="120">
                        <DataGrid AutoGenerateColumns="False" CanUserAddRows="True">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="源字段" Width="*" />
                                <DataGridTextColumn Header="目标字段" Width="*" />
                                <DataGridTextColumn Header="转换规则" Width="*" />
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>

                    <StackPanel Margin="0,8,0,0" Orientation="Horizontal">
                        <Button
                            Content="添加映射"
                            Margin="0,0,8,0"
                            Width="80" />
                        <Button Content="删除映射" Width="80" />
                    </StackPanel>
                </StackPanel>
            </GroupBox>
        </StackPanel>

    </DataTemplate>

    <!--  输出节点属性面板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="OutputNodePropertyPanel">

        <StackPanel Margin="0">
            <!--  基本信息组  -->
            <GroupBox Header="基本信息" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点名称:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}" />
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点类型:" VerticalAlignment="Center" />
                        <TextBlock
                            FontWeight="Medium"
                            Grid.Column="1"
                            Text="{Binding NodeTypeMetadata.Name}"
                            VerticalAlignment="Center" />
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!--  输出配置组  -->
            <GroupBox Header="输出配置" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="输出类型:" VerticalAlignment="Center" />
                        <ComboBox Grid.Column="1">
                            <ComboBoxItem Content="文件输出" IsSelected="True" />
                            <ComboBoxItem Content="数据库" />
                            <ComboBoxItem Content="邮件发送" />
                            <ComboBoxItem Content="报表生成" />
                        </ComboBox>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="输出路径:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Margin="0,0,8,0" />
                        <Button
                            Content="浏览..."
                            Grid.Column="2"
                            Width="60" />
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="输出格式:" VerticalAlignment="Center" />
                        <ComboBox Grid.Column="1">
                            <ComboBoxItem Content="Excel (.xlsx)" IsSelected="True" />
                            <ComboBoxItem Content="CSV (.csv)" />
                            <ComboBoxItem Content="PDF (.pdf)" />
                            <ComboBoxItem Content="Word (.docx)" />
                        </ComboBox>
                    </Grid>

                    <CheckBox Content="覆盖已存在文件" IsChecked="False" />
                </StackPanel>
            </GroupBox>

            <!--  模板配置组  -->
            <GroupBox Header="模板配置">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="模板文件:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Margin="0,0,8,0" />
                        <Button
                            Content="选择..."
                            Grid.Column="2"
                            Width="60" />
                    </Grid>

                    <CheckBox Content="使用默认模板" IsChecked="True" />
                    <CheckBox Content="包含数据统计" Margin="0,4,0,0" />
                </StackPanel>
            </GroupBox>
        </StackPanel>

    </DataTemplate>

    <!--  数据计算节点属性面板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataCalculationPropertyPanel">

        <StackPanel Margin="0">
            <!--  基本信息组  -->
            <GroupBox
                Header="基本信息"
                Margin="0,0,0,16"
                Padding="12">
                <StackPanel>
                    <!--  节点名称和状态指示器  -->
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            FontWeight="Medium"
                            Text="节点名称:"
                            VerticalAlignment="Center" />
                        <TextBox
                            Grid.Column="1"
                            Margin="8,0,8,0"
                            Padding="8,6"
                            Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  状态指示器  -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Ellipse
                                Fill="{Binding StatusBrush}"
                                Height="12"
                                Margin="3,0"
                                Width="12" />
                            <Ellipse
                                Fill="Red"
                                Height="12"
                                Margin="3,0"
                                Width="12" />
                        </StackPanel>
                    </Grid>

                    <!--  节点类型  -->
                    <Grid Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            FontWeight="Medium"
                            Text="节点类型:"
                            VerticalAlignment="Center" />
                        <ComboBox
                            Grid.Column="1"
                            Margin="8,0,0,0"
                            Padding="8,6">
                            <ComboBoxItem Content="函数节点" IsSelected="True" />
                            <ComboBoxItem Content="数据输入节点" />
                            <ComboBoxItem Content="数据输出节点" />
                            <ComboBoxItem Content="条件节点" />
                            <ComboBoxItem Content="智能体节点" />
                        </ComboBox>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!--  配置选项组  -->
            <GroupBox
                Header="配置选项"
                Margin="0,0,0,16"
                Padding="12">
                <StackPanel>
                    <!--  计算类型选择  -->
                    <Border
                        BorderBrush="{StaticResource Brush.Border}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,0,0,12"
                        Padding="10">
                        <StackPanel>
                            <TextBlock
                                FontWeight="Medium"
                                Margin="0,0,0,8"
                                Text="计算类型" />
                            <ComboBox Padding="8,6">
                                <ComboBoxItem Content="基础运算" IsSelected="True" />
                                <ComboBoxItem Content="统计函数" />
                                <ComboBoxItem Content="三角函数" />
                                <ComboBoxItem Content="对数函数" />
                                <ComboBoxItem Content="自定义函数" />
                            </ComboBox>
                        </StackPanel>
                    </Border>

                    <!--  输入参数配置  -->
                    <Border
                        BorderBrush="{StaticResource Brush.Border}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Margin="0,0,0,12"
                        Padding="10">
                        <StackPanel>
                            <TextBlock
                                FontWeight="Medium"
                                Margin="0,0,0,8"
                                Text="输入参数" />
                            <ComboBox Padding="8,6">
                                <ComboBoxItem Content="数值输入" IsSelected="True" />
                                <ComboBoxItem Content="数组输入" />
                                <ComboBoxItem Content="表格输入" />
                                <ComboBoxItem Content="文本输入" />
                            </ComboBox>
                        </StackPanel>
                    </Border>

                    <!--  计算公式  -->
                    <Border
                        BorderBrush="{StaticResource Brush.Border}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="10">
                        <StackPanel>
                            <TextBlock
                                FontWeight="Medium"
                                Margin="0,0,0,8"
                                Text="计算公式" />
                            <TextBox
                                AcceptsReturn="True"
                                Height="60"
                                Padding="8,6"
                                Text="A + B * C"
                                TextWrapping="Wrap"
                                VerticalScrollBarVisibility="Auto" />
                        </StackPanel>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!--  高级设置组  -->
            <GroupBox
                Header="高级设置"
                Margin="0,0,0,16"
                Padding="12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  精度设置  -->
                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                        <TextBlock
                            FontWeight="Medium"
                            Margin="0,0,0,8"
                            Text="精度设置" />
                        <ComboBox Padding="8,6">
                            <ComboBoxItem Content="整数" />
                            <ComboBoxItem Content="1位小数" />
                            <ComboBoxItem Content="2位小数" IsSelected="True" />
                            <ComboBoxItem Content="3位小数" />
                            <ComboBoxItem Content="4位小数" />
                        </ComboBox>
                    </StackPanel>

                    <!--  错误处理  -->
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                        <TextBlock
                            FontWeight="Medium"
                            Margin="0,0,0,8"
                            Text="错误处理" />
                        <ComboBox Padding="8,6">
                            <ComboBoxItem Content="返回错误" IsSelected="True" />
                            <ComboBoxItem Content="返回默认值" />
                            <ComboBoxItem Content="跳过处理" />
                            <ComboBoxItem Content="停止执行" />
                        </ComboBox>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!--  变量映射表格  -->
            <GroupBox Header="变量映射" Padding="12">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <DataGrid
                        AutoGenerateColumns="False"
                        CanUserAddRows="True"
                        CanUserDeleteRows="True"
                        Grid.Row="0"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        Height="150">
                        <DataGrid.Columns>
                            <DataGridCheckBoxColumn Header="启用" Width="60" />
                            <DataGridTextColumn Header="变量名" Width="80" />
                            <DataGridTextColumn Header="数据源" Width="120" />
                            <DataGridTextColumn Header="默认值" Width="80" />
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Right"
                        Margin="0,8,0,0"
                        Orientation="Horizontal">
                        <Button
                            Content="添加变量"
                            Margin="0,0,8,0"
                            Padding="8,6"
                            Width="80" />
                        <Button
                            Content="删除变量"
                            Padding="8,6"
                            Width="80" />
                    </StackPanel>
                </Grid>
            </GroupBox>
        </StackPanel>

    </DataTemplate>

    <!--  控制节点属性面板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ControlNodePropertyPanel">

        <StackPanel Margin="0">
            <!--  基本信息组  -->
            <GroupBox Header="基本信息" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点名称:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}" />
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="节点类型:" VerticalAlignment="Center" />
                        <TextBlock
                            FontWeight="Medium"
                            Grid.Column="1"
                            Text="{Binding NodeTypeMetadata.Name}"
                            VerticalAlignment="Center" />
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!--  控制配置组  -->
            <GroupBox Header="控制配置" Margin="0,0,0,16">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="控制类型:" VerticalAlignment="Center" />
                        <ComboBox Grid.Column="1">
                            <ComboBoxItem Content="条件判断" IsSelected="True" />
                            <ComboBoxItem Content="循环处理" />
                            <ComboBoxItem Content="错误处理" />
                            <ComboBoxItem Content="流程控制" />
                        </ComboBox>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Margin="0,4,0,0"
                            Text="条件表达式:"
                            VerticalAlignment="Top" />
                        <TextBox
                            AcceptsReturn="True"
                            Grid.Column="1"
                            Height="60"
                            TextWrapping="Wrap"
                            VerticalScrollBarVisibility="Auto" />
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="最大迭代:" VerticalAlignment="Center" />
                        <TextBox Grid.Column="1" Text="100" />
                    </Grid>

                    <CheckBox Content="错误时继续执行" IsChecked="False" />
                </StackPanel>
            </GroupBox>

            <!--  超时配置组  -->
            <GroupBox Header="超时配置">
                <StackPanel Margin="8">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="超时时间:" VerticalAlignment="Center" />
                        <TextBox
                            Grid.Column="1"
                            Margin="0,0,8,0"
                            Text="30" />
                        <TextBlock
                            Grid.Column="2"
                            Text="秒"
                            VerticalAlignment="Center" />
                    </Grid>

                    <CheckBox Content="启用超时检测" IsChecked="True" />
                </StackPanel>
            </GroupBox>
        </StackPanel>

    </DataTemplate>

</ResourceDictionary>



