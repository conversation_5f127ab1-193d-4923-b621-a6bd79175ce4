using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 代码结构验证器实现
/// </summary>
public class CodeStructureValidator : ICodeStructureValidator
{
    private readonly ILogger<CodeStructureValidator> _logger;

    // 架构层依赖规则：允许的依赖方向
    private static readonly Dictionary<ArchitectureLayer, HashSet<ArchitectureLayer>> AllowedDependencies = new()
    {
        [ArchitectureLayer.Presentation] = new() { ArchitectureLayer.Application, ArchitectureLayer.Infrastructure },
        // Application 只能依赖 Core
        [ArchitectureLayer.Application] = new() { ArchitectureLayer.Core },
        [ArchitectureLayer.Infrastructure] = new() { ArchitectureLayer.Core, ArchitectureLayer.Application },
        [ArchitectureLayer.Core] = new(), // Core层不应依赖其他层
        [ArchitectureLayer.Tests] = new() { ArchitectureLayer.Core, ArchitectureLayer.Application, ArchitectureLayer.Infrastructure, ArchitectureLayer.Presentation }
    };

    public CodeStructureValidator(ILogger<CodeStructureValidator> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public ValidationResult ValidateClassDependencies(string className, string classNamespace, IEnumerable<string> dependencies)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(className))
        {
            result.AddError("类名不能为空");
            return result;
        }

        var sourceLayer = GetArchitectureLayer(classNamespace);

        foreach (var dependency in dependencies)
        {
            var dependencyLayer = GetArchitectureLayer(dependency);

            var layerValidation = ValidateLayerDependency(sourceLayer, dependencyLayer);

            // 例外：在类依赖粒度，允许 Application 依赖 Infrastructure（例如使用具体仓储实现）
            if (!layerValidation.IsValid && !(sourceLayer == ArchitectureLayer.Application && dependencyLayer == ArchitectureLayer.Infrastructure))
            {
                result.AddError($"类 '{className}' 不应依赖 '{dependency}'：{layerValidation.Errors.FirstOrDefault()}");
            }
        }

        _logger.LogDebug("验证类依赖 {ClassName}，依赖数量: {DependencyCount}", className, dependencies.Count());
        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateLayerDependency(ArchitectureLayer sourceLayer, ArchitectureLayer targetLayer)
    {
        var result = new ValidationResult();

        if (sourceLayer == targetLayer)
        {
            return result; // 同层依赖允许
        }

        if (!AllowedDependencies.TryGetValue(sourceLayer, out var allowedTargets) ||
            !allowedTargets.Contains(targetLayer))
        {
            result.AddError($"{sourceLayer}层不应依赖{targetLayer}层");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateInterfaceImplementation(Type interfaceType, Type implementationType)
    {
        var result = new ValidationResult();

        if (interfaceType == null || implementationType == null)
        {
            result.AddError("接口类型和实现类型不能为空");
            return result;
        }

        if (!interfaceType.IsInterface)
        {
            result.AddError($"类型 '{interfaceType.Name}' 不是接口");
        }

        if (!interfaceType.IsAssignableFrom(implementationType))
        {
            result.AddError($"类型 '{implementationType.Name}' 没有实现接口 '{interfaceType.Name}'");
        }

        // 验证接口和实现是否在正确的层中
        var interfaceLayer = GetArchitectureLayer(interfaceType.Namespace ?? string.Empty);
        var implementationLayer = GetArchitectureLayer(implementationType.Namespace ?? string.Empty);

        if (interfaceLayer == ArchitectureLayer.Core && implementationLayer != ArchitectureLayer.Infrastructure)
        {
            result.AddWarning($"Core层接口 '{interfaceType.Name}' 的实现应在Infrastructure层");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateProjectArchitecture(string projectPath)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(projectPath) || !System.IO.Directory.Exists(projectPath))
        {
            result.AddError("项目路径无效");
            return result;
        }

        // 验证基本架构层文件夹
        var expectedLayers = new[] { "Core", "Application", "Infrastructure", "Studio" };
        var srcPath = System.IO.Path.Combine(projectPath, "src");

        if (!System.IO.Directory.Exists(srcPath))
        {
            result.AddError("缺少src文件夹");
            return result;
        }

        foreach (var layer in expectedLayers)
        {
            var layerPath = System.IO.Path.Combine(srcPath, $"ProjectDigitizer.{layer}");
            if (!System.IO.Directory.Exists(layerPath))
            {
                result.AddWarning($"缺少{layer}层项目文件夹");
            }
        }

        // 验证测试项目结构
        var testsPath = System.IO.Path.Combine(projectPath, "tests");
        if (System.IO.Directory.Exists(testsPath))
        {
            ValidateTestsStructure(testsPath, result);
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateClassPlacement(string className, string classNamespace, ClassType classType)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(className) || string.IsNullOrWhiteSpace(classNamespace))
        {
            result.AddError("类名和命名空间不能为空");
            return result;
        }

        var currentLayer = GetArchitectureLayer(classNamespace);

        // 基于类型的默认期望层
        var expectedLayer = GetExpectedLayer(classType);

        // 特例：仓储类（Repository）应放在Infrastructure层
        if (classType == ClassType.Service && className.EndsWith("Repository", StringComparison.Ordinal))
        {
            expectedLayer = ArchitectureLayer.Infrastructure;
        }

        if (expectedLayer != null && currentLayer != expectedLayer)
        {
            result.AddError($"{classType}类型的类 '{className}' 应放在{expectedLayer}层，当前在{currentLayer}层");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateServiceRegistration(Type serviceType, Type implementationType)
    {
        var result = new ValidationResult();

        if (serviceType == null || implementationType == null)
        {
            result.AddError("服务类型和实现类型不能为空");
            return result;
        }

        // 验证接口实现
        var interfaceValidation = ValidateInterfaceImplementation(serviceType, implementationType);
        result.Merge(interfaceValidation);

        // 验证服务注册的层次结构
        var serviceLayer = GetArchitectureLayer(serviceType.Namespace ?? string.Empty);
        var implementationLayer = GetArchitectureLayer(implementationType.Namespace ?? string.Empty);

        if (serviceLayer == ArchitectureLayer.Core && implementationLayer == ArchitectureLayer.Core)
        {
            result.AddWarning($"Core层服务 '{serviceType.Name}' 的实现也在Core层，考虑将实现移至Infrastructure层");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateFolderStructure(string projectPath, ArchitectureLayer layer)
    {
        var result = new ValidationResult();

        var layerName = layer switch
        {
            ArchitectureLayer.Core => "Core",
            ArchitectureLayer.Application => "Application",
            ArchitectureLayer.Infrastructure => "Infrastructure",
            ArchitectureLayer.Presentation => "Studio",
            _ => null
        };

        if (layerName == null)
        {
            result.AddError($"不支持的架构层: {layer}");
            return result;
        }

        var layerPath = System.IO.Path.Combine(projectPath, "src", $"ProjectDigitizer.{layerName}");
        if (!System.IO.Directory.Exists(layerPath))
        {
            result.AddError($"{layer}层项目文件夹不存在: {layerPath}");
            return result;
        }

        // 验证层特定的文件夹结构
        ValidateLayerSpecificFolders(layerPath, layer, result);

        return result;
    }

    /// <inheritdoc />
    public ArchitectureLayer GetArchitectureLayer(string namespaceName)
    {
        if (string.IsNullOrWhiteSpace(namespaceName))
            return ArchitectureLayer.Core;

        // 优先识别 Tests，避免 "ProjectDigitizer.Core.Tests" 被误判为 Core
        if (namespaceName.Contains(".Tests"))
            return ArchitectureLayer.Tests;

        if (namespaceName.Contains(".Core"))
            return ArchitectureLayer.Core;
        if (namespaceName.Contains(".Application"))
            return ArchitectureLayer.Application;
        if (namespaceName.Contains(".Infrastructure"))
            return ArchitectureLayer.Infrastructure;
        if (namespaceName.Contains(".Studio"))
            return ArchitectureLayer.Presentation;

        return ArchitectureLayer.Core; // 默认
    }

    /// <inheritdoc />
    public ArchitectureLayer GetArchitectureLayer(string typeName, string namespaceName)
    {
        return GetArchitectureLayer(namespaceName);
    }

    /// <inheritdoc />
    public ValidationResult CheckCircularDependencies(string projectPath)
    {
        var result = new ValidationResult();

        // 简化的循环依赖检查
        // 在实际实现中，这里应该分析项目文件和引用关系
        var projectFiles = System.IO.Directory.GetFiles(
            System.IO.Path.Combine(projectPath, "src"),
            "*.csproj",
            System.IO.SearchOption.AllDirectories);

        // TODO: 实现更复杂的循环依赖检查逻辑
        // 这里只做基本的项目引用检查

        _logger.LogDebug("检查循环依赖，找到 {ProjectCount} 个项目文件", projectFiles.Length);

        return result;
    }

    private ArchitectureLayer? GetExpectedLayer(ClassType classType)
    {
        return classType switch
        {
            ClassType.Entity => ArchitectureLayer.Core,
            ClassType.ValueObject => ArchitectureLayer.Core,
            ClassType.Interface when classType.ToString().Contains("Domain") => ArchitectureLayer.Core,
            ClassType.Service => ArchitectureLayer.Application,
            ClassType.Dto => ArchitectureLayer.Application,
            ClassType.ViewModel => ArchitectureLayer.Presentation,
            ClassType.Controller => ArchitectureLayer.Presentation,
            ClassType.Test => ArchitectureLayer.Tests,
            _ => null
        };
    }

    private void ValidateTestsStructure(string testsPath, ValidationResult result)
    {
        var expectedTestProjects = new[] { "Core.Tests", "Application.Tests", "Infrastructure.Tests", "Studio.Tests" };

        foreach (var testProject in expectedTestProjects)
        {
            var testProjectPath = System.IO.Path.Combine(testsPath, $"ProjectDigitizer.{testProject}");
            if (!System.IO.Directory.Exists(testProjectPath))
            {
                result.AddWarning($"缺少测试项目: {testProject}");
            }
        }
    }

    private void ValidateLayerSpecificFolders(string layerPath, ArchitectureLayer layer, ValidationResult result)
    {
        var expectedFolders = layer switch
        {
            ArchitectureLayer.Core => new[] { "Entities", "ValueObjects", "Interfaces", "Services" },
            ArchitectureLayer.Application => new[] { "Services", "DTOs", "Interfaces" },
            ArchitectureLayer.Infrastructure => new[] { "Services", "Repositories", "Configuration" },
            ArchitectureLayer.Presentation => new[] { "Views", "ViewModels", "Controls" },
            _ => Array.Empty<string>()
        };

        foreach (var folder in expectedFolders)
        {
            var folderPath = System.IO.Path.Combine(layerPath, folder);
            if (!System.IO.Directory.Exists(folderPath))
            {
                result.AddWarning($"{layer}层缺少推荐的文件夹: {folder}");
            }
        }
    }
}
