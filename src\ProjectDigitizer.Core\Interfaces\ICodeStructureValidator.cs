using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 代码结构验证器接口
/// </summary>
public interface ICodeStructureValidator
{
    /// <summary>
    /// 验证类的依赖关系是否符合Clean Architecture
    /// </summary>
    /// <param name="className">类名</param>
    /// <param name="classNamespace">类的命名空间</param>
    /// <param name="dependencies">依赖的类型列表</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateClassDependencies(string className, string classNamespace, IEnumerable<string> dependencies);

    /// <summary>
    /// 验证层间依赖方向是否正确
    /// </summary>
    /// <param name="sourceLayer">源层</param>
    /// <param name="targetLayer">目标层</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateLayerDependency(ArchitectureLayer sourceLayer, ArchitectureLayer targetLayer);

    /// <summary>
    /// 验证接口和实现的分离
    /// </summary>
    /// <param name="interfaceType">接口类型</param>
    /// <param name="implementationType">实现类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateInterfaceImplementation(Type interfaceType, Type implementationType);

    /// <summary>
    /// 验证项目的整体架构结构
    /// </summary>
    /// <param name="projectPath">项目根路径</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateProjectArchitecture(string projectPath);

    /// <summary>
    /// 验证类是否放在正确的层中
    /// </summary>
    /// <param name="className">类名</param>
    /// <param name="classNamespace">类的命名空间</param>
    /// <param name="classType">类类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateClassPlacement(string className, string classNamespace, ClassType classType);

    /// <summary>
    /// 验证服务注册是否符合架构规范
    /// </summary>
    /// <param name="serviceType">服务类型</param>
    /// <param name="implementationType">实现类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateServiceRegistration(Type serviceType, Type implementationType);

    /// <summary>
    /// 验证文件夹结构是否符合架构层次
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <param name="layer">架构层</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateFolderStructure(string projectPath, ArchitectureLayer layer);

    /// <summary>
    /// 获取类型所属的架构层
    /// </summary>
    /// <param name="typeName">类型名称</param>
    /// <param name="namespaceName">命名空间</param>
    /// <returns>架构层</returns>
    ArchitectureLayer GetArchitectureLayer(string typeName, string namespaceName);

    /// <summary>
    /// 检查是否存在循环依赖
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <returns>验证结果</returns>
    ValidationResult CheckCircularDependencies(string projectPath);
}

/// <summary>
/// 代码结构验证结果
/// </summary>
public class CodeStructureValidationResult : ValidationResult
{
    /// <summary>
    /// 架构违规列表
    /// </summary>
    public List<ArchitectureViolation> ArchitectureViolations { get; set; } = new();

    /// <summary>
    /// 依赖关系图
    /// </summary>
    public Dictionary<string, List<string>> DependencyGraph { get; set; } = new();

    /// <summary>
    /// 层间依赖统计
    /// </summary>
    public Dictionary<string, int> LayerDependencyCount { get; set; } = new();
}

/// <summary>
/// 架构违规信息
/// </summary>
public class ArchitectureViolation
{
    /// <summary>
    /// 违规类型
    /// </summary>
    public ViolationType Type { get; set; }

    /// <summary>
    /// 源类型
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// 目标类型
    /// </summary>
    public string TargetType { get; set; } = string.Empty;

    /// <summary>
    /// 违规描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public ViolationSeverity Severity { get; set; }

    /// <summary>
    /// 建议修复方案
    /// </summary>
    public string SuggestedFix { get; set; } = string.Empty;
}

/// <summary>
/// 违规类型
/// </summary>
public enum ViolationType
{
    /// <summary>层间依赖违规</summary>
    LayerDependencyViolation,
    /// <summary>循环依赖</summary>
    CircularDependency,
    /// <summary>接口分离违规</summary>
    InterfaceSegregationViolation,
    /// <summary>单一职责违规</summary>
    SingleResponsibilityViolation,
    /// <summary>依赖倒置违规</summary>
    DependencyInversionViolation,
    /// <summary>类放置错误</summary>
    ClassPlacementViolation
}

/// <summary>
/// 违规严重程度
/// </summary>
public enum ViolationSeverity
{
    /// <summary>信息</summary>
    Info,
    /// <summary>警告</summary>
    Warning,
    /// <summary>错误</summary>
    Error,
    /// <summary>严重错误</summary>
    Critical
}
