using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Threading;

namespace ProjectDigitizer.Infrastructure.UI
{
    /// <summary>
    /// 响应式UI管理器接口
    /// </summary>
    public interface IReactiveUIManager : IDisposable
    {
        /// <summary>
        /// 当前UI更新配置
        /// </summary>
        ReactiveUIConfig CurrentConfig { get; }

        /// <summary>
        /// UI更新统计信息
        /// </summary>
        UIUpdateStats UpdateStats { get; }

        /// <summary>
        /// UI更新批处理完成事件
        /// </summary>
        event Action<UIBatchUpdateEventArgs>? BatchUpdateCompleted;

        /// <summary>
        /// UI性能警告事件
        /// </summary>
        event Action<UIPerformanceWarning>? PerformanceWarning;

        /// <summary>
        /// 开始响应式UI管理
        /// </summary>
        void StartReactiveUI();

        /// <summary>
        /// 停止响应式UI管理
        /// </summary>
        void StopReactiveUI();

        /// <summary>
        /// 注册ViewModel进行响应式管理
        /// </summary>
        /// <param name="viewModel">要管理的ViewModel</param>
        /// <param name="config">特定的配置</param>
        void RegisterViewModel(INotifyPropertyChanged viewModel, ViewModelConfig? config = null);

        /// <summary>
        /// 取消注册ViewModel
        /// </summary>
        /// <param name="viewModel">要取消注册的ViewModel</param>
        void UnregisterViewModel(INotifyPropertyChanged viewModel);

        /// <summary>
        /// 批量更新UI属性
        /// </summary>
        /// <param name="updates">要更新的属性列表</param>
        void BatchUpdateProperties(IEnumerable<PropertyUpdate> updates);

        /// <summary>
        /// 延迟执行UI更新
        /// </summary>
        /// <param name="updateAction">更新操作</param>
        /// <param name="delay">延迟时间（毫秒）</param>
        void DelayedUpdate(Action updateAction, int delay = 100);

        /// <summary>
        /// 节流执行UI更新
        /// </summary>
        /// <param name="key">节流键</param>
        /// <param name="updateAction">更新操作</param>
        /// <param name="throttleMs">节流时间（毫秒）</param>
        void ThrottledUpdate(string key, Action updateAction, int throttleMs = 100);

        /// <summary>
        /// 防抖执行UI更新
        /// </summary>
        /// <param name="key">防抖键</param>
        /// <param name="updateAction">更新操作</param>
        /// <param name="debounceMs">防抖时间（毫秒）</param>
        void DebouncedUpdate(string key, Action updateAction, int debounceMs = 300);

        /// <summary>
        /// 设置属性更新优先级
        /// </summary>
        /// <param name="viewModel">ViewModel</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="priority">优先级</param>
        void SetPropertyPriority(INotifyPropertyChanged viewModel, string propertyName, UpdatePriority priority);

        /// <summary>
        /// 暂停指定ViewModel的UI更新
        /// </summary>
        /// <param name="viewModel">要暂停的ViewModel</param>
        void SuspendUpdates(INotifyPropertyChanged viewModel);

        /// <summary>
        /// 恢复指定ViewModel的UI更新
        /// </summary>
        /// <param name="viewModel">要恢复的ViewModel</param>
        void ResumeUpdates(INotifyPropertyChanged viewModel);

        /// <summary>
        /// 强制刷新所有待处理的UI更新
        /// </summary>
        void FlushPendingUpdates();

        /// <summary>
        /// 获取当前UI更新性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        UIPerformanceMetrics GetPerformanceMetrics();

        /// <summary>
        /// 应用响应式UI配置
        /// </summary>
        /// <param name="config">配置</param>
        void ApplyConfig(ReactiveUIConfig config);
    }

    /// <summary>
    /// 响应式UI配置
    /// </summary>
    public class ReactiveUIConfig
    {
        /// <summary>
        /// 是否启用批处理更新
        /// </summary>
        public bool EnableBatchUpdates { get; set; } = true;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 20;

        /// <summary>
        /// 批处理延迟（毫秒）
        /// </summary>
        public int BatchDelayMs { get; set; } = 50;

        /// <summary>
        /// 是否启用节流
        /// </summary>
        public bool EnableThrottling { get; set; } = true;

        /// <summary>
        /// 默认节流时间（毫秒）
        /// </summary>
        public int DefaultThrottleMs { get; set; } = 100;

        /// <summary>
        /// 是否启用防抖
        /// </summary>
        public bool EnableDebouncing { get; set; } = true;

        /// <summary>
        /// 默认防抖时间（毫秒）
        /// </summary>
        public int DefaultDebounceMs { get; set; } = 300;

        /// <summary>
        /// 最大待处理更新数量
        /// </summary>
        public int MaxPendingUpdates { get; set; } = 1000;

        /// <summary>
        /// UI线程优先级
        /// </summary>
        public DispatcherPriority UIThreadPriority { get; set; } = DispatcherPriority.Normal;

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;
    }

    /// <summary>
    /// ViewModel配置
    /// </summary>
    public class ViewModelConfig
    {
        /// <summary>
        /// ViewModel名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用批处理
        /// </summary>
        public bool EnableBatching { get; set; } = true;

        /// <summary>
        /// 更新优先级
        /// </summary>
        public UpdatePriority Priority { get; set; } = UpdatePriority.Normal;

        /// <summary>
        /// 属性特定配置
        /// </summary>
        public Dictionary<string, PropertyConfig> PropertyConfigs { get; set; } = new();
    }

    /// <summary>
    /// 属性配置
    /// </summary>
    public class PropertyConfig
    {
        /// <summary>
        /// 更新优先级
        /// </summary>
        public UpdatePriority Priority { get; set; } = UpdatePriority.Normal;

        /// <summary>
        /// 节流时间（毫秒）
        /// </summary>
        public int ThrottleMs { get; set; } = 100;

        /// <summary>
        /// 防抖时间（毫秒）
        /// </summary>
        public int DebounceMs { get; set; } = 300;

        /// <summary>
        /// 是否启用批处理
        /// </summary>
        public bool EnableBatching { get; set; } = true;
    }

    /// <summary>
    /// 属性更新信息
    /// </summary>
    public class PropertyUpdate
    {
        /// <summary>
        /// 目标ViewModel
        /// </summary>
        public INotifyPropertyChanged ViewModel { get; set; } = null!;

        /// <summary>
        /// 属性名
        /// </summary>
        public string PropertyName { get; set; } = string.Empty;

        /// <summary>
        /// 新值
        /// </summary>
        public object? NewValue { get; set; }

        /// <summary>
        /// 更新优先级
        /// </summary>
        public UpdatePriority Priority { get; set; } = UpdatePriority.Normal;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// UI更新统计信息
    /// </summary>
    public class UIUpdateStats
    {
        /// <summary>
        /// 总更新次数
        /// </summary>
        public long TotalUpdates { get; set; }

        /// <summary>
        /// 批处理更新次数
        /// </summary>
        public long BatchedUpdates { get; set; }

        /// <summary>
        /// 节流跳过的更新次数
        /// </summary>
        public long ThrottledUpdates { get; set; }

        /// <summary>
        /// 防抖跳过的更新次数
        /// </summary>
        public long DebouncedUpdates { get; set; }

        /// <summary>
        /// 平均更新延迟（毫秒）
        /// </summary>
        public double AverageUpdateDelayMs { get; set; }

        /// <summary>
        /// 当前待处理更新数量
        /// </summary>
        public int PendingUpdatesCount { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// UI批处理更新事件参数
    /// </summary>
    public class UIBatchUpdateEventArgs : EventArgs
    {
        /// <summary>
        /// 批处理的更新数量
        /// </summary>
        public int UpdateCount { get; set; }

        /// <summary>
        /// 批处理执行时间（毫秒）
        /// </summary>
        public double ExecutionTimeMs { get; set; }

        /// <summary>
        /// 涉及的ViewModel数量
        /// </summary>
        public int ViewModelCount { get; set; }

        /// <summary>
        /// 批处理类型
        /// </summary>
        public BatchUpdateType BatchType { get; set; }
    }

    /// <summary>
    /// UI性能警告
    /// </summary>
    public class UIPerformanceWarning
    {
        /// <summary>
        /// 警告级别
        /// </summary>
        public UIWarningLevel Level { get; set; }

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 相关的ViewModel
        /// </summary>
        public INotifyPropertyChanged? RelatedViewModel { get; set; }

        /// <summary>
        /// 当前性能指标
        /// </summary>
        public UIPerformanceMetrics CurrentMetrics { get; set; } = new();

        /// <summary>
        /// 建议的解决方案
        /// </summary>
        public IEnumerable<string> SuggestedSolutions { get; set; } = new List<string>();
    }

    /// <summary>
    /// UI性能指标
    /// </summary>
    public class UIPerformanceMetrics
    {
        /// <summary>
        /// UI响应时间（毫秒）
        /// </summary>
        public double UIResponseTimeMs { get; set; }

        /// <summary>
        /// 更新频率（每秒）
        /// </summary>
        public double UpdateFrequency { get; set; }

        /// <summary>
        /// 批处理效率（0-1）
        /// </summary>
        public double BatchingEfficiency { get; set; }

        /// <summary>
        /// 节流效率（0-1）
        /// </summary>
        public double ThrottlingEfficiency { get; set; }

        /// <summary>
        /// 整体UI性能评分（0-100）
        /// </summary>
        public double OverallUIPerformanceScore { get; set; }

        /// <summary>
        /// 注册的ViewModel数量
        /// </summary>
        public int RegisteredViewModelsCount { get; set; }
    }

    /// <summary>
    /// 更新优先级
    /// </summary>
    public enum UpdatePriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low,

        /// <summary>
        /// 正常优先级
        /// </summary>
        Normal,

        /// <summary>
        /// 高优先级
        /// </summary>
        High,

        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical
    }

    /// <summary>
    /// 批处理更新类型
    /// </summary>
    public enum BatchUpdateType
    {
        /// <summary>
        /// 时间触发的批处理
        /// </summary>
        TimeBased,

        /// <summary>
        /// 大小触发的批处理
        /// </summary>
        SizeBased,

        /// <summary>
        /// 手动触发的批处理
        /// </summary>
        Manual
    }

    /// <summary>
    /// UI警告级别
    /// </summary>
    public enum UIWarningLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }
}
