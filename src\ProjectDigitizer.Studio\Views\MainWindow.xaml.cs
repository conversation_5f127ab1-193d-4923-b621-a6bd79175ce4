using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

using Microsoft.Win32;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Extensions;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Selectors;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.ViewModels;

using Wpf.Ui.Appearance;

namespace ProjectDigitizer.Studio.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Wpf.Ui.Controls.FluentWindow
    {
        /// <summary>
        /// 画布视图模型
        /// </summary>
        private readonly CanvasViewModel _canvasViewModel;

        /// <summary>
        /// 项目文件适配器
        /// </summary>
        private readonly IProjectFileAdapter _projectFileService;

        /// <summary>
        /// 视口更新循环防护
        /// </summary>
        private bool _isUpdatingViewport = false;

        /// <summary>
        /// 异步操作管理器
        /// </summary>
        private readonly IAsyncOperationManager _operationManager;

        /// <summary>
        /// 进度指示器视图模型
        /// </summary>
        private readonly ProgressIndicatorViewModel _progressIndicator;

        /// <summary>
        /// 当前项目信息
        /// </summary>
        private ProjectInfo _currentProjectInfo;

        /// <summary>
        /// 当前项目文件路径
        /// </summary>
        private string? _currentProjectPath;

        public MainWindow(CanvasViewModel canvasViewModel, IProjectFileAdapter projectFileService, IAsyncOperationManager operationManager)
        {
            InitializeComponent();

            // 通过依赖注入获取服务
            _canvasViewModel = canvasViewModel ?? throw new ArgumentNullException(nameof(canvasViewModel));
            _projectFileService = projectFileService ?? throw new ArgumentNullException(nameof(projectFileService));
            _operationManager = operationManager ?? throw new ArgumentNullException(nameof(operationManager));
            _currentProjectInfo = new ProjectInfo();

            // 初始化进度指示器
            _progressIndicator = new ProgressIndicatorViewModel(_operationManager);
            ProgressIndicator.SetViewModel(_progressIndicator);

            // 设置数据上下文
            DataContext = _canvasViewModel;

            // 重新启用事件订阅，但保持谨慎
            System.Diagnostics.Debug.WriteLine("重新启用CanvasViewModel事件订阅");
            _canvasViewModel.PropertyChanged += CanvasViewModel_PropertyChanged; // 重新启用以支持连接线样式变更
            _canvasViewModel.FitToNodesRequested += CanvasViewModel_FitToNodesRequested;

            // 更新窗口标题
            UpdateWindowTitle();

            // 添加键盘快捷键
            AddKeyboardShortcuts();

            // 监听画布视口变化
            Loaded += MainWindow_Loaded;

            // 监听窗口关闭事件
            Closing += MainWindow_Closing;

            // 初始化主题（默认浅色 + Mica 背景）
            try { ApplicationThemeManager.Apply(ApplicationTheme.Light); ThemeResourceService.ApplyDark(false); } catch { }
        }

        /// <summary>
        /// 窗口加载完成事件处理
        /// </summary>
        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 初始化视口信息
            UpdateViewportInfo();

            // 订阅画布视口更新事件（带循环保护）
            if (Canvas != null)
            {
                Canvas.ViewportUpdated += Canvas_ViewportUpdated;
            }
        }

        /// <summary>
        /// 画布视口更新事件处理
        /// </summary>
        private void Canvas_ViewportUpdated(object sender, EventArgs e)
        {
            // 防止视口更新循环
            if (_isUpdatingViewport)
            {
                return;
            }

            UpdateViewportInfo();

            // 同步更新缩放控制器显示（处理鼠标滚轮缩放同步）
            if (Canvas != null && ZoomControl != null)
            {
                ZoomControl.SetZoomLevel(Canvas.ViewportZoom);
            }
        }

        /// <summary>
        /// 更新视口信息到ViewModel
        /// </summary>
        private void UpdateViewportInfo()
        {
            if (Canvas != null)
            {
                if (_isUpdatingViewport) return; // 防止循环
                _isUpdatingViewport = true;
                try
                {
                    // 获取画布的实际大小（容错：若为0则跳过）
                    var canvasWidth = Canvas.ActualWidth;
                    var canvasHeight = Canvas.ActualHeight;
                    if (canvasWidth <= 0 || canvasHeight <= 0)
                    {
                        return;
                    }
                    var canvasSize = new Size(canvasWidth, canvasHeight);

                    // 计算视口中心（考虑缩放和平移）
                    var centerX = canvasWidth / 2.0;
                    var centerY = canvasHeight / 2.0;

                    // 获取缩放级别
                    var zoom = Canvas.ViewportZoom;

                    // 计算实际可见视口区域
                    var viewportLocation = Canvas.ViewportLocation;
                    var viewportWidth = canvasWidth / zoom;
                    var viewportHeight = canvasHeight / zoom;
                    var viewport = new Rect(
                        viewportLocation.X,
                        viewportLocation.Y,
                        viewportWidth,
                        viewportHeight
                    );

                    // 如果有ViewportTransform，应用变换到中心点
                    if (Canvas.ViewportTransform != null)
                    {
                        var transform = Canvas.ViewportTransform;
                        var center = transform.Transform(new Point(centerX, centerY));
                        centerX = center.X;
                        centerY = center.Y;
                    }

                    // 更新ViewModel，传递完整的视口信息
                    _canvasViewModel.UpdateViewportInfo(
                        new Point(centerX, centerY),
                        zoom,
                        canvasSize,
                        viewport
                    );

                    // 同步缩放控制器显示（避免滚轮缩放时不同步）
                    if (ZoomControl != null)
                    {
                        ZoomControl.SetZoomLevel(zoom);
                    }
                }
                finally
                {
                    _isUpdatingViewport = false;
                }
            }
        }

        /// <summary>
        /// 画布视图模型属性变化处理
        /// </summary>
        private void CanvasViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(CanvasViewModel.SelectedNode))
            {
                // 重新启用PropertyPanel更新，但保持安全处理
                try
                {
                    System.Diagnostics.Debug.WriteLine($"SelectedNode changed: {_canvasViewModel.SelectedNode?.Title ?? "null"}");
                    PropertyPanel.SetCurrentNode(_canvasViewModel.SelectedNode);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"PropertyPanel update error: {ex.Message}");
                }
            }
            else if (e.PropertyName == nameof(CanvasViewModel.IsPropertyPanelVisible))
            {
                System.Diagnostics.Debug.WriteLine($"IsPropertyPanelVisible changed: {_canvasViewModel.IsPropertyPanelVisible}");
            }
            else if (e.PropertyName == nameof(CanvasViewModel.CurrentConnectionLineStyle))
            {
                // 连接线样式变更处理
                UpdateConnectionLineStyle();
            }
        }

        /// <summary>
        /// 适应节点视图请求处理
        /// </summary>
        private void CanvasViewModel_FitToNodesRequested(Rect nodesBounds)
        {
            // 防止在视口更新过程中触发新的适应操作
            if (_isUpdatingViewport)
            {
                return;
            }

            if (Canvas != null && nodesBounds.Width > 0 && nodesBounds.Height > 0)
            {
                _isUpdatingViewport = true;
                try
                {
                    // 计算合适的缩放级别和视口位置
                    var canvasWidth = Canvas.ActualWidth;
                    var canvasHeight = Canvas.ActualHeight;

                    if (canvasWidth > 0 && canvasHeight > 0)
                    {
                        // 添加边距
                        const double margin = 100;
                        var availableWidth = canvasWidth - 2 * margin;
                        var availableHeight = canvasHeight - 2 * margin;

                        // 计算缩放比例
                        var scaleX = availableWidth / nodesBounds.Width;
                        var scaleY = availableHeight / nodesBounds.Height;
                        var scale = Math.Min(scaleX, scaleY);

                        // 限制缩放范围
                        scale = Math.Max(0.1, Math.Min(2.0, scale));

                        // 计算视口中心位置
                        var centerX = nodesBounds.X + nodesBounds.Width / 2;
                        var centerY = nodesBounds.Y + nodesBounds.Height / 2;

                        // 应用缩放和位置
                        Canvas.ViewportZoom = scale;
                        Canvas.ViewportLocation = new Point(
                            centerX - (canvasWidth / scale) / 2,
                            centerY - (canvasHeight / scale) / 2
                        );

                        // 临时禁用缩放控制器更新，防止Stack overflow
                        // if (ZoomControl != null)
                        // {
                        //     ZoomControl.SetZoomLevel(scale);
                        // }

                        System.Diagnostics.Debug.WriteLine($"自动适应视图：缩放={scale:F2}, 中心=({centerX:F0}, {centerY:F0})");
                    }
                }
                finally
                {
                    _isUpdatingViewport = false;
                }
            }
        }

        /// <summary>
        /// 画布选择变化事件处理
        /// </summary>
        private void Canvas_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 单选时显示属性面板，多选或无选择时隐藏
            if (Canvas.SelectedItems?.Count == 1 && Canvas.SelectedItems[0] is ModuleNodeViewModel singleNode)
            {
                _canvasViewModel.SelectedNode = singleNode;
                _canvasViewModel.IsPropertyPanelVisible = true;
            }
            else
            {
                _canvasViewModel.SelectedNode = null;
                _canvasViewModel.IsPropertyPanelVisible = false;
            }
        }

        /// <summary>
        /// 全部启用按钮点击事件
        /// </summary>
        private void BtnEnableAll_Click(object sender, RoutedEventArgs e)
        {
            _canvasViewModel.SetAllModulesEnabled(true);
        }

        /// <summary>
        /// 全部关闭按钮点击事件
        /// </summary>
        private void BtnDisableAll_Click(object sender, RoutedEventArgs e)
        {
            _canvasViewModel.SetAllModulesEnabled(false);
        }

        /// <summary>
        /// 清空画布按钮点击事件
        /// </summary>
        private void BtnClearCanvas_Click(object sender, RoutedEventArgs e)
        {
            if (MessageBox.Show("确定要清空画布吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                _canvasViewModel.Clear();
            }
        }

        /// <summary>
        /// 删除选中按钮点击事件
        /// </summary>
        private void BtnDeleteSelected_Click(object sender, RoutedEventArgs e)
        {
            if (_canvasViewModel.SelectedNode != null)
            {
                _canvasViewModel.RemoveNode(_canvasViewModel.SelectedNode);
            }
        }

        /// <summary>
        /// 隐藏关闭的模块按钮点击事件
        /// </summary>
        private void BtnHideClosedModules_Click(object sender, RoutedEventArgs e)
        {
            _canvasViewModel.HideClosedModules();
        }

        /// <summary>
        /// 自动布局按钮点击事件
        /// </summary>
        private async void BtnAutoLayoutHierarchical_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("=== 自动布局按钮被点击 ===");

            var button = sender as Button;

            try
            {
                // 禁用按钮防止重复点击
                if (button != null)
                {
                    button.IsEnabled = false;
                    button.Content = "布局中...";
                }

                System.Diagnostics.Debug.WriteLine("开始执行自动布局...");

                // 直接调用自动布局功能，不使用复杂的异步操作管理器
                await _canvasViewModel.ApplyAutoLayoutAsync();

                System.Diagnostics.Debug.WriteLine("自动布局执行完成");
                // TODO: 集成 WPFUI Snackbar 服务后在此提示 "自动布局完成"
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动布局失败: {ex.Message}");
                MessageBox.Show($"自动布局失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "自动布局";
                }
                System.Diagnostics.Debug.WriteLine("=== 自动布局按钮事件处理完成 ===");
            }
        }





        /// <summary>
        /// 查找指定类型的祖先元素
        /// </summary>
        private static T? FindAncestor<T>(DependencyObject current) where T : DependencyObject
        {
            do
            {
                if (current is T ancestor)
                {
                    return ancestor;
                }
                current = VisualTreeHelper.GetParent(current);
            }
            while (current != null);

            return null;
        }

        /// <summary>
        /// 检查元素是否为连接器相关元素
        /// </summary>
        private bool IsConnectorElement(FrameworkElement? element)
        {
            if (element == null) return false;

            // 检查元素本身或其祖先是否为连接器相关类型
            var current = element as DependencyObject;
            while (current != null)
            {
                // 检查Nodify连接器类型
                if (current is Nodify.NodeInput || current is Nodify.NodeOutput)
                {
                    return true;
                }

                // 检查元素名称或类型名称是否包含连接器相关关键字
                if (current is FrameworkElement fe)
                {
                    var typeName = fe.GetType().Name.ToLower();
                    if (typeName.Contains("connector") || typeName.Contains("input") || typeName.Contains("output"))
                    {
                        return true;
                    }

                    // 检查元素名称
                    if (!string.IsNullOrEmpty(fe.Name))
                    {
                        var name = fe.Name.ToLower();
                        if (name.Contains("connector") || name.Contains("input") || name.Contains("output"))
                        {
                            return true;
                        }
                    }
                }

                current = VisualTreeHelper.GetParent(current);
            }

            return false;
        }



        /// <summary>
        /// 手动更新UI中的选择状态
        /// </summary>
        private void UpdateUISelection()
        {
            // 遍历所有ItemContainer，手动设置IsSelected状态
            var containers = FindVisualChildren<Nodify.ItemContainer>(Canvas);
            foreach (var container in containers)
            {
                if (container.DataContext is ModuleNodeViewModel nodeVM)
                {
                    container.IsSelected = nodeVM.IsSelected;
                }
            }
        }

        /// <summary>
        /// 查找指定类型的所有可视化子元素
        /// </summary>
        private IEnumerable<T> FindVisualChildren<T>(DependencyObject? depObj) where T : DependencyObject
        {
            if (depObj == null) yield break;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(depObj, i);

                if (child != null)
                {
                    if (child is T t)
                    {
                        yield return t;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        #region 项目文件操作

        /// <summary>
        /// 新建项目
        /// </summary>
        private void NewProject()
        {
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show("当前项目有未保存的更改，是否保存？", "确认",
                    MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SaveProject();
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return;
                }
            }

            // 清空画布
            _canvasViewModel.Clear();

            // 重置项目信息
            _currentProjectInfo = new ProjectInfo();
            _currentProjectPath = null;

            UpdateWindowTitle();


        }

        /// <summary>
        /// 保存项目
        /// </summary>
        private async void SaveProject()
        {
            if (string.IsNullOrEmpty(_currentProjectPath))
            {
                SaveProjectAs();
                return;
            }

            try
            {
                // 使用异步操作管理器执行保存操作
                var operationHandle = await _operationManager.StartOperationAsync(
                    "保存项目",
                    async (progress, cancellationToken) =>
                    {
                        progress.Report(new OperationProgress(0, "准备保存数据..."));

                        var projectFile = _projectFileService.CreateProjectFileFromCanvas(_canvasViewModel, _currentProjectInfo);

                        progress.Report(new OperationProgress(50, "写入文件..."));
                        await _projectFileService.SaveProjectAsync(projectFile, _currentProjectPath);

                        progress.Report(new OperationProgress(100, "保存完成"));
                    });

                await operationHandle.WaitAsync();

                UpdateWindowTitle();
                MessageBox.Show("项目保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (OperationCanceledException)
            {
                MessageBox.Show("保存操作已取消", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存项目失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 另存为项目
        /// </summary>
        private async void SaveProjectAs()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "保存项目文件",
                Filter = "工程文件 (*.wftd)|*.wftd|模板文件 (*.wft)|*.wft|所有文件 (*.*)|*.*",
                DefaultExt = "wftd",
                FileName = _currentProjectInfo.Name
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    _currentProjectPath = saveFileDialog.FileName;

                    // 使用异步操作管理器执行另存为操作
                    var operationHandle = await _operationManager.StartOperationAsync(
                        "另存为项目",
                        async (progress, cancellationToken) =>
                        {
                            progress.Report(new OperationProgress(0, "准备保存数据..."));

                            var projectFile = _projectFileService.CreateProjectFileFromCanvas(_canvasViewModel, _currentProjectInfo);

                            progress.Report(new OperationProgress(50, "写入文件..."));
                            await _projectFileService.SaveProjectAsync(projectFile, _currentProjectPath);

                            progress.Report(new OperationProgress(100, "保存完成"));
                        });

                    await operationHandle.WaitAsync();

                    UpdateWindowTitle();
                    MessageBox.Show("项目保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (OperationCanceledException)
                {
                    MessageBox.Show("保存操作已取消", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存项目失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 打开项目
        /// </summary>
        private async void OpenProject()
        {
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show("当前项目有未保存的更改，是否保存？", "确认",
                    MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SaveProject();
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return;
                }
            }

            var openFileDialog = new OpenFileDialog
            {
                Title = "打开项目文件",
                Filter = "工程文件 (*.wftd)|*.wftd|模板文件 (*.wft)|*.wft|所有文件 (*.*)|*.*",
                DefaultExt = "wftd"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 使用异步操作管理器执行打开操作
                    var operationHandle = await _operationManager.StartOperationAsync(
                        "打开项目",
                        async (progress, cancellationToken) =>
                        {
                            progress.Report(new OperationProgress(0, "读取项目文件..."));

                            var projectFile = await _projectFileService.LoadProjectAsync(openFileDialog.FileName);

                            progress.Report(new OperationProgress(50, "恢复项目数据..."));

                            // 恢复项目信息
                            _currentProjectInfo = projectFile.ProjectInfo;
                            _currentProjectPath = openFileDialog.FileName;

                            progress.Report(new OperationProgress(75, "恢复画布状态..."));

                            // 恢复画布
                            _projectFileService.RestoreCanvasFromProjectFile(projectFile, _canvasViewModel);

                            progress.Report(new OperationProgress(100, "加载完成"));
                        });

                    await operationHandle.WaitAsync();

                    UpdateWindowTitle();
                    MessageBox.Show("项目加载成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (OperationCanceledException)
                {
                    MessageBox.Show("打开操作已取消", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开项目失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 检查是否有未保存的更改
        /// </summary>
        private bool HasUnsavedChanges()
        {
            // 简单实现：如果有节点就认为有更改
            // 后续可以实现更精确的更改检测
            return _canvasViewModel.Nodes.Count > 0;
        }

        /// <summary>
        /// 更新窗口标题
        /// </summary>
        private void UpdateWindowTitle()
        {
            var projectName = string.IsNullOrEmpty(_currentProjectInfo.Name) ? "新项目" : _currentProjectInfo.Name;
            var fileName = string.IsNullOrEmpty(_currentProjectPath) ? "" : $" - {Path.GetFileName(_currentProjectPath)}";
            Title = $"ProjectDigitizer - {projectName}{fileName}";
        }

        #endregion

        #region 菜单事件处理

        /// <summary>
        /// 文件菜单 - 新建项目
        /// </summary>
        private void MenuItem_NewProject_Click(object sender, RoutedEventArgs e)
        {
            NewProject();
        }

        /// <summary>
        /// 文件菜单 - 打开项目
        /// </summary>
        private void MenuItem_OpenProject_Click(object sender, RoutedEventArgs e)
        {
            OpenProject();
        }

        /// <summary>
        /// 文件菜单 - 保存项目
        /// </summary>
        private void MenuItem_SaveProject_Click(object sender, RoutedEventArgs e)
        {
            SaveProject();
        }

        /// <summary>
        /// 文件菜单 - 另存为项目
        /// </summary>
        private void MenuItem_SaveProjectAs_Click(object sender, RoutedEventArgs e)
        {
            SaveProjectAs();
        }

        #endregion

        #region 键盘快捷键

        /// <summary>
        /// 添加键盘快捷键
        /// </summary>
        private void AddKeyboardShortcuts()
        {
            // Ctrl+N - 新建项目
            var newCommand = new RoutedCommand();
            newCommand.InputGestures.Add(new KeyGesture(Key.N, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(newCommand, (s, e) => NewProject()));

            // Ctrl+O - 打开项目
            var openCommand = new RoutedCommand();
            openCommand.InputGestures.Add(new KeyGesture(Key.O, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(openCommand, (s, e) => OpenProject()));

            // Ctrl+S - 保存项目
            var saveCommand = new RoutedCommand();
            saveCommand.InputGestures.Add(new KeyGesture(Key.S, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(saveCommand, (s, e) => SaveProject()));

            // Ctrl+Shift+S - 另存为项目
            var saveAsCommand = new RoutedCommand();
            saveAsCommand.InputGestures.Add(new KeyGesture(Key.S, ModifierKeys.Control | ModifierKeys.Shift));
            CommandBindings.Add(new CommandBinding(saveAsCommand, (s, e) => SaveProjectAs()));

            // Delete - 删除选中的节点
            var deleteCommand = new RoutedCommand();
            deleteCommand.InputGestures.Add(new KeyGesture(Key.Delete));
            CommandBindings.Add(new CommandBinding(deleteCommand, (s, e) => DeleteSelectedNodes()));

            // Ctrl+A - 全选节点
            var selectAllCommand = new RoutedCommand();
            selectAllCommand.InputGestures.Add(new KeyGesture(Key.A, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(selectAllCommand, (s, e) => SelectAllNodes()));

            // Escape - 清除选择
            var clearSelectionCommand = new RoutedCommand();
            clearSelectionCommand.InputGestures.Add(new KeyGesture(Key.Escape));
            CommandBindings.Add(new CommandBinding(clearSelectionCommand, (s, e) => _canvasViewModel.ClearSelection()));
        }

        /// <summary>
        /// 删除选中的节点和连接线
        /// </summary>
        private void DeleteSelectedNodes()
        {
            var selectedNodes = _canvasViewModel.SelectedItems.OfType<ModuleNodeViewModel>().ToList();
            var selectedConnections = Canvas.SelectedItems?.OfType<ConnectionViewModel>().ToList() ?? new List<ConnectionViewModel>();

            if (selectedNodes.Count == 0 && selectedConnections.Count == 0)
                return;

            // 构建删除确认消息
            var messageParts = new List<string>();
            if (selectedNodes.Count > 0)
            {
                var nodeMessage = selectedNodes.Count == 1
                    ? $"节点 '{selectedNodes[0].Title}'"
                    : $"{selectedNodes.Count} 个节点";
                messageParts.Add(nodeMessage);
            }

            if (selectedConnections.Count > 0)
            {
                var connectionMessage = selectedConnections.Count == 1
                    ? $"连接线 '{selectedConnections[0].GetConnectionDescription()}'"
                    : $"{selectedConnections.Count} 条连接线";
                messageParts.Add(connectionMessage);
            }

            var message = $"确定要删除 {string.Join(" 和 ", messageParts)} 吗？";

            var result = MessageBox.Show(message, "确认删除",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 删除选中的连接线
                foreach (var connection in selectedConnections)
                {
                    _canvasViewModel.RemoveConnection(connection);
                }

                // 删除选中的节点
                foreach (var node in selectedNodes)
                {
                    _canvasViewModel.RemoveNode(node);
                }
            }
        }

        /// <summary>
        /// 全选节点
        /// </summary>
        private void SelectAllNodes()
        {
            _canvasViewModel.ClearSelection();

            foreach (var node in _canvasViewModel.Nodes)
            {
                _canvasViewModel.SelectedItems.Add(node);
                node.IsSelected = true;
            }

            _canvasViewModel.UpdateSelectionState();
        }

        #endregion

        #region 工具栏拖拽功能

        private bool _isDragging = false;
        private Point _dragStartPoint;
        private TemplateItem? _draggedItem;

        /// <summary>
        /// 模板项鼠标按下事件
        /// </summary>
        private void TemplateItem_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                var border = sender as Border;
                if (border?.DataContext is TemplateItem templateItem)
                {
                    _dragStartPoint = e.GetPosition(border);
                    _draggedItem = templateItem;
                    border.CaptureMouse();
                }
            }
        }

        /// <summary>
        /// 模板项鼠标移动事件
        /// </summary>
        private void TemplateItem_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && _draggedItem != null)
            {
                var border = sender as Border;
                if (border != null)
                {
                    var currentPosition = e.GetPosition(border);
                    var diff = _dragStartPoint - currentPosition;

                    // 检查是否开始拖拽（移动距离超过阈值）
                    if (!_isDragging && (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                                        Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance))
                    {
                        _isDragging = true;

                        // 开始拖拽操作
                        var dragData = new DataObject(typeof(TemplateItem), _draggedItem);
                        DragDrop.DoDragDrop(border, dragData, DragDropEffects.Copy);

                        // 重置拖拽状态
                        _isDragging = false;
                        _draggedItem = null;
                        border.ReleaseMouseCapture();
                    }
                }
            }
        }

        /// <summary>
        /// 模板项鼠标释放事件
        /// </summary>
        private void TemplateItem_MouseUp(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            if (border != null)
            {
                border.ReleaseMouseCapture();

                // 如果没有进行拖拽，则执行点击操作（添加模块）
                if (!_isDragging && _draggedItem != null)
                {
                    _canvasViewModel.AddModuleCommand.Execute(_draggedItem);
                }

                _isDragging = false;
                _draggedItem = null;
            }
        }

        #endregion

        #region 拖拽功能

        /// <summary>
        /// 处理从工具栏拖拽模块到画布
        /// </summary>
        private void Canvas_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(TemplateItem)))
            {
                var templateItem = (TemplateItem)e.Data.GetData(typeof(TemplateItem));
                var position = e.GetPosition(Canvas);

                // 转换屏幕坐标到画布坐标
                var canvasPosition = Canvas.ViewportTransform?.Inverse?.Transform(position) ?? position;

                _canvasViewModel.AddModule(templateItem.ModuleType, canvasPosition);

                e.Handled = true;
            }
        }

        /// <summary>
        /// 允许拖拽到画布
        /// </summary>
        private void Canvas_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(TemplateItem)))
            {
                e.Effects = DragDropEffects.Copy;
                e.Handled = true;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }

        #endregion

        #region 缺失的事件处理程序

        /// <summary>
        /// 画布预览鼠标按下事件
        /// </summary>
        private void Canvas_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {
            // 右键点击显示上下文菜单
            if (e.RightButton == MouseButtonState.Pressed)
            {
                var hitTest = VisualTreeHelper.HitTest(Canvas, e.GetPosition(Canvas));
                if (hitTest?.VisualHit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"右键点击检测到元素: {hitTest.VisualHit.GetType().Name}");

                    // 检查是否点击在连接线上
                    var connectionElement = FindAncestor<Nodify.LineConnection>(hitTest.VisualHit);
                    System.Diagnostics.Debug.WriteLine($"连接线元素: {connectionElement?.GetType().Name ?? "null"}");

                    if (connectionElement?.DataContext is ConnectionViewModel connection)
                    {
                        System.Diagnostics.Debug.WriteLine($"找到连接线: {connection.GetConnectionDescription()}");
                        // 右键点击连接线
                        ShowConnectionContextMenu(connection, e.GetPosition(Canvas));
                        e.Handled = true;
                        return;
                    }
                    else if (connectionElement != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"连接线元素DataContext类型: {connectionElement.DataContext?.GetType().Name ?? "null"}");
                    }

                    // 检查是否点击在节点上
                    var nodeContainer = FindAncestor<Nodify.ItemContainer>(hitTest.VisualHit);
                    if (nodeContainer?.DataContext is ModuleNodeViewModel node)
                    {
                        System.Diagnostics.Debug.WriteLine($"找到节点: {node.Title}");
                        // 右键点击节点
                        ShowNodeContextMenu(node, e.GetPosition(Canvas));
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("右键点击空白区域");
                        // 右键点击空白区域
                        ShowCanvasContextMenu(e.GetPosition(Canvas));
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("HitTest返回null");
                    // 右键点击空白区域
                    ShowCanvasContextMenu(e.GetPosition(Canvas));
                }

                e.Handled = true;
                return;
            }

            // 处理多选逻辑
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                var hitTest = VisualTreeHelper.HitTest(Canvas, e.GetPosition(Canvas));
                Nodify.ItemContainer? nodeContainer = null;

                if (hitTest?.VisualHit != null)
                {
                    nodeContainer = FindAncestor<Nodify.ItemContainer>(hitTest.VisualHit);
                }

                if (nodeContainer?.DataContext is ModuleNodeViewModel node)
                {


                    // 检查是否点击在连接器上
                    var connector = e.OriginalSource as FrameworkElement;
                    var isConnectorClick = IsConnectorElement(connector);

                    // 检查节点是否锁定
                    if (node.IsLocked)
                    {
                        // 锁定的节点：允许选择和连接器交互，但不允许拖拽节点本身
                        if (isConnectorClick)
                        {
                            // 如果点击的是连接器，让Nodify处理连接逻辑
                            return;
                        }

                        // 如果点击的是节点本身，处理选择逻辑
                        if (Keyboard.Modifiers.HasFlag(ModifierKeys.Control))
                        {
                            _canvasViewModel.ToggleNodeSelection(node);
                        }
                        else
                        {
                            _canvasViewModel.SelectSingleNode(node);
                        }
                        e.Handled = true; // 阻止节点拖拽，但不影响连接器交互
                    }
                    else
                    {
                        // 未锁定的节点：正常处理
                        if (Keyboard.Modifiers.HasFlag(ModifierKeys.Control))
                        {
                            // Ctrl+点击：切换选择状态
                            _canvasViewModel.ToggleNodeSelection(node);
                            e.Handled = true;
                        }
                        else if (!node.IsSelected)
                        {
                            // 普通点击未选中的节点：单选
                            _canvasViewModel.SelectSingleNode(node);
                        }
                        // 如果点击已选中的节点且没有按Ctrl，让Nodify处理拖拽
                    }
                }
                else
                {
                    // 点击空白区域：清除选择（除非按住Ctrl进行框选）
                    if (!Keyboard.Modifiers.HasFlag(ModifierKeys.Control))
                    {
                        _canvasViewModel.ClearSelection();
                    }
                }
            }
        }

        /// <summary>
        /// 查找父级元素
        /// </summary>
        private T? FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parent = VisualTreeHelper.GetParent(child);
            if (parent == null) return null;

            if (parent is T parentT)
                return parentT;

            return FindParent<T>(parent);
        }

        /// <summary>
        /// 项目容器选中事件
        /// </summary>
        private void ItemContainer_Selected(object sender, RoutedEventArgs e)
        {
            // 处理项目容器选中事件
        }




        /// <summary>
        /// 项目容器鼠标按下事件
        /// </summary>
        private void ItemContainer_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // 处理项目容器鼠标按下事件
        }

        /// <summary>
        /// 项目容器鼠标双击事件
        /// </summary>
        private void ItemContainer_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // 处理项目容器鼠标双击事件
        }

        /// <summary>
        /// 连接线右键点击事件
        /// </summary>
        private void Connection_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Nodify.LineConnection lineConnection && lineConnection.DataContext is ConnectionViewModel connection)
            {
                System.Diagnostics.Debug.WriteLine($"连接线右键点击: {connection.GetConnectionDescription()}");

                // 获取鼠标位置
                var position = e.GetPosition(Canvas);

                // 显示连接线上下文菜单
                ShowConnectionContextMenu(connection, position);

                // 阻止事件冒泡
                e.Handled = true;
            }
        }



        /// <summary>
        /// 显示节点上下文菜单
        /// </summary>
        private void ShowNodeContextMenu(ModuleNodeViewModel node, Point position)
        {
            var contextMenu = new ContextMenu();

            // 删除节点
            var deleteItem = new MenuItem
            {
                Header = "删除节点",
                Icon = new System.Windows.Controls.TextBlock { Text = "🗑️", FontSize = 14 }
            };
            deleteItem.Click += (s, e) => _canvasViewModel.RemoveNode(node);
            contextMenu.Items.Add(deleteItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 复制节点
            var copyItem = new MenuItem
            {
                Header = "复制节点",
                Icon = new System.Windows.Controls.TextBlock { Text = "📋", FontSize = 14 }
            };
            copyItem.Click += (s, e) => CopyNode(node);
            contextMenu.Items.Add(copyItem);

            // 锁定/解锁位置
            var lockItem = new MenuItem
            {
                Header = node.IsLocked ? "解锁位置" : "锁定位置",
                Icon = new System.Windows.Controls.TextBlock { Text = node.IsLocked ? "🔓" : "🔒", FontSize = 14 }
            };
            lockItem.Click += (s, e) => ToggleNodeLock(node);
            contextMenu.Items.Add(lockItem);

            // 启用/禁用节点
            var enableItem = new MenuItem
            {
                Header = node.IsEnabled ? "禁用节点" : "启用节点",
                Icon = new System.Windows.Controls.TextBlock { Text = node.IsEnabled ? "⏸️" : "▶️", FontSize = 14 }
            };
            enableItem.Click += (s, e) => ToggleNodeEnabled(node);
            contextMenu.Items.Add(enableItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 属性
            var propertiesItem = new MenuItem
            {
                Header = "属性",
                Icon = new System.Windows.Controls.TextBlock { Text = "⚙️", FontSize = 14 }
            };
            propertiesItem.Click += (s, e) => ShowNodeProperties(node);
            contextMenu.Items.Add(propertiesItem);

            // 显示菜单
            contextMenu.PlacementTarget = Canvas;
            contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.RelativePoint;
            contextMenu.HorizontalOffset = position.X;
            contextMenu.VerticalOffset = position.Y;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// 显示连接线上下文菜单
        /// </summary>
        private void ShowConnectionContextMenu(ConnectionViewModel connection, Point position)
        {
            var contextMenu = new ContextMenu();

            // 启用/禁用连接
            var enableItem = new MenuItem
            {
                Header = connection.IsEnabled ? "禁用连接" : "启用连接",
                Icon = new System.Windows.Controls.TextBlock { Text = connection.IsEnabled ? "⏸️" : "▶️", FontSize = 14 }
            };
            enableItem.Click += (s, e) => ToggleConnectionEnabled(connection);
            contextMenu.Items.Add(enableItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 删除连接
            var deleteItem = new MenuItem
            {
                Header = "删除连接",
                Icon = new System.Windows.Controls.TextBlock { Text = "🗑️", FontSize = 14 }
            };
            deleteItem.Click += (s, e) => DeleteConnection(connection);
            contextMenu.Items.Add(deleteItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 查看连接属性
            var propertiesItem = new MenuItem
            {
                Header = "连接属性",
                Icon = new System.Windows.Controls.TextBlock { Text = "ℹ️", FontSize = 14 }
            };
            propertiesItem.Click += (s, e) => ShowConnectionProperties(connection);
            contextMenu.Items.Add(propertiesItem);

            // 显示菜单
            contextMenu.PlacementTarget = Canvas;
            contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.RelativePoint;
            contextMenu.HorizontalOffset = position.X;
            contextMenu.VerticalOffset = position.Y;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// 打开插件管理窗口
        /// </summary>
        private void OpenPluginManager_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var win = App.GetService<PluginManagerWindow>();
                win.Owner = this;
                win.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开插件管理失败: {ex.Message}");
                MessageBox.Show($"打开插件管理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示画布上下文菜单
        /// </summary>
        private void ShowCanvasContextMenu(Point position)
        {
            var contextMenu = new ContextMenu();

            // 粘贴节点（如果有复制的节点）
            var pasteItem = new MenuItem
            {
                Header = "粘贴",
                Icon = new System.Windows.Controls.TextBlock { Text = "📋", FontSize = 14 },
                IsEnabled = false // TODO: 实现复制粘贴功能后启用
            };
            contextMenu.Items.Add(pasteItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 全选
            var selectAllItem = new MenuItem
            {
                Header = "全选",
                Icon = new System.Windows.Controls.TextBlock { Text = "🔲", FontSize = 14 }
            };
            selectAllItem.Click += (s, e) => SelectAllNodes();
            contextMenu.Items.Add(selectAllItem);

            // 清除选择
            var clearSelectionItem = new MenuItem
            {
                Header = "清除选择",
                Icon = new System.Windows.Controls.TextBlock { Text = "❌", FontSize = 14 }
            };
            clearSelectionItem.Click += (s, e) => _canvasViewModel.ClearSelection();
            contextMenu.Items.Add(clearSelectionItem);

            // 分隔符
            contextMenu.Items.Add(new Separator());

            // 自动布局
            var autoLayoutItem = new MenuItem
            {
                Header = "自动布局",
                Icon = new System.Windows.Controls.TextBlock { Text = "📐", FontSize = 14 }
            };
            autoLayoutItem.Click += async (s, e) =>
            {
                System.Diagnostics.Debug.WriteLine("=== 右键菜单自动布局被点击 ===");

                try
                {
                    System.Diagnostics.Debug.WriteLine("开始执行右键菜单自动布局...");
                    await _canvasViewModel.ApplyAutoLayoutAsync();
                    System.Diagnostics.Debug.WriteLine("右键菜单自动布局执行完成");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"右键菜单自动布局失败: {ex.Message}");
                }
            };
            contextMenu.Items.Add(autoLayoutItem);

            // 显示菜单
            contextMenu.PlacementTarget = Canvas;
            contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.RelativePoint;
            contextMenu.HorizontalOffset = position.X;
            contextMenu.VerticalOffset = position.Y;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// 复制节点（TODO: 实现完整的复制粘贴功能）
        /// </summary>
        private void CopyNode(ModuleNodeViewModel node)
        {
            // TODO: 实现节点复制功能
            MessageBox.Show("复制功能将在后续版本中实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 切换节点锁定状态
        /// </summary>
        private void ToggleNodeLock(ModuleNodeViewModel node)
        {
            node.IsLocked = !node.IsLocked;
        }

        /// <summary>
        /// 切换节点启用状态
        /// </summary>
        private void ToggleNodeEnabled(ModuleNodeViewModel node)
        {
            node.IsEnabled = !node.IsEnabled;
        }

        /// <summary>
        /// 切换连接线启用状态
        /// </summary>
        private void ToggleConnectionEnabled(ConnectionViewModel connection)
        {
            _canvasViewModel.ToggleConnectionEnabled(connection);
        }

        /// <summary>
        /// 删除连接线
        /// </summary>
        private void DeleteConnection(ConnectionViewModel connection)
        {
            var result = MessageBox.Show(
                $"确定要删除连接线 \"{connection.GetConnectionDescription()}\" 吗？",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _canvasViewModel.RemoveConnection(connection);
            }
        }

        /// <summary>
        /// 显示连接线属性
        /// </summary>
        private void ShowConnectionProperties(ConnectionViewModel connection)
        {
            var sourceNode = (connection.Source?.Node as ModuleNodeViewModel)?.Title ?? "未知节点";
            var targetNode = (connection.Target?.Node as ModuleNodeViewModel)?.Title ?? "未知节点";
            var sourceConnector = connection.Source?.Title ?? "未知连接器";
            var targetConnector = connection.Target?.Title ?? "未知连接器";
            var status = connection.IsEnabled ? "启用" : "禁用";

            var message = $"连接线属性：\n\n" +
                         $"源节点：{sourceNode}\n" +
                         $"源连接器：{sourceConnector}\n" +
                         $"目标节点：{targetNode}\n" +
                         $"目标连接器：{targetConnector}\n" +
                         $"状态：{status}\n" +
                         $"连接ID：{connection.Id}";

            MessageBox.Show(message, "连接线属性", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示节点属性
        /// </summary>
        private void ShowNodeProperties(ModuleNodeViewModel node)
        {
            _canvasViewModel.SelectSingleNode(node);
        }

        #endregion

        #region 缩放控制器事件处理

        /// <summary>
        /// 缩放变化事件处理
        /// </summary>
        private void ZoomControl_ZoomChanged(object sender, Controls.Canvas.ZoomChangedEventArgs e)
        {
            if (_isUpdatingViewport)
            {
                // 来自我们主动设置的同步，不再回写，避免循环
                return;
            }

            if (Canvas != null)
            {
                _isUpdatingViewport = true;
                try
                {
                    Canvas.ViewportZoom = e.ZoomLevel;
                }
                finally
                {
                    _isUpdatingViewport = false;
                }
            }
        }

        /// <summary>
        /// 重置缩放事件处理
        /// </summary>
        private void ZoomControl_ResetZoom(object sender, EventArgs e)
        {
            if (Canvas != null)
            {
                _isUpdatingViewport = true;
                try
                {
                    Canvas.ViewportZoom = 1.0;
                    Canvas.ViewportLocation = new Point(0, 0);

                    if (ZoomControl != null)
                    {
                        ZoomControl.SetZoomLevel(Canvas.ViewportZoom);
                    }
                }
                finally
                {
                    _isUpdatingViewport = false;
                }
            }
        }

        /// <summary>
        /// 适应画布事件处理
        /// </summary>
        private void ZoomControl_FitToCanvas(object sender, EventArgs e)
        {
            if (Canvas != null)
            {
                _isUpdatingViewport = true;
                try
                {
                    Canvas.FitToScreen();
                    if (ZoomControl != null)
                    {
                        ZoomControl.SetZoomLevel(Canvas.ViewportZoom);
                    }
                }
                finally
                {
                    _isUpdatingViewport = false;
                }
            }
        }

        #endregion

        #region Telerik模板设计器

        /// <summary>
        /// 打开Telerik模板设计器
        /// </summary>
        private void OpenTelerikTemplateDesigner_Click(object sender, RoutedEventArgs e)
        {
            // try
            // {
            //     var telerikDesigner = new Windows.TelerikTemplateDesignerWindow();
            //     telerikDesigner.Show();
            // }
            // catch (Exception ex)
            // {
            //     MessageBox.Show($"打开Telerik模板设计器失败: {ex.Message}", "错误",
            //         MessageBoxButton.OK, MessageBoxImage.Error);
            // }
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private async void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 检查是否有未保存的更改
                if (HasUnsavedChanges())
                {
                    var result = MessageBox.Show("当前项目有未保存的更改，是否保存？", "确认",
                        MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 取消关闭，先保存
                        e.Cancel = true;
                        SaveProject();
                        return;
                    }
                    else if (result == MessageBoxResult.Cancel)
                    {
                        // 取消关闭
                        e.Cancel = true;
                        return;
                    }
                }

                // 检查是否有活动的异步操作
                var activeOperations = _operationManager.GetActiveOperations().ToList();
                if (activeOperations.Any())
                {
                    var result = MessageBox.Show($"当前有 {activeOperations.Count} 个操作正在进行中，是否取消这些操作并退出？",
                        "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 取消关闭，等待操作完成
                        e.Cancel = true;

                        try
                        {
                            await _operationManager.CancelAllOperationsAsync();
                            // 操作取消完成后，重新关闭窗口
                            Close();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"取消操作时发生错误：{ex.Message}", "错误",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        // 取消关闭
                        e.Cancel = true;
                    }
                }
                else
                {
                    // 清理资源
                    _progressIndicator?.Dispose();
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止关闭
                System.Diagnostics.Debug.WriteLine($"窗口关闭时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 连接线样式管理

        /// <summary>
        /// 更新连接线样式
        /// </summary>
        private void UpdateConnectionLineStyle()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] UpdateConnectionLineStyle: {_canvasViewModel.CurrentConnectionLineStyle}");
                System.Diagnostics.Debug.WriteLine($"[DEBUG] 连接线样式更新成功: {_canvasViewModel.CurrentConnectionLineStyle.GetDisplayName()}");

                // 更新模板选择器的当前样式
                var connectionTemplateSelector = this.FindResource("ConnectionTemplateSelector") as ConnectionTemplateSelector;
                if (connectionTemplateSelector != null)
                {
                    connectionTemplateSelector.UpdateStyle(_canvasViewModel.CurrentConnectionLineStyle);
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] 模板选择器样式已更新");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[WARNING] 无法找到ConnectionTemplateSelector资源");
                }

                // 强制刷新连接线显示
                RefreshConnectionDisplay();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 更新连接线样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新连接线可见性
        /// </summary>
        private void UpdateConnectionLineVisibility()
        {
            try
            {
                var currentStyle = _canvasViewModel.CurrentConnectionLineStyle;
                System.Diagnostics.Debug.WriteLine($"[DEBUG] 更新连接线可见性: {currentStyle}");

                // 遍历所有连接线并更新其样式
                foreach (var connection in _canvasViewModel.Connections)
                {
                    UpdateSingleConnectionStyle(connection, currentStyle);
                }

                System.Diagnostics.Debug.WriteLine($"[DEBUG] 连接线可见性更新完成，共处理 {_canvasViewModel.Connections.Count} 个连接");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 更新连接线可见性失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新单个连接的样式
        /// </summary>
        private void UpdateSingleConnectionStyle(ConnectionViewModel connection, ConnectionLineStyle style)
        {
            try
            {
                // 触发连接线重新渲染 - 使用反射调用protected方法
                var onPropertyChangedMethod = typeof(ConnectionViewModel).GetMethod("OnPropertyChanged",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (onPropertyChangedMethod != null)
                {
                    onPropertyChangedMethod.Invoke(connection, new object[] { nameof(connection.ConnectionColor) });
                    onPropertyChangedMethod.Invoke(connection, new object[] { nameof(connection.Source) });
                    onPropertyChangedMethod.Invoke(connection, new object[] { nameof(connection.Target) });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 更新单个连接样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新连接线显示
        /// </summary>
        private void RefreshConnectionDisplay()
        {
            try
            {
                // 方法1: 通过InvalidateVisual强制重绘
                Canvas.InvalidateVisual();

                // 方法2: 强制重新选择模板 - 临时清空并重新设置连接线集合
                var connections = _canvasViewModel.Connections.ToList();
                _canvasViewModel.Connections.Clear();

                // 稍微延迟后重新添加连接，确保UI有时间更新
                System.Windows.Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    foreach (var connection in connections)
                    {
                        _canvasViewModel.Connections.Add(connection);
                    }
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] 重新添加了 {connections.Count} 个连接");
                }), System.Windows.Threading.DispatcherPriority.Render);

                System.Diagnostics.Debug.WriteLine("[DEBUG] 连接线显示刷新完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 刷新连接线显示失败: {ex.Message}");
            }
        }

        #endregion

        #region 快捷动作与主题/反馈
        private void QuickFitButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomControl_FitToCanvas(sender, EventArgs.Empty);
        }

        private void ThemeToggle_Checked(object sender, RoutedEventArgs e)
        {
            try { ApplicationThemeManager.Apply(ApplicationTheme.Dark); ThemeResourceService.ApplyDark(true); } catch { }
        }

        private void ThemeToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            try { ApplicationThemeManager.Apply(ApplicationTheme.Light); ThemeResourceService.ApplyDark(false); } catch { }
        }
        #endregion
    }
}

