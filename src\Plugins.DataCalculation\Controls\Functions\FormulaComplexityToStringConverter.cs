using System;
using System.Globalization;
using System.Windows.Data;

using Plugins.DataCalculation.Models;

namespace Plugins.DataCalculation.Controls.Functions
{
    internal sealed class FormulaComplexityToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is FormulaComplexity fc)
            {
                return fc switch
                {
                    FormulaComplexity.Simple => "简单",
                    FormulaComplexity.Medium => "中等",
                    FormulaComplexity.Complex => "复杂",
                    FormulaComplexity.Advanced => "高级",
                    _ => fc.ToString()
                };
            }
            if (value is string s)
            {
                // Fallback for string inputs
                return s switch
                {
                    "Simple" => "简单",
                    "Medium" => "中等",
                    "Complex" => "复杂",
                    "Advanced" => "高级",
                    _ => s
                };
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
            => throw new NotSupportedException();
    }
}

