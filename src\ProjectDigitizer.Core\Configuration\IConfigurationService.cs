using Microsoft.Extensions.Configuration;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取应用程序设置
        /// </summary>
        /// <returns>应用程序设置</returns>
        ApplicationSettings GetApplicationSettings();

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="key">配置键</param>
        /// <returns>配置值</returns>
        T? GetValue<T>(string key);

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue);

        /// <summary>
        /// 获取配置节
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <returns>配置对象</returns>
        T? GetSection<T>(string sectionName) where T : class, new();

        /// <summary>
        /// 绑定配置节到对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="sectionName">节名称</param>
        /// <param name="instance">要绑定的对象实例</param>
        void BindSection<T>(string sectionName, T instance) where T : class;

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult ValidateConfiguration();

        /// <summary>
        /// 重新加载配置
        /// </summary>
        void ReloadConfiguration();

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        /// <param name="name">连接字符串名称</param>
        /// <returns>连接字符串</returns>
        string? GetConnectionString(string name);

        /// <summary>
        /// 配置更改事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
    }

    /// <summary>
    /// 配置更改事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 更改的配置键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 旧值
        /// </summary>
        public object? OldValue { get; set; }

        /// <summary>
        /// 新值
        /// </summary>
        public object? NewValue { get; set; }

        /// <summary>
        /// 更改时间
        /// </summary>
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    }
}
