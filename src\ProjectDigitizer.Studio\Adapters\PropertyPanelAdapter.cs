using System;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Interfaces.UI;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Adapters
{
    /// <summary>
    /// 属性面板解析适配器：
    /// - 优先使用插件实现的 IPropertyPanelProvider（无需依赖 Studio 类型）
    /// - 其次按约定键（PropertyPanel.ModuleType.{ModuleType}）从资源中查找模板
    /// - 最后回退到类别通用模板（由调用方决定）
    /// </summary>
    public static class PropertyPanelAdapter
    {
        public static FrameworkElement? Resolve(ModuleType moduleType, ModuleNodeViewModel node)
        {
            // 1) 尝试插件 Provider
            var element = TryResolveFromProviders(moduleType, node);
            if (element != null)
                return element;

            // 2) 约定键模板（由插件通过资源提供）
            var key = $"PropertyPanel.ModuleType.{moduleType}";
            if (System.Windows.Application.Current?.Resources.Contains(key) == true)
            {
                if (System.Windows.Application.Current.Resources[key] is DataTemplate dt)
                {
                    var content = dt.LoadContent();
                    if (content is FrameworkElement fe)
                    {
                        fe.DataContext = node;
                        InjectContextAndServices(fe, node);
                        return fe;
                    }
                }
            }

            return null;
        }

        private static FrameworkElement? TryResolveFromProviders(ModuleType moduleType, ModuleNodeViewModel node)
        {
            try
            {
                var catalog = App.GetOptionalService<IPluginCatalog>();
                if (catalog?.Assemblies is not { Count: > 0 })
                    return null;

                var providerType = typeof(IPropertyPanelProvider);
                var providers = catalog.Assemblies
                    .SelectMany(a =>
                    {
                        try { return a.GetTypes(); } catch { return Array.Empty<Type>(); }
                    })
                    .Where(t => providerType.IsAssignableFrom(t) && t is { IsAbstract: false, IsInterface: false })
                    .Select(t => (IPropertyPanelProvider?)Activator.CreateInstance(t))
                    .Where(p => p != null && p.TargetModuleType == moduleType)
                    .ToList();

                foreach (var p in providers!)
                {
                    if (!string.IsNullOrWhiteSpace(p!.ResourceKey)
                        && System.Windows.Application.Current?.Resources.Contains(p.ResourceKey) == true)
                    {
                        var template = System.Windows.Application.Current!.Resources[p.ResourceKey] as DataTemplate;
                        if (template != null)
                        {
                            var content = template.LoadContent();
                            if (content is FrameworkElement fe)
                            {
                                fe.DataContext = node;
                                InjectContextAndServices(fe, node);
                                return fe;
                            }
                        }
                    }
                }
            }
            catch
            {
            }

            return null;
        }

        private static void InjectContextAndServices(FrameworkElement fe, ModuleNodeViewModel node)
        {
            try
            {
                // NodeContext 注入
                var nodeContext = new NodeContextAdapter(node);
                var propNc = fe.GetType().GetProperty("NodeContext", BindingFlags.Public | BindingFlags.Instance);
                if (propNc != null && propNc.PropertyType.IsAssignableFrom(typeof(ProjectDigitizer.Application.Interfaces.UI.INodeContext)))
                {
                    propNc.SetValue(fe, nodeContext);
                }

                // GraphQuery 注入
                var graphQuery = App.GetOptionalService<ProjectDigitizer.Application.Interfaces.UI.INodeGraphQuery>();
                var propGq = fe.GetType().GetProperty("GraphQuery", BindingFlags.Public | BindingFlags.Instance);
                if (graphQuery != null && propGq != null && propGq.PropertyType.IsInstanceOfType(graphQuery))
                {
                    propGq.SetValue(fe, graphQuery);
                }
            }
            catch
            {
            }
        }
    }
}
