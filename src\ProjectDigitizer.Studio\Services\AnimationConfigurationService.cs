using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Infrastructure.UI.Animation
{
    /// <summary>
    /// 动画配置服务
    /// 管理全局动画设置和性能优化选项
    /// </summary>
    public class AnimationConfigurationService : INotifyPropertyChanged
    {
        #region 单例模式

        private static readonly Lazy<AnimationConfigurationService> _instance =
            new Lazy<AnimationConfigurationService>(() => new AnimationConfigurationService());

        public static AnimationConfigurationService Instance => _instance.Value;

        private AnimationConfigurationService()
        {
            LoadDefaultSettings();
        }

        #endregion

        #region 私有字段

        private bool _isAnimationEnabled = true;
        private bool _isFlowAnimationEnabled = true;
        private bool _isConnectionAnimationEnabled = true;
        private bool _isPulseAnimationEnabled = true;
        private double _animationSpeed = 1.0;
        private int _maxConcurrentAnimations = 50;
        private bool _isPerformanceModeEnabled = false;
        private bool _isReducedMotionEnabled = false;

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否启用动画
        /// </summary>
        public bool IsAnimationEnabled
        {
            get => _isAnimationEnabled;
            set => SetProperty(ref _isAnimationEnabled, value);
        }

        /// <summary>
        /// 是否启用连接线流动动画
        /// </summary>
        public bool IsFlowAnimationEnabled
        {
            get => _isFlowAnimationEnabled;
            set => SetProperty(ref _isFlowAnimationEnabled, value);
        }

        /// <summary>
        /// 是否启用连接动画
        /// </summary>
        public bool IsConnectionAnimationEnabled
        {
            get => _isConnectionAnimationEnabled;
            set => SetProperty(ref _isConnectionAnimationEnabled, value);
        }

        /// <summary>
        /// 是否启用脉冲动画
        /// </summary>
        public bool IsPulseAnimationEnabled
        {
            get => _isPulseAnimationEnabled;
            set => SetProperty(ref _isPulseAnimationEnabled, value);
        }

        /// <summary>
        /// 动画速度倍数 (0.5 - 2.0)
        /// </summary>
        public double AnimationSpeed
        {
            get => _animationSpeed;
            set => SetProperty(ref _animationSpeed, Math.Clamp(value, 0.5, 2.0));
        }

        /// <summary>
        /// 最大并发动画数量
        /// </summary>
        public int MaxConcurrentAnimations
        {
            get => _maxConcurrentAnimations;
            set => SetProperty(ref _maxConcurrentAnimations, Math.Max(1, value));
        }

        /// <summary>
        /// 是否启用性能模式（减少动画效果）
        /// </summary>
        public bool IsPerformanceModeEnabled
        {
            get => _isPerformanceModeEnabled;
            set
            {
                if (SetProperty(ref _isPerformanceModeEnabled, value))
                {
                    ApplyPerformanceMode(value);
                }
            }
        }

        /// <summary>
        /// 是否启用减少动画模式（无障碍功能）
        /// </summary>
        public bool IsReducedMotionEnabled
        {
            get => _isReducedMotionEnabled;
            set
            {
                if (SetProperty(ref _isReducedMotionEnabled, value))
                {
                    ApplyReducedMotionMode(value);
                }
            }
        }

        #endregion

        #region 事件

        public event PropertyChangedEventHandler? PropertyChanged;

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载默认设置
        /// </summary>
        private void LoadDefaultSettings()
        {
            // 可以从配置文件或注册表加载设置
            // 这里使用默认值
        }

        /// <summary>
        /// 应用性能模式
        /// </summary>
        private void ApplyPerformanceMode(bool enabled)
        {
            if (enabled)
            {
                // 性能模式：减少动画效果
                MaxConcurrentAnimations = 20;
                AnimationSpeed = 1.5; // 加快动画速度
            }
            else
            {
                // 恢复正常模式
                MaxConcurrentAnimations = 50;
                AnimationSpeed = 1.0;
            }
        }

        /// <summary>
        /// 应用减少动画模式
        /// </summary>
        private void ApplyReducedMotionMode(bool enabled)
        {
            if (enabled)
            {
                // 减少动画模式：禁用大部分动画
                IsFlowAnimationEnabled = false;
                IsPulseAnimationEnabled = false;
                AnimationSpeed = 2.0; // 加快剩余动画
            }
            else
            {
                // 恢复正常模式
                IsFlowAnimationEnabled = true;
                IsPulseAnimationEnabled = true;
                AnimationSpeed = 1.0;
            }
        }

        /// <summary>
        /// 设置属性值并触发通知
        /// </summary>
        private bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;

            field = value;
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            return true;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 检查是否应该播放指定类型的动画
        /// </summary>
        public bool ShouldPlayAnimation(AnimationType animationType)
        {
            if (!IsAnimationEnabled) return false;

            return animationType switch
            {
                AnimationType.Flow => IsFlowAnimationEnabled,
                AnimationType.Connection => IsConnectionAnimationEnabled,
                AnimationType.Pulse => IsPulseAnimationEnabled,
                _ => true
            };
        }

        /// <summary>
        /// 获取调整后的动画持续时间
        /// </summary>
        public TimeSpan GetAdjustedDuration(TimeSpan originalDuration)
        {
            if (!IsAnimationEnabled) return TimeSpan.Zero;

            var adjustedTicks = (long)(originalDuration.Ticks / AnimationSpeed);
            return new TimeSpan(adjustedTicks);
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        public void SaveSettings()
        {
            // 可以保存到配置文件或注册表
            // 这里暂时不实现
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            IsAnimationEnabled = true;
            IsFlowAnimationEnabled = true;
            IsConnectionAnimationEnabled = true;
            IsPulseAnimationEnabled = true;
            AnimationSpeed = 1.0;
            MaxConcurrentAnimations = 50;
            IsPerformanceModeEnabled = false;
            IsReducedMotionEnabled = false;
        }

        #endregion
    }

    /// <summary>
    /// 动画类型枚举
    /// </summary>
    public enum AnimationType
    {
        Flow,
        Connection,
        Pulse,
        Hover,
        Selection
    }
}
