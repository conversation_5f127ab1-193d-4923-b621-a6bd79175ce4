<ui:FluentWindow
    Height="520"
    MinHeight="420"
    MinWidth="640"
    ResizeMode="CanResizeWithGrip"
    ShowInTaskbar="False"
    Title="全部函数"
    UseLayoutRounding="True"
    Width="820"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d"
    x:Class="Plugins.DataCalculation.Controls.Functions.AllFunctionsWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:local="clr-namespace:Plugins.DataCalculation.Controls.Functions"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Grid>
        <Grid.RowDefinitions>
            <!--  标题栏  -->
            <RowDefinition Height="Auto" />
            <!--  内容 / 操作区  -->
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  顶部标题栏（WPF UI）  -->
        <ui:TitleBar Grid.Row="0" Title="全部函数" />

        <!--  内容区：查询 + 列表（上） / 详情（下，可调整）  -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- 顶部查询区（与列表在同一列） -->
            <Border
                Grid.Row="0"
                Background="{DynamicResource Brush.SurfaceVariant}"
                BorderBrush="{DynamicResource Brush.Divider}"
                BorderThickness="1"
                CornerRadius="4"
                Margin="0,0,0,8"
                Padding="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="8" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- 左：类别筛选（移除图标） -->
                    <ComboBox
                        Grid.Column="0"
                        SelectionChanged="OnFilterChanged"
                        VerticalAlignment="Center"
                        Width="140"
                        x:Name="CategoryComboBox">
                        <ComboBoxItem Content="全部类别" IsSelected="True" />
                        <ComboBoxItem Content="逻辑" />
                        <ComboBoxItem Content="数学" />
                        <ComboBoxItem Content="文本" />
                        <ComboBoxItem Content="统计" />
                        <ComboBoxItem Content="其他" />
                    </ComboBox>

                    <!-- 右：搜索框（无图标，控件自带边框，拉伸填满） -->
                    <ui:TextBox
                        Grid.Column="2"
                        Margin="0"
                        TextChanged="OnFilterChanged"
                        ToolTip="搜索函数名/描述/示例"
                        VerticalAlignment="Center"
                        MinWidth="220"
                        HorizontalAlignment="Stretch"
                        x:Name="SearchTextBox" />
                </Grid>
            </Border>

            <!-- 列表区（可与下方详情通过分隔条调整） -->
            <DataGrid
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                Grid.Row="1"
                IsReadOnly="True"
                Margin="0"
                MouseDoubleClick="FunctionsGrid_MouseDoubleClick"
                SelectionChanged="FunctionsGrid_SelectionChanged"
                SelectionMode="Extended"
                x:Name="FunctionsGrid">
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Binding="{Binding Name}"
                        Header="名称"
                        Width="120" />
                    <DataGridTextColumn
                        Binding="{Binding ParmRange}"
                        Header="参数"
                        Width="90" />
                    <DataGridTextColumn
                        Binding="{Binding CategoryZh}"
                        Header="类别"
                        Width="80" />
                    <DataGridTextColumn
                        Binding="{Binding DescriptionZh}"
                        Header="说明"
                        Width="*" />
                    <DataGridTextColumn
                        Binding="{Binding ExampleZh}"
                        Header="示例"
                        Width="180" />
                </DataGrid.Columns>
            </DataGrid>

            <!--  水平分隔条  -->
            <GridSplitter
                Background="{DynamicResource Brush.SurfaceVariant}"
                Grid.Row="2"
                HorizontalAlignment="Stretch"
                Height="6"
                ShowsPreview="True"
                VerticalAlignment="Center" />

            <!--  详情区：签名/参数（底部）  -->
            <Border
                BorderBrush="{DynamicResource Brush.Divider}"
                BorderThickness="1"
                CornerRadius="4"
                Grid.Row="3"
                Margin="0"
                Padding="8">
                <StackPanel>
                    <TextBlock FontWeight="SemiBold" Text="签名" />
                    <TextBlock
                        Margin="0,2,0,6"
                        TextWrapping="Wrap"
                        x:Name="SigTextBlock" />
                    <TextBlock FontWeight="SemiBold" Text="参数" />
                    <TextBlock
                        Margin="0,2,0,0"
                        TextWrapping="Wrap"
                        x:Name="ParamsTextBlock" />
                </StackPanel>
            </Border>
        </Grid>

        <!--  操作区  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Right"
            Margin="10"
            Orientation="Horizontal">
            <ui:Button
                Click="InsertToEditorButton_Click"
                Margin="0,0,8,0"
                MinWidth="150"
                Padding="14,8,14,8">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="Play"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock
                            Text="插入到编辑器"
                            TextTrimming="None"
                            TextWrapping="NoWrap" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
            <ui:Button
                Click="CopyExampleButton_Click"
                Margin="0,0,8,0"
                Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="ContentCopy"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock Text="复制示例" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
            <ui:Button
                Click="CopyNamesButton_Click"
                Margin="0,0,8,0"
                Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="ContentCopy"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock Text="复制名称" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
            <ui:Button
                Click="CloseButton_Click"
                IsDefault="True"
                Width="80">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <ipack:PackIconMaterial
                            Height="14"
                            Kind="Close"
                            Margin="0,0,4,0"
                            Width="14" />
                        <TextBlock Text="关闭" />
                    </StackPanel>
                </Button.Content>
            </ui:Button>
        </StackPanel>
    </Grid>
</ui:FluentWindow>
