using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Selectors
{
    /// <summary>
    /// 连接线模板选择器
    /// 根据连接线样式动态选择对应的DataTemplate
    /// </summary>
    public class ConnectionTemplateSelector : DataTemplateSelector
    {
        /// <summary>
        /// 直线/正交线连接模板
        /// 使用 nodify:LineConnection
        /// </summary>
        public DataTemplate? LineConnectionTemplate { get; set; }

        /// <summary>
        /// 贝塞尔曲线连接模板
        /// 使用 nodify:Connection
        /// </summary>
        public DataTemplate? BezierConnectionTemplate { get; set; }

        /// <summary>
        /// 阶梯线连接模板
        /// 使用 nodify:StepConnection
        /// </summary>
        public DataTemplate? StepConnectionTemplate { get; set; }

        /// <summary>
        /// 电路线连接模板
        /// 使用 nodify:CircuitConnection
        /// </summary>
        public DataTemplate? CircuitConnectionTemplate { get; set; }

        /// <summary>
        /// 默认连接线模板
        /// 当无法确定样式时使用
        /// </summary>
        public DataTemplate? DefaultTemplate { get; set; }

        /// <summary>
        /// 当前连接线样式
        /// 由CanvasViewModel设置，用于确定使用哪种模板
        /// </summary>
        public ConnectionLineStyle CurrentStyle { get; set; } = ConnectionLineStyle.Bezier;

        /// <summary>
        /// 选择合适的连接线模板
        /// </summary>
        /// <param name="item">数据项，通常是ConnectionViewModel</param>
        /// <param name="container">容器对象</param>
        /// <returns>选中的DataTemplate</returns>
        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            // 调试信息
            System.Diagnostics.Debug.WriteLine($"[DEBUG] ConnectionTemplateSelector called");
            System.Diagnostics.Debug.WriteLine($"[DEBUG] CurrentStyle = {CurrentStyle}");
            System.Diagnostics.Debug.WriteLine($"[DEBUG] Item type = {item?.GetType().Name}");

            // 验证输入项是否为ConnectionViewModel
            if (item is ConnectionViewModel connectionViewModel)
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Connection from {connectionViewModel.Source?.Title} to {connectionViewModel.Target?.Title}");

                // 根据当前样式选择对应的模板
                var selectedTemplate = CurrentStyle switch
                {
                    ConnectionLineStyle.Line => LineConnectionTemplate ?? DefaultTemplate,
                    ConnectionLineStyle.Bezier => BezierConnectionTemplate ?? DefaultTemplate,
                    ConnectionLineStyle.Step => StepConnectionTemplate ?? DefaultTemplate,
                    ConnectionLineStyle.Circuit => CircuitConnectionTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };

                System.Diagnostics.Debug.WriteLine($"[DEBUG] Selected template: {selectedTemplate?.GetType().Name ?? "null"}");
                return selectedTemplate;
            }

            // 如果不是ConnectionViewModel，返回默认模板
            System.Diagnostics.Debug.WriteLine($"[DEBUG] Returning DefaultTemplate for non-ConnectionViewModel item");
            return DefaultTemplate;
        }

        /// <summary>
        /// 更新连接线样式
        /// 当画布的连接线样式发生变化时调用此方法
        /// </summary>
        /// <param name="newStyle">新的连接线样式</param>
        public void UpdateStyle(ConnectionLineStyle newStyle)
        {
            if (CurrentStyle != newStyle)
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] ConnectionTemplateSelector style changed from {CurrentStyle} to {newStyle}");
                CurrentStyle = newStyle;
            }
        }

        /// <summary>
        /// 验证所有必需的模板是否已设置
        /// </summary>
        /// <returns>如果所有模板都已设置返回true，否则返回false</returns>
        public bool ValidateTemplates()
        {
            var isValid = LineConnectionTemplate != null &&
                         BezierConnectionTemplate != null &&
                         StepConnectionTemplate != null &&
                         CircuitConnectionTemplate != null &&
                         DefaultTemplate != null;

            if (!isValid)
            {
                System.Diagnostics.Debug.WriteLine("[WARNING] ConnectionTemplateSelector: Some templates are not set");
                System.Diagnostics.Debug.WriteLine($"  LineConnectionTemplate: {LineConnectionTemplate != null}");
                System.Diagnostics.Debug.WriteLine($"  BezierConnectionTemplate: {BezierConnectionTemplate != null}");
                System.Diagnostics.Debug.WriteLine($"  StepConnectionTemplate: {StepConnectionTemplate != null}");
                System.Diagnostics.Debug.WriteLine($"  CircuitConnectionTemplate: {CircuitConnectionTemplate != null}");
                System.Diagnostics.Debug.WriteLine($"  DefaultTemplate: {DefaultTemplate != null}");
            }

            return isValid;
        }
    }
}
