using System.IO;
using System.Text.Json;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Infrastructure.Repositories;

/// <summary>
/// 基于文件的存储库实现
/// 适用于配置文件、项目文件等基于文件系统的数据存储
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class FileRepository<T> : Repository<T> where T : class
{
    private readonly string _filePath;
    private readonly JsonSerializerOptions _jsonOptions;
    private bool _isLoaded = false;

    public FileRepository(string filePath, ILogger<Repository<T>> logger) : base(logger)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        _filePath = filePath;
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };
    }

    protected override async Task<T?> FindByIdInternalAsync(object id, CancellationToken cancellationToken)
    {
        await LoadEntitiesIfNeededAsync(cancellationToken);

        // 假设实体有Id属性，实际实现中需要根据具体的实体类型来处理
        var idString = id.ToString();
        return _entities.FirstOrDefault(e => GetEntityId(e)?.ToString() == idString);
    }

    protected override async Task<T?> FindExistingEntityAsync(T entity, CancellationToken cancellationToken)
    {
        await LoadEntitiesIfNeededAsync(cancellationToken);

        var entityId = GetEntityId(entity);
        if (entityId == null)
            return null;

        return _entities.FirstOrDefault(e => GetEntityId(e)?.Equals(entityId) == true);
    }

    protected override async Task LoadEntitiesIfNeededAsync(CancellationToken cancellationToken)
    {
        if (_isLoaded)
            return;

        try
        {
            _logger.LogDebug("从文件加载实体: {FilePath}", _filePath);

            if (!File.Exists(_filePath))
            {
                _logger.LogDebug("文件不存在，创建空集合: {FilePath}", _filePath);
                _isLoaded = true;
                return;
            }

            var jsonContent = await File.ReadAllTextAsync(_filePath, cancellationToken);

            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                _logger.LogDebug("文件内容为空: {FilePath}", _filePath);
                _isLoaded = true;
                return;
            }

            var entities = JsonSerializer.Deserialize<List<T>>(jsonContent, _jsonOptions);
            if (entities != null)
            {
                _entities.Clear();
                _entities.AddRange(entities);
                _logger.LogDebug("从文件加载了 {Count} 个实体", entities.Count);
            }

            _isLoaded = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从文件加载实体时发生错误: {FilePath}", _filePath);
            throw new FileOperationException($"加载文件失败: {ex.Message}", _filePath, ex);
        }
    }

    protected override async Task SaveChangesAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("保存实体到文件: {FilePath}", _filePath);

            // 确保目录存在
            var directory = Path.GetDirectoryName(_filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogDebug("创建目录: {Directory}", directory);
            }

            var jsonContent = JsonSerializer.Serialize(_entities, _jsonOptions);
            await File.WriteAllTextAsync(_filePath, jsonContent, cancellationToken);

            _logger.LogDebug("保存了 {Count} 个实体到文件", _entities.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存实体到文件时发生错误: {FilePath}", _filePath);
            throw new FileOperationException($"保存文件失败: {ex.Message}", _filePath, ex);
        }
    }

    /// <summary>
    /// 获取实体的ID
    /// 子类可以重写此方法来提供特定的ID获取逻辑
    /// </summary>
    protected virtual object? GetEntityId(T entity)
    {
        if (entity == null)
            return null;

        // 尝试通过反射获取Id属性
        var idProperty = typeof(T).GetProperty("Id");
        if (idProperty != null)
        {
            return idProperty.GetValue(entity);
        }

        // 如果没有Id属性，返回对象的哈希码作为标识
        return entity.GetHashCode();
    }
}
