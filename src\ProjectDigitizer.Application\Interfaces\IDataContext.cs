using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 数据上下文接口
/// 提供数据访问的统一入口点
/// </summary>
public interface IDataContext : IApplicationService, IDisposable
{
    /// <summary>
    /// 获取指定类型的存储库
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>存储库实例</returns>
    IRepository<T> GetRepository<T>() where T : class;

    /// <summary>
    /// 保存所有更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的记录数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务实例</returns>
    Task<IDataTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据事务接口
/// </summary>
public interface IDataTransaction : IDisposable
{
    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}
