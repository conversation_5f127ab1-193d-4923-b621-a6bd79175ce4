using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Services;

/// <summary>
/// 错误恢复管理器
/// 协调多个错误恢复策略
/// </summary>
public class ErrorRecoveryManager : IErrorRecoveryManager
{
    private readonly List<IErrorRecoveryStrategy> _strategies;
    private readonly ILogger<ErrorRecoveryManager> _logger;

    public ErrorRecoveryManager(
        IEnumerable<IErrorRecoveryStrategy> strategies,
        ILogger<ErrorRecoveryManager> logger)
    {
        _strategies = strategies.OrderByDescending(s => s.Priority).ToList();
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 尝试恢复异常
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="context">恢复上下文</param>
    /// <returns>恢复结果</returns>
    public async Task<RecoveryResult> TryRecoverAsync(Exception exception, object? context = null)
    {
        _logger.LogInformation("开始尝试恢复异常: {ExceptionType}", exception.GetType().Name);

        foreach (var strategy in _strategies)
        {
            try
            {
                if (strategy.CanRecover(exception))
                {
                    _logger.LogDebug("使用策略 {StrategyType} 尝试恢复异常", strategy.GetType().Name);

                    var result = await strategy.RecoverAsync(exception, context);

                    if (result.IsSuccessful)
                    {
                        _logger.LogInformation("异常恢复成功，使用策略: {StrategyType}, 消息: {Message}",
                            strategy.GetType().Name, result.Message);
                        return result;
                    }
                    else
                    {
                        _logger.LogWarning("异常恢复失败，策略: {StrategyType}, 消息: {Message}",
                            strategy.GetType().Name, result.Message);

                        // 如果需要用户干预，直接返回结果
                        if (result.RequiresUserIntervention)
                        {
                            return result;
                        }
                    }
                }
            }
            catch (Exception strategyException)
            {
                _logger.LogError(strategyException, "恢复策略 {StrategyType} 执行时发生异常", strategy.GetType().Name);
            }
        }

        _logger.LogWarning("所有恢复策略都无法处理异常: {ExceptionType}", exception.GetType().Name);
        return RecoveryResult.Failure("没有可用的恢复策略");
    }

    /// <summary>
    /// 检查是否可以恢复异常
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>是否可以恢复</returns>
    public bool CanRecover(Exception exception)
    {
        return _strategies.Any(strategy => strategy.CanRecover(exception));
    }

    /// <summary>
    /// 获取可用的恢复策略
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>可用的恢复策略列表</returns>
    public IEnumerable<IErrorRecoveryStrategy> GetAvailableStrategies(Exception exception)
    {
        return _strategies.Where(strategy => strategy.CanRecover(exception));
    }

    /// <inheritdoc />
    public IEnumerable<IErrorRecoveryStrategy> GetAvailableStrategies(Type exceptionType)
    {
        // 创建异常实例进行测试（这不是最佳实践，但为了兼容接口）
        try
        {
            var exception = Activator.CreateInstance(exceptionType) as Exception;
            if (exception != null)
            {
                return GetAvailableStrategies(exception);
            }
        }
        catch
        {
            // 如果无法创建实例，返回空列表
        }

        return Enumerable.Empty<IErrorRecoveryStrategy>();
    }

    /// <inheritdoc />
    public void RegisterStrategy(IErrorRecoveryStrategy strategy)
    {
        AddStrategy(strategy);
    }

    /// <summary>
    /// 添加恢复策略
    /// </summary>
    /// <param name="strategy">恢复策略</param>
    public void AddStrategy(IErrorRecoveryStrategy strategy)
    {
        if (strategy == null) throw new ArgumentNullException(nameof(strategy));

        _strategies.Add(strategy);
        _strategies.Sort((a, b) => b.Priority.CompareTo(a.Priority));

        _logger.LogInformation("添加恢复策略: {StrategyType}, 优先级: {Priority}",
            strategy.GetType().Name, strategy.Priority);
    }

    /// <summary>
    /// 移除恢复策略
    /// </summary>
    /// <param name="strategyType">策略类型</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveStrategy(Type strategyType)
    {
        var strategy = _strategies.FirstOrDefault(s => s.GetType() == strategyType);
        if (strategy != null)
        {
            _strategies.Remove(strategy);
            _logger.LogInformation("移除恢复策略: {StrategyType}", strategyType.Name);
            return true;
        }

        return false;
    }
}
