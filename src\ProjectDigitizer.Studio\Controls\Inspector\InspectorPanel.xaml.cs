using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Inspector
{
    /// <summary>
    /// Unity风格的Inspector面板
    /// </summary>
    public partial class InspectorPanel : UserControl
    {
        private ModuleNodeViewModel? _currentNode;
        private readonly Dictionary<Type, InspectorComponentAttribute> _componentRegistry = new();
        private readonly List<InspectorComponent> _activeComponents = new();

        public static readonly DependencyProperty CurrentNodeProperty = DependencyProperty.Register(
            nameof(CurrentNode), typeof(ModuleNodeViewModel), typeof(InspectorPanel),
            new PropertyMetadata(null, OnCurrentNodeChanged));

        public ModuleNodeViewModel? CurrentNode
        {
            get => (ModuleNodeViewModel?)GetValue(CurrentNodeProperty);
            set => SetValue(CurrentNodeProperty, value);
        }

        private static void OnCurrentNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is InspectorPanel panel)
            {
                panel.SetCurrentNode(e.NewValue as ModuleNodeViewModel);
            }
        }

        public InspectorPanel()
        {
            InitializeComponent();
            RegisterComponents();
        }

        /// <summary>
        /// 注册所有可用的Inspector组件
        /// </summary>
        private void RegisterComponents()
        {
            // 扫描 Studio 自身以及插件程序集中的 Inspector 组件
            var assemblies = new List<Assembly> { Assembly.GetExecutingAssembly() };

            try
            {
                var catalog = App.GetOptionalService<ProjectDigitizer.Application.Interfaces.IPluginCatalog>();
                if (catalog?.Assemblies is { Count: > 0 })
                {
                    assemblies.AddRange(catalog.Assemblies);
                }
            }
            catch
            {
                // 忽略插件目录获取异常，降级为仅扫描本程序集
            }

            foreach (var asm in assemblies.Distinct())
            {
                try
                {
                    var componentTypes = asm.GetTypes()
                        .Where(t => t.IsSubclassOf(typeof(InspectorComponent)) && !t.IsAbstract)
                        .ToList();

                    foreach (var componentType in componentTypes)
                    {
                        var attribute = componentType.GetCustomAttribute<InspectorComponentAttribute>();
                        if (attribute != null)
                        {
                            _componentRegistry[componentType] = attribute;
                        }
                    }
                }
                catch (ReflectionTypeLoadException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[Inspector] Reflection load error for {asm.FullName}: {ex.Message}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[Inspector] Error scanning assembly {asm.FullName}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"[Inspector] Registered {_componentRegistry.Count} components");
        }

        /// <summary>
        /// 设置当前节点
        /// </summary>
        private void SetCurrentNode(ModuleNodeViewModel? node)
        {
            if (_currentNode == node) return;

            _currentNode = node;
            UpdateNodeTitle();
            RefreshComponents();
        }

        /// <summary>
        /// 更新节点标题显示
        /// </summary>
        private void UpdateNodeTitle()
        {
            if (_currentNode != null)
            {
                NodeTitleText.Text = $"{_currentNode.Title} ({_currentNode.Module?.Type})";
            }
            else
            {
                NodeTitleText.Text = "未选择节点";
            }
        }

        /// <summary>
        /// 刷新组件列表
        /// </summary>
        private void RefreshComponents()
        {
            // 清空现有组件
            ClearComponents();

            if (_currentNode?.Module == null) return;

            // 获取适用于当前节点类型的组件
            var applicableComponents = GetApplicableComponents(_currentNode.Module.Type);

            // 按优先级排序
            applicableComponents = applicableComponents
                .OrderBy(kvp => _componentRegistry[kvp.Key].Priority)
                .ToList();

            // 创建并添加组件
            foreach (var (componentType, attribute) in applicableComponents)
            {
                try
                {
                    var component = CreateComponent(componentType);
                    if (component != null)
                    {
                        AddComponent(component);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[Inspector] Error creating component {componentType.Name}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取适用于指定模块类型的组件
        /// </summary>
        private List<KeyValuePair<Type, InspectorComponentAttribute>> GetApplicableComponents(ModuleType moduleType)
        {
            return _componentRegistry
                .Where(kvp => IsComponentApplicable(kvp.Value, moduleType))
                .ToList();
        }

        /// <summary>
        /// 检查组件是否适用于指定的模块类型
        /// </summary>
        private bool IsComponentApplicable(InspectorComponentAttribute attribute, ModuleType moduleType)
        {
            // 创建组件实例来检查是否适用
            try
            {
                var componentType = _componentRegistry.FirstOrDefault(kvp => kvp.Value == attribute).Key;
                if (componentType == null) return false;

                var tempComponent = (InspectorComponent)Activator.CreateInstance(componentType)!;
                var tempNode = new ModuleNodeViewModel { Module = new ModuleModel { Type = moduleType } };

                bool isApplicable = tempComponent.IsApplicableToNode(tempNode);

                System.Diagnostics.Debug.WriteLine($"[Inspector] Component {componentType.Name} applicable to {moduleType}: {isApplicable}");

                return isApplicable;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[Inspector] Error checking component applicability: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建组件实例
        /// </summary>
        private InspectorComponent? CreateComponent(Type componentType)
        {
            try
            {
                var component = (InspectorComponent)Activator.CreateInstance(componentType)!;
                component.CurrentNode = _currentNode;
                return component;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[Inspector] Failed to create component {componentType.Name}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 添加组件到面板
        /// </summary>
        private void AddComponent(InspectorComponent component)
        {
            var attribute = _componentRegistry[component.GetType()];

            // 创建Expander容器
            var expander = new Expander
            {
                Header = attribute.Title,
                IsExpanded = attribute.IsExpandedByDefault,
                Style = (Style)FindResource("InspectorComponentStyle"),
                Content = component
            };

            // 设置组件属性
            expander.SetValue(FrameworkElement.TagProperty, component);

            ComponentsContainer.Children.Add(expander);
            _activeComponents.Add(component);

            System.Diagnostics.Debug.WriteLine($"[Inspector] Added component: {attribute.Title}");
        }

        /// <summary>
        /// 清空所有组件
        /// </summary>
        private void ClearComponents()
        {
            ComponentsContainer.Children.Clear();
            _activeComponents.Clear();
        }

        /// <summary>
        /// 添加组件按钮点击事件
        /// </summary>
        private void AddComponentButton_Click(object sender, RoutedEventArgs e)
        {
            ShowAddComponentMenu();
        }

        /// <summary>
        /// 显示添加组件菜单
        /// </summary>
        private void ShowAddComponentMenu()
        {
            if (_currentNode?.Module == null) return;

            // 获取可添加的组件（排除已添加的）
            var availableComponents = GetApplicableComponents(_currentNode.Module.Type)
                .Where(kvp => !_activeComponents.Any(c => c.GetType() == kvp.Key))
                .Where(kvp => _componentRegistry[kvp.Key].CanBeRemoved) // 只显示可移除的组件（意味着可选添加）
                .ToList();

            if (!availableComponents.Any())
            {
                MessageBox.Show("没有可添加的组件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 创建上下文菜单
            var contextMenu = new ContextMenu();
            foreach (var (componentType, attribute) in availableComponents)
            {
                var menuItem = new MenuItem
                {
                    Header = attribute.Title,
                    Tag = componentType
                };
                menuItem.Click += (s, e) => AddComponentFromMenu(componentType);
                contextMenu.Items.Add(menuItem);
            }

            contextMenu.PlacementTarget = AddComponentButton;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// 从菜单添加组件
        /// </summary>
        private void AddComponentFromMenu(Type componentType)
        {
            try
            {
                var component = CreateComponent(componentType);
                if (component != null)
                {
                    AddComponent(component);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加组件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 应用所有组件的更改到节点
        /// </summary>
        public void ApplyAllComponents()
        {
            foreach (var component in _activeComponents)
            {
                try
                {
                    component.ApplyToNode();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[Inspector] Error applying component {component.GetType().Name}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 验证所有组件
        /// </summary>
        public bool ValidateAllComponents(out List<string> errors)
        {
            errors = new List<string>();
            bool isValid = true;

            foreach (var component in _activeComponents)
            {
                if (!component.ValidateComponent(out string error))
                {
                    isValid = false;
                    errors.Add($"{component.ComponentTitle}: {error}");
                }
            }

            return isValid;
        }
    }
}
