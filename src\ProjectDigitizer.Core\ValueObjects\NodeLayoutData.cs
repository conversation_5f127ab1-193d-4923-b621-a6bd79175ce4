using System;
using System.Collections.Generic;

namespace ProjectDigitizer.Core.ValueObjects
{
    /// <summary>
    /// 节点位置信息
    /// </summary>
    public class NodePosition
    {
        public string NodeId { get; set; } = string.Empty;
        public double X { get; set; }
        public double Y { get; set; }
        // 新增：节点实际尺寸（用于尺寸感知布局与碰撞）
        public double Width { get; set; }
        public double Height { get; set; }
        public bool IsLocked { get; set; }
        public string Title { get; set; } = string.Empty;
        public Entities.NodeType NodeType { get; set; }
    }

    /// <summary>
    /// 连接信息
    /// </summary>
    public class ConnectionInfo
    {
        public string Id { get; set; } = string.Empty;
        public string SourceNodeId { get; set; } = string.Empty;
        public string SourceConnectorId { get; set; } = string.Empty;
        public string TargetNodeId { get; set; } = string.Empty;
        public string TargetConnectorId { get; set; } = string.Empty;
        public bool IsInputConnection { get; set; }
    }

    /// <summary>
    /// 布局算法类型
    /// </summary>
    public enum LayoutAlgorithm
    {
        /// <summary>
        /// 层次布局 - 按照数据流方向排列，支持多行换行
        /// </summary>
        Hierarchical,

        /// <summary>
        /// 均衡网格布局 - 根据可见画布比例将节点均匀铺满网格
        /// </summary>
        BalancedGrid,

        // Force-directed layout
        ForceDirected,

        // Reingold–Tilford Tidy Tree
        TreeTidy,

        // Radial Tree
        RadialTree
    }

    /// <summary>
    /// 布局配置选项
    /// </summary>
    public class LayoutOptions
    {
        /// <summary>节点间水平间距</summary>
        public double HorizontalSpacing { get; set; } = 300;

        /// <summary>节点间垂直间距</summary>
        public double VerticalSpacing { get; set; } = 160;

        /// <summary>布局起始位置X</summary>
        public double StartX { get; set; } = 100;

        /// <summary>布局起始位置Y</summary>
        public double StartY { get; set; } = 100;

        /// <summary>是否启用动画过渡</summary>
        public bool EnableAnimation { get; set; } = true;

        /// <summary>动画持续时间（毫秒）</summary>
        public int AnimationDuration { get; set; } = 1000;

        /// <summary>画布可见宽度（用于换行计算）</summary>
        public double CanvasWidth { get; set; } = 1200;

        /// <summary>画布可见高度</summary>
        public double CanvasHeight { get; set; } = 800;

        /// <summary>节点估算宽度（用于换行计算）</summary>
        public double NodeWidth { get; set; } = 250;

        /// <summary>节点估算高度</summary>
        public double NodeHeight { get; set; } = 120;

        /// <summary>行间距（多行布局时的行间距）</summary>
        public double RowSpacing { get; set; } = 200;

        /// <summary>避免重叠的安全边距</summary>
        public double SafetyMargin { get; set; } = 20;

        // 美观增强配置
        /// <summary>多轮双向 sweep 的最大轮数（barycenter 排序优化）</summary>
        public int MaxSweepRounds { get; set; } = 3;

        /// <summary>早停阈值：上一轮到下一轮交叉数改善比例低于该阈值时提前停止（0.0~1.0）</summary>
        public double EarlyStopThreshold { get; set; } = 0.01;

        /// <summary>启用 Brandes–Köpf 垂直对齐与水平压缩（占位，后续步骤使用）</summary>
        public bool EnableBKAlignment { get; set; } = true;

        /// <summary>边通道最小间距（用于并行边/同层边的并行偏移）</summary>
        public double EdgeChannelSpacing { get; set; } = 20;

        /// <summary>自适应间距（根据层/度密度调整间距）</summary>
        public bool EnableAdaptiveSpacing { get; set; } = true;

        // Force-directed layout options
        public int Iterations { get; set; } = 200;
        public double InitialTemperature { get; set; } = 400;
        public double MinDistance { get; set; } = 120;
        public bool PreventOverlap { get; set; } = true;
    }



    /// <summary>
    /// 布局结果
    /// </summary>
    public class LayoutResult
    {
        public Dictionary<string, NodePosition> NodePositions { get; set; } = new();
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
    }
}
