<local:InspectorComponent
    d:DesignHeight="400"
    d:DesignWidth="340"
    mc:Ignorable="d"
    x:Class="Plugins.DataCalculation.DataCalculationComponent"
    PreviewKeyDown="OnComponentPreviewKeyDown"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:functions="clr-namespace:Plugins.DataCalculation.Controls.Functions"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:local="clr-namespace:ProjectDigitizer.Studio.Controls.Inspector;assembly=ProjectDigitizer.Studio"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!-- Keep default WPF UI ComboBox style; margins will be set per-control. -->
        <!--  文本样式：标签/标题/说明（颜色与字号遵循全局 Typography，但可微调）  -->
        <Style TargetType="TextBlock" x:Key="CaptionText">
            <Setter Property="FontSize" Value="11" />
            <Setter Property="Foreground" Value="{StaticResource Brush.TextSecondary}" />
            <Setter Property="Opacity" Value="0.8" />
            <Setter Property="Margin" Value="0,2,0,0" />
        </Style>

        <!--  编辑器统一边距  -->
        <!--  面板内的无阴影“胶囊”按钮（扁平化，hover 加描边）  -->
        <Style
            BasedOn="{StaticResource Button.Secondary}"
            TargetType="Button"
            x:Key="Inspector.ButtonFlat">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="3"
                            x:Name="Root">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" TargetName="Root" Value="{StaticResource Brush.Primary}" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.6" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  图标统一尺寸（小号）  -->
        <Style TargetType="ipack:PackIconMaterial" x:Key="Icon.Small">
            <Setter Property="Width" Value="14" />
            <Setter Property="Height" Value="14" />
        </Style>

        <!-- Fallback resources: ensure component can instantiate before being
             attached to InspectorPanel (which defines these keys). Keep them
             minimal and aligned with InspectorPanel.xaml. -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <Style x:Key="InspectorLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="{DynamicResource Brush.Text}" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,0,8,4" />
        </Style>
        <Style x:Key="InspectorEditorStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>
        <Style x:Key="InspectorCardBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource Brush.SurfaceVariant}" />
            <Setter Property="BorderBrush" Value="{DynamicResource Brush.Divider}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="8" />
            <Setter Property="Margin" Value="0,4,0,4" />
        </Style>
    </UserControl.Resources>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
        <StackPanel Background="{StaticResource Brush.SurfaceVariant}" MinWidth="320" KeyboardNavigation.TabNavigation="Continue">
            <!--  统一信息条（非阻塞提示/错误/成功）  -->
            <ui:InfoBar
                IsOpen="False"
                Margin="0,0,0,12"
                Message=""
                Severity="Informational"
                Title="提示"
                x:Name="StatusInfoBar" />

            <!--  基本设置  -->
            <GroupBox Header="基本设置">
                <StackPanel>
                    <ui:TextBlock Style="{StaticResource InspectorLabelStyle}" Text="计算模式" />
                    <ComboBox
                        SelectedIndex="0"
                        Margin="0,0,0,12"
                        x:Name="CalculationModeComboBox"
                        MinWidth="180"
                        ToolTip="选择表达式的执行方式：实时、手动或定时。"
                        AutomationProperties.Name="计算模式">
                        <ComboBoxItem Content="实时计算" />
                        <ComboBoxItem Content="手动计算" />
                        <ComboBoxItem Content="定时计算" />
                    </ComboBox>

                    <TextBlock Style="{StaticResource InspectorLabelStyle}" Text="计算精度" />
                    <Slider
                        IsSnapToTickEnabled="True"
                        Maximum="10"
                        Minimum="0"
                        Style="{StaticResource InspectorEditorStyle}"
                        TickFrequency="1"
                        TickPlacement="BottomRight"
                        Value="2"
                        x:Name="PrecisionSlider"
                        ToolTip="控制数值结果显示的小数位数"
                        AutomationProperties.Name="小数精度" />
                    <ui:NumberBox Minimum="0" Maximum="10" Width="64" Margin="0,4,0,8"
                                   Value="{Binding ElementName=PrecisionSlider, Path=Value, Mode=TwoWay}"
                                   ToolTip="精确输入小数位数"
                                   AutomationProperties.Name="小数精度数值" />
                    <TextBlock Style="{StaticResource CaptionText}" Text="{Binding Value, ElementName=PrecisionSlider, StringFormat='小数位数: {0}'}" />

                    <TextBlock Style="{StaticResource InspectorLabelStyle}" Text="错误处理" />
                    <ComboBox
                        SelectedIndex="0"
                        Margin="0,0,0,12"
                        x:Name="ErrorHandlingComboBox"
                        MinWidth="180"
                        ToolTip="表达式出错时的处理方式。"
                        AutomationProperties.Name="容错策略">
                        <ComboBoxItem Content="返回错误值" />
                        <ComboBoxItem Content="使用默认值" />
                        <ComboBoxItem Content="记录错误" />
                        <ComboBoxItem Content="停止执行" />
                    </ComboBox>
                    <TextBlock Style="{StaticResource CaptionText}"
                               Text="提示：Ctrl+Enter 执行全部函数；Ctrl+D 复制当前函数；Delete 删除当前函数" />
                </StackPanel>
            </GroupBox>

            <!--  动态变量  -->
            <GroupBox Header="动态变量">
                <StackPanel>
                    <WrapPanel Margin="0,0,0,6">
                        <ui:Button
                            Click="AddVariableButton_Click"
                            Content="添加变量"
                            Margin="0,0,8,0"
                            Padding="16,6"
                            Style="{StaticResource Inspector.ButtonFlat}"
                            x:Name="AddVariableButton"
                            ToolTip="添加一个变量以引用上游节点的输出"
                            AutomationProperties.Name="添加变量">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ipack:PackIconMaterial
                                            Height="16"
                                            Kind="Plus"
                                            Margin="0,0,4,0"
                                            Width="16" />
                                        <TextBlock Text="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </ui:Button>
                        <Button
                            Click="RefreshVariablesButton_Click"
                            Content="刷新"
                            Padding="16,6"
                            Style="{StaticResource Inspector.ButtonFlat}"
                            x:Name="RefreshVariablesButton"
                            ToolTip="重新扫描与本节点相连的上游节点"
                            AutomationProperties.Name="刷新变量来源">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ipack:PackIconMaterial
                                            Height="16"
                                            Kind="Refresh"
                                            Margin="0,0,4,0"
                                            Width="16" />
                                        <TextBlock Text="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </WrapPanel>

                    <TextBlock Style="{StaticResource CaptionText}"
                               Text="变量在表达式中以 {名称} 的形式引用。点击 + 可将变量插入到当前聚焦的表达式。"/>

                    <ItemsControl
                        x:Name="VariablesItemsControl"
                        ScrollViewer.CanContentScroll="True"
                        VirtualizingStackPanel.IsVirtualizing="True"
                        VirtualizingStackPanel.VirtualizationMode="Recycling">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource InspectorCardBorderStyle}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <!--  变量名称 + 插入到编辑器 + 删除  -->
                                        <Grid Grid.Row="0" Margin="0,0,0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <ui:TextBox
                                                FontWeight="Medium"
                                                Grid.Column="0"
                                                PlaceholderText="变量名"
                                                Text="{Binding VariableName, UpdateSourceTrigger=PropertyChanged}"
                                                AutomationProperties.Name="变量名" />
                                            <Button
                                                Grid.Column="1"
                                                Height="24"
                                                Margin="4,0"
                                                Style="{StaticResource Button.Text}"
                                                Click="InsertVariableToken_Click"
                                                ToolTip="在光标处插入 {变量}">
                                                <Button.Content>
                                                    <ipack:PackIconMaterial
                                                        Kind="Plus"
                                                        Style="{StaticResource Icon.Small}" />
                                                </Button.Content>
                                                <Button.Tag>
                                                    <Binding Path="VariableName" />
                                                </Button.Tag>
                                            </Button>
                                            <Button
                                                Grid.Column="2"
                                                Height="24"
                                                Margin="4,0"
                                                Style="{StaticResource Button.Text}"
                                                ToolTip="删除变量">
                                                <Button.Content>
                                                    <ipack:PackIconMaterial
                                                        Kind="Delete"
                                                        Style="{StaticResource Icon.Small}" />
                                                </Button.Content>
                                            </Button>
                                        </Grid>

                                        <!--  源节点选择 + 连接按钮  -->
                                        <Grid Grid.Row="1" Margin="0,0,0,8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <ComboBox
                                                DisplayMemberPath="DisplayText"
                                                Grid.Column="0"
                                                ItemsSource="{Binding AvailableSourceNodes}"
                                            SelectedItem="{Binding SelectedSourceNode}"
                                            ToolTip="选择此变量的上游节点"
                                            AutomationProperties.Name="变量来源节点" />
                                            <Button
                                                Grid.Column="1"
                                                Height="24"
                                                Margin="4,0"
                                                Style="{StaticResource Button.Text}"
                                                ToolTip="连接到节点">
                                                <Button.Content>
                                                    <ipack:PackIconMaterial
                                                        Kind="Link"
                                                        Style="{StaticResource Icon.Small}" />
                                                </Button.Content>
                                            </Button>
                                        </Grid>

                                        <!--  源节点属性选择  -->
                                        <ComboBox
                                            DisplayMemberPath="PropertyName"
                                            Grid.Row="2"
                                            IsEnabled="{Binding HasSelectedSourceNode}"
                                            ItemsSource="{Binding AvailableProperties}"
                                            SelectedItem="{Binding SelectedProperty}"
                                            ToolTip="选择要引用的输出属性"
                                            AutomationProperties.Name="变量输出属性" />
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!--  空态提示  -->
                    <Border
                        Background="{DynamicResource Brush.SurfaceVariant}"
                        BorderBrush="{DynamicResource Brush.Divider}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="12"
                        Visibility="Collapsed"
                        x:Name="EmptyVariablesPanel">
                        <StackPanel HorizontalAlignment="Center">
                            <ipack:PackIconMaterial
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                Height="32"
                                HorizontalAlignment="Center"
                                Kind="Variable"
                                Margin="0,0,0,8"
                                Width="32" />
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                HorizontalAlignment="Center"
                                Text="还没有定义变量" />
                            <TextBlock
                                FontSize="11"
                                HorizontalAlignment="Center"
                                Margin="0,4,0,0"
                                Opacity="0.7"
                                Text="点击‘添加变量’或从连接的节点选择变量" />
                        </StackPanel>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!--  函数列表  -->
            <GroupBox Header="函数列表">
                <StackPanel>
                    <WrapPanel Margin="0,0,0,6">
                        <Button
                            Click="AddFunctionButton_Click"
                            Content="添加函数"
                            Margin="0,0,8,0"
                            Padding="16,6"
                            Style="{StaticResource Inspector.ButtonFlat}"
                            x:Name="AddFunctionButton"
                            ToolTip="添加一个新的计算函数"
                            AutomationProperties.Name="添加函数">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ipack:PackIconMaterial
                                            Height="16"
                                            Kind="Plus"
                                            Margin="0,0,4,0"
                                            Width="16" />
                                        <TextBlock Text="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                        <Button
                            Click="ImportTemplateButton_Click"
                            Content="导入模板"
                            Margin="0,0,8,0"
                            Padding="16,6"
                            Style="{StaticResource Inspector.ButtonFlat}"
                            x:Name="ImportTemplateButton"
                            ToolTip="从内置模板库导入常用函数"
                            AutomationProperties.Name="导入函数模板">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ipack:PackIconMaterial
                                            Height="16"
                                            Kind="Login"
                                            Margin="0,0,4,0"
                                            Width="16" />
                                        <TextBlock Text="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                        <Button
                            Click="ShowAllFunctionsButton_Click"
                            Content="全部函数"
                            Padding="16,6"
                            Style="{StaticResource Inspector.ButtonFlat}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <ipack:PackIconMaterial
                                            Height="16"
                                            Kind="Function"
                                            Margin="0,0,4,0"
                                            Width="16" />
                                        <TextBlock Text="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </WrapPanel>
                    <TextBlock Style="{StaticResource CaptionText}"
                               Text="点击 ▶ 预览结果；名称用于输出标识，建议简短易懂。"/>

                    <ItemsControl
                        x:Name="FunctionsItemsControl"
                        ScrollViewer.CanContentScroll="True"
                        VirtualizingStackPanel.IsVirtualizing="True"
                        VirtualizingStackPanel.VirtualizationMode="Recycling">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource InspectorCardBorderStyle}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <!--  函数头部：名称 + 预览 + 删除  -->
                                        <Grid Grid.Row="0" Margin="0,0,0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                            </Grid.ColumnDefinitions>
                                            <ui:TextBox
                                                FontWeight="Medium"
                                                Grid.Column="0"
                                                PlaceholderText="函数名称"
                                                Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                AutomationProperties.Name="函数名称" />
                                            <Button
                                                Grid.Column="1"
                                                Height="24"
                                                Margin="4,0"
                                                Style="{StaticResource Button.Text}"
                                                ToolTip="预览计算">
                                                <Button.Content>
                                                    <ipack:PackIconMaterial
                                                        Kind="Play"
                                                        Style="{StaticResource Icon.Small}" />
                                                </Button.Content>
                                            </Button>
                                            <Button
                                                Click="DeleteFunctionButton_Click"
                                                Grid.Column="2"
                                                Height="24"
                                                Margin="4,0"
                                                Style="{StaticResource Button.Text}"
                                                Tag="{Binding}">
                                                <Button.Content>
                                                    <ipack:PackIconMaterial
                                                        Kind="Delete"
                                                        Style="{StaticResource Icon.Small}" />
                                                </Button.Content>
                                            </Button>
                                        </Grid>

                                        <!--  表达式编辑器  -->
                                        <functions:AvalonFormulaEditor
                                            Grid.Row="1"
                                            MinHeight="100"
                                            Text="{Binding Expression, UpdateSourceTrigger=PropertyChanged}" />

                                        <!--  校验错误  -->
                                        <TextBlock
                                            FontSize="11"
                                            Foreground="#E53935"
                                            Grid.Row="2"
                                            Text="{Binding ValidationError}"
                                            TextWrapping="Wrap"
                                            Visibility="{Binding HasValidationError, Converter={StaticResource BooleanToVisibilityConverter}}" />

                                        <!--  预览结果  -->
                                        <Border
                                            Background="{DynamicResource Brush.SurfaceVariant}"
                                            BorderBrush="{DynamicResource Brush.Divider}"
                                            BorderThickness="1"
                                            CornerRadius="4"
                                            Grid.Row="3"
                                            Padding="6,2"
                                            Visibility="{Binding HasResult, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <StackPanel>
                                                <TextBlock
                                                    FontSize="10"
                                                    Opacity="0.7"
                                                    Text="执行结果:" />
                                                <TextBox
                                                    FontFamily="Consolas"
                                                    FontSize="11"
                                                    BorderThickness="0"
                                                    Background="Transparent"
                                                    IsReadOnly="True"
                                                    Text="{Binding Result}"
                                                    TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!--  空态提示  -->
                    <Border
                        Background="{DynamicResource Brush.SurfaceVariant}"
                        BorderBrush="{DynamicResource Brush.Divider}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="12"
                        Visibility="Collapsed"
                        x:Name="EmptyFunctionsPanel">
                        <StackPanel HorizontalAlignment="Center">
                            <ipack:PackIconMaterial
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                Height="32"
                                HorizontalAlignment="Center"
                                Kind="Function"
                                Margin="0,0,0,8"
                                Width="32" />
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                HorizontalAlignment="Center"
                                Text="还没有定义函数" />
                            <TextBlock
                                FontSize="11"
                                HorizontalAlignment="Center"
                                Margin="0,4,0,0"
                                Opacity="0.7"
                                Text="点击‘添加函数’开始配置计算逻辑" />
                        </StackPanel>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!--  输出设置  -->
            <GroupBox Header="输出设置">
                <StackPanel>
                    <TextBlock Style="{StaticResource InspectorLabelStyle}" Text="输出格式" />
                    <ComboBox
                        SelectedIndex="0"
                        Margin="0,0,0,12"
                        x:Name="OutputFormatComboBox"
                        MinWidth="180"
                        ToolTip="设置结果输出的格式"
                        AutomationProperties.Name="输出格式">
                        <ComboBoxItem Content="数值" />
                        <ComboBoxItem Content="文本" />
                        <ComboBoxItem Content="JSON" />
                        <ComboBoxItem Content="表格" />
                    </ComboBox>

                    <CheckBox Content="缓存结果" x:Name="CacheResultsCheckBox" />
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </ScrollViewer>
</local:InspectorComponent>


