using System;

namespace CalcEngine
{
    /// <summary>
    /// CalcEngine 基础异常。
    /// </summary>
    public class CalcEngineException : Exception
    {
        public CalcEngineException() { }
        public CalcEngineException(string? message) : base(message) { }
        public CalcEngineException(string? message, Exception? innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 表达式解析异常：包含出错位置与上下文预览，便于快速定位问题。
    /// </summary>
    public sealed class CalcEngineParseException : CalcEngineException
    {
        /// <summary>
        /// 出错的原始表达式。
        /// </summary>
        public string Expression { get; }

        /// <summary>
        /// 出错位置（基于字符串索引）。
        /// </summary>
        public int Position { get; }

        /// <summary>
        /// 出错位置附近的上下文预览（最多 ~40 字符）。
        /// </summary>
        public string Preview { get; }

        public CalcEngineParseException(string message, string expression, int position, string preview)
            : base(BuildMessage(message, position, preview))
        {
            Expression = expression;
            Position = position;
            Preview = preview;
        }

        private static string BuildMessage(string message, int position, string preview)
        {
            return $"{message} (pos {position}). 片段: '{preview}'";
        }
    }
}

