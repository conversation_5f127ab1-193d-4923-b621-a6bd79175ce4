using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;

namespace Plugins.DataCalculation;

/// <summary>
/// 插件入口：将“数据计算”节点的模板与处理器注册到 INodeRegistry。
/// - 与原 Plugins.DataCalculation 功能等价，统一于本插件中提供 UI 资源。
/// </summary>
public sealed class DataCalculationModule : INodeModule
{
    public NodeModuleInfo Info => new()
    {
        Id = "Plugins.DataCalculation",
        Name = "数据计算（合并版）",
        Description = "在同一插件中提供节点注册与 UI 资源",
        Version = "1.0.0"
    };

    /// <summary>
    /// 注册节点模板与处理器。
    /// </summary>
    public void RegisterNodes(INodeRegistry registry)
    {
        registry.RegisterTemplate(ModuleType.DataCalculation, () => new DataCalculationTemplate());
        registry.RegisterProcessor(ModuleType.DataCalculation, () => new DataCalculationProcessor());
    }
}



