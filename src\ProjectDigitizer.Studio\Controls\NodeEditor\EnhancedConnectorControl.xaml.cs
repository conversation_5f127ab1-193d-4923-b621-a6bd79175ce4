using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.NodeEditor
{
    /// <summary>
    /// EnhancedConnectorControl.xaml 的交互逻辑
    /// </summary>
    public partial class EnhancedConnectorControl : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 连接器类型依赖属性
        /// </summary>
        public static readonly DependencyProperty ConnectorTypeProperty =
            DependencyProperty.Register(nameof(ConnectorType), typeof(ConnectorDataType), typeof(EnhancedConnectorControl),
                new PropertyMetadata(ConnectorDataType.Any, OnConnectorTypeChanged));

        /// <summary>
        /// 是否为输入连接器依赖属性
        /// </summary>
        public static readonly DependencyProperty IsInputProperty =
            DependencyProperty.Register(nameof(IsInput), typeof(bool), typeof(EnhancedConnectorControl),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否已连接依赖属性
        /// </summary>
        public static readonly DependencyProperty IsConnectedProperty =
            DependencyProperty.Register(nameof(IsConnected), typeof(bool), typeof(EnhancedConnectorControl),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否兼容依赖属性
        /// </summary>
        public static readonly DependencyProperty IsCompatibleProperty =
            DependencyProperty.Register(nameof(IsCompatible), typeof(bool), typeof(EnhancedConnectorControl),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否高亮依赖属性
        /// </summary>
        public static readonly DependencyProperty IsHighlightedProperty =
            DependencyProperty.Register(nameof(IsHighlighted), typeof(bool), typeof(EnhancedConnectorControl),
                new PropertyMetadata(false));

        #endregion

        #region 属性

        /// <summary>
        /// 连接器类型
        /// </summary>
        public ConnectorDataType ConnectorType
        {
            get => (ConnectorDataType)GetValue(ConnectorTypeProperty);
            set => SetValue(ConnectorTypeProperty, value);
        }

        /// <summary>
        /// 是否为输入连接器
        /// </summary>
        public bool IsInput
        {
            get => (bool)GetValue(IsInputProperty);
            set => SetValue(IsInputProperty, value);
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get => (bool)GetValue(IsConnectedProperty);
            set => SetValue(IsConnectedProperty, value);
        }

        /// <summary>
        /// 是否兼容
        /// </summary>
        public bool IsCompatible
        {
            get => (bool)GetValue(IsCompatibleProperty);
            set => SetValue(IsCompatibleProperty, value);
        }

        /// <summary>
        /// 是否高亮
        /// </summary>
        public bool IsHighlighted
        {
            get => (bool)GetValue(IsHighlightedProperty);
            set => SetValue(IsHighlightedProperty, value);
        }

        #endregion

        #region 路由事件

        /// <summary>
        /// 连接器点击事件
        /// </summary>
        public static readonly RoutedEvent ConnectorClickEvent =
            EventManager.RegisterRoutedEvent(nameof(ConnectorClick), RoutingStrategy.Bubble,
                typeof(RoutedEventHandler), typeof(EnhancedConnectorControl));

        /// <summary>
        /// 连接器点击事件
        /// </summary>
        public event RoutedEventHandler ConnectorClick
        {
            add => AddHandler(ConnectorClickEvent, value);
            remove => RemoveHandler(ConnectorClickEvent, value);
        }

        /// <summary>
        /// 连接器拖拽开始事件
        /// </summary>
        public static readonly RoutedEvent ConnectorDragStartEvent =
            EventManager.RegisterRoutedEvent(nameof(ConnectorDragStart), RoutingStrategy.Bubble,
                typeof(RoutedEventHandler), typeof(EnhancedConnectorControl));

        /// <summary>
        /// 连接器拖拽开始事件
        /// </summary>
        public event RoutedEventHandler ConnectorDragStart
        {
            add => AddHandler(ConnectorDragStartEvent, value);
            remove => RemoveHandler(ConnectorDragStartEvent, value);
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public EnhancedConnectorControl()
        {
            InitializeComponent();

            // 设置数据上下文为自己，以便绑定依赖属性
            DataContext = this;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 连接器类型改变时的处理
        /// </summary>
        private static void OnConnectorTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is EnhancedConnectorControl control)
            {
                // 可以在这里添加类型改变时的特殊处理逻辑
                control.UpdateToolTip();
            }
        }

        /// <summary>
        /// 鼠标按下事件
        /// </summary>
        protected override void OnMouseDown(MouseButtonEventArgs e)
        {
            base.OnMouseDown(e);

            if (e.LeftButton == MouseButtonState.Pressed)
            {
                // 触发连接器点击事件
                RaiseEvent(new RoutedEventArgs(ConnectorClickEvent, this));

                // 如果是拖拽操作，触发拖拽开始事件
                if (e.ClickCount == 1)
                {
                    RaiseEvent(new RoutedEventArgs(ConnectorDragStartEvent, this));
                }

                e.Handled = true;
            }
        }

        /// <summary>
        /// 更新工具提示
        /// </summary>
        private void UpdateToolTip()
        {
            var direction = IsInput ? "输入" : "输出";
            var typeName = GetDataTypeName(ConnectorType);
            ToolTip = $"{direction}连接器 - {typeName}";
        }

        /// <summary>
        /// 获取数据类型的中文名称
        /// </summary>
        private string GetDataTypeName(ConnectorDataType dataType)
        {
            return dataType switch
            {
                ConnectorDataType.Any => "通用类型",
                ConnectorDataType.Number => "数值类型",
                ConnectorDataType.Text => "文本类型",
                ConnectorDataType.Boolean => "布尔类型",
                ConnectorDataType.File => "文件类型",
                ConnectorDataType.Geometry => "几何类型",
                ConnectorDataType.Control => "控制流",
                _ => "未知类型"
            };
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置兼容性状态
        /// </summary>
        /// <param name="isCompatible">是否兼容</param>
        /// <param name="highlight">是否高亮显示</param>
        public void SetCompatibilityState(bool isCompatible, bool highlight = false)
        {
            IsCompatible = isCompatible;
            IsHighlighted = highlight;
        }

        /// <summary>
        /// 重置状态
        /// </summary>
        public void ResetState()
        {
            IsCompatible = true;
            IsHighlighted = false;
        }

        #endregion
    }
}
