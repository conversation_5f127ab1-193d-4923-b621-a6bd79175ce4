using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace Plugins.DataCalculation.Controls.Functions
{
    /// <summary>
    /// 所有函数展示窗口：基于 CalcEngineFunctionCatalog 的函数元数据，提供分类与搜索过滤。
    /// 双击/按钮支持复制函数名或示例，并可插入到最近获得焦点的编辑器。
    /// </summary>
    public partial class AllFunctionsWindow : Wpf.Ui.Controls.FluentWindow
    {
        private readonly ObservableCollection<FunctionView> _items = [];
        private readonly ListCollectionView _view;

        public AllFunctionsWindow()
        {
            InitializeComponent();


            IReadOnlyDictionary<string, CalcEngineFunctionCatalog.FunctionInfo> dict = CalcEngineFunctionCatalog.Load();
            foreach (KeyValuePair<string, CalcEngineFunctionCatalog.FunctionInfo> kv in dict.OrderBy(k => k.Key))
            {
                _items.Add(FunctionView.FromInfo(kv.Value));
            }

            _view = (ListCollectionView)CollectionViewSource.GetDefaultView(_items);
            _view.Filter = FilterItem;
            FunctionsGrid.ItemsSource = _view;

            // 应用 WPF UI 背景（Acrylic），通过反射兼容不同版本的 Wpf.Ui 外观 API
            // TryApplyWpfUiBackdropAcrylic();
        }

        private void TryApplyWpfUiBackdropAcrylic()
        {
            try
            {
                Assembly asm = typeof(Wpf.Ui.Controls.FluentWindow).Assembly;
                Type? typeBg = asm.GetType("Wpf.Ui.Appearance.Background");
                Type? typeBd = asm.GetType("Wpf.Ui.Appearance.Backdrop");
                Type? typeEnum = asm.GetType("Wpf.Ui.Appearance.BackgroundType") ??
                               asm.GetType("Wpf.Ui.Appearance.BackdropType");
                Type? applyOwner = typeBg ?? typeBd;
                if (applyOwner == null || typeEnum == null)
                {
                    return;
                }

                // 尝试解析 Acrylic 枚举值，失败则退回 Mica
                object? enumValue = Enum.TryParse(typeEnum, "Acrylic", ignoreCase: true, out object? acrylic)
                    ? acrylic
                    : (Enum.TryParse(typeEnum, "Mica", ignoreCase: true, out object? mica) ? mica : null);
                if (enumValue == null)
                {
                    return;
                }

                // 寻找签名类似 Apply(Enum, Window, bool, bool) 或 Apply(Enum, Window)
                MethodInfo[] methods = applyOwner.GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .Where(m => m.Name == "Apply")
                    .ToArray();
                foreach (MethodInfo? m in methods)
                {
                    ParameterInfo[] ps = m.GetParameters();
                    if (ps.Length == 4 && ps[0].ParameterType == typeEnum &&
                        typeof(Window).IsAssignableFrom(ps[1].ParameterType))
                    {
                        m.Invoke(null, [enumValue, this, true, true]);
                        return;
                    }

                    if (ps.Length == 2 && ps[0].ParameterType == typeEnum &&
                        typeof(Window).IsAssignableFrom(ps[1].ParameterType))
                    {
                        m.Invoke(null, [enumValue, this]);
                        return;
                    }
                }
            }
            catch
            {
                // 忽略背景应用失败
            }
        }

        /// <summary>
        /// 过滤规则：按类别与关键字筛选。
        /// </summary>
        private bool FilterItem(object obj)
        {
            if (obj is not FunctionView f)
            {
                return false;
            }
            // 控件在 InitializeComponent 期间可能尚未就绪，做空值保护
            if (SearchTextBox == null || CategoryComboBox == null)
            {
                return true;
            }

            var kw = (SearchTextBox.Text ?? string.Empty).Trim();
            var cat = (CategoryComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "全部类别";

            bool passCategory = cat switch
            {
                "全部类别" => true,
                "逻辑" => f.CategoryZh == "逻辑",
                "数学" => f.CategoryZh == "数学",
                "文本" => f.CategoryZh == "文本",
                "统计" => f.CategoryZh == "统计",
                "其他" => f.CategoryZh == "其他",
                _ => true
            };

            if (!passCategory)
            {
                return false;
            }

            if (string.IsNullOrEmpty(kw))
            {
                return true;
            }

            // 模糊匹配：支持子序列匹配，兼容简单包含
            return FuzzyIsMatch(f.Name ?? string.Empty, kw)
                   || FuzzyIsMatch(f.DescriptionZh ?? string.Empty, kw)
                   || FuzzyIsMatch(f.ExampleZh ?? string.Empty, kw)
                   || (f.Name?.IndexOf(kw, StringComparison.OrdinalIgnoreCase) ?? -1) >= 0
                   || (f.DescriptionZh?.IndexOf(kw, StringComparison.OrdinalIgnoreCase) ?? -1) >= 0
                   || (f.ExampleZh?.IndexOf(kw, StringComparison.OrdinalIgnoreCase) ?? -1) >= 0;
        }

        private static bool FuzzyIsMatch(string text, string pattern)
        {
            if (string.IsNullOrEmpty(pattern)) return true;
            if (string.IsNullOrEmpty(text)) return false;

            int ti = 0;
            foreach (char pc in pattern)
            {
                int found = text.IndexOf(pc.ToString(), ti, StringComparison.OrdinalIgnoreCase);
                if (found < 0) return false;
                ti = found + 1;
            }
            return true;
        }

        private void OnFilterChanged(object sender, RoutedEventArgs e)
        {
            _view?.Refresh();
        }

        private void FunctionsGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (FunctionsGrid.SelectedItem is FunctionView f)
            {
                try
                {
                    Clipboard.SetText(f.Name ?? string.Empty);
                }
                catch
                {
                    /* ignore clipboard failures */
                }
            }
        }

        private void CopyNamesButton_Click(object sender, RoutedEventArgs e)
        {
            var names = FunctionsGrid.SelectedItems.Cast<FunctionView>().Select(i => i.Name)
                .Where(n => !string.IsNullOrEmpty(n));
            string text = string.Join(", ", names!);
            try
            {
                Clipboard.SetText(text);
            }
            catch
            {
                /* ignore */
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e) => Close();

        private static string CategoryToZh(CalcEngineFunctionCatalog.FunctionCategory c) => c switch
        {
            CalcEngineFunctionCatalog.FunctionCategory.Logical => "逻辑",
            CalcEngineFunctionCatalog.FunctionCategory.Math => "数学",
            CalcEngineFunctionCatalog.FunctionCategory.Text => "文本",
            CalcEngineFunctionCatalog.FunctionCategory.Statistical => "统计",
            _ => "其他"
        };

        private static string ParmRangeText(int min, int max)
        {
            if (min == max)
            {
                return min.ToString();
            }

            string m = max == int.MaxValue ? "N" : max.ToString();
            return $"{min}~{m}";
        }

        private sealed class FunctionView
        {
            public string Name { get; init; } = string.Empty;
            public string ParmRange { get; init; } = string.Empty;
            public string CategoryZh { get; init; } = string.Empty;
            public string? DescriptionZh { get; init; }
            public string? ExampleZh { get; init; }
            public string[]? ParamNamesZh { get; init; }
            public int ParmMin { get; init; }
            public int ParmMax { get; init; }

            public static FunctionView FromInfo(CalcEngineFunctionCatalog.FunctionInfo info) => new()
            {
                Name = info.Name,
                ParmRange = ParmRangeText(info.ParmMin, info.ParmMax),
                CategoryZh = CategoryToZh(info.Category),
                DescriptionZh = info.DescriptionZh,
                ExampleZh = info.ExampleZh,
                ParamNamesZh = info.ParamNamesZh,
                ParmMin = info.ParmMin,
                ParmMax = info.ParmMax
            };
        }

        private void FunctionsGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FunctionsGrid.SelectedItem is not FunctionView f)
            {
                SigTextBlock.Text = string.Empty;
                ParamsTextBlock.Text = string.Empty;
                return;
            }

            SigTextBlock.Text = BuildSignature(f);
            ParamsTextBlock.Text = BuildParams(f);
        }

        private static string BuildSignature(FunctionView f)
        {
            if (f.ParamNamesZh is { Length: > 0 })
            {
                string names = string.Join(", ", f.ParamNamesZh);
                return $"{f.Name}({names}{(f.ParmMax > f.ParamNamesZh.Length ? ", ..." : string.Empty)})";
            }

            if (f.ParmMin == 0)
            {
                return $"{f.Name}()";
            }

            return $"{f.Name}(…)";
        }

        private static string BuildParams(FunctionView f)
        {
            if (f.ParamNamesZh is { Length: > 0 })
            {
                return string.Join(", ", f.ParamNamesZh);
            }

            return f.ParmMin == f.ParmMax
                ? $"参数: {f.ParmMin}"
                : $"参数: {f.ParmMin} ~ {(f.ParmMax == int.MaxValue ? "N" : f.ParmMax.ToString())}";
        }

        private void InsertToEditorButton_Click(object sender, RoutedEventArgs e)
        {
            if (FunctionsGrid.SelectedItem is not FunctionView f)
            {
                return;
            }

            AvalonFormulaEditor? editor = AvalonFormulaEditor.TryGetLastFocused();
            if (editor == null)
            {
                MessageBox.Show("未找到活动的公式编辑器，请先点击公式编辑器后再试。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            string text = f.ParmMin == 0 ? $"{f.Name}()" : $"{f.Name}()"; // 一律插入 NAME()，光标置于括号内
            editor.InsertAtCaret(text, caretOffsetFromEnd: 1);
            Close();
        }

        private void CopyExampleButton_Click(object sender, RoutedEventArgs e)
        {
            if (FunctionsGrid.SelectedItem is not FunctionView f)
            {
                return;
            }

            string text = string.IsNullOrWhiteSpace(f.ExampleZh)
                ? (f.ParmMin == 0 ? $"{f.Name}()" : $"{f.Name}(…)")
                : f.ExampleZh!;
            try
            {
                Clipboard.SetText(text);
            }
            catch
            {
            }
        }
    }
}
