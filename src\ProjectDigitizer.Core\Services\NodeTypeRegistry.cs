using System;
using System.Collections.Generic;
using System.Linq;

using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Core.Services
{
    /// <summary>
    /// 节点类型注册表 - 管理ModuleType到NodeType的映射和元数据
    /// </summary>
    public static class NodeTypeRegistry
    {
        private static readonly Dictionary<ModuleType, NodeType> _moduleToNodeTypeMapping;
        private static readonly Dictionary<NodeType, NodeTypeMetadata> _nodeTypeMetadata;

        static NodeTypeRegistry()
        {
            _moduleToNodeTypeMapping = InitializeModuleTypeMapping();
            _nodeTypeMetadata = InitializeNodeTypeMetadata();
        }

        /// <summary>
        /// 根据ModuleType获取对应的NodeType
        /// </summary>
        public static NodeType GetNodeType(ModuleType moduleType)
        {
            return _moduleToNodeTypeMapping.TryGetValue(moduleType, out var nodeType)
                ? nodeType
                : NodeType.Transform; // 默认为转换节点
        }

        /// <summary>
        /// 获取NodeType的元数据
        /// </summary>
        public static NodeTypeMetadata GetNodeTypeMetadata(NodeType nodeType)
        {
            return _nodeTypeMetadata.TryGetValue(nodeType, out var metadata)
                ? metadata
                : _nodeTypeMetadata[NodeType.Transform]; // 默认返回转换节点元数据
        }

        /// <summary>
        /// 获取指定NodeType的所有ModuleType
        /// </summary>
        public static IEnumerable<ModuleType> GetModuleTypesByNodeType(NodeType nodeType)
        {
            return _moduleToNodeTypeMapping
                .Where(kvp => kvp.Value == nodeType)
                .Select(kvp => kvp.Key);
        }

        /// <summary>
        /// 初始化ModuleType到NodeType的映射
        /// </summary>
        private static Dictionary<ModuleType, NodeType> InitializeModuleTypeMapping()
        {
            return new Dictionary<ModuleType, NodeType>
            {
                // 输入节点 - 数据源
                { ModuleType.FileInput, NodeType.Input },
                { ModuleType.DatabaseInput, NodeType.Input },
                { ModuleType.APIInput, NodeType.Input },
                { ModuleType.CADInput, NodeType.Input },
                { ModuleType.ExcelInput, NodeType.Input },
                { ModuleType.CSVInput, NodeType.Input },
                { ModuleType.XMLInput, NodeType.Input },
                { ModuleType.JSONInput, NodeType.Input },
                { ModuleType.ManualDataInput, NodeType.Input },

                // 常量节点 - 数学常量
                { ModuleType.PiConstant, NodeType.Input },
                { ModuleType.EConstant, NodeType.Input },
                { ModuleType.GoldenRatioConstant, NodeType.Input },

                // 转换节点 - 数据处理
                { ModuleType.PipeLine, NodeType.Transform },
                { ModuleType.RiserPipe, NodeType.Transform },
                { ModuleType.PressureBox, NodeType.Transform },
                { ModuleType.Excavation, NodeType.Transform },
                { ModuleType.Demolition, NodeType.Transform },
                { ModuleType.AntiCorrosion, NodeType.Transform },
                { ModuleType.LightningProtection, NodeType.Transform },
                { ModuleType.WarningBand, NodeType.Transform },
                { ModuleType.WeldInspection, NodeType.Transform },
                { ModuleType.InstallationTeam, NodeType.Transform },
                { ModuleType.Measures, NodeType.Transform },
                { ModuleType.DataFilter, NodeType.Transform },
                { ModuleType.TagSearch, NodeType.Transform },
                { ModuleType.DataCalculation, NodeType.Transform },
                { ModuleType.DataTransform, NodeType.Transform },
                { ModuleType.DataValidation, NodeType.Transform },
                { ModuleType.DataMerge, NodeType.Transform },
                { ModuleType.DataSort, NodeType.Transform },
                { ModuleType.DataGroup, NodeType.Transform },
                { ModuleType.ArrayExpansion, NodeType.Transform },

                // 输出节点 - 数据目标
                { ModuleType.FileGeneration, NodeType.Output },
                { ModuleType.CADExport, NodeType.Output },
                { ModuleType.ExcelExport, NodeType.Output },
                { ModuleType.NotificationAlert, NodeType.Output },
                { ModuleType.DatabaseOutput, NodeType.Output },
                { ModuleType.ReportGeneration, NodeType.Output },
                { ModuleType.EmailSender, NodeType.Output },
                { ModuleType.PrintOutput, NodeType.Output },
                { ModuleType.WebServiceOutput, NodeType.Output },
                { ModuleType.FTPUpload, NodeType.Output },
                { ModuleType.CloudStorage, NodeType.Output },
                { ModuleType.DialogChat, NodeType.Output },
                { ModuleType.OtherOutput, NodeType.Output },

                // 控制节点 - 流程控制
                { ModuleType.ClickTrigger, NodeType.Control },
                { ModuleType.AssociationTrigger, NodeType.Control },
                { ModuleType.TimedTrigger, NodeType.Control },
                { ModuleType.FileChangeTrigger, NodeType.Control },
                { ModuleType.EnvironmentTrigger, NodeType.Control },
                { ModuleType.ConditionalBranch, NodeType.Control },
                { ModuleType.LoopProcessor, NodeType.Control },
                { ModuleType.ErrorHandler, NodeType.Control },
                { ModuleType.FlowControl, NodeType.Control },
                { ModuleType.ScriptExecutor, NodeType.Control },
                { ModuleType.VariableManager, NodeType.Control },
                { ModuleType.StateManager, NodeType.Control },
                { ModuleType.AIAgent, NodeType.Control }
            };
        }

        /// <summary>
        /// 初始化NodeType元数据
        /// </summary>
        private static Dictionary<NodeType, NodeTypeMetadata> InitializeNodeTypeMetadata()
        {
            return new Dictionary<NodeType, NodeTypeMetadata>
            {
                {
                    NodeType.Input,
                    new NodeTypeMetadata
                    {
                        NodeType = NodeType.Input,
                        Name = "输入节点",
                        Description = "数据源节点，用于从各种数据源读取数据",
                        IconGlyph = "\uE8B7", // 输入图标
                        ColorTheme = "#4CAF50", // 绿色主题
                        SupportedDataTypes = new List<string> { "File", "Database", "API", "CAD", "Excel", "CSV", "XML", "JSON" },
                        AllowMultipleInputs = false,
                        AllowMultipleOutputs = true
                    }
                },
                {
                    NodeType.Transform,
                    new NodeTypeMetadata
                    {
                        NodeType = NodeType.Transform,
                        Name = "转换节点",
                        Description = "数据处理节点，用于数据清洗、转换和计算",
                        IconGlyph = "\uE8AB", // 转换图标
                        ColorTheme = "#2196F3", // 蓝色主题
                        SupportedDataTypes = new List<string> { "Any" },
                        AllowMultipleInputs = true,
                        AllowMultipleOutputs = true
                    }
                },
                {
                    NodeType.Output,
                    new NodeTypeMetadata
                    {
                        NodeType = NodeType.Output,
                        Name = "输出节点",
                        Description = "数据目标节点，用于将处理后的数据输出到各种目标",
                        IconGlyph = "\uE8B5", // 输出图标
                        ColorTheme = "#FF9800", // 橙色主题
                        SupportedDataTypes = new List<string> { "File", "Database", "Report", "Email", "Print", "Web", "Cloud" },
                        AllowMultipleInputs = true,
                        AllowMultipleOutputs = false
                    }
                },
                {
                    NodeType.Control,
                    new NodeTypeMetadata
                    {
                        NodeType = NodeType.Control,
                        Name = "控制节点",
                        Description = "流程控制节点，用于条件判断、循环处理和错误处理",
                        IconGlyph = "\uE8F8", // 控制图标
                        ColorTheme = "#9C27B0", // 紫色主题
                        SupportedDataTypes = new List<string> { "Control", "Logic", "State" },
                        AllowMultipleInputs = true,
                        AllowMultipleOutputs = true
                    }
                }
            };
        }

        /// <summary>
        /// 注册自定义ModuleType到NodeType的映射
        /// </summary>
        public static void RegisterModuleTypeMapping(ModuleType moduleType, NodeType nodeType)
        {
            _moduleToNodeTypeMapping[moduleType] = nodeType;
        }

        /// <summary>
        /// 注册自定义NodeType元数据
        /// </summary>
        public static void RegisterNodeTypeMetadata(NodeType nodeType, NodeTypeMetadata metadata)
        {
            _nodeTypeMetadata[nodeType] = metadata;
        }

        /// <summary>
        /// 获取所有已注册的NodeType
        /// </summary>
        public static IEnumerable<NodeType> GetAllNodeTypes()
        {
            return _nodeTypeMetadata.Keys;
        }

        /// <summary>
        /// 获取所有已注册的ModuleType
        /// </summary>
        public static IEnumerable<ModuleType> GetAllModuleTypes()
        {
            return _moduleToNodeTypeMapping.Keys;
        }
    }
}
