using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows.Media.Animation;

using ProjectDigitizer.Infrastructure.UI.Animation;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 动画管理器
    /// 负责协调和管理所有动画效果，优化性能
    /// </summary>
    public class AnimationManager : IDisposable
    {
        #region 单例模式

        private static readonly Lazy<AnimationManager> _instance =
            new Lazy<AnimationManager>(() => new AnimationManager());

        public static AnimationManager Instance => _instance.Value;

        private AnimationManager()
        {
            _config = AnimationConfigurationService.Instance;
            _activeAnimations = new ConcurrentDictionary<string, AnimationInfo>();
            _animationQueue = new Queue<PendingAnimation>();
            _timer = new Timer(ProcessAnimationQueue, null, TimeSpan.FromMilliseconds(16), TimeSpan.FromMilliseconds(16));
        }

        #endregion

        #region 私有字段

        private readonly AnimationConfigurationService _config;
        private readonly ConcurrentDictionary<string, AnimationInfo> _activeAnimations;
        private readonly Queue<PendingAnimation> _animationQueue;
        private readonly Timer _timer;
        private readonly object _queueLock = new object();
        private bool _disposed = false;

        #endregion

        #region 内部类

        private class AnimationInfo
        {
            public Storyboard Storyboard { get; set; } = null!;
            public AnimationType Type { get; set; }
            public DateTime StartTime { get; set; }
            public string ElementId { get; set; } = string.Empty;
        }

        private class PendingAnimation
        {
            public Storyboard Storyboard { get; set; } = null!;
            public AnimationType Type { get; set; }
            public string ElementId { get; set; } = string.Empty;
            public int Priority { get; set; }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 开始动画
        /// </summary>
        public bool StartAnimation(Storyboard storyboard, AnimationType type, string elementId, int priority = 0)
        {
            if (_disposed || storyboard == null) return false;

            // 检查是否应该播放此类型的动画
            if (!_config.ShouldPlayAnimation(type)) return false;

            // 调整动画速度
            AdjustAnimationSpeed(storyboard);

            // 检查是否超过最大并发动画数
            if (_activeAnimations.Count >= _config.MaxConcurrentAnimations)
            {
                // 添加到队列
                lock (_queueLock)
                {
                    _animationQueue.Enqueue(new PendingAnimation
                    {
                        Storyboard = storyboard,
                        Type = type,
                        ElementId = elementId,
                        Priority = priority
                    });
                }
                return false;
            }

            return ExecuteAnimation(storyboard, type, elementId);
        }

        /// <summary>
        /// 停止动画
        /// </summary>
        public void StopAnimation(string elementId)
        {
            if (_activeAnimations.TryRemove(elementId, out var animationInfo))
            {
                try
                {
                    animationInfo.Storyboard.Stop();
                }
                catch
                {
                    // 忽略停止动画时的异常
                }
            }
        }

        /// <summary>
        /// 停止所有动画
        /// </summary>
        public void StopAllAnimations()
        {
            var animations = _activeAnimations.Values.ToList();
            _activeAnimations.Clear();

            foreach (var animation in animations)
            {
                try
                {
                    animation.Storyboard.Stop();
                }
                catch
                {
                    // 忽略停止动画时的异常
                }
            }

            lock (_queueLock)
            {
                _animationQueue.Clear();
            }
        }

        /// <summary>
        /// 获取活动动画数量
        /// </summary>
        public int GetActiveAnimationCount()
        {
            return _activeAnimations.Count;
        }

        /// <summary>
        /// 获取队列中的动画数量
        /// </summary>
        public int GetQueuedAnimationCount()
        {
            lock (_queueLock)
            {
                return _animationQueue.Count;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行动画
        /// </summary>
        private bool ExecuteAnimation(Storyboard storyboard, AnimationType type, string elementId)
        {
            try
            {
                var animationInfo = new AnimationInfo
                {
                    Storyboard = storyboard,
                    Type = type,
                    StartTime = DateTime.Now,
                    ElementId = elementId
                };

                // 设置动画完成事件
                storyboard.Completed += (s, e) => OnAnimationCompleted(elementId);

                // 添加到活动动画列表
                _activeAnimations[elementId] = animationInfo;

                // 开始动画
                storyboard.Begin();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 动画完成处理
        /// </summary>
        private void OnAnimationCompleted(string elementId)
        {
            _activeAnimations.TryRemove(elementId, out _);
        }

        /// <summary>
        /// 调整动画速度
        /// </summary>
        private void AdjustAnimationSpeed(Storyboard storyboard)
        {
            if (Math.Abs(_config.AnimationSpeed - 1.0) < 0.01) return;

            storyboard.SpeedRatio = _config.AnimationSpeed;
        }

        /// <summary>
        /// 处理动画队列
        /// </summary>
        private void ProcessAnimationQueue(object? state)
        {
            if (_disposed) return;

            // 清理过期的动画
            CleanupExpiredAnimations();

            // 处理队列中的动画
            lock (_queueLock)
            {
                while (_animationQueue.Count > 0 && _activeAnimations.Count < _config.MaxConcurrentAnimations)
                {
                    var pendingAnimation = _animationQueue.Dequeue();
                    ExecuteAnimation(pendingAnimation.Storyboard, pendingAnimation.Type, pendingAnimation.ElementId);
                }
            }
        }

        /// <summary>
        /// 清理过期的动画
        /// </summary>
        private void CleanupExpiredAnimations()
        {
            var expiredAnimations = _activeAnimations
                .Where(kvp => DateTime.Now - kvp.Value.StartTime > TimeSpan.FromMinutes(5))
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var elementId in expiredAnimations)
            {
                StopAnimation(elementId);
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _timer?.Dispose();
            StopAllAnimations();
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
