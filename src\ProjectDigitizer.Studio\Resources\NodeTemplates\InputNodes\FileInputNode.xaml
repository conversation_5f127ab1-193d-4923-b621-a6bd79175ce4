<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 文件输入节点模板 ==========  -->
    <!--  专用于 ModuleType.FileInput 的节点模板  -->

    <!--  文件输入节点内容模板  -->
    <DataTemplate x:Key="FileInputContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  文件图标  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Kind="File"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18" />

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：文件信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  文件路径信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="12"
                                    Kind="FolderOpen"
                                    Margin="0,0,4,0"
                                    Opacity="0.8"
                                    Width="12" />
                                <TextBlock
                                    FontSize="10"
                                    Foreground="White"
                                    MaxWidth="120"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.FilePath, FallbackValue='未选择文件'}"
                                    TextTrimming="CharacterEllipsis"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  浏览文件按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="浏览文件">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="FolderSearch"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="文件状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Warning">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#E8F5E8"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  文件格式  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="格式:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#4CAF50"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White"
                                    Text="{Binding NodeProperties.FileFormat, FallbackValue='Auto'}" />
                            </Border>
                        </Grid>

                        <!--  编码信息  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="编码:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Foreground="#4CAF50"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.Encoding, FallbackValue='UTF-8'}"
                                VerticalAlignment="Center" />
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  包含标题行  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="标题行" />
                                <ipack:PackIconMaterial
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Width="12">
                                    <ipack:PackIconMaterial.Style>
                                        <Style TargetType="ipack:PackIconMaterial">
                                            <Setter Property="Kind" Value="CheckboxMarkedOutline" />
                                            <Setter Property="Foreground" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding NodeProperties.HasHeader}" Value="False">
                                                    <Setter Property="Kind" Value="CheckboxBlankOutline" />
                                                    <Setter Property="Foreground" Value="#999" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ipack:PackIconMaterial.Style>
                                </ipack:PackIconMaterial>
                            </StackPanel>

                            <!--  文件大小  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="大小" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#4CAF50"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.FileSize, FallbackValue='--'}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  文件输入节点主模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="FileInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">

            <!--  使用高级连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  使用专用内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource FileInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
