using System;

using FluentAssertions;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Application.Tests.Services;

public class NodeRegistryTests
{
    private sealed class DummyTemplate : INodeTemplate
    {
        public ModuleType ModuleType => ModuleType.FileGeneration;
        public string Name => "Dummy";
        public string Description => string.Empty;
        public string IconGlyph => string.Empty;
        public NodePropertySchema PropertySchema => new();
        public INode CreateInstance() => throw new NotImplementedException();
        public string DefaultCodeTemplate => string.Empty;
    }

    private sealed class DummyProcessor : INodeProcessor
    {
        public ModuleType SupportedModuleType => ModuleType.FileGeneration;
        public ValidationResult ValidateConfiguration(INode node) => new();
        public IEnumerable<IPortDefinition> GetInputPorts() => Array.Empty<IPortDefinition>();
        public IEnumerable<IPortDefinition> GetOutputPorts() => Array.Empty<IPortDefinition>();
        public NodeExecutionResult Execute(INode node, Dictionary<string, object> inputData) => new();
    }

    [Fact]
    public void Register_And_Resolve_Template_Processor_Should_Work()
    {
        // arrange
        INodeRegistry registry = new NodeRegistry();

        // act
        registry.RegisterTemplate(ModuleType.FileGeneration, () => new DummyTemplate());
        registry.RegisterProcessor(ModuleType.FileGeneration, () => new DummyProcessor());

        // assert
        registry.GetRegisteredModuleTypes().Should().Contain(ModuleType.FileGeneration);
        registry.GetTemplate(ModuleType.FileGeneration).Should().BeOfType<DummyTemplate>();
        registry.GetProcessor(ModuleType.FileGeneration).Should().BeOfType<DummyProcessor>();
    }
}
