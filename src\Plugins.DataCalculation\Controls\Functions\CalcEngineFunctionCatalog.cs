using System;
using System.Collections.Generic;
using System.Linq;

namespace Plugins.DataCalculation.Controls.Functions;

/// <summary>
/// 从第三方 CalcEngine 中提取可用函数的目录，提供中文说明与签名信息。
/// 仅聚合元数据，不改动 CalcEngine 的实现。
/// </summary>
internal static class CalcEngineFunctionCatalog
{
    public enum FunctionCategory
    {
        Logical,
        Math,
        Text,
        Statistical,
        Other
    }

    internal sealed record FunctionInfo
    {
        public required string Name { get; init; }
        public required int ParmMin { get; init; }
        public required int ParmMax { get; init; }
        public string DescriptionZh { get; init; } = "内置函数";
        public string[]? ParamNamesZh { get; init; }
        public string? ExampleZh { get; init; }
        public FunctionCategory Category { get; init; } = FunctionCategory.Other;
    }

    /// <summary>
    /// 加载所有第三方函数，并结合内置的中文元数据生成函数信息列表。
    /// </summary>
    public static IReadOnlyDictionary<string, FunctionInfo> Load()
    {
        var engine = new CalcEngine.CalcEngine();
        var dict = new Dictionary<string, FunctionInfo>(StringComparer.OrdinalIgnoreCase);

        foreach (var kv in engine.Functions)
        {
            var name = kv.Key;
            var def = kv.Value;
            var meta = GetMetadataOrDefault(name, def.ParmMin, def.ParmMax);
            dict[name] = meta;
        }
        return dict;
    }

    private static FunctionInfo GetMetadataOrDefault(string name, int parmMin, int parmMax)
    {
        // 预置常见函数的中文说明/参数/示例（仅覆盖已知项；其余使用通用描述）。
        if (_known.TryGetValue(name, out var info))
        {
            // 与实际注册参数范围对齐（以 CalcEngine 为准）。
            info = info with { ParmMin = parmMin, ParmMax = parmMax };
            return info;
        }

        return new FunctionInfo
        {
            Name = name,
            ParmMin = parmMin,
            ParmMax = parmMax,
            DescriptionZh = parmMin == parmMax
                ? $"内置函数（参数个数: {parmMin}）"
                : $"内置函数（参数个数: {parmMin}~{(parmMax == int.MaxValue ? "N" : parmMax)}）",
            ParamNamesZh = null,
            ExampleZh = parmMin == 0 ? $"示例: {name}()" : BuildExample(name, parmMin, parmMax),
            Category = FunctionCategory.Other
        };
    }

    private static string BuildExample(string name, int parmMin, int parmMax)
    {
        var argCnt = Math.Clamp(parmMin, 1, 3);
        var args = string.Join(", ", Enumerable.Range(1, argCnt).Select(i => $"arg{i}"));
        var suffix = parmMax > parmMin ? ", ..." : string.Empty;
        return $"示例: {name}({args}{suffix})";
    }

    private static readonly Dictionary<string, FunctionInfo> _known = new(StringComparer.OrdinalIgnoreCase)
    {
        // 逻辑
        ["IF"] = new FunctionInfo { Name = "IF", ParmMin = 3, ParmMax = 3, DescriptionZh = "条件判断：条件为真返回第二个参数，否则返回第三个参数", ParamNamesZh = new[] { "条件", "真值", "假值" }, ExampleZh = "示例: IF(A>0, 1, 0)", Category = FunctionCategory.Logical },
        ["AND"] = new FunctionInfo { Name = "AND", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "逻辑与：所有参数为真返回 TRUE", ParamNamesZh = new[] { "条件1", "条件2", "..." }, ExampleZh = "示例: AND(A>0, B<10)", Category = FunctionCategory.Logical },
        ["OR"] = new FunctionInfo { Name = "OR", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "逻辑或：任意参数为真返回 TRUE", ParamNamesZh = new[] { "条件1", "条件2", "..." }, ExampleZh = "示例: OR(A>0, B<10)", Category = FunctionCategory.Logical },
        ["NOT"] = new FunctionInfo { Name = "NOT", ParmMin = 1, ParmMax = 1, DescriptionZh = "逻辑非：布尔值取反", Category = FunctionCategory.Logical },
        ["TRUE"] = new FunctionInfo { Name = "TRUE", ParmMin = 0, ParmMax = 0, DescriptionZh = "布尔常量 TRUE", Category = FunctionCategory.Logical },
        ["FALSE"] = new FunctionInfo { Name = "FALSE", ParmMin = 0, ParmMax = 0, DescriptionZh = "布尔常量 FALSE", Category = FunctionCategory.Logical },

        // 数学/三角
        ["SUM"] = new FunctionInfo { Name = "SUM", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "求和", Category = FunctionCategory.Math },
        ["SUMIF"] = new FunctionInfo { Name = "SUMIF", ParmMin = 2, ParmMax = 3, DescriptionZh = "按条件求和：对满足条件的值求和", Category = FunctionCategory.Math },
        ["ABS"] = new FunctionInfo { Name = "ABS", ParmMin = 1, ParmMax = 1, DescriptionZh = "绝对值", Category = FunctionCategory.Math },
        ["ROUND"] = new FunctionInfo { Name = "ROUND", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "四舍五入到指定小数位", Category = FunctionCategory.Math },
        ["CEILING"] = new FunctionInfo { Name = "CEILING", ParmMin = 1, ParmMax = 2, DescriptionZh = "向上取整，可指定步长", Category = FunctionCategory.Math },
        ["FLOOR"] = new FunctionInfo { Name = "FLOOR", ParmMin = 1, ParmMax = 2, DescriptionZh = "向下取整，可指定步长", Category = FunctionCategory.Math },
        ["INT"] = new FunctionInfo { Name = "INT", ParmMin = 1, ParmMax = 1, DescriptionZh = "取整（向下取整）", Category = FunctionCategory.Math },
        ["SIGN"] = new FunctionInfo { Name = "SIGN", ParmMin = 1, ParmMax = 1, DescriptionZh = "返回数值符号（-1/0/1）", Category = FunctionCategory.Math },
        ["SQRT"] = new FunctionInfo { Name = "SQRT", ParmMin = 1, ParmMax = 1, DescriptionZh = "平方根", Category = FunctionCategory.Math },
        ["POWER"] = new FunctionInfo { Name = "POWER", ParmMin = 2, ParmMax = 2, DescriptionZh = "幂运算", Category = FunctionCategory.Math },
        ["PI"] = new FunctionInfo { Name = "PI", ParmMin = 0, ParmMax = 0, DescriptionZh = "圆周率 π", Category = FunctionCategory.Math },
        ["EXP"] = new FunctionInfo { Name = "EXP", ParmMin = 1, ParmMax = 1, DescriptionZh = "e 的指数", Category = FunctionCategory.Math },
        ["LN"] = new FunctionInfo { Name = "LN", ParmMin = 1, ParmMax = 1, DescriptionZh = "自然对数", Category = FunctionCategory.Math },
        ["LOG"] = new FunctionInfo { Name = "LOG", ParmMin = 1, ParmMax = 2, DescriptionZh = "对数（可指定底）", Category = FunctionCategory.Math },
        ["LOG10"] = new FunctionInfo { Name = "LOG10", ParmMin = 1, ParmMax = 1, DescriptionZh = "以 10 为底的对数", Category = FunctionCategory.Math },
        ["RAND"] = new FunctionInfo { Name = "RAND", ParmMin = 0, ParmMax = 0, DescriptionZh = "返回 0~1 的随机数", Category = FunctionCategory.Math },
        ["RANDBETWEEN"] = new FunctionInfo { Name = "RANDBETWEEN", ParmMin = 2, ParmMax = 2, DescriptionZh = "返回指定范围内的随机整数", Category = FunctionCategory.Math },
        ["SIN"] = new FunctionInfo { Name = "SIN", ParmMin = 1, ParmMax = 1, DescriptionZh = "正弦", Category = FunctionCategory.Math },
        ["COS"] = new FunctionInfo { Name = "COS", ParmMin = 1, ParmMax = 1, DescriptionZh = "余弦", Category = FunctionCategory.Math },
        ["TAN"] = new FunctionInfo { Name = "TAN", ParmMin = 1, ParmMax = 1, DescriptionZh = "正切", Category = FunctionCategory.Math },
        ["SINH"] = new FunctionInfo { Name = "SINH", ParmMin = 1, ParmMax = 1, DescriptionZh = "双曲正弦", Category = FunctionCategory.Math },
        ["COSH"] = new FunctionInfo { Name = "COSH", ParmMin = 1, ParmMax = 1, DescriptionZh = "双曲余弦", Category = FunctionCategory.Math },
        ["TANH"] = new FunctionInfo { Name = "TANH", ParmMin = 1, ParmMax = 1, DescriptionZh = "双曲正切", Category = FunctionCategory.Math },
        ["ATAN"] = new FunctionInfo { Name = "ATAN", ParmMin = 1, ParmMax = 1, DescriptionZh = "反正切", Category = FunctionCategory.Math },
        ["ATAN2"] = new FunctionInfo { Name = "ATAN2", ParmMin = 2, ParmMax = 2, DescriptionZh = "根据 x、y 计算反正切", Category = FunctionCategory.Math },
        ["ASIN"] = new FunctionInfo { Name = "ASIN", ParmMin = 1, ParmMax = 1, DescriptionZh = "反正弦", Category = FunctionCategory.Math },
        ["ACOS"] = new FunctionInfo { Name = "ACOS", ParmMin = 1, ParmMax = 1, DescriptionZh = "反余弦", Category = FunctionCategory.Math },
        ["TRUNC"] = new FunctionInfo { Name = "TRUNC", ParmMin = 1, ParmMax = 1, DescriptionZh = "截断为整数", Category = FunctionCategory.Math },
        ["WEIGHTED"] = new FunctionInfo { Name = "WEIGHTED", ParmMin = 2, ParmMax = 2, DescriptionZh = "加权值：value * weight", Category = FunctionCategory.Math },

        // 统计
        ["AVERAGE"] = new FunctionInfo { Name = "AVERAGE", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "平均值（忽略文本）", Category = FunctionCategory.Statistical },
        ["AVERAGEA"] = new FunctionInfo { Name = "AVERAGEA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "平均值（文本计为0，TRUE计1，FALSE计0）", Category = FunctionCategory.Statistical },
        ["COUNT"] = new FunctionInfo { Name = "COUNT", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "计数（仅数值）", Category = FunctionCategory.Statistical },
        ["COUNTA"] = new FunctionInfo { Name = "COUNTA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "计数（非空）", Category = FunctionCategory.Statistical },
        ["COUNTBLANK"] = new FunctionInfo { Name = "COUNTBLANK", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "空白计数", Category = FunctionCategory.Statistical },
        ["COUNTIF"] = new FunctionInfo { Name = "COUNTIF", ParmMin = 2, ParmMax = 2, DescriptionZh = "按条件计数", Category = FunctionCategory.Statistical },
        ["MAX"] = new FunctionInfo { Name = "MAX", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "最大值", Category = FunctionCategory.Statistical },
        ["MAXA"] = new FunctionInfo { Name = "MAXA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "最大值（文本计为0）", Category = FunctionCategory.Statistical },
        ["MIN"] = new FunctionInfo { Name = "MIN", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "最小值", Category = FunctionCategory.Statistical },
        ["MINA"] = new FunctionInfo { Name = "MINA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "最小值（文本计为0）", Category = FunctionCategory.Statistical },
        ["MEDIAN"] = new FunctionInfo { Name = "MEDIAN", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "中位数", Category = FunctionCategory.Statistical },
        ["RANGE"] = new FunctionInfo { Name = "RANGE", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "极差（最大-最小）", Category = FunctionCategory.Statistical },
        ["STDEV"] = new FunctionInfo { Name = "STDEV", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "样本标准差（仅数值）", Category = FunctionCategory.Statistical },
        ["STDEVA"] = new FunctionInfo { Name = "STDEVA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "样本标准差（文本计0，TRUE=1）", Category = FunctionCategory.Statistical },
        ["STDEVP"] = new FunctionInfo { Name = "STDEVP", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "总体标准差（仅数值）", Category = FunctionCategory.Statistical },
        ["STDEVPA"] = new FunctionInfo { Name = "STDEVPA", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "总体标准差（文本计0，TRUE=1）", Category = FunctionCategory.Statistical },

        // 文本
        ["TEXT"] = new FunctionInfo { Name = "TEXT", ParmMin = 2, ParmMax = 2, DescriptionZh = "按格式将数值转为文本", ParamNamesZh = new[] { "数值", "格式字符串" }, ExampleZh = "示例: TEXT(1234, \"n2\")", Category = FunctionCategory.Text },
        ["LEN"] = new FunctionInfo { Name = "LEN", ParmMin = 1, ParmMax = 1, DescriptionZh = "文本长度", Category = FunctionCategory.Text },
        ["LEFT"] = new FunctionInfo { Name = "LEFT", ParmMin = 1, ParmMax = 2, DescriptionZh = "左侧子串", Category = FunctionCategory.Text },
        ["RIGHT"] = new FunctionInfo { Name = "RIGHT", ParmMin = 1, ParmMax = 2, DescriptionZh = "右侧子串", Category = FunctionCategory.Text },
        ["MID"] = new FunctionInfo { Name = "MID", ParmMin = 3, ParmMax = 3, DescriptionZh = "按起始与长度截取", Category = FunctionCategory.Text },
        ["FIND"] = new FunctionInfo { Name = "FIND", ParmMin = 2, ParmMax = 3, DescriptionZh = "区分大小写查找，返回位置（从1开始，未找到为-1）", Category = FunctionCategory.Text },
        ["SEARCH"] = new FunctionInfo { Name = "SEARCH", ParmMin = 2, ParmMax = 2, DescriptionZh = "不区分大小写查找，返回位置（从1开始，未找到为-1）", Category = FunctionCategory.Text },
        ["SUBSTITUTE"] = new FunctionInfo { Name = "SUBSTITUTE", ParmMin = 3, ParmMax = 4, DescriptionZh = "文本替换（可指定第几次出现）", Category = FunctionCategory.Text },
        ["UPPER"] = new FunctionInfo { Name = "UPPER", ParmMin = 1, ParmMax = 1, DescriptionZh = "转大写", Category = FunctionCategory.Text },
        ["LOWER"] = new FunctionInfo { Name = "LOWER", ParmMin = 1, ParmMax = 1, DescriptionZh = "转小写", Category = FunctionCategory.Text },
        ["PROPER"] = new FunctionInfo { Name = "PROPER", ParmMin = 1, ParmMax = 1, DescriptionZh = "单词首字母大写", Category = FunctionCategory.Text },
        ["REPLACE"] = new FunctionInfo { Name = "REPLACE", ParmMin = 4, ParmMax = 4, DescriptionZh = "按位置替换子串", Category = FunctionCategory.Text },
        ["REPT"] = new FunctionInfo { Name = "REPT", ParmMin = 2, ParmMax = 2, DescriptionZh = "重复文本", Category = FunctionCategory.Text },
        ["CONCATENATE"] = new FunctionInfo { Name = "CONCATENATE", ParmMin = 1, ParmMax = int.MaxValue, DescriptionZh = "拼接文本", Category = FunctionCategory.Text },
        ["CHAR"] = new FunctionInfo { Name = "CHAR", ParmMin = 1, ParmMax = 1, DescriptionZh = "按编码返回字符", Category = FunctionCategory.Text },
        ["CODE"] = new FunctionInfo { Name = "CODE", ParmMin = 1, ParmMax = 1, DescriptionZh = "返回首字符的编码", Category = FunctionCategory.Text },
        ["T"] = new FunctionInfo { Name = "T", ParmMin = 1, ParmMax = 1, DescriptionZh = "转文本", Category = FunctionCategory.Text },
        ["VALUE"] = new FunctionInfo { Name = "VALUE", ParmMin = 1, ParmMax = 1, DescriptionZh = "按当前区域解析为数值", Category = FunctionCategory.Text },
    };
}
