using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Services
{
    /// <summary>
    /// 项目文件管理服务
    /// 负责项目文件的保存、加载和管理
    /// </summary>
    public class ProjectFileService : IProjectFileService
    {
        private readonly ILogger<ProjectFileService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public ProjectFileService(ILogger<ProjectFileService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
            };
        }

        /// <summary>
        /// 保存项目文件
        /// </summary>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="filePath">文件路径</param>
        public async Task SaveProjectAsync(ProjectFile projectFile, string filePath)
        {
            try
            {
                _logger.LogInformation("开始保存项目文件到: {FilePath}", filePath);

                // 验证输入
                if (projectFile == null)
                    throw new ArgumentNullException(nameof(projectFile));

                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    _logger.LogDebug("创建目录: {Directory}", directory);
                }

                // 更新最后修改时间
                projectFile.LastModifiedTime = DateTime.Now;

                // 序列化为JSON
                var json = JsonSerializer.Serialize(projectFile, _jsonOptions);

                // 写入文件
                await File.WriteAllTextAsync(filePath, json);

                _logger.LogInformation("项目文件保存成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存项目文件失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 加载项目文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>项目文件数据</returns>
        public async Task<ProjectFile> LoadProjectAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("开始加载项目文件: {FilePath}", filePath);

                // 验证输入
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"项目文件不存在: {filePath}");

                // 读取文件内容
                var json = await File.ReadAllTextAsync(filePath);

                // 反序列化
                var projectFile = JsonSerializer.Deserialize<ProjectFile>(json, _jsonOptions);

                if (projectFile == null)
                    throw new InvalidOperationException("项目文件格式无效");

                _logger.LogInformation("项目文件加载成功: {FilePath}", filePath);
                return projectFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载项目文件失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 创建新的项目文件
        /// </summary>
        /// <param name="projectName">项目名称</param>
        /// <param name="description">项目描述</param>
        /// <returns>新的项目文件</returns>
        public ProjectFile CreateNewProject(string projectName, string? description = null)
        {
            try
            {
                _logger.LogInformation("创建新项目: {ProjectName}", projectName);

                if (string.IsNullOrWhiteSpace(projectName))
                    throw new ArgumentException("项目名称不能为空", nameof(projectName));

                var projectFile = new ProjectFile
                {
                    ProjectInfo = new ProjectInfo
                    {
                        Name = projectName,
                        Description = description ?? string.Empty,
                        CreateTime = DateTime.Now
                    },
                    CreatedTime = DateTime.Now,
                    LastModifiedTime = DateTime.Now,
                    Version = "1.0.0",
                    Templates = new List<TemplateData>()
                };

                _logger.LogInformation("新项目创建成功: {ProjectName}", projectName);
                return projectFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建新项目失败: {ProjectName}", projectName);
                throw;
            }
        }

        /// <summary>
        /// 验证项目文件格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateProjectFileAsync(string filePath)
        {
            try
            {
                _logger.LogDebug("验证项目文件格式: {FilePath}", filePath);

                if (!File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath);
                var projectFile = JsonSerializer.Deserialize<ProjectFile>(json, _jsonOptions);

                return projectFile != null && !string.IsNullOrWhiteSpace(projectFile.ProjectInfo?.Name);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "项目文件格式验证失败: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 获取项目文件信息（不完全加载）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>项目文件基本信息</returns>
        public async Task<ProjectFileInfo> GetProjectInfoAsync(string filePath)
        {
            try
            {
                _logger.LogDebug("获取项目文件信息: {FilePath}", filePath);

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"项目文件不存在: {filePath}");

                var json = await File.ReadAllTextAsync(filePath);
                using var document = JsonDocument.Parse(json);
                var root = document.RootElement;

                var projectName = "未知项目";
                var description = "";

                // 尝试从projectInfo中获取信息
                if (root.TryGetProperty("projectInfo", out var projectInfoElement))
                {
                    if (projectInfoElement.TryGetProperty("name", out var nameElement))
                        projectName = nameElement.GetString() ?? "未知项目";
                    if (projectInfoElement.TryGetProperty("description", out var descElement))
                        description = descElement.GetString() ?? "";
                }

                var info = new ProjectFileInfo
                {
                    FilePath = filePath,
                    ProjectName = projectName,
                    Description = description,
                    Version = root.TryGetProperty("version", out var versionElement) ? versionElement.GetString() ?? "1.0.0" : "1.0.0",
                    CreatedTime = root.TryGetProperty("createdTime", out var createdElement) && createdElement.TryGetDateTime(out var created) ? created : DateTime.MinValue,
                    LastModifiedTime = root.TryGetProperty("lastModifiedTime", out var modifiedElement) && modifiedElement.TryGetDateTime(out var modified) ? modified : DateTime.MinValue,
                    FileSize = new FileInfo(filePath).Length
                };

                return info;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取项目文件信息失败: {FilePath}", filePath);
                throw;
            }
        }
    }
}
