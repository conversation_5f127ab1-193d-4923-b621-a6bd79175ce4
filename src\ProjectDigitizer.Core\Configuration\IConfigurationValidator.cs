namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 配置验证接口
    /// </summary>
    public interface IConfigurationValidator
    {
        /// <summary>
        /// 验证配置对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configuration">配置对象</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult Validate<T>(T configuration) where T : class;

        /// <summary>
        /// 验证应用程序设置
        /// </summary>
        /// <param name="settings">应用程序设置</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult ValidateApplicationSettings(ApplicationSettings settings);
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<ConfigurationValidationError> Errors { get; set; } = new();

        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<ConfigurationValidationWarning> Warnings { get; set; } = new();

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="errorMessage">错误消息</param>
        public void AddError(string propertyName, string errorMessage)
        {
            Errors.Add(new ConfigurationValidationError
            {
                PropertyName = propertyName,
                ErrorMessage = errorMessage
            });
            IsValid = false;
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="warningMessage">警告消息</param>
        public void AddWarning(string propertyName, string warningMessage)
        {
            Warnings.Add(new ConfigurationValidationWarning
            {
                PropertyName = propertyName,
                WarningMessage = warningMessage
            });
        }
    }

    /// <summary>
    /// 配置验证错误
    /// </summary>
    public class ConfigurationValidationError
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; set; } = string.Empty;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 配置验证警告
    /// </summary>
    public class ConfigurationValidationWarning
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string WarningMessage { get; set; } = string.Empty;
    }
}
