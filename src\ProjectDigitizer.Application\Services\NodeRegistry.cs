using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Application.Services;

/// <summary>
/// 线程安全的节点注册表实现。
/// </summary>
public class NodeRegistry : INodeRegistry
{
    private readonly ConcurrentDictionary<ModuleType, Func<INodeTemplate>> _templates = new();
    private readonly ConcurrentDictionary<ModuleType, Func<INodeProcessor>> _processors = new();

    public void Register(ModuleType moduleType, Func<INodeTemplate> templateFactory, Func<INodeProcessor> processorFactory)
    {
        if (templateFactory is null) throw new ArgumentNullException(nameof(templateFactory));
        if (processorFactory is null) throw new ArgumentNullException(nameof(processorFactory));

        _templates[moduleType] = templateFactory;
        _processors[moduleType] = processorFactory;
    }

    public void RegisterTemplate(ModuleType moduleType, Func<INodeTemplate> templateFactory)
    {
        if (templateFactory is null) throw new ArgumentNullException(nameof(templateFactory));
        _templates[moduleType] = templateFactory;
    }

    public void RegisterProcessor(ModuleType moduleType, Func<INodeProcessor> processorFactory)
    {
        if (processorFactory is null) throw new ArgumentNullException(nameof(processorFactory));
        _processors[moduleType] = processorFactory;
    }

    public INodeTemplate? GetTemplate(ModuleType moduleType)
    {
        return _templates.TryGetValue(moduleType, out var factory) ? factory() : null;
    }

    public INodeProcessor? GetProcessor(ModuleType moduleType)
    {
        return _processors.TryGetValue(moduleType, out var factory) ? factory() : null;
    }

    public IReadOnlyCollection<ModuleType> GetRegisteredModuleTypes()
    {
        var keys = new HashSet<ModuleType>(_templates.Keys);
        keys.UnionWith(_processors.Keys);
        return keys;
    }
}

