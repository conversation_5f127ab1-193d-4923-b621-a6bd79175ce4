<Window x:Class="ProjectDigitizer.Studio.Controls.Functions.AllFunctionsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
        Title="选择函数"
        Style="{StaticResource Dialog.Window}"
        ResizeMode="CanResizeWithGrip"
        WindowStartupLocation="CenterOwner">
    <!-- 统一现代化对话框布局：标题在窗口模板中，内容自带留白 -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部：搜索与标题区 -->
        <DockPanel Grid.Row="0" LastChildFill="True" Margin="0,8,0,0">
            <StackPanel Orientation="Horizontal" DockPanel.Dock="Left" VerticalAlignment="Center">
                <ipack:PackIconMaterial Kind="FunctionVariant" Width="18" Height="18" Foreground="{StaticResource Brush.TextSecondary}" Margin="0,0,8,0"/>
                <TextBlock Text="函数库" FontWeight="SemiBold" VerticalAlignment="Center"/>
            </StackPanel>
            <Grid Height="34" Margin="8,0,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <ipack:PackIconMaterial Grid.Column="0" Kind="Magnify" Foreground="{StaticResource Brush.Placeholder}" VerticalAlignment="Center" Margin="8,0"/>
                <TextBox x:Name="SearchBox"
                         Grid.Column="1"
                         Margin="4,0"
                         MinWidth="320"/>
                <Button Grid.Column="2"
                        Style="{StaticResource Button.Text}"
                        Content="清除"
                        Click="OnClearSearch"/>
            </Grid>
        </DockPanel>

        <!-- 中部：函数列表 -->
        <ListView x:Name="FunctionList" Grid.Row="1" Margin="0,8,0,8" MouseDoubleClick="OnCommit" SelectionMode="Single">
            <ListView.View>
                <GridView AllowsColumnReorder="True">
                    <GridViewColumn Header="名称" Width="160" DisplayMemberBinding="{Binding Name}"/>
                    <GridViewColumn Header="显示名" Width="180" DisplayMemberBinding="{Binding DisplayName}"/>
                    <GridViewColumn Header="签名" Width="280" DisplayMemberBinding="{Binding Signature}"/>
                </GridView>
            </ListView.View>
        </ListView>

        <!-- 底部：操作按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,4">
            <Button x:Name="OkButton" Style="{StaticResource Button.Primary}" Content="插入" MinWidth="96" Margin="0,0,8,0" Click="OnCommit" IsDefault="True"/>
            <Button Style="{StaticResource Button.Secondary}" Content="取消" MinWidth="96" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
