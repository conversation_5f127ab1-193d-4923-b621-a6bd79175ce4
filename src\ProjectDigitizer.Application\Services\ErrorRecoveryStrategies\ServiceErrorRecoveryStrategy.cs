using System;
using System.Threading.Tasks;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Exceptions;

namespace ProjectDigitizer.Application.Services.ErrorRecoveryStrategies;

/// <summary>
/// 服务错误恢复策略
/// </summary>
public class ServiceErrorRecoveryStrategy : IErrorRecoveryStrategy
{
    public int Priority => 20;

    public bool CanRecover(Exception exception)
    {
        return exception is ServiceException or CoordinationException or OperationTimeoutException;
    }

    public async Task<RecoveryResult> RecoverAsync(Exception exception, object? context)
    {
        await Task.Delay(100); // 模拟恢复延迟

        return exception switch
        {
            ServiceException serviceEx => await RecoverServiceException(serviceEx, context),
            CoordinationException coordEx => await RecoverCoordinationException(coordEx, context),
            OperationTimeoutException timeoutEx => await RecoverTimeoutException(timeoutEx, context),
            _ => RecoveryResult.Failure("不支持的异常类型")
        };
    }

    private async Task<RecoveryResult> RecoverServiceException(ServiceException exception, object? context)
    {
        // 对于服务异常，可以尝试重试
        await Task.Delay(50);

        return RecoveryResult.Success(
            $"服务 {exception.ServiceName} 异常已记录，建议稍后重试",
            new { ServiceName = exception.ServiceName, CanRetry = true });
    }

    private async Task<RecoveryResult> RecoverCoordinationException(CoordinationException exception, object? context)
    {
        // 对于协调异常，需要检查各个服务的状态
        await Task.Delay(50);

        return RecoveryResult.Failure(
            $"多服务协调失败，涉及服务: {string.Join(", ", exception.InvolvedServices)}",
            requiresUserIntervention: true);
    }

    private async Task<RecoveryResult> RecoverTimeoutException(OperationTimeoutException exception, object? context)
    {
        // 对于超时异常，可以建议用户重试
        await Task.Delay(50);

        return RecoveryResult.Success(
            $"操作 {exception.OperationName} 超时，建议检查网络连接后重试",
            new { OperationName = exception.OperationName, Timeout = exception.Timeout });
    }
}
