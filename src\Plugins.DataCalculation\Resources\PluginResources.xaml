<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:dc="clr-namespace:Plugins.DataCalculation">
    <!--  合并插件内资源映射：
          - NodeTemplate.ModuleType.DataCalculation: 节点模板（在 NodeTemplates 中定义）
          - PropertyPanel.ModuleType.DataCalculation: 属性面板（DynamicPropertyPanel 使用）
    -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/Plugins.DataCalculation;component/Resources/NodeTemplates/TransformNodes/DataCalculationNode.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!--  已迁移到新的 Inspector 系统，禁用旧的 DynamicPropertyPanel 入口，避免与主窗口重复显示  -->
    <!-- <DataTemplate x:Key="PropertyPanel.ModuleType.DataCalculation">
        <dc:DataCalculationComponent />
    </DataTemplate> -->
</ResourceDictionary>
