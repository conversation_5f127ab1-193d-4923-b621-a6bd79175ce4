using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;

using ProjectDigitizer.Core.Services;

namespace ProjectDigitizer.Studio.Controls.Functions
{
    public partial class AllFunctionsDialog : Window
    {
        public string? SelectedText { get; private set; }

        private readonly List<ExcelFunctionCatalog.ExcelFunctionInfo> _all;
        private readonly DispatcherTimer _debounceTimer;

        public AllFunctionsDialog()
        {
            InitializeComponent();
            _all = ExcelFunctionCatalog.GetInfoMap().Values.Distinct().OrderBy(f => f.Name).ToList();
            FunctionList.ItemsSource = _all;

            // 轻微防抖，避免大列表频繁刷新造成卡顿
            _debounceTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(160) };
            _debounceTimer.Tick += (_, __) =>
            {
                _debounceTimer.Stop();
                ApplyFilter();
            };

            SearchBox.TextChanged += (_, __) =>
            {
                _debounceTimer.Stop();
                _debounceTimer.Start();
            };

            // 支持 Enter 提交
            SearchBox.KeyDown += (s, e) =>
            {
                if (e.Key == Key.Enter)
                {
                    ApplyFilter();
                    OnCommit(s, new RoutedEventArgs());
                    e.Handled = true;
                }
            };
        }

        private void ApplyFilter()
        {
            var q = (SearchBox.Text ?? string.Empty).Trim();
            if (string.IsNullOrEmpty(q))
            {
                FunctionList.ItemsSource = _all;
                return;
            }

            // 简单排序：优先名称开头匹配，其次名称包含、别名、签名
            var filtered = _all
                .Select(f => (f, score: Score(f, q)))
                .Where(x => x.score > 0)
                .OrderByDescending(x => x.score)
                .ThenBy(x => x.f.Name)
                .Select(x => x.f)
                .ToList();

            FunctionList.ItemsSource = filtered;
        }

        private static int Score(ExcelFunctionCatalog.ExcelFunctionInfo f, string q)
        {
            var score = 0;
            // 名称优先级最高
            var name = f.Name ?? string.Empty;
            if (name.StartsWith(q, StringComparison.OrdinalIgnoreCase)) score += 200;
            else if (name.Contains(q, StringComparison.OrdinalIgnoreCase)) score += 120;

            // 别名次之
            if (f.Aliases?.Any() == true)
            {
                if (f.Aliases.Any(a => a.StartsWith(q, StringComparison.OrdinalIgnoreCase))) score += 90;
                else if (f.Aliases.Any(a => a.Contains(q, StringComparison.OrdinalIgnoreCase))) score += 60;
            }

            // 显示名/签名作为补充
            if (!string.IsNullOrEmpty(f.DisplayName) && f.DisplayName.Contains(q, StringComparison.OrdinalIgnoreCase)) score += 50;
            if (!string.IsNullOrEmpty(f.Signature) && f.Signature.Contains(q, StringComparison.OrdinalIgnoreCase)) score += 30;

            return score;
        }

        private void OnClearSearch(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(SearchBox.Text))
            {
                SearchBox.Clear();
                FunctionList.ItemsSource = _all;
            }
        }

        private void OnCommit(object? sender, RoutedEventArgs e)
        {
            if (FunctionList.SelectedItem is ExcelFunctionCatalog.ExcelFunctionInfo info)
            {
                // 插入标准名称，并附带括号，方便继续输入参数
                SelectedText = info.Name + "()";
                DialogResult = true;
                Close();
            }
            else if (FunctionList.Items.Count > 0)
            {
                FunctionList.SelectedIndex = 0;
            }
        }
    }
}
