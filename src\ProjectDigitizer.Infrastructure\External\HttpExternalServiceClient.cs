using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Infrastructure.External;

/// <summary>
/// 基于HttpClient的外部服务客户端实现
/// </summary>
public class HttpExternalServiceClient : IExternalServiceClient, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<HttpExternalServiceClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private bool _disposed = false;

    public HttpExternalServiceClient(HttpClient httpClient, ILogger<HttpExternalServiceClient> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    /// <summary>
    /// 发送HTTP GET请求
    /// </summary>
    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogDebug("发送GET请求: {Endpoint}", endpoint);

            var stopwatch = Stopwatch.StartNew();
            var response = await _httpClient.GetAsync(endpoint, cancellationToken);
            stopwatch.Stop();

            _logger.LogDebug("GET请求完成: {Endpoint}, 状态码: {StatusCode}, 耗时: {ElapsedMs}ms",
                endpoint, response.StatusCode, stopwatch.ElapsedMilliseconds);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<T>(_jsonOptions, cancellationToken);
                return result;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("GET请求失败: {Endpoint}, 状态码: {StatusCode}, 错误: {Error}",
                    endpoint, response.StatusCode, errorContent);
                return null;
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP GET请求异常: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP GET请求失败: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "HTTP GET请求超时: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP GET请求超时: {endpoint}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GET请求发生未知错误: {Endpoint}", endpoint);
            throw new InfrastructureException($"GET请求失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 发送HTTP POST请求
    /// </summary>
    public async Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class
    {
        try
        {
            _logger.LogDebug("发送POST请求: {Endpoint}", endpoint);

            var stopwatch = Stopwatch.StartNew();
            var response = await _httpClient.PostAsJsonAsync(endpoint, request, _jsonOptions, cancellationToken);
            stopwatch.Stop();

            _logger.LogDebug("POST请求完成: {Endpoint}, 状态码: {StatusCode}, 耗时: {ElapsedMs}ms",
                endpoint, response.StatusCode, stopwatch.ElapsedMilliseconds);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<TResponse>(_jsonOptions, cancellationToken);
                return result;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("POST请求失败: {Endpoint}, 状态码: {StatusCode}, 错误: {Error}",
                    endpoint, response.StatusCode, errorContent);
                return null;
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP POST请求异常: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP POST请求失败: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "HTTP POST请求超时: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP POST请求超时: {endpoint}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "POST请求发生未知错误: {Endpoint}", endpoint);
            throw new InfrastructureException($"POST请求失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 发送HTTP PUT请求
    /// </summary>
    public async Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class
    {
        try
        {
            _logger.LogDebug("发送PUT请求: {Endpoint}", endpoint);

            var stopwatch = Stopwatch.StartNew();
            var response = await _httpClient.PutAsJsonAsync(endpoint, request, _jsonOptions, cancellationToken);
            stopwatch.Stop();

            _logger.LogDebug("PUT请求完成: {Endpoint}, 状态码: {StatusCode}, 耗时: {ElapsedMs}ms",
                endpoint, response.StatusCode, stopwatch.ElapsedMilliseconds);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<TResponse>(_jsonOptions, cancellationToken);
                return result;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("PUT请求失败: {Endpoint}, 状态码: {StatusCode}, 错误: {Error}",
                    endpoint, response.StatusCode, errorContent);
                return null;
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP PUT请求异常: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP PUT请求失败: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "HTTP PUT请求超时: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP PUT请求超时: {endpoint}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PUT请求发生未知错误: {Endpoint}", endpoint);
            throw new InfrastructureException($"PUT请求失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 发送HTTP DELETE请求
    /// </summary>
    public async Task<bool> DeleteAsync(string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("发送DELETE请求: {Endpoint}", endpoint);

            var stopwatch = Stopwatch.StartNew();
            var response = await _httpClient.DeleteAsync(endpoint, cancellationToken);
            stopwatch.Stop();

            _logger.LogDebug("DELETE请求完成: {Endpoint}, 状态码: {StatusCode}, 耗时: {ElapsedMs}ms",
                endpoint, response.StatusCode, stopwatch.ElapsedMilliseconds);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("DELETE请求失败: {Endpoint}, 状态码: {StatusCode}, 错误: {Error}",
                    endpoint, response.StatusCode, errorContent);
            }

            return response.IsSuccessStatusCode;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP DELETE请求异常: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP DELETE请求失败: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "HTTP DELETE请求超时: {Endpoint}", endpoint);
            throw new InfrastructureException($"HTTP DELETE请求超时: {endpoint}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DELETE请求发生未知错误: {Endpoint}", endpoint);
            throw new InfrastructureException($"DELETE请求失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    public async Task<ServiceHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        var healthStatus = new ServiceHealthStatus
        {
            CheckTime = DateTime.UtcNow
        };

        try
        {
            _logger.LogDebug("检查服务健康状态");

            var stopwatch = Stopwatch.StartNew();

            // 尝试发送健康检查请求
            var response = await _httpClient.GetAsync("health", cancellationToken);

            stopwatch.Stop();
            healthStatus.ResponseTime = stopwatch.Elapsed;

            if (response.IsSuccessStatusCode)
            {
                healthStatus.IsHealthy = true;
                healthStatus.Message = "服务正常";

                try
                {
                    var content = await response.Content.ReadAsStringAsync(cancellationToken);
                    if (!string.IsNullOrEmpty(content))
                    {
                        var healthData = JsonSerializer.Deserialize<Dictionary<string, object>>(content, _jsonOptions);
                        if (healthData != null)
                        {
                            healthStatus.Details = healthData;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析健康检查响应时发生错误");
                }
            }
            else
            {
                healthStatus.IsHealthy = false;
                healthStatus.Message = $"服务返回错误状态码: {response.StatusCode}";
            }

            _logger.LogDebug("健康检查完成: {IsHealthy}, 响应时间: {ResponseTimeMs}ms",
                healthStatus.IsHealthy, healthStatus.ResponseTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            healthStatus.IsHealthy = false;
            healthStatus.Message = $"健康检查失败: {ex.Message}";

            _logger.LogError(ex, "服务健康检查异常");
        }

        return healthStatus;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _httpClient?.Dispose();
            _disposed = true;
        }
    }
}
