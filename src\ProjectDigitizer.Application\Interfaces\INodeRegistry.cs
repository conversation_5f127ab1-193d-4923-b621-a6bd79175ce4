using System;
using System.Collections.Generic;
using System.Reflection;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 节点注册表：集中管理各节点的模板与处理器映射关系。
/// </summary>
public interface INodeRegistry
{
    /// <summary>
    /// 注册某个模块类型对应的模板与处理器。
    /// </summary>
    /// <param name="moduleType">模块类型</param>
    /// <param name="templateFactory">模板工厂</param>
    /// <param name="processorFactory">处理器工厂</param>
    void Register(ModuleType moduleType, Func<INodeTemplate> templateFactory, Func<INodeProcessor> processorFactory);

    /// <summary>
    /// 仅注册模板工厂。
    /// </summary>
    void RegisterTemplate(ModuleType moduleType, Func<INodeTemplate> templateFactory);

    /// <summary>
    /// 仅注册处理器工厂。
    /// </summary>
    void RegisterProcessor(ModuleType moduleType, Func<INodeProcessor> processorFactory);

    /// <summary>
    /// 获取模板实例。
    /// </summary>
    INodeTemplate? GetTemplate(ModuleType moduleType);

    /// <summary>
    /// 获取处理器实例。
    /// </summary>
    INodeProcessor? GetProcessor(ModuleType moduleType);

    /// <summary>
    /// 列出已注册的模块类型。
    /// </summary>
    IReadOnlyCollection<ModuleType> GetRegisteredModuleTypes();
}

