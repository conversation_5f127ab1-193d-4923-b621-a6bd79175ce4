using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Performance;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Extensions;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Tests.Services;

/// <summary>
/// ProjectFileAdapter 测试类
/// </summary>
public class ProjectFileAdapterTests : IDisposable
{
    private readonly ProjectFileAdapter _adapter;
    private readonly Mock<IProjectFileService> _projectFileServiceMock;
    private readonly Mock<ILogger<ProjectFileAdapter>> _loggerMock;
    private readonly Mock<ICanvasVirtualizationManager> _virtualizationManagerMock;
    private readonly Mock<IPerformanceOptimizedConnectorManager> _connectorManagerMock;
    private readonly Mock<INodeLayoutService> _nodeLayoutServiceMock;
    private readonly Mock<INodeRegistry> _nodeRegistryMock;
    private readonly string _testDirectory;

    public ProjectFileAdapterTests()
    {
        _projectFileServiceMock = new Mock<IProjectFileService>();
        _loggerMock = new Mock<ILogger<ProjectFileAdapter>>();
        _virtualizationManagerMock = new Mock<ICanvasVirtualizationManager>();
        _connectorManagerMock = new Mock<IPerformanceOptimizedConnectorManager>();
        _nodeLayoutServiceMock = new Mock<INodeLayoutService>();
        _adapter = new ProjectFileAdapter(_projectFileServiceMock.Object, _loggerMock.Object);
        _nodeRegistryMock = new Mock<INodeRegistry>();

        // 创建测试目录
        _testDirectory = Path.Combine(Path.GetTempPath(), "ProjectDigitizerAdapterTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);
    }

    private CanvasViewModel CreateCanvasViewModel()
    {
        return new CanvasViewModel(
            _virtualizationManagerMock.Object,
            _connectorManagerMock.Object,
            _nodeLayoutServiceMock.Object,
            _nodeRegistryMock.Object);
    }

    [Fact]
    public void Constructor_WithNullProjectFileService_ShouldThrowArgumentNullException()
    {
        // Arrange
        var logger = new Mock<ILogger<ProjectFileAdapter>>();

        // Act & Assert
        var exception = Assert.Throws<ArgumentNullException>(() => new ProjectFileAdapter(null!, logger.Object));
        exception.ParamName.Should().Be("projectFileService");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange
        var service = new Mock<IProjectFileService>();

        // Act & Assert
        var exception = Assert.Throws<ArgumentNullException>(() => new ProjectFileAdapter(service.Object, null!));
        exception.ParamName.Should().Be("logger");
    }

    [Fact]
    public async Task SaveProjectAsync_ShouldCallUnderlyingService()
    {
        // Arrange
        var projectFile = new ProjectFile();
        var filePath = "test.json";

        // Act
        await _adapter.SaveProjectAsync(projectFile, filePath);

        // Assert
        _projectFileServiceMock.Verify(s => s.SaveProjectAsync(projectFile, filePath), Times.Once);
    }

    [Fact]
    public async Task LoadProjectAsync_ShouldCallUnderlyingService()
    {
        // Arrange
        var filePath = "test.json";
        var expectedProject = new ProjectFile();
        _projectFileServiceMock.Setup(s => s.LoadProjectAsync(filePath))
            .ReturnsAsync(expectedProject);

        // Act
        var result = await _adapter.LoadProjectAsync(filePath);

        // Assert
        result.Should().BeSameAs(expectedProject);
        _projectFileServiceMock.Verify(s => s.LoadProjectAsync(filePath), Times.Once);
    }

    [Fact]
    public void CreateNewProject_ShouldCallUnderlyingService()
    {
        // Arrange
        var projectName = "测试项目";
        var description = "测试描述";
        var expectedProject = new ProjectFile();
        _projectFileServiceMock.Setup(s => s.CreateNewProject(projectName, description))
            .Returns(expectedProject);

        // Act
        var result = _adapter.CreateNewProject(projectName, description);

        // Assert
        result.Should().BeSameAs(expectedProject);
        _projectFileServiceMock.Verify(s => s.CreateNewProject(projectName, description), Times.Once);
    }

    [Fact]
    public void CreateProjectFileFromCanvas_WithEmptyCanvas_ShouldCreateValidProjectFile()
    {
        // Arrange
        var canvasViewModel = CreateCanvasViewModel();
        canvasViewModel.ViewportCenter = new Point(100, 200);
        canvasViewModel.ViewportZoom = 1.5;
        var projectInfo = new ProjectInfo { Name = "测试项目" };

        // Act
        var result = _adapter.CreateProjectFileFromCanvas(canvasViewModel, projectInfo);

        // Assert
        result.Should().NotBeNull();
        result.ProjectInfo.Should().BeSameAs(projectInfo);
        result.CanvasData.Should().NotBeNull();
        result.CanvasData.Nodes.Should().BeEmpty();
        result.CanvasData.Connections.Should().BeEmpty();
        result.CanvasData.Settings.OffsetX.Should().Be(100);
        result.CanvasData.Settings.OffsetY.Should().Be(200);
        result.CanvasData.Settings.ZoomLevel.Should().Be(1.5);
    }

    [Fact]
    public void CreateProjectFileFromCanvas_WithNodes_ShouldConvertNodesCorrectly()
    {
        // Arrange
        var module = new ModuleModel
        {
            Id = "test-module-1",
            Type = ModuleType.FileInput,
            Parameters = new Dictionary<string, object> { { "param1", "value1" } }
        };

        var nodeViewModel = new ModuleNodeViewModel();
        nodeViewModel.Title = "测试节点";
        nodeViewModel.Location = new Point(50, 100);
        nodeViewModel.IsExpanded = true;
        nodeViewModel.IsEnabled = true;
        nodeViewModel.Module = module;

        var canvasViewModel = CreateCanvasViewModel();
        canvasViewModel.Nodes.Add(nodeViewModel);
        canvasViewModel.ViewportCenter = new Point(0, 0);
        canvasViewModel.ViewportZoom = 1.0;

        // Act
        var result = _adapter.CreateProjectFileFromCanvas(canvasViewModel);

        // Assert
        result.CanvasData.Nodes.Should().HaveCount(1);
        var nodeData = result.CanvasData.Nodes[0];
        nodeData.Id.Should().Be("test-module-1");
        // 注意：Title来自于nodeViewModel.Title，而不是module的属性
        nodeData.Title.Should().Be("测试节点");
        nodeData.Type.Should().Be(ModuleType.FileInput);
        nodeData.X.Should().Be(50);
        nodeData.Y.Should().Be(100);
        nodeData.IsExpanded.Should().BeTrue();
        nodeData.IsEnabled.Should().BeTrue();
        nodeData.PropertyValues.Should().ContainKey("param1");
        nodeData.PropertyValues["param1"].Should().Be("value1");
    }

    [Fact]
    public void RestoreCanvasFromProjectFile_WithEmptyProjectFile_ShouldClearCanvas()
    {
        // Arrange
        var canvasViewModel = CreateCanvasViewModel();
        canvasViewModel.Nodes.Add(new ModuleNodeViewModel());
        canvasViewModel.Connections.Add(new ConnectionViewModel());

        var projectFile = new ProjectFile
        {
            CanvasData = new CanvasData
            {
                Nodes = new List<NodeData>(),
                Connections = new List<ConnectionData>(),
                Settings = new CanvasSettings
                {
                    OffsetX = 150,
                    OffsetY = 250,
                    ZoomLevel = 2.0
                }
            }
        };

        // Act
        _adapter.RestoreCanvasFromProjectFile(projectFile, canvasViewModel);

        // Assert
        canvasViewModel.Nodes.Should().BeEmpty();
        canvasViewModel.Connections.Should().BeEmpty();
        canvasViewModel.ViewportCenter.X.Should().Be(150);
        canvasViewModel.ViewportCenter.Y.Should().Be(250);
        canvasViewModel.ViewportZoom.Should().Be(2.0);
    }

    [Fact]
    public void RestoreCanvasFromProjectFile_WithNodes_ShouldRestoreNodesCorrectly()
    {
        // Arrange
        var canvasViewModel = CreateCanvasViewModel();

        var nodeData = new NodeData
        {
            Id = "test-node-1",
            Title = "恢复的节点",
            Type = ModuleType.PipeLine,
            X = 75,
            Y = 125,
            IsExpanded = false,
            IsEnabled = true,
            PropertyValues = new Dictionary<string, object?> { { "param2", "value2" } }
        };

        var projectFile = new ProjectFile
        {
            CanvasData = new CanvasData
            {
                Nodes = new List<NodeData> { nodeData },
                Connections = new List<ConnectionData>(),
                Settings = new CanvasSettings()
            }
        };

        // Act
        _adapter.RestoreCanvasFromProjectFile(projectFile, canvasViewModel);

        // Assert
        canvasViewModel.Nodes.Should().HaveCount(1);
        var restoredNode = canvasViewModel.Nodes[0];
        restoredNode.Title.Should().Be("恢复的节点");
        restoredNode.Location.X.Should().Be(75);
        restoredNode.Location.Y.Should().Be(125);
        restoredNode.IsExpanded.Should().BeFalse();
        restoredNode.IsEnabled.Should().BeTrue();
        restoredNode.Module.Should().NotBeNull();
        restoredNode.Module!.Id.Should().Be("test-node-1");
        restoredNode.Module.Type.Should().Be(ModuleType.PipeLine);
        restoredNode.Module.Parameters.Should().ContainKey("param2");
        restoredNode.Module.Parameters["param2"].Should().Be("value2");
    }

    [Fact]
    public void RestoreCanvasFromProjectFile_WithNullCanvasData_ShouldLogWarningAndReturn()
    {
        // Arrange
        var canvasViewModel = CreateCanvasViewModel();
        var projectFile = new ProjectFile { CanvasData = null! };

        // Act
        _adapter.RestoreCanvasFromProjectFile(projectFile, canvasViewModel, _loggerMock.Object);

        // Assert
        // 应该记录警告日志，但不抛出异常
        _loggerMock.Verify(
            x => x.Log(
                Microsoft.Extensions.Logging.LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("项目文件中没有画布数据")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    public void Dispose()
    {
        // 清理测试目录
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }
}
