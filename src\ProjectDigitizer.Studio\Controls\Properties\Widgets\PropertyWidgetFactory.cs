using System;
using System.Collections.Generic;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Studio.Controls.Properties.Widgets
{
    /// <summary>
    /// 属性编辑器工厂
    /// </summary>
    public static class PropertyWidgetFactory
    {
        private static readonly Dictionary<string, Func<IPropertyWidget>> WidgetCreators = new()
        {
            { "text", () => new TextPropertyWidget() },
            { "number", () => new NumberPropertyWidget() },
            { "boolean", () => new BooleanPropertyWidget() },
            { "select", () => new SelectPropertyWidget() },
            { "file", () => new TextPropertyWidget() } // 暂时使用文本控件，后续可扩展
        };

        /// <summary>
        /// 创建属性编辑器
        /// </summary>
        public static IPropertyWidget CreateWidget(PropertyDefinition propertyDefinition)
        {
            var widgetType = propertyDefinition.UiWidget ?? "text";

            if (WidgetCreators.TryGetValue(widgetType, out var creator))
            {
                var widget = creator();
                widget.PropertyDefinition = propertyDefinition;
                return widget;
            }

            // 默认返回文本编辑器
            var defaultWidget = new TextPropertyWidget();
            defaultWidget.PropertyDefinition = propertyDefinition;
            return defaultWidget;
        }

        /// <summary>
        /// 注册自定义编辑器
        /// </summary>
        public static void RegisterWidget(string widgetType, Func<IPropertyWidget> creator)
        {
            WidgetCreators[widgetType] = creator;
        }
    }

    // 注意：具体的控件实现已移到单独的文件中
}
