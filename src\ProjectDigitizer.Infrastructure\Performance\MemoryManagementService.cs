using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Windows.Threading;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Infrastructure.Performance
{
    /// <summary>
    /// 内存管理服务实现
    /// </summary>
    public class MemoryManagementService : IMemoryManagementService
    {
        private readonly DispatcherTimer _monitorTimer;
        private readonly Queue<MemoryUsageInfo> _memoryHistory;
        private readonly object _lockObject = new();
        private bool _isMonitoring;
        private bool _disposed;

        // 内存阈值设置
        private long _warningThresholdMB = 512; // 512MB警告阈值
        private long _criticalThresholdMB = 1024; // 1GB严重阈值

        // 历史记录设置
        private const int MAX_HISTORY_SIZE = 200;
        private const int MONITOR_INTERVAL_MS = 5000; // 5秒监控间隔

        // GC计数器
        private int _lastGen0Count;
        private int _lastGen1Count;
        private int _lastGen2Count;

        public event Action<MemoryWarningEventArgs>? MemoryWarning;
        public event Action<GarbageCollectionEventArgs>? GarbageCollectionCompleted;

        public MemoryUsageInfo CurrentMemoryUsage { get; private set; }

        public IEnumerable<MemoryUsageInfo> MemoryUsageHistory
        {
            get
            {
                lock (_lockObject)
                {
                    return _memoryHistory.ToArray();
                }
            }
        }

        public MemoryManagementService()
        {
            _memoryHistory = new Queue<MemoryUsageInfo>();
            CurrentMemoryUsage = new MemoryUsageInfo();

            _monitorTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(MONITOR_INTERVAL_MS)
            };
            _monitorTimer.Tick += OnMonitorTimerTick;

            // 初始化GC计数器
            _lastGen0Count = GC.CollectionCount(0);
            _lastGen1Count = GC.CollectionCount(1);
            _lastGen2Count = GC.CollectionCount(2);
        }

        public void StartMonitoring()
        {
            if (_disposed) return;

            if (!_isMonitoring)
            {
                _isMonitoring = true;
                _monitorTimer.Start();
                UpdateMemoryUsage();
            }
        }

        public void StopMonitoring()
        {
            if (_isMonitoring)
            {
                _isMonitoring = false;
                _monitorTimer.Stop();
            }
        }

        public void ForceGarbageCollection(int generation = -1)
        {
            if (_disposed) return;

            var beforeMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            var stopwatch = Stopwatch.StartNew();

            if (generation == -1)
            {
                // 全面GC
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            else
            {
                GC.Collect(generation);
            }

            stopwatch.Stop();
            var afterMemory = GC.GetTotalMemory(false) / (1024 * 1024);

            // 触发GC完成事件
            GarbageCollectionCompleted?.Invoke(new GarbageCollectionEventArgs
            {
                Generation = generation,
                MemoryBeforeMB = beforeMemory,
                MemoryAfterMB = afterMemory,
                DurationMs = stopwatch.ElapsedMilliseconds
            });

            // 更新内存使用情况
            UpdateMemoryUsage();
        }

        public IEnumerable<MemoryCleanupSuggestion> GetCleanupSuggestions()
        {
            var suggestions = new List<MemoryCleanupSuggestion>();
            var currentUsage = CurrentMemoryUsage;

            // 基于当前内存使用情况生成建议
            if (currentUsage.WorkingSetMB > _warningThresholdMB)
            {
                suggestions.Add(new MemoryCleanupSuggestion
                {
                    Type = MemoryCleanupType.ForceGarbageCollection,
                    Description = "执行强制垃圾回收以释放未使用的内存",
                    ExpectedMemoryReleaseMB = EstimateGCMemoryRelease(),
                    Priority = currentUsage.WorkingSetMB > _criticalThresholdMB ? CleanupPriority.Critical : CleanupPriority.High,
                    ExecuteAction = () => ForceGarbageCollection()
                });
            }

            // 检查大对象堆
            var lohInfo = GetLargeObjectHeapInfo();
            if (lohInfo.NeedsCompaction)
            {
                suggestions.Add(new MemoryCleanupSuggestion
                {
                    Type = MemoryCleanupType.CompactLargeObjectHeap,
                    Description = "压缩大对象堆以减少内存碎片",
                    ExpectedMemoryReleaseMB = (long)(lohInfo.SizeMB * lohInfo.FragmentationRatio),
                    Priority = CleanupPriority.Medium,
                    ExecuteAction = () => CompactLargeObjectHeap()
                });
            }

            return suggestions;
        }

        public void ExecuteCleanup(IEnumerable<MemoryCleanupSuggestion> suggestions)
        {
            if (_disposed) return;

            var orderedSuggestions = suggestions
                .OrderByDescending(s => s.Priority)
                .ThenByDescending(s => s.ExpectedMemoryReleaseMB);

            foreach (var suggestion in orderedSuggestions)
            {
                try
                {
                    suggestion.ExecuteAction?.Invoke();
                    Thread.Sleep(100); // 给系统一点时间处理
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"执行内存清理建议失败: {ex.Message}");
                }
            }

            // 更新内存使用情况
            UpdateMemoryUsage();
        }

        public MemoryLeakDetectionResult DetectMemoryLeaks()
        {
            var result = new MemoryLeakDetectionResult();
            var suspects = new List<MemoryLeakSuspect>();

            // 简单的内存泄漏检测逻辑
            if (_memoryHistory.Count >= 10)
            {
                var recentHistory = _memoryHistory.TakeLast(10).ToArray();
                var memoryGrowth = recentHistory.Last().WorkingSetMB - recentHistory.First().WorkingSetMB;
                var avgGrowthPerSample = memoryGrowth / 10.0;

                if (avgGrowthPerSample > 10) // 每个样本平均增长超过10MB
                {
                    result.HasMemoryLeaks = true;
                    suspects.Add(new MemoryLeakSuspect
                    {
                        ObjectType = "Unknown",
                        InstanceCount = -1,
                        MemorySizeMB = (long)memoryGrowth,
                        GrowthTrend = avgGrowthPerSample > 50 ? MemoryGrowthTrend.AbnormalGrowth : MemoryGrowthTrend.RapidGrowth
                    });
                }
            }

            result.Suspects = suspects;
            result.Report = GenerateMemoryLeakReport(result);

            return result;
        }

        public void SetMemoryThresholds(long warningThresholdMB, long criticalThresholdMB)
        {
            _warningThresholdMB = warningThresholdMB;
            _criticalThresholdMB = criticalThresholdMB;
        }

        public LargeObjectHeapInfo GetLargeObjectHeapInfo()
        {
            // 获取大对象堆信息（简化实现）
            var totalMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            var lohSize = totalMemory / 4; // 估算LOH大小约为总内存的1/4

            return new LargeObjectHeapInfo
            {
                SizeMB = lohSize,
                ObjectCount = -1, // 无法直接获取
                FragmentationRatio = EstimateLOHFragmentation(),
                NeedsCompaction = lohSize > 100 && EstimateLOHFragmentation() > 0.3
            };
        }

        private void OnMonitorTimerTick(object? sender, EventArgs e)
        {
            UpdateMemoryUsage();
            CheckMemoryThresholds();
            CheckGarbageCollection();
        }

        private void UpdateMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var managedMemory = GC.GetTotalMemory(false);

                var memoryInfo = new MemoryUsageInfo
                {
                    Timestamp = DateTime.Now,
                    WorkingSetMB = process.WorkingSet64 / (1024 * 1024),
                    PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                    ManagedMemoryMB = managedMemory / (1024 * 1024),
                    GCHeapSizeMB = managedMemory / (1024 * 1024),
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2),
                    LargeObjectHeapMB = GetLargeObjectHeapInfo().SizeMB
                };

                CurrentMemoryUsage = memoryInfo;

                lock (_lockObject)
                {
                    _memoryHistory.Enqueue(memoryInfo);
                    while (_memoryHistory.Count > MAX_HISTORY_SIZE)
                    {
                        _memoryHistory.Dequeue();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新内存使用情况失败: {ex.Message}");
            }
        }

        private void CheckMemoryThresholds()
        {
            var currentMemory = CurrentMemoryUsage.WorkingSetMB;

            if (currentMemory > _criticalThresholdMB)
            {
                TriggerMemoryWarning(MemoryWarningLevel.Critical, "内存使用量达到严重阈值");
            }
            else if (currentMemory > _warningThresholdMB)
            {
                TriggerMemoryWarning(MemoryWarningLevel.Warning, "内存使用量达到警告阈值");
            }
        }

        private void CheckGarbageCollection()
        {
            var currentGen0 = GC.CollectionCount(0);
            var currentGen1 = GC.CollectionCount(1);
            var currentGen2 = GC.CollectionCount(2);

            // 检测是否发生了GC
            if (currentGen2 > _lastGen2Count)
            {
                NotifyGarbageCollection(2);
            }
            else if (currentGen1 > _lastGen1Count)
            {
                NotifyGarbageCollection(1);
            }
            else if (currentGen0 > _lastGen0Count)
            {
                NotifyGarbageCollection(0);
            }

            _lastGen0Count = currentGen0;
            _lastGen1Count = currentGen1;
            _lastGen2Count = currentGen2;
        }

        private void TriggerMemoryWarning(MemoryWarningLevel level, string message)
        {
            var suggestions = GetCleanupSuggestions();
            MemoryWarning?.Invoke(new MemoryWarningEventArgs
            {
                Level = level,
                CurrentUsage = CurrentMemoryUsage,
                Message = message,
                SuggestedActions = suggestions
            });
        }

        private void NotifyGarbageCollection(int generation)
        {
            // 简化的GC通知
            GarbageCollectionCompleted?.Invoke(new GarbageCollectionEventArgs
            {
                Generation = generation,
                MemoryBeforeMB = -1,
                MemoryAfterMB = CurrentMemoryUsage.ManagedMemoryMB,
                DurationMs = -1
            });
        }

        private long EstimateGCMemoryRelease()
        {
            // 估算GC可能释放的内存量
            return Math.Max(CurrentMemoryUsage.ManagedMemoryMB / 4, 10);
        }

        private double EstimateLOHFragmentation()
        {
            // 简化的LOH碎片化估算
            return 0.2; // 假设20%的碎片化
        }

        private void CompactLargeObjectHeap()
        {
            // .NET Framework 4.5.1+支持LOH压缩
            try
            {
                System.Runtime.GCSettings.LargeObjectHeapCompactionMode = System.Runtime.GCLargeObjectHeapCompactionMode.CompactOnce;
                GC.Collect();
            }
            catch
            {
                // 如果不支持LOH压缩，则执行常规GC
                ForceGarbageCollection();
            }
        }

        private string GenerateMemoryLeakReport(MemoryLeakDetectionResult result)
        {
            if (!result.HasMemoryLeaks)
            {
                return "未检测到明显的内存泄漏。";
            }

            var report = "检测到可能的内存泄漏:\n";
            foreach (var suspect in result.Suspects)
            {
                report += $"- {suspect.ObjectType}: {suspect.MemorySizeMB}MB, 趋势: {suspect.GrowthTrend}\n";
            }

            return report;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _monitorTimer?.Stop();
                _disposed = true;
            }
        }
    }
}
