using System.Windows.Controls;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Common
{
    /// <summary>
    /// ProgressIndicator.xaml 的交互逻辑
    /// </summary>
    public partial class ProgressIndicator : UserControl
    {
        public ProgressIndicator()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 设置进度指示器的数据上下文
        /// </summary>
        /// <param name="viewModel">进度指示器视图模型</param>
        public void SetViewModel(ProgressIndicatorViewModel viewModel)
        {
            DataContext = viewModel;
        }
    }
}
