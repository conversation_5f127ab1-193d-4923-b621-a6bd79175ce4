using System;

namespace CalcEngine
{
    /// <summary>
    /// 函数定义：保存函数的参数上下限与具体实现委托。
    /// 仅作为 CalcEngine 内部函数注册与元数据查询使用。
    /// </summary>
    public class FunctionDefinition
    {
        /// <summary>
        /// 允许的最小参数个数。
        /// </summary>
        public int ParmMin;

        /// <summary>
        /// 允许的最大参数个数；当为 <see cref="int.MaxValue"/> 时表示可变参数上限不限。
        /// </summary>
        public int ParmMax;

        /// <summary>
        /// 实现该函数的计算委托。
        /// </summary>
        public CalcEngineFunction Function;

        /// <summary>
        /// 使用参数上下限与委托实例化函数定义。
        /// </summary>
        /// <param name="parmMin">最小参数个数。</param>
        /// <param name="parmMax">最大参数个数。</param>
        /// <param name="function">计算实现委托。</param>
        public FunctionDefinition(int parmMin, int parmMax, CalcEngineFunction function)
        {
            ParmMin = parmMin;
            ParmMax = parmMax;
            Function = function;
        }
    }
}

