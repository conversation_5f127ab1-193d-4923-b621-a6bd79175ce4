using System;
using System.Collections.Generic;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Core.DomainServices
{
    /// <summary>
    /// π常量函数
    /// </summary>
    public class PiConstantFunction : FunctionProviderBase
    {
        public override string Name => "PI";
        public override string DisplayName => "π常量";
        public override string Description => "返回数学常量π的值";
        public override string Category => "数学常量";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "precision",
                    DisplayName = "精度",
                    Description = "小数位数精度（2-15位）",
                    DataType = FieldDataType.Number,
                    IsRequired = false,
                    DefaultValue = 6,
                    MinValue = 2,
                    MaxValue = 15
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var precision = GetParameterValue<int>(parameters, "precision", 6);

                // 验证精度范围
                if (precision < 2 || precision > 15)
                {
                    precision = 6; // 使用默认精度
                }

                var result = Math.Round(Math.PI, precision);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"π常量计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "PI() = 3.141593",
                "PI(2) = 3.14",
                "PI(10) = 3.**********"
            };
        }


    }

    /// <summary>
    /// e常量函数
    /// </summary>
    public class EConstantFunction : FunctionProviderBase
    {
        public override string Name => "E";
        public override string DisplayName => "e常量";
        public override string Description => "返回数学常量e（自然对数的底）的值";
        public override string Category => "数学常量";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "precision",
                    DisplayName = "精度",
                    Description = "小数位数精度（2-15位）",
                    DataType = FieldDataType.Number,
                    IsRequired = false,
                    DefaultValue = 6,
                    MinValue = 2,
                    MaxValue = 15
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var precision = GetParameterValue<int>(parameters, "precision", 6);

                if (precision < 2 || precision > 15)
                {
                    precision = 6;
                }

                var result = Math.Round(Math.E, precision);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"e常量计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "E() = 2.718282",
                "E(2) = 2.72",
                "E(10) = 2.**********"
            };
        }
    }

    /// <summary>
    /// 黄金比例常量函数
    /// </summary>
    public class GoldenRatioConstantFunction : FunctionProviderBase
    {
        private const double GOLDEN_RATIO = 1.6180339887498948482045868343656;

        public override string Name => "PHI";
        public override string DisplayName => "φ黄金比例";
        public override string Description => "返回黄金比例常量φ的值";
        public override string Category => "数学常量";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "precision",
                    DisplayName = "精度",
                    Description = "小数位数精度（2-15位）",
                    DataType = FieldDataType.Number,
                    IsRequired = false,
                    DefaultValue = 6,
                    MinValue = 2,
                    MaxValue = 15
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var precision = GetParameterValue<int>(parameters, "precision", 6);

                if (precision < 2 || precision > 15)
                {
                    precision = 6;
                }

                var result = Math.Round(GOLDEN_RATIO, precision);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"黄金比例常量计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "PHI() = 1.618034",
                "PHI(2) = 1.62",
                "PHI(10) = 1.6180339887"
            };
        }
    }
}
