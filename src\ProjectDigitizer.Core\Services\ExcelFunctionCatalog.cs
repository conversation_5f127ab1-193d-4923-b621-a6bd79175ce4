using System;
using System.Collections.Generic;
using System.Linq;

namespace ProjectDigitizer.Core.Services
{
    /// <summary>
    /// Excel 风格函数元数据目录（名称、签名、描述、别名、分类）。
    /// 仅提供元数据用于提示/高亮/保留字判断，不承担执行职责。
    /// </summary>
    public static class ExcelFunctionCatalog
    {
        public sealed class ExcelFunctionInfo
        {
            public string Name { get; init; } = string.Empty;
            public string DisplayName { get; init; } = string.Empty;
            public string Category { get; init; } = string.Empty;
            public string Description { get; init; } = string.Empty;
            public string Signature { get; init; } = string.Empty;
            public IReadOnlyList<string> Aliases { get; init; } = Array.Empty<string>();
        }

        private static readonly List<ExcelFunctionInfo> s_functions = new()
        {
            // Math & Statistical
            new ExcelFunctionInfo { Name = "SUM", DisplayName = "求和", Category = "数学", Description = "返回所有参数之和。", Signature = "SUM(number1, [number2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "AVERAGE", DisplayName = "平均值", Category = "统计", Description = "返回参数的算术平均值。", Signature = "AVERAGE(number1, [number2], ...)", Aliases = new[]{"AVG"} },
            new ExcelFunctionInfo { Name = "COUNT", DisplayName = "计数", Category = "统计", Description = "返回包含数字的参数个数。", Signature = "COUNT(value1, [value2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "COUNTA", DisplayName = "非空计数", Category = "统计", Description = "返回非空参数的个数。", Signature = "COUNTA(value1, [value2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "MAX", DisplayName = "最大值", Category = "数学", Description = "返回参数中的最大值。", Signature = "MAX(number1, [number2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "MIN", DisplayName = "最小值", Category = "数学", Description = "返回参数中的最小值。", Signature = "MIN(number1, [number2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ABS", DisplayName = "绝对值", Category = "数学", Description = "返回数值的绝对值。", Signature = "ABS(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ROUND", DisplayName = "四舍五入", Category = "数学", Description = "将数字四舍五入到指定的位数。", Signature = "ROUND(number, [digits])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ROUNDUP", DisplayName = "向上取整", Category = "数学", Description = "按绝对值远离零方向进位。", Signature = "ROUNDUP(number, [digits])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ROUNDDOWN", DisplayName = "向下取整", Category = "数学", Description = "按绝对值趋向零方向舍去。", Signature = "ROUNDDOWN(number, [digits])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "INT", DisplayName = "取整", Category = "数学", Description = "向下取整为最接近的整数。", Signature = "INT(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "FLOOR", DisplayName = "向下舍入", Category = "数学", Description = "按指定基数向下舍入。", Signature = "FLOOR(number, significance)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "CEILING", DisplayName = "向上舍入", Category = "数学", Description = "按指定基数向上舍入。", Signature = "CEILING(number, significance)", Aliases = new[]{"CEIL"} },
            new ExcelFunctionInfo { Name = "POWER", DisplayName = "乘幂", Category = "数学", Description = "返回数字的乘幂结果。", Signature = "POWER(base, exponent)", Aliases = new[]{"POW"} },
            new ExcelFunctionInfo { Name = "SQRT", DisplayName = "平方根", Category = "数学", Description = "返回平方根。", Signature = "SQRT(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "MOD", DisplayName = "取余", Category = "数学", Description = "返回两数相除的余数。", Signature = "MOD(number, divisor)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "PI", DisplayName = "圆周率", Category = "数学常量", Description = "返回 π 的值。", Signature = "PI()", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "E", DisplayName = "自然常数 e", Category = "数学常量", Description = "返回 e 的值。", Signature = "E()", Aliases = Array.Empty<string>() },

            // Trigonometry & exponential/log
            new ExcelFunctionInfo { Name = "SIN", DisplayName = "正弦", Category = "三角函数", Description = "返回角度的正弦值（弧度）。", Signature = "SIN(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "COS", DisplayName = "余弦", Category = "三角函数", Description = "返回角度的余弦值（弧度）。", Signature = "COS(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "TAN", DisplayName = "正切", Category = "三角函数", Description = "返回角度的正切值（弧度）。", Signature = "TAN(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ASIN", DisplayName = "反正弦", Category = "三角函数", Description = "返回反正弦（弧度）。", Signature = "ASIN(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ACOS", DisplayName = "反余弦", Category = "三角函数", Description = "返回反余弦（弧度）。", Signature = "ACOS(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "ATAN", DisplayName = "反正切", Category = "三角函数", Description = "返回反正切（弧度）。", Signature = "ATAN(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "EXP", DisplayName = "e 的指数", Category = "指数对数", Description = "返回 e 的指定乘幂。", Signature = "EXP(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "LN", DisplayName = "自然对数", Category = "指数对数", Description = "返回自然对数。", Signature = "LN(number)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "LOG", DisplayName = "对数", Category = "指数对数", Description = "返回指定底的对数。", Signature = "LOG(number, [base])", Aliases = new[]{"LOG10"} },

            // Text
            new ExcelFunctionInfo { Name = "TEXT", DisplayName = "格式化文本", Category = "文本", Description = "按格式将数值转换为文本。", Signature = "TEXT(value, format_text)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "LEN", DisplayName = "文本长度", Category = "文本", Description = "返回文本长度。", Signature = "LEN(text)", Aliases = new[]{"LENGTH"} },
            new ExcelFunctionInfo { Name = "CONCAT", DisplayName = "连接文本", Category = "文本", Description = "连接多个文本字符串。", Signature = "CONCAT(text1, [text2], ...)", Aliases = new[]{"CONCATENATE"} },
            new ExcelFunctionInfo { Name = "LEFT", DisplayName = "左侧文本", Category = "文本", Description = "返回文本开头指定个字符。", Signature = "LEFT(text, [num_chars])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "RIGHT", DisplayName = "右侧文本", Category = "文本", Description = "返回文本末尾指定个字符。", Signature = "RIGHT(text, [num_chars])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "MID", DisplayName = "中间文本", Category = "文本", Description = "从文本中返回指定位置的字符。", Signature = "MID(text, start_num, num_chars)", Aliases = new[]{"SUBSTRING"} },
            new ExcelFunctionInfo { Name = "UPPER", DisplayName = "转大写", Category = "文本", Description = "将文本转换为大写。", Signature = "UPPER(text)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "LOWER", DisplayName = "转小写", Category = "文本", Description = "将文本转换为小写。", Signature = "LOWER(text)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "TRIM", DisplayName = "清理空格", Category = "文本", Description = "删除文本中的多余空格。", Signature = "TRIM(text)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "REPLACE", DisplayName = "替换", Category = "文本", Description = "替换文本中的部分字符。", Signature = "REPLACE(old_text, start_num, num_chars, new_text)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "SUBSTITUTE", DisplayName = "替换子串", Category = "文本", Description = "用新文本替换旧文本中的子串。", Signature = "SUBSTITUTE(text, old_text, new_text, [instance_num])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "FIND", DisplayName = "查找", Category = "文本", Description = "区分大小写的查找位置。", Signature = "FIND(find_text, within_text, [start_num])", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "SEARCH", DisplayName = "搜索", Category = "文本", Description = "不区分大小写的查找位置。", Signature = "SEARCH(find_text, within_text, [start_num])", Aliases = Array.Empty<string>() },

            // Logical & error
            new ExcelFunctionInfo { Name = "IF", DisplayName = "条件判断", Category = "逻辑", Description = "按条件返回不同结果。", Signature = "IF(condition, value_if_true, value_if_false)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "AND", DisplayName = "与", Category = "逻辑", Description = "所有参数为 TRUE 时返回 TRUE。", Signature = "AND(logical1, [logical2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "OR", DisplayName = "或", Category = "逻辑", Description = "任一参数为 TRUE 时返回 TRUE。", Signature = "OR(logical1, [logical2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "NOT", DisplayName = "非", Category = "逻辑", Description = "对逻辑值取反。", Signature = "NOT(logical)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "XOR", DisplayName = "异或", Category = "逻辑", Description = "返回参数中为 TRUE 的数量为奇数时 TRUE。", Signature = "XOR(logical1, [logical2], ...)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "IFERROR", DisplayName = "错误替代", Category = "错误处理", Description = "表达式错误时返回替代结果。", Signature = "IFERROR(value, value_if_error)", Aliases = Array.Empty<string>() },

            // Date & time (元数据，仅列出常用项)
            new ExcelFunctionInfo { Name = "TODAY", DisplayName = "今天", Category = "日期时间", Description = "返回当前日期。", Signature = "TODAY()", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "NOW", DisplayName = "现在", Category = "日期时间", Description = "返回当前日期时间。", Signature = "NOW()", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "DATE", DisplayName = "日期", Category = "日期时间", Description = "按年/月/日构造日期。", Signature = "DATE(year, month, day)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "YEAR", DisplayName = "年", Category = "日期时间", Description = "返回日期的年份。", Signature = "YEAR(date)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "MONTH", DisplayName = "月", Category = "日期时间", Description = "返回日期的月份。", Signature = "MONTH(date)", Aliases = Array.Empty<string>() },
            new ExcelFunctionInfo { Name = "DAY", DisplayName = "日", Category = "日期时间", Description = "返回日期的日。", Signature = "DAY(date)", Aliases = Array.Empty<string>() },
        };

        public static IReadOnlyList<ExcelFunctionInfo> Functions => s_functions;

        /// <summary>
        /// 返回所有函数名（包含别名），用于提示/高亮/保留字。
        /// </summary>
        public static HashSet<string> AllNames
        {
            get
            {
                var set = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                foreach (var f in s_functions)
                {
                    set.Add(f.Name);
                    foreach (var a in f.Aliases)
                        set.Add(a);
                }
                return set;
            }
        }

        /// <summary>
        /// 名称到签名的映射（包含别名，别名继承主签名）。
        /// </summary>
        public static Dictionary<string, string> GetSignatureMap()
        {
            var map = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            foreach (var f in s_functions)
            {
                map[f.Name] = f.Signature;
                foreach (var a in f.Aliases)
                    map[a] = f.Signature;
            }
            return map;
        }

        /// <summary>
        /// 名称到函数信息的映射（别名映射到主信息）。
        /// </summary>
        public static Dictionary<string, ExcelFunctionInfo> GetInfoMap()
        {
            var map = new Dictionary<string, ExcelFunctionInfo>(StringComparer.OrdinalIgnoreCase);
            foreach (var f in s_functions)
            {
                map[f.Name] = f;
                foreach (var a in f.Aliases)
                    map[a] = f;
            }
            return map;
        }
    }
}
