<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== AI代理节点模板 ==========  -->
    <!--  专用于 ModuleType.AIAgent 的节点模板  -->

    <!--  AI代理节点专用样式  -->
    <Style
        BasedOn="{StaticResource BaseNodeStyle}"
        TargetType="nodify:Node"
        x:Key="AIAgentNodeStyle">
        <Setter Property="Width" Value="300" />
        <Setter Property="Height" Value="140" />
    </Style>

    <!--  AI代理节点内容模板  -->
    <DataTemplate x:Key="AIAgentContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏 - AI代理专用  -->
                <Border
                    Background="{StaticResource ModernHeaderGradientPink}"
                    CornerRadius="12,12,0,0"
                    Grid.Row="0"
                    Height="56"
                    Padding="8,4">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  AI图标  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Kind="Robot"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18" />

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：AI特有信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  模型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="12"
                                    Kind="Brain"
                                    Margin="0,0,4,0"
                                    Opacity="0.8"
                                    Width="12" />
                                <TextBlock
                                    FontSize="10"
                                    Foreground="White"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.Model, FallbackValue='GPT-3.5'}"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="AI配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  测试按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="测试AI响应">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Play"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="AI执行状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Running">
                                                    <Setter Property="Fill" Value="#2196F3" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Processing">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域 - AI代理专用  -->
                <Border
                    Background="#FFF3E0"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  AI代理类型  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="类型:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#E91E63"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White"
                                    Text="{Binding NodeProperties.AgentType, FallbackValue='ChatBot'}" />
                            </Border>
                        </Grid>

                        <!--  系统提示词预览  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="提示:"
                                VerticalAlignment="Top" />

                            <TextBlock
                                FontSize="9"
                                Foreground="#777"
                                Grid.Column="1"
                                MaxHeight="30"
                                Text="{Binding NodeProperties.SystemPrompt, FallbackValue='未设置系统提示词'}"
                                TextTrimming="CharacterEllipsis"
                                TextWrapping="Wrap"
                                ToolTip="{Binding NodeProperties.SystemPrompt}" />
                        </Grid>

                        <!--  参数信息  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  最大令牌数  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="令牌" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#E91E63"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.MaxTokens, FallbackValue=1000}" />
                            </StackPanel>

                            <!--  温度参数  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="温度" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#E91E63"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.Temperature, FallbackValue=0.7, StringFormat=F1}" />
                            </StackPanel>

                            <!--  超时时间  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="超时" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#E91E63"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.TimeoutSeconds, FallbackValue=30, StringFormat={}{0}s}" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  AI代理节点主模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="AIAgentNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource AIAgentNodeStyle}">

            <!--  使用高级连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  使用专用内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource AIAgentContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
