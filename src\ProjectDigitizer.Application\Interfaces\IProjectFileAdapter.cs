using System.Threading.Tasks;

using ProjectDigitizer.Application.DTOs;

namespace ProjectDigitizer.Application.Interfaces
{
    /// <summary>
    /// 项目文件适配器接口
    /// 负责项目文件的保存和加载操作
    /// </summary>
    public interface IProjectFileAdapter
    {
        /// <summary>
        /// 保存项目文件
        /// </summary>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="filePath">文件路径</param>
        Task SaveProjectAsync(ProjectFile projectFile, string filePath);

        /// <summary>
        /// 加载项目文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>项目文件数据</returns>
        Task<ProjectFile> LoadProjectAsync(string filePath);

        /// <summary>
        /// 创建新的项目文件
        /// </summary>
        /// <param name="projectName">项目名称</param>
        /// <param name="description">项目描述</param>
        /// <returns>新的项目文件</returns>
        ProjectFile CreateNewProject(string projectName, string? description = null);
    }
}
