<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========== 效果资源 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的所有效果定义 -->

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="ModernDropShadowEffect"
                      ShadowDepth="4"
                      BlurRadius="8"
                      Color="#888"
                      Opacity="0.3"/>

    <!-- 端口光晕效果 -->
    <BlurEffect x:Key="PortGlowEffect"
                Radius="4"/>

    <!-- 标准节点阴影效果 -->
    <DropShadowEffect x:Key="StandardNodeShadow"
                      Color="#000000"
                      Opacity="0.15"
                      ShadowDepth="4"
                      BlurRadius="12"/>

    <!-- 选中状态光晕效果 -->
    <DropShadowEffect x:Key="SelectedNodeGlow"
                      Color="#4A90E2"
                      Opacity="0.6"
                      ShadowDepth="0"
                      BlurRadius="12"/>

    <!-- 锁定状态阴影效果 -->
    <DropShadowEffect x:Key="LockedNodeShadow"
                      Color="Gray"
                      Opacity="0.3"
                      ShadowDepth="2"
                      BlurRadius="4"/>

    <!-- 连接器阴影效果 -->
    <DropShadowEffect x:Key="ConnectorShadow"
                      Color="Black"
                      Opacity="0.2"
                      ShadowDepth="1"
                      BlurRadius="2"/>

    <!-- 连接器光晕效果 -->
    <DropShadowEffect x:Key="ConnectorGlow"
                      Color="#4A90E2"
                      Opacity="0.4"
                      ShadowDepth="0"
                      BlurRadius="4"/>

    <!-- 徽章阴影效果 -->
    <DropShadowEffect x:Key="BadgeShadow"
                      Color="#000000"
                      Opacity="0.25"
                      ShadowDepth="2"
                      BlurRadius="4"/>

    <!-- 卡片阴影效果 -->
    <DropShadowEffect x:Key="CardShadow"
                      Color="Black"
                      Opacity="0.1"
                      ShadowDepth="2"
                      BlurRadius="4"/>

</ResourceDictionary>
