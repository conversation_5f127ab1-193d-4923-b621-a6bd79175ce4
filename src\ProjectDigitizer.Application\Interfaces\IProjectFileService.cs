using System.Threading.Tasks;

using ProjectDigitizer.Application.DTOs;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 项目文件服务接口
/// </summary>
public interface IProjectFileService
{
    /// <summary>
    /// 创建新项目
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="description">项目描述</param>
    /// <returns>项目文件对象</returns>
    ProjectFile CreateNewProject(string projectName, string? description = null);

    /// <summary>
    /// 保存项目文件
    /// </summary>
    /// <param name="projectFile">项目文件对象</param>
    /// <param name="filePath">文件路径</param>
    Task SaveProjectAsync(ProjectFile projectFile, string filePath);

    /// <summary>
    /// 加载项目文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>项目文件对象</returns>
    Task<ProjectFile> LoadProjectAsync(string filePath);

    /// <summary>
    /// 验证项目文件格式
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否为有效的项目文件</returns>
    Task<bool> ValidateProjectFileAsync(string filePath);

    /// <summary>
    /// 获取项目文件信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>项目文件信息</returns>
    Task<ProjectFileInfo> GetProjectInfoAsync(string filePath);
}
