using System;
using System.Collections.Generic;

namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 性能监测接口
    /// </summary>
    public interface IPerformanceMonitor : IDisposable
    {
        PerformanceMetrics CurrentMetrics { get; }
        IEnumerable<PerformanceMetrics> MetricsHistory { get; }
        event Action<PerformanceReport>? PerformanceReported;
        void StartMonitoring();
        void StopMonitoring();
        void BeginFrame();
        void EndFrame();
        void BeginNodeRender(int nodeCount);
        void RecordConnectorUpdate(int connectorCount);
        void RecordMemoryUsage();
    }

    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public double FPS { get; set; }
        public double LastFrameTime { get; set; }
        public int TotalFrames { get; set; }
        public long MemoryUsageMB { get; set; }
        public int NodesRendered { get; set; }
        public int ConnectorsUpdated { get; set; }
    }

    public class PerformanceReport
    {
        public DateTime Timestamp { get; set; }
        public double AverageFPS { get; set; }
        public double AverageFrameTime { get; set; }
        public double MaxFrameTime { get; set; }
        public long CurrentMemoryMB { get; set; }
        public int NodesRendered { get; set; }
        public int ConnectorsUpdated { get; set; }
        public PerformanceLevel PerformanceLevel { get; set; }

        public override string ToString()
        {
            return $"FPS: {AverageFPS:F1}, 帧时间: {AverageFrameTime:F1}ms, 内存: {CurrentMemoryMB}MB, 节点: {NodesRendered}, 级别: {PerformanceLevel}";
        }
    }

    public enum PerformanceLevel
    {
        Unknown,
        Poor,
        Fair,
        Good,
        Excellent
    }
}

