using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;

using Nodify; // 确保Nodify命名空间被引用

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Performance;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Services;
using ProjectDigitizer.Core.ValueObjects;
using ProjectDigitizer.Infrastructure.UI.Animation;
using ProjectDigitizer.Studio.Configuration;
using ProjectDigitizer.Studio.Controls.Canvas.Services;
using ProjectDigitizer.Studio.Controls.Properties.Services;
using ProjectDigitizer.Studio.Helpers;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 画布视图模型 - 包含性能优化功能
    /// </summary>
    public class CanvasViewModel : INotifyPropertyChanged
    {
        private readonly ICanvasVirtualizationManager _virtualizationManager;
        private readonly IPerformanceOptimizedConnectorManager _connectorManager;
        private readonly INodeLayoutService _layoutService;
        private readonly INodeRegistry _nodeRegistry;
        private readonly ConnectionStyleSyncService _connectionStyleService;
        private readonly FieldReferenceTracker _referenceTracker;

        // 布局状态标志，防止布局循环
        private bool _isAnimating = false;
        private bool _isLayoutInProgress = false;
        private bool _isUpdatingViewportInfo = false;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            try
            {
                if (PropertyChanged != null)
                {
                    System.Diagnostics.Debug.WriteLine($"触发PropertyChanged事件: {GetType().Name}.{propertyName}");
                    PropertyChanged.Invoke(this, new PropertyChangedEventArgs(propertyName));
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"PropertyChanged事件为null: {GetType().Name}.{propertyName}");
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不阻止程序运行
                System.Diagnostics.Debug.WriteLine($"PropertyChanged事件异常: {propertyName}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 均衡网格自动布局：依据当前可见画布宽高，将未锁定节点按行列均匀铺开并居中。
        /// </summary>
        private async Task ApplyBalancedGridLayoutAsync(LayoutOptions options)
        {
            var lockedNodes = Nodes.Where(n => n.IsLocked).ToList();
            var unlockedNodes = Nodes.Where(n => !n.IsLocked).ToList();

            if (!unlockedNodes.Any())
            {
                System.Diagnostics.Debug.WriteLine("均衡布局：没有可布局的未锁定节点");
                return;
            }

            // 使用可见画布尺寸；尽量与 ApplyHierarchicalLayoutAsync 的取值保持一致
            var canvasWidth = Math.Max(800, ActualCanvasSize.Width);
            var canvasHeight = Math.Max(600, ActualCanvasSize.Height);
            var leftPanelWidth = 250;
            var availableWidth = Math.Max(600, canvasWidth - leftPanelWidth);
            var startX = Math.Max(leftPanelWidth + 50, ActualViewport.X + 50);
            var startY = Math.Max(50, ActualViewport.Y + 50);

            // 节点尺寸与自适应间距（更均匀地利用宽度）
            var nodeW = options.NodeWidth > 0 ? options.NodeWidth : 250;
            var nodeH = options.NodeHeight > 0 ? options.NodeHeight : 120;
            const double minHS = 50.0; // 最小水平间距，允许更紧凑排布
            const double minVS = 50.0; // 最小垂直间距
            var count = unlockedNodes.Count;

            // 以画布宽高比估算理想列数（越接近正方形越均衡）
            var aspect = availableWidth / Math.Max(1.0, canvasHeight);
            var idealCols = Math.Max(1, (int)Math.Round(Math.Sqrt(count * aspect)));

            // 根据最小间距推导最大可行列数
            var maxFeasibleCols = Math.Max(1, (int)Math.Floor((availableWidth + minHS) / (nodeW + minHS)));

            // 首选不少于3列（如果可行），以避免“仅两列”的观感
            var minPreferredCols = Math.Min(3, maxFeasibleCols);
            var cols = Math.Min(maxFeasibleCols, Math.Max(idealCols, minPreferredCols));

            // 动态计算水平间距，使整行“对齐填满”可用宽度
            double hs = 0;
            if (cols > 1)
            {
                hs = (availableWidth - cols * nodeW) / (cols - 1);
                if (hs < minHS)
                {
                    // 降低列数直到满足最小间距
                    while (cols > 1)
                    {
                        cols--;
                        hs = (availableWidth - cols * nodeW) / Math.Max(1, cols - 1);
                        if (hs >= minHS) break;
                    }
                }
            }

            // 行数与自适应垂直间距
            var rows = Math.Max(1, (int)Math.Ceiling(count / (double)cols));
            double vs = rows > 1 ? (canvasHeight - rows * nodeH) / (rows - 1) : 0;
            if (rows > 1 && vs < minVS)
            {
                vs = minVS; // 不压得太紧
            }

            // 居中放置
            var totalW = cols * nodeW + (cols - 1) * hs;
            var totalH = rows * nodeH + (rows - 1) * vs;
            var originX = startX + Math.Max(0, (availableWidth - totalW) / 2.0);
            var originY = startY + Math.Max(0, (canvasHeight - totalH) / 2.0);

            // 为减少位移感，按当前从上到下、左到右顺序布局
            var ordered = unlockedNodes
                .OrderBy(n => n.Location.Y)
                .ThenBy(n => n.Location.X)
                .ToList();

            var targetPositions = new Dictionary<ModuleNodeViewModel, Point>(ordered.Count);
            for (int i = 0; i < ordered.Count; i++)
            {
                var node = ordered[i];
                var r = i / cols;
                var indexInRow = i - r * cols;
                var isLastRow = r == rows - 1;
                var lastCount = count % cols;
                var itemsInRow = isLastRow && lastCount != 0 ? lastCount : cols;
                var rowStartOffset = (cols - itemsInRow) * (nodeW + hs) / 2.0; // 让最后一行居中

                var x = originX + rowStartOffset + indexInRow * (nodeW + hs);
                var y = originY + r * (nodeH + vs);

                // 粗略避开已锁定节点的重叠（右下偏移）
                foreach (var obs in lockedNodes)
                {
                    var dx = Math.Abs(x - obs.Location.X);
                    var dy = Math.Abs(y - obs.Location.Y);
                    if (dx < nodeW && dy < nodeH)
                    {
                        x += nodeW / 2.0;
                        y += nodeH / 2.0;
                    }
                }

                targetPositions[node] = new Point(x, y);
            }

            if (options.EnableAnimation)
            {
                await AnimateNodesToPositionsAsync(targetPositions, options.AnimationDuration);
            }
            else
            {
                foreach (var kvp in targetPositions)
                {
                    kvp.Key.Location = kvp.Value;
                }
            }
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        private ModuleNodeViewModel? _selectedNode;
        private bool _isPropertyPanelVisible;
        private Point _viewportCenter = new Point(400, 300); // 默认视口中心
        private double _viewportZoom = 1.0; // 默认缩放级别
        private Size _actualCanvasSize = new Size(1200, 800); // 实际画布尺寸
        private Rect _actualViewport = new Rect(0, 0, 1200, 800); // 实际可见视口
        private ConnectionLineStyle _currentConnectionLineStyle = Configuration.UIConfiguration.DefaultConnectionLineStyle; // 当前连接线样式

        /// <summary>
        /// 节点集合
        /// </summary>
        public ObservableCollection<ModuleNodeViewModel> Nodes { get; }

        /// <summary>
        /// 连接线集合
        /// </summary>
        public ObservableCollection<ConnectionViewModel> Connections { get; }

        /// <summary>
        /// 选中的节点集合（支持多选）
        /// </summary>
        public ObservableCollection<object> SelectedItems { get; }

        /// <summary>
        /// 属性面板是否可见
        /// </summary>
        public bool IsPropertyPanelVisible
        {
            get => _isPropertyPanelVisible;
            set => SetProperty(ref _isPropertyPanelVisible, value);
        }

        /// <summary>
        /// 选中的节点（单选）
        /// </summary>
        public ModuleNodeViewModel? SelectedNode
        {
            get => _selectedNode;
            set
            {
                if (_selectedNode != value)
                {
                    _selectedNode = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 视口中心位置
        /// </summary>
        public Point ViewportCenter
        {
            get => _viewportCenter;
            set => SetProperty(ref _viewportCenter, value);
        }

        /// <summary>
        /// 视口缩放级别
        /// </summary>
        public double ViewportZoom
        {
            get => _viewportZoom;
            set => SetProperty(ref _viewportZoom, value);
        }

        /// <summary>
        /// 实际画布尺寸
        /// </summary>
        public Size ActualCanvasSize
        {
            get => _actualCanvasSize;
            set => SetProperty(ref _actualCanvasSize, value);
        }

        /// <summary>
        /// 实际可见视口
        /// </summary>
        public Rect ActualViewport
        {
            get => _actualViewport;
            set => SetProperty(ref _actualViewport, value);
        }

        /// <summary>
        /// 当前连接线样式
        /// </summary>
        public ConnectionLineStyle CurrentConnectionLineStyle
        {
            get => _currentConnectionLineStyle;
            set
            {
                if (SetProperty(ref _currentConnectionLineStyle, value))
                {
                    // 样式变更时触发连接线重新渲染
                    OnConnectionLineStyleChanged();
                }
            }
        }

        public CanvasViewModel(
            ICanvasVirtualizationManager virtualizationManager,
            IPerformanceOptimizedConnectorManager connectorManager,
            INodeLayoutService layoutService,
            INodeRegistry nodeRegistry)
        {
            // 通过依赖注入获取性能优化组件
            _virtualizationManager = virtualizationManager ?? throw new ArgumentNullException(nameof(virtualizationManager));
            _connectorManager = connectorManager ?? throw new ArgumentNullException(nameof(connectorManager));
            _layoutService = layoutService ?? throw new ArgumentNullException(nameof(layoutService));
            _nodeRegistry = nodeRegistry ?? throw new ArgumentNullException(nameof(nodeRegistry));

            // 初始化连接样式服务
            _referenceTracker = new FieldReferenceTracker();
            _connectionStyleService = new ConnectionStyleSyncService();
            _connectionStyleService.SetReferenceTracker(_referenceTracker);

            Nodes = new ObservableCollection<ModuleNodeViewModel>();
            Connections = new ObservableCollection<ConnectionViewModel>();
            SelectedItems = new ObservableCollection<object>();
            AddModuleCommand = new DelegateCommand<TemplateItem>(ExecuteAddModule, CanExecuteAddModule);
            PendingConnection = new PendingConnectionViewModel();
            DisconnectConnectorCommand = new DelegateCommand<ConnectorViewModel>(ExecuteDisconnectConnector);
            CreateConnectionCommand = new DelegateCommand<object>(
              target =>
              {
                  var targetConnector = target as ConnectorViewModel;
                  PendingConnection.Target = targetConnector;
                  CreateConnection(PendingConnection.Source, targetConnector);
              },
              target => CanCreateConnection(PendingConnection.Source, target as ConnectorViewModel));
            StartConnectionCommand = new DelegateCommand<ConnectorViewModel>(
              connector =>
              {
                  PendingConnection.Source = connector;
                  PendingConnection.IsVisible = true;
              },
              c => c != null && (c.SupportsMultipleConnections || !c.IsConnected || !c.IsInput));
            RemoveConnectionCommand = new DelegateCommand<ConnectionViewModel>(ExecuteRemoveConnection);

            // 初始化连接线样式切换命令
            ChangeConnectionLineStyleCommand = new DelegateCommand<ConnectionLineStyle>(ExecuteChangeConnectionLineStyle);

            // 初始化自动布局命令（Auto 策略）
            AutoLayoutCommand = new DelegateCommand(async () =>
            {
                System.Diagnostics.Debug.WriteLine("=== AutoLayoutCommand 被执行（Auto 策略） ===");
                try
                {
                    await ApplyAutoLayoutAsync();
                    System.Diagnostics.Debug.WriteLine("AutoLayoutCommand 执行完成");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"AutoLayoutCommand 执行失败: {ex.Message}");
                    // 可以在这里添加用户友好的错误处理
                }
            });

            // 设置PendingConnection的命令
            PendingConnection.StartCommand = StartConnectionCommand;
            PendingConnection.FinishCommand = CreateConnectionCommand;

            InitializeTemplateCategories();

            // 启用多选集合变化监听
            SelectedItems.CollectionChanged += SelectedItems_CollectionChanged;

            // 启用虚拟化 - 监听节点集合变化
            Nodes.CollectionChanged += (s, e) =>
            {
                if (e.NewItems != null)
                {
                    foreach (ModuleNodeViewModel node in e.NewItems)
                    {
                        _virtualizationManager.AddNode<ModuleNodeViewModel>(node);
                    }
                }

                if (e.OldItems != null)
                {
                    foreach (ModuleNodeViewModel node in e.OldItems)
                    {
                        _virtualizationManager.RemoveNode<ModuleNodeViewModel>(node);
                    }
                }
            };
        }

        /// <summary>
        /// 添加一个新模块到画布
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <param name="position">位置</param>
        /// <returns>添加的节点</returns>
        public ModuleNodeViewModel AddModule(ModuleType moduleType, Point position)
        {
            // 确保位置在可见区域内，避免被UI元素遮挡
            var adjustedPosition = EnsureNodeVisibility(position);

            // 创建模块模型
            var module = new ModuleModel
            {
                Type = moduleType,
                NodeType = NodeTypeRegistry.GetNodeType(moduleType),
                Name = GetModuleTypeName(moduleType),
                Description = $"{GetModuleTypeName(moduleType)}模块"
            };

            // 创建节点视图模型
            var nodeViewModel = new ModuleNodeViewModel
            {
                Module = module,
                Location = adjustedPosition
            };

            // 订阅节点启用状态变化事件
            nodeViewModel.EnabledChanged += OnNodeEnabledChanged;

            // 添加到集合
            Nodes.Add(nodeViewModel);

            // 直接设置选中状态，不调用任何方法
            SelectedNode = nodeViewModel;
            IsPropertyPanelVisible = true;

            System.Diagnostics.Debug.WriteLine($"新节点已添加: {nodeViewModel.Title} 位置: ({adjustedPosition.X:F1}, {adjustedPosition.Y:F1})");

            return nodeViewModel;
        }

        /// <summary>
        /// 移除节点
        /// </summary>
        /// <param name="node">要移除的节点</param>
        public void RemoveNode(ModuleNodeViewModel node)
        {
            // 取消订阅事件
            node.EnabledChanged -= OnNodeEnabledChanged;

            // 移除与该节点相关的所有连接
            var connectionsToRemove = Connections
                .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
                .ToList();

            foreach (var connection in connectionsToRemove)
            {
                Connections.Remove(connection);
            }

            // 移除节点
            Nodes.Remove(node);

            // 从选择集合中移除
            if (SelectedItems.Contains(node))
            {
                SelectedItems.Remove(node);
            }

            // 如果移除的是当前选中的节点，清除选中
            if (SelectedNode == node)
            {
                SelectedNode = null;
            }

            // 更新属性面板可见性
            IsPropertyPanelVisible = SelectedNode != null;
        }

        /// <summary>
        /// 节点启用状态变化事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="isEnabled">是否启用</param>
        private void OnNodeEnabledChanged(object? sender, bool isEnabled)
        {
            if (sender is ModuleNodeViewModel node)
            {
                if (!isEnabled)
                {
                    // 节点被禁用时，禁用所有连接到该节点的连接线
                    DisableNodeConnections(node);
                    System.Diagnostics.Debug.WriteLine($"节点 {node.Title} 已禁用，禁用相关连接线");
                }
                else
                {
                    // 节点被启用时，检查并可能重新启用相关连接线
                    EnableNodeConnections(node);
                    System.Diagnostics.Debug.WriteLine($"节点 {node.Title} 已启用，检查相关连接线状态");
                }
            }
        }

        /// <summary>
        /// 禁用节点的所有连接线
        /// </summary>
        /// <param name="node">节点</param>
        private void DisableNodeConnections(ModuleNodeViewModel node)
        {
            var nodeConnections = Connections
                .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
                .ToList();

            foreach (var connection in nodeConnections)
            {
                connection.IsEnabled = false;
                System.Diagnostics.Debug.WriteLine($"  禁用连接线: {connection.GetConnectionDescription()}");
            }
        }

        /// <summary>
        /// 启用节点的相关连接线（仅当两端节点都启用时）
        /// </summary>
        /// <param name="node">节点</param>
        private void EnableNodeConnections(ModuleNodeViewModel node)
        {
            var nodeConnections = Connections
                .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
                .ToList();

            foreach (var connection in nodeConnections)
            {
                // 只有当连接的两端节点都启用时，才启用连接线
                if (connection.CanBeEnabledBasedOnNodes())
                {
                    connection.IsEnabled = true;
                    System.Diagnostics.Debug.WriteLine($"  启用连接线: {connection.GetConnectionDescription()}");
                }
            }
        }

        /// <summary>
        /// 移除节点的所有连接（用于删除节点时）
        /// </summary>
        /// <param name="node">节点</param>
        private void RemoveNodeConnections(ModuleNodeViewModel node)
        {
            var connectionsToRemove = Connections
                .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
                .ToList();

            foreach (var connection in connectionsToRemove)
            {
                // 更新连接器状态
                if (connection.Source != null)
                {
                    connection.Source.IsConnected = false;
                }
                if (connection.Target != null)
                {
                    connection.Target.IsConnected = false;
                }

                // 移除连接
                Connections.Remove(connection);
            }
        }

        /// <summary>
        /// 批量移除选中的节点
        /// </summary>
        public void RemoveSelectedNodes()
        {
            var selectedNodes = SelectedItems.OfType<ModuleNodeViewModel>().ToList();

            if (selectedNodes.Count == 0)
                return;

            // 批量移除节点
            foreach (var node in selectedNodes)
            {
                // 移除与该节点相关的所有连接
                var connectionsToRemove = Connections
                    .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
                    .ToList();

                foreach (var connection in connectionsToRemove)
                {
                    Connections.Remove(connection);
                }

                // 移除节点
                Nodes.Remove(node);
            }

            // 清除选择状态
            ClearSelection();
        }

        /// <summary>
        /// 添加连接
        /// </summary>
        /// <param name="source">源连接点</param>
        /// <param name="target">目标连接点</param>
        /// <returns>成功返回true，否则返回false</returns>
        public bool AddConnection(ConnectorViewModel source, ConnectorViewModel target)
        {
            // 检查连接是否已存在
            if (Connections.Any(c => c.Source == source && c.Target == target))
            {
                return false;
            }

            // 创建连接
            var connection = new ConnectionViewModel(source, target);

            // 添加到集合
            Connections.Add(connection);
            return true;
        }



        /// <summary>
        /// 切换连接线的启用状态
        /// </summary>
        /// <param name="connection">连接线</param>
        public void ToggleConnectionEnabled(ConnectionViewModel connection)
        {
            if (connection.IsEnabled)
            {
                // 禁用连接线
                connection.IsEnabled = false;
                System.Diagnostics.Debug.WriteLine($"手动禁用连接线: {connection.GetConnectionDescription()}");
            }
            else
            {
                // 启用连接线（需要检查节点状态）
                if (connection.CanBeEnabledBasedOnNodes())
                {
                    connection.IsEnabled = true;
                    System.Diagnostics.Debug.WriteLine($"手动启用连接线: {connection.GetConnectionDescription()}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"无法启用连接线（节点被禁用）: {connection.GetConnectionDescription()}");
                }
            }
        }

        /// <summary>
        /// 创建连接（按照Nodify官方示例模式）
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        internal void CreateConnection(ConnectorViewModel? source, ConnectorViewModel? target)
        {
            System.Diagnostics.Debug.WriteLine($"CreateConnection called: source={source?.Title}, target={target?.Title}");

            if (source == null || target == null)
            {
                System.Diagnostics.Debug.WriteLine("Source or target is null, keeping pending connection visible");
                PendingConnection.IsVisible = true;
                return;
            }

            // 确保source是输出点，target是输入点
            if (source.IsInput && !target.IsInput)
            {
                var temp = source;
                source = target;
                target = temp;
            }

            // 验证连接的有效性
            if (!CanCreateConnection(source, target))
            {
                System.Diagnostics.Debug.WriteLine($"无效的连接尝试: {source.Title} -> {target.Title}");
                return;
            }

            // 检查是否已存在相同的连接
            if (Connections.Any(c => (c.Source == source && c.Target == target) ||
                                   (c.Source == target && c.Target == source)))
            {
                System.Diagnostics.Debug.WriteLine($"连接已存在: {source.Title} -> {target.Title}");
                return;
            }

            // 检查目标连接器是否支持多连接
            if (!target.CanAddConnection())
            {
                System.Diagnostics.Debug.WriteLine($"目标连接器不支持多连接，断开现有连接: {target.Title}");
                DisconnectConnector(target);
            }

            try
            {
                // 创建新连接
                var connection = new ConnectionViewModel(source, target)
                {
                    Id = Guid.NewGuid().ToString()
                };

                // 添加到集合（连接状态由ConnectorViewModel自动管理）
                Connections.Add(connection);

                // 注册连接到样式服务以启用动画
                RegisterConnectionForAnimation(connection, source, target);

                System.Diagnostics.Debug.WriteLine($"连接创建成功: {source.Title} -> {target.Title}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建连接时发生错误: {ex.Message}");
            }
            finally
            {
                // 隐藏待定连接
                PendingConnection.IsVisible = false;
            }
        }

        /// <summary>
        /// 注册连接到动画服务
        /// </summary>
        /// <param name="connection">连接对象</param>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        private void RegisterConnectionForAnimation(ConnectionViewModel connection, ConnectorViewModel source, ConnectorViewModel target)
        {
            try
            {
                // 生成字段ID - 使用连接器标题或节点信息
                var fieldId = GenerateFieldId(source, target);

                // 注册连接到样式服务
                _connectionStyleService.RegisterConnection(connection, fieldId);

                // 通知引用追踪器连接状态变化
                _referenceTracker.NotifyConnectionStateChanged(fieldId, true, connection);

                System.Diagnostics.Debug.WriteLine($"连接已注册到动画服务: {fieldId}, FlowState: {connection.FlowState}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册连接动画失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成字段ID用于连接注册
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        /// <returns>字段ID</returns>
        private string GenerateFieldId(ConnectorViewModel source, ConnectorViewModel target)
        {
            // 使用源节点和目标节点的信息生成唯一的字段ID
            var sourceNodeName = (source.Node as ModuleNodeViewModel)?.Title ?? "Unknown";
            var targetNodeName = (target.Node as ModuleNodeViewModel)?.Title ?? "Unknown";
            var sourceTitle = source.Title ?? "Output";
            var targetTitle = target.Title ?? "Input";

            return $"{sourceNodeName}.{sourceTitle}->{targetNodeName}.{targetTitle}";
        }

        /// <summary>
        /// 检查是否可以创建连接
        /// </summary>
        internal bool CanCreateConnection(ConnectorViewModel? source, ConnectorViewModel? target)
        {
            if (source == null || target == null)
            {
                return false;
            }

            // 基本验证
            if (source == target || source.Node == target.Node)
            {
                return false;
            }

            // 确保一个是输入，一个是输出
            if (source.IsInput == target.IsInput)
            {
                return false;
            }

            // 检查数据类型兼容性（如果已设置）
            if (!string.IsNullOrEmpty(source.DataType) &&
                !string.IsNullOrEmpty(target.DataType) &&
                source.DataType != target.DataType)
            {
                return false;
            }

            // 检查是否会形成循环
            if (WouldCreateCycle(source, target))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查添加连接是否会形成循环
        /// </summary>
        private bool WouldCreateCycle(ConnectorViewModel source, ConnectorViewModel target)
        {
            // 获取起点和终点节点
            var sourceNode = source.Node as ModuleNodeViewModel;
            var targetNode = target.Node as ModuleNodeViewModel;

            if (sourceNode == null || targetNode == null)
            {
                return false;
            }

            // 如果source是输入点，交换起点和终点
            if (source.IsInput)
            {
                var temp = sourceNode;
                sourceNode = targetNode;
                targetNode = temp;
            }

            // 使用HashSet来跟踪已访问的节点
            var visited = new HashSet<ModuleNodeViewModel>();
            return HasCycle(sourceNode, targetNode, visited);
        }

        /// <summary>
        /// 递归检查是否存在循环
        /// </summary>
        private bool HasCycle(ModuleNodeViewModel current, ModuleNodeViewModel target, HashSet<ModuleNodeViewModel> visited)
        {
            if (current == target)
            {
                return true;
            }

            if (!visited.Add(current))
            {
                return false;
            }

            // 获取当前节点的所有输出连接
            var connections = Connections.Where(c =>
                (c.Source?.Node as ModuleNodeViewModel) == current);

            foreach (var conn in connections)
            {
                var nextNode = conn.Target?.Node as ModuleNodeViewModel;
                if (nextNode != null && HasCycle(nextNode, target, visited))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 断开连接器的所有连接
        /// </summary>
        public void DisconnectConnector(ConnectorViewModel connector)
        {
            var connectionsToRemove = Connections.Where(c =>
                c.Source == connector || c.Target == connector).ToList();

            foreach (var connection in connectionsToRemove)
            {
                // 注销连接动画
                UnregisterConnectionFromAnimation(connection);

                Connections.Remove(connection);
            }

            // 清除连接器的连接记录（连接状态会自动更新）
            connector.ClearConnections();
        }

        /// <summary>
        /// 移除单个连接
        /// </summary>
        /// <param name="connection">要移除的连接</param>
        public void RemoveConnection(ConnectionViewModel connection)
        {
            if (connection != null && Connections.Contains(connection))
            {
                // 注销连接动画
                UnregisterConnectionFromAnimation(connection);

                // 从集合中移除
                Connections.Remove(connection);

                // 从连接器中移除连接记录（连接状态会自动更新）
                connection.Source?.RemoveConnection(connection);
                connection.Target?.RemoveConnection(connection);
            }
        }

        /// <summary>
        /// 从动画服务注销连接
        /// </summary>
        /// <param name="connection">连接对象</param>
        private void UnregisterConnectionFromAnimation(ConnectionViewModel connection)
        {
            try
            {
                if (connection.Source != null && connection.Target != null)
                {
                    // 生成字段ID
                    var fieldId = GenerateFieldId(connection.Source, connection.Target);

                    // 从样式服务注销连接
                    _connectionStyleService.UnregisterConnection(connection.Id, fieldId);

                    // 通知引用追踪器连接状态变化
                    _referenceTracker.NotifyConnectionStateChanged(fieldId, false);

                    System.Diagnostics.Debug.WriteLine($"连接已从动画服务注销: {fieldId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注销连接动画失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空画布
        /// </summary>
        public void Clear()
        {
            Connections.Clear();
            Nodes.Clear();
            SelectedNode = null;
            SelectedItems.Clear();

            // 更新属性面板可见性
            IsPropertyPanelVisible = false;
        }

        /// <summary>
        /// 设置所有模块的启用状态
        /// </summary>
        /// <param name="isEnabled">是否启用</param>
        public void SetAllModulesEnabled(bool isEnabled)
        {
            foreach (var node in Nodes)
            {
                if (node.Module != null)
                {
                    node.Module.IsEnabled = isEnabled;
                }
            }
        }

        /// <summary>
        /// 隐藏所有关闭的模块
        /// </summary>
        public void HideClosedModules()
        {
            foreach (var node in Nodes)
            {
                if (node.Module != null && !node.Module.IsEnabled)
                {
                    node.IsLightBulbOn = false; // IsLightBulbOn 是 ModuleNodeViewModel 的属性
                }
            }
        }

        /// <summary>
        /// 模板分类集合
        /// </summary>
        public ObservableCollection<TemplateCategory> TemplateCategories { get; } = new ObservableCollection<TemplateCategory>();

        /// <summary>
        /// 添加模块命令
        /// </summary>
        public ICommand AddModuleCommand { get; }

        /// <summary>
        /// 挂起连接视图模型
        /// </summary>
        public PendingConnectionViewModel PendingConnection { get; }

        /// <summary>
        /// 断开连接器命令
        /// </summary>
        public ICommand DisconnectConnectorCommand { get; }

        /// <summary>
        /// 执行断开连接器命令
        /// </summary>
        /// <param name="connector">要断开的连接器</param>
        private void ExecuteDisconnectConnector(ConnectorViewModel connector)
        {
            if (connector != null)
            {
                DisconnectConnector(connector);
                System.Diagnostics.Debug.WriteLine($"已断开连接器: {connector.Title}");
            }
        }

        /// <summary>
        /// 执行移除连接命令
        /// </summary>
        /// <param name="connection">要移除的连接</param>
        private void ExecuteRemoveConnection(ConnectionViewModel connection)
        {
            if (connection != null)
            {
                RemoveConnection(connection);
                System.Diagnostics.Debug.WriteLine($"已移除连接: {connection.Source?.Title} -> {connection.Target?.Title}");
            }
        }

        /// <summary>
        /// 执行连接线样式切换命令
        /// </summary>
        /// <param name="newStyle">新的连接线样式</param>
        private void ExecuteChangeConnectionLineStyle(ConnectionLineStyle newStyle)
        {
            System.Diagnostics.Debug.WriteLine($"[DEBUG] 切换连接线样式: {CurrentConnectionLineStyle} -> {newStyle}");

            if (CurrentConnectionLineStyle != newStyle)
            {
                CurrentConnectionLineStyle = newStyle;
                System.Diagnostics.Debug.WriteLine($"[DEBUG] 连接线样式已切换为: {newStyle.GetDisplayName()}");
            }
        }

        /// <summary>
        /// 连接线样式变更时的处理逻辑
        /// </summary>
        private void OnConnectionLineStyleChanged()
        {
            System.Diagnostics.Debug.WriteLine($"[DEBUG] OnConnectionLineStyleChanged: {CurrentConnectionLineStyle}");

            try
            {
                // 触发连接线重新渲染
                // 通过PropertyChanged事件通知UI更新连接线模板
                OnPropertyChanged(nameof(CurrentConnectionLineStyle));

                // 可以在这里添加样式配置的持久化保存逻辑
                SaveConnectionLineStyleConfiguration();

                System.Diagnostics.Debug.WriteLine($"[DEBUG] 连接线样式变更处理完成: {CurrentConnectionLineStyle.GetDisplayName()}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 连接线样式变更处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存连接线样式配置
        /// </summary>
        private void SaveConnectionLineStyleConfiguration()
        {
            try
            {
                // 这里可以实现配置的持久化保存
                // 例如保存到应用程序设置或配置文件中
                System.Diagnostics.Debug.WriteLine($"[DEBUG] 保存连接线样式配置: {CurrentConnectionLineStyle}");

                // TODO: 实现具体的配置保存逻辑
                // Properties.Settings.Default.ConnectionLineStyle = CurrentConnectionLineStyle.ToString();
                // Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] 保存连接线样式配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建连接命令
        /// </summary>
        public ICommand CreateConnectionCommand { get; }

        /// <summary>
        /// 开始连接命令
        /// </summary>
        public ICommand StartConnectionCommand { get; }

        /// <summary>
        /// 移除连接命令
        /// </summary>
        public ICommand RemoveConnectionCommand { get; }

        /// <summary>
        /// 自动布局命令
        /// </summary>
        public ICommand AutoLayoutCommand { get; }

        /// <summary>
        /// 连接线样式切换命令
        /// </summary>
        public ICommand ChangeConnectionLineStyleCommand { get; }

        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            // 只清除ViewModel的状态，不手动操作IsSelected
            // 让Nodify的内置机制处理IsSelected属性
            SelectedNode = null;
            SelectedItems.Clear();
            IsPropertyPanelVisible = false;
        }

        /// <summary>
        /// 选择单个节点
        /// </summary>
        /// <param name="node">要选择的节点</param>
        public void SelectSingleNode(ModuleNodeViewModel node)
        {
            // 清除所有选择
            ClearSelection();

            // 选中指定节点
            node.IsSelected = true;
            SelectedNode = node;
            SelectedItems.Add(node);
            IsPropertyPanelVisible = true;
        }

        /// <summary>
        /// 切换节点的多选状态
        /// </summary>
        /// <param name="node">要切换的节点</param>
        public void ToggleNodeSelection(ModuleNodeViewModel node)
        {
            if (SelectedItems.Contains(node))
            {
                // 取消选择
                SelectedItems.Remove(node);
                node.IsSelected = false;
            }
            else
            {
                // 添加到选择
                SelectedItems.Add(node);
                node.IsSelected = true;
            }

            // 手动更新选择状态
            UpdateSelectionState();
        }

        /// <summary>
        /// 多选集合变化事件处理
        /// </summary>
        private void SelectedItems_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // 安全地更新选择状态
            try
            {
                UpdateSelectionState();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SelectedItems_CollectionChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新选择状态和属性面板可见性
        /// </summary>
        public void UpdateSelectionState()
        {
            if (SelectedItems.Count == 1)
            {
                // 单选：显示属性面板
                var selectedNode = SelectedItems.Cast<ModuleNodeViewModel>().First();
                SelectedNode = selectedNode;
                IsPropertyPanelVisible = true;
            }
            else
            {
                // 多选或无选择：隐藏属性面板
                SelectedNode = null;
                IsPropertyPanelVisible = false;
            }
        }

        /// <summary>
        /// 执行添加模块命令
        /// </summary>
        /// <param name="templateItem">模板项</param>
        private void ExecuteAddModule(TemplateItem templateItem)
        {
            if (templateItem != null)
            {
                // 获取智能位置，确保新节点在可见区域内
                var position = GetSmartNodePosition();
                AddModule(templateItem.ModuleType, position);
            }
        }

        /// <summary>
        /// 判断是否可以执行添加模块命令
        /// </summary>
        /// <param name="templateItem">模板项</param>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteAddModule(TemplateItem templateItem)
        {
            return templateItem != null;
        }

        /// <summary>
        /// 获取智能节点位置，确保新节点在可见区域内且不重叠
        /// </summary>
        /// <returns>新节点的位置</returns>
        private Point GetSmartNodePosition()
        {
            // 基础位置：视口中心偏移一些距离，避免正好在中心
            var baseX = ViewportCenter.X - 125; // 稍微偏左（节点宽度的一半）
            var baseY = ViewportCenter.Y - 60;  // 稍微偏上（节点高度的一半）

            // 节点的大小估算（用于避免重叠）
            const double nodeWidth = 250;
            const double nodeHeight = 120;
            const double horizontalSpacing = 50; // 水平间距增加
            const double verticalSpacing = 40;   // 垂直间距增加

            // 如果没有现有节点，直接返回基础位置
            if (Nodes.Count == 0)
            {
                return new Point(baseX, baseY);
            }

            // 尝试在视口可见区域内找到不重叠的位置
            var attempts = 0;
            const int maxAttempts = 30; // 减少尝试次数，使用更清晰的网格
            const int columnsPerRow = 3; // 每行3列，更整齐

            while (attempts < maxAttempts)
            {
                // 计算候选位置 - 使用更清晰的网格布局
                var column = attempts % columnsPerRow;
                var row = attempts / columnsPerRow;

                var candidateX = baseX + column * (nodeWidth + horizontalSpacing);
                var candidateY = baseY + row * (nodeHeight + verticalSpacing);

                var candidatePosition = new Point(candidateX, candidateY);

                // 使用矩形重叠检测而不是圆形距离检测
                bool hasOverlap = false;
                foreach (var existingNode in Nodes)
                {
                    if (IsRectangleOverlap(
                        candidatePosition, nodeWidth, nodeHeight,
                        existingNode.Location, nodeWidth, nodeHeight,
                        horizontalSpacing / 2)) // 使用间距的一半作为最小距离
                    {
                        hasOverlap = true;
                        break;
                    }
                }

                if (!hasOverlap)
                {
                    System.Diagnostics.Debug.WriteLine($"找到不重叠位置: 第{attempts + 1}次尝试, 位置({candidateX:F1}, {candidateY:F1})");
                    return candidatePosition;
                }

                attempts++;
            }

            // 如果找不到不重叠的位置，使用强制偏移策略
            var fallbackX = baseX + (Nodes.Count % 8) * 40;
            var fallbackY = baseY + (Nodes.Count / 8) * 50;

            System.Diagnostics.Debug.WriteLine($"使用后备位置: ({fallbackX:F1}, {fallbackY:F1})");
            return new Point(fallbackX, fallbackY);
        }

        /// <summary>
        /// 检查两个矩形是否重叠（考虑最小间距）
        /// </summary>
        /// <param name="pos1">第一个矩形位置</param>
        /// <param name="width1">第一个矩形宽度</param>
        /// <param name="height1">第一个矩形高度</param>
        /// <param name="pos2">第二个矩形位置</param>
        /// <param name="width2">第二个矩形宽度</param>
        /// <param name="height2">第二个矩形高度</param>
        /// <param name="minDistance">最小间距</param>
        /// <returns>是否重叠</returns>
        private static bool IsRectangleOverlap(Point pos1, double width1, double height1,
                                             Point pos2, double width2, double height2,
                                             double minDistance)
        {
            // 扩展矩形边界以包含最小间距
            var left1 = pos1.X - minDistance;
            var right1 = pos1.X + width1 + minDistance;
            var top1 = pos1.Y - minDistance;
            var bottom1 = pos1.Y + height1 + minDistance;

            var left2 = pos2.X;
            var right2 = pos2.X + width2;
            var top2 = pos2.Y;
            var bottom2 = pos2.Y + height2;

            // 检查是否重叠
            return !(right1 <= left2 || left1 >= right2 || bottom1 <= top2 || top1 >= bottom2);
        }

        /// <summary>
        /// 更新视口信息（由UI层调用）
        /// </summary>
        /// <param name="center">视口中心</param>
        /// <param name="zoom">缩放级别</param>
        /// <param name="canvasSize">画布实际尺寸</param>
        /// <param name="viewport">实际可见视口</param>
        public void UpdateViewportInfo(Point center, double zoom, Size? canvasSize = null, Rect? viewport = null)
        {
            // 防止视口信息更新循环
            if (_isUpdatingViewportInfo || _isLayoutInProgress || _isAnimating)
            {
                return;
            }

            _isUpdatingViewportInfo = true;
            try
            {
                ViewportCenter = center;
                ViewportZoom = zoom;

                // 更新实际画布尺寸
                if (canvasSize.HasValue)
                {
                    ActualCanvasSize = canvasSize.Value;
                }

                // 更新实际可见视口
                if (viewport.HasValue)
                {
                    ActualViewport = viewport.Value;
                    // 更新虚拟化管理器的视口信息
                    _virtualizationManager.UpdateViewport(new ViewportRect(viewport.Value.X, viewport.Value.Y, viewport.Value.Width, viewport.Value.Height), zoom);
                }
                else
                {
                    // 如果没有提供视口信息，使用默认计算方式
                    var defaultViewport = new Rect(center.X - 400, center.Y - 300, 800, 600);
                    ActualViewport = defaultViewport;
                    _virtualizationManager.UpdateViewport(new ViewportRect(defaultViewport.X, defaultViewport.Y, defaultViewport.Width, defaultViewport.Height), zoom);
                }
            }
            finally
            {
                _isUpdatingViewportInfo = false;
            }
        }

        /// <summary>
        /// 计算所有节点的边界矩形
        /// </summary>
        /// <returns>包含所有节点的边界矩形</returns>
        public Rect CalculateNodesBounds()
        {
            if (!Nodes.Any())
                return new Rect(0, 0, 0, 0);

            var minX = double.MaxValue;
            var minY = double.MaxValue;
            var maxX = double.MinValue;
            var maxY = double.MinValue;

            const double nodeWidth = 250;
            const double nodeHeight = 120;

            foreach (var node in Nodes)
            {
                var x = node.Location.X;
                var y = node.Location.Y;

                minX = Math.Min(minX, x);
                minY = Math.Min(minY, y);
                maxX = Math.Max(maxX, x + nodeWidth);
                maxY = Math.Max(maxY, y + nodeHeight);
            }

            // 添加一些边距
            const double margin = 50;
            return new Rect(
              minX - margin,
              minY - margin,
              maxX - minX + 2 * margin,
              maxY - minY + 2 * margin
            );
        }

        /// <summary>
        /// 自动调整视图以适应所有节点
        /// </summary>
        public event Action<Rect>? FitToNodesRequested;

        /// <summary>
        /// 触发适应所有节点的视图调整
        /// </summary>
        public void FitToNodes()
        {
            System.Diagnostics.Debug.WriteLine("=== FitToNodes 被调用 ===");

            // 防止在布局过程中触发视图调整，避免布局循环
            if (_isLayoutInProgress || _isAnimating)
            {
                System.Diagnostics.Debug.WriteLine("布局或动画进行中，跳过FitToNodes调用");
                return;
            }

            var bounds = CalculateNodesBounds();
            System.Diagnostics.Debug.WriteLine($"计算的节点边界: {bounds}");

            if (bounds.Width > 0 && bounds.Height > 0)
            {
                System.Diagnostics.Debug.WriteLine("触发FitToNodesRequested事件");
                FitToNodesRequested?.Invoke(bounds);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("节点边界无效，跳过FitToNodes");
            }
        }

        /// <summary>
        /// 确保节点位置在可见区域内，避免被UI元素遮挡
        /// </summary>
        /// <param name="originalPosition">原始位置</param>
        /// <returns>调整后的位置</returns>
        private Point EnsureNodeVisibility(Point originalPosition)
        {
            var adjustedX = originalPosition.X;
            var adjustedY = originalPosition.Y;

            // 节点大小估算
            const double nodeWidth = 250;
            const double nodeHeight = 120;
            const double margin = 20; // 边距

            // 确保节点不会超出视口边界（考虑缩放）
            var viewportWidth = 800; // 默认视口宽度
            var viewportHeight = 600; // 默认视口高度

            // 左边界检查（避免被左侧模板面板遮挡）
            var leftPanelWidth = 250; // 左侧面板宽度
            if (adjustedX < leftPanelWidth + margin)
            {
                adjustedX = leftPanelWidth + margin;
            }

            // 右边界检查（避免被右侧属性面板遮挡）
            var rightPanelWidth = IsPropertyPanelVisible ? 300 : 0; // 右侧面板宽度
            var maxX = viewportWidth - rightPanelWidth - nodeWidth - margin;
            if (adjustedX > maxX)
            {
                adjustedX = maxX;
            }

            // 上边界检查
            if (adjustedY < margin)
            {
                adjustedY = margin;
            }

            // 下边界检查
            var maxY = viewportHeight - nodeHeight - margin;
            if (adjustedY > maxY)
            {
                adjustedY = maxY;
            }

            return new Point(adjustedX, adjustedY);
        }

        /// <summary>
        /// 初始化模板分类
        /// </summary>
        private void InitializeTemplateCategories()
        {
            // 输入类 - 数据源
            var inputCategory = new TemplateCategory
            {
                Name = "输入节点",
                Color = Color.FromRgb(76, 175, 80) // 绿色
            };
            inputCategory.Items.Add(new TemplateItem { Name = "文件输入", Description = "从文件读取数据", ModuleType = ModuleType.FileInput });
            inputCategory.Items.Add(new TemplateItem { Name = "数据库输入", Description = "从数据库读取数据", ModuleType = ModuleType.DatabaseInput });
            inputCategory.Items.Add(new TemplateItem { Name = "API输入", Description = "从API接口读取数据", ModuleType = ModuleType.APIInput });
            inputCategory.Items.Add(new TemplateItem { Name = "CAD输入", Description = "从CAD文件读取数据", ModuleType = ModuleType.CADInput });
            inputCategory.Items.Add(new TemplateItem { Name = "Excel输入", Description = "从Excel文件读取数据", ModuleType = ModuleType.ExcelInput });
            inputCategory.Items.Add(new TemplateItem { Name = "CSV输入", Description = "从CSV文件读取数据", ModuleType = ModuleType.CSVInput });
            inputCategory.Items.Add(new TemplateItem { Name = "XML输入", Description = "从XML文件读取数据", ModuleType = ModuleType.XMLInput });
            inputCategory.Items.Add(new TemplateItem { Name = "JSON输入", Description = "从JSON文件读取数据", ModuleType = ModuleType.JSONInput });
            inputCategory.Items.Add(new TemplateItem { Name = "手动输入数据", Description = "手动输入数据节点", ModuleType = ModuleType.ManualDataInput });

            // 数学常量节点
            inputCategory.Items.Add(new TemplateItem { Name = "π常量", Description = "数学常量π (3.14159...)", ModuleType = ModuleType.PiConstant });
            inputCategory.Items.Add(new TemplateItem { Name = "e常量", Description = "自然对数底e (2.71828...)", ModuleType = ModuleType.EConstant });
            inputCategory.Items.Add(new TemplateItem { Name = "φ黄金比例", Description = "黄金比例常量φ (1.61803...)", ModuleType = ModuleType.GoldenRatioConstant });

            TemplateCategories.Add(inputCategory);

            // 常规数据类
            var dataCategory = new TemplateCategory
            {
                Name = "常规数据类",
                Color = Color.FromRgb(66, 133, 244) // 蓝色
            };
            dataCategory.Items.Add(new TemplateItem { Name = "平面管线", Description = "平面管线类模块", ModuleType = ModuleType.PipeLine });
            dataCategory.Items.Add(new TemplateItem { Name = "立管", Description = "立管类模块", ModuleType = ModuleType.RiserPipe });
            dataCategory.Items.Add(new TemplateItem { Name = "调压箱调压柜", Description = "调压箱调压柜类模块", ModuleType = ModuleType.PressureBox });
            dataCategory.Items.Add(new TemplateItem { Name = "开挖回填", Description = "开挖回填模块", ModuleType = ModuleType.Excavation });
            dataCategory.Items.Add(new TemplateItem { Name = "破除恢复", Description = "破除恢复模块", ModuleType = ModuleType.Demolition });
            dataCategory.Items.Add(new TemplateItem { Name = "防腐", Description = "防腐模块", ModuleType = ModuleType.AntiCorrosion });
            dataCategory.Items.Add(new TemplateItem { Name = "防雷防静电", Description = "防雷防静电模块", ModuleType = ModuleType.LightningProtection });
            TemplateCategories.Add(dataCategory);

            // 数据衍生关联类
            var relationCategory = new TemplateCategory
            {
                Name = "数据衍生关联类",
                Color = Color.FromRgb(15, 157, 88) // 绿色
            };
            relationCategory.Items.Add(new TemplateItem { Name = "警示带示踪线", Description = "警示带示踪线模块", ModuleType = ModuleType.WarningBand });
            relationCategory.Items.Add(new TemplateItem { Name = "焊口探伤", Description = "焊口探伤模块", ModuleType = ModuleType.WeldInspection });
            relationCategory.Items.Add(new TemplateItem { Name = "安装台班", Description = "安装台班模块", ModuleType = ModuleType.InstallationTeam });
            relationCategory.Items.Add(new TemplateItem { Name = "措施", Description = "措施模块", ModuleType = ModuleType.Measures });
            TemplateCategories.Add(relationCategory);

            // 触发器类
            var triggerCategory = new TemplateCategory
            {
                Name = "触发器类",
                Color = Color.FromRgb(255, 152, 0) // 橙色
            };
            triggerCategory.Items.Add(new TemplateItem { Name = "点击触发器", Description = "点击触发器模块", ModuleType = ModuleType.ClickTrigger });
            triggerCategory.Items.Add(new TemplateItem { Name = "关联触发器", Description = "关联触发器模块", ModuleType = ModuleType.AssociationTrigger });
            triggerCategory.Items.Add(new TemplateItem { Name = "定时触发器", Description = "定时触发器模块", ModuleType = ModuleType.TimedTrigger });
            triggerCategory.Items.Add(new TemplateItem { Name = "文件变化触发器", Description = "文件变化触发器模块", ModuleType = ModuleType.FileChangeTrigger });
            triggerCategory.Items.Add(new TemplateItem { Name = "环境触发器", Description = "环境触发器模块", ModuleType = ModuleType.EnvironmentTrigger });
            TemplateCategories.Add(triggerCategory);

            // 处理类
            var processCategory = new TemplateCategory
            {
                Name = "处理类",
                Color = Color.FromRgb(156, 39, 176) // 紫色
            };
            processCategory.Items.Add(new TemplateItem { Name = "数据过滤", Description = "数据过滤模块", ModuleType = ModuleType.DataFilter });
            processCategory.Items.Add(new TemplateItem { Name = "标签搜索", Description = "标签搜索模块", ModuleType = ModuleType.TagSearch });
            // 数据计算改为动态插件加载（仅当插件存在时显示）
            processCategory.Items.Add(new TemplateItem { Name = "数据验证", Description = "数据验证模块", ModuleType = ModuleType.DataValidation });
            processCategory.Items.Add(new TemplateItem { Name = "数据转换", Description = "数据转换模块", ModuleType = ModuleType.DataTransform });
            processCategory.Items.Add(new TemplateItem { Name = "数据条件", Description = "数据条件模块", ModuleType = ModuleType.DataCondition });
            processCategory.Items.Add(new TemplateItem { Name = "数组展开", Description = "数组展开节点", ModuleType = ModuleType.ArrayExpansion });
            processCategory.Items.Add(new TemplateItem { Name = "其他", Description = "其他处理模块", ModuleType = ModuleType.Other });

            // 动态插件节点：将 Group=处理器 的插件模板按需追加（保留固定列表的其它项）
            AppendPluginTemplatesToProcessCategory(processCategory, context: null);

            TemplateCategories.Add(processCategory);

            // 整理类
            var organizeCategory = new TemplateCategory
            {
                Name = "整理类",
                Color = Color.FromRgb(139, 92, 246) // 中紫色
            };
            organizeCategory.Items.Add(new TemplateItem { Name = "表格管理", Description = "表格管理模块", ModuleType = ModuleType.TableManager });
            organizeCategory.Items.Add(new TemplateItem { Name = "图形API", Description = "图形API模块", ModuleType = ModuleType.GraphicsAPI });
            organizeCategory.Items.Add(new TemplateItem { Name = "Excel/CSV", Description = "Excel/CSV处理模块", ModuleType = ModuleType.ExcelCSV });
            organizeCategory.Items.Add(new TemplateItem { Name = "Word处理", Description = "Word处理模块", ModuleType = ModuleType.WordProcessor });
            TemplateCategories.Add(organizeCategory);

            // 输出类
            var outputCategory = new TemplateCategory
            {
                Name = "输出类",
                Color = Color.FromRgb(168, 85, 247) // 浅紫色
            };
            outputCategory.Items.Add(new TemplateItem { Name = "文件生成", Description = "文件生成模块", ModuleType = ModuleType.FileGeneration });
            outputCategory.Items.Add(new TemplateItem { Name = "手动定位", Description = "手动定位模块", ModuleType = ModuleType.ManualLocation });
            outputCategory.Items.Add(new TemplateItem { Name = "指定路径", Description = "指定路径模块", ModuleType = ModuleType.SpecifiedPath });
            outputCategory.Items.Add(new TemplateItem { Name = "第三方API", Description = "第三方API模块", ModuleType = ModuleType.ThirdPartyAPI });
            outputCategory.Items.Add(new TemplateItem { Name = "CAD导出", Description = "CAD导出模块", ModuleType = ModuleType.CADExport });
            outputCategory.Items.Add(new TemplateItem { Name = "Excel导出", Description = "Excel导出模块", ModuleType = ModuleType.ExcelExport });
            outputCategory.Items.Add(new TemplateItem { Name = "CSV导出", Description = "CSV导出模块", ModuleType = ModuleType.CSVExport });
            outputCategory.Items.Add(new TemplateItem { Name = "Word导出", Description = "Word导出模块", ModuleType = ModuleType.WordExport });
            outputCategory.Items.Add(new TemplateItem { Name = "PPT导出", Description = "PPT导出模块", ModuleType = ModuleType.PPTExport });
            outputCategory.Items.Add(new TemplateItem { Name = "图片导出", Description = "图片导出模块", ModuleType = ModuleType.ImageExport });
            outputCategory.Items.Add(new TemplateItem { Name = "发布释放", Description = "发布释放模块", ModuleType = ModuleType.PublishRelease });
            outputCategory.Items.Add(new TemplateItem { Name = "通知警报", Description = "通知警报模块", ModuleType = ModuleType.NotificationAlert });
            outputCategory.Items.Add(new TemplateItem { Name = "对话聊天", Description = "对话聊天模块", ModuleType = ModuleType.DialogChat });
            outputCategory.Items.Add(new TemplateItem { Name = "其他输出", Description = "其他输出模块", ModuleType = ModuleType.OtherOutput });
            TemplateCategories.Add(outputCategory);

            // 控制类
            var controlCategory = new TemplateCategory
            {
                Name = "控制节点",
                Color = Color.FromRgb(156, 39, 176) // 紫色
            };
            controlCategory.Items.Add(new TemplateItem { Name = "条件分支", Description = "条件分支控制", ModuleType = ModuleType.ConditionalBranch });
            controlCategory.Items.Add(new TemplateItem { Name = "循环处理", Description = "循环处理控制", ModuleType = ModuleType.LoopProcessor });
            controlCategory.Items.Add(new TemplateItem { Name = "错误处理", Description = "错误处理控制", ModuleType = ModuleType.ErrorHandler });
            controlCategory.Items.Add(new TemplateItem { Name = "流程控制", Description = "流程控制节点", ModuleType = ModuleType.FlowControl });
            controlCategory.Items.Add(new TemplateItem { Name = "脚本执行", Description = "脚本执行节点", ModuleType = ModuleType.ScriptExecutor });
            controlCategory.Items.Add(new TemplateItem { Name = "变量管理", Description = "变量管理节点", ModuleType = ModuleType.VariableManager });
            controlCategory.Items.Add(new TemplateItem { Name = "状态管理", Description = "状态管理节点", ModuleType = ModuleType.StateManager });
            controlCategory.Items.Add(new TemplateItem { Name = "智能体", Description = "AI智能体节点", ModuleType = ModuleType.AIAgent });
            TemplateCategories.Add(controlCategory);
        }

        /// <summary>
        /// 获取模块类型的名称
        /// </summary>
        /// <param name="type">模块类型</param>
        /// <returns>名称</returns>
        private static string GetModuleTypeName(ModuleType type)
        {
            return type switch
            {
                // 输入类
                ModuleType.FileInput => "文件输入",
                ModuleType.DatabaseInput => "数据库输入",
                ModuleType.APIInput => "API输入",
                ModuleType.CADInput => "CAD输入",
                ModuleType.ExcelInput => "Excel输入",
                ModuleType.CSVInput => "CSV输入",
                ModuleType.XMLInput => "XML输入",
                ModuleType.JSONInput => "JSON输入",
                ModuleType.ManualDataInput => "手动输入数据",

                // 常量类
                ModuleType.PiConstant => "π常量",
                ModuleType.EConstant => "e常量",
                ModuleType.GoldenRatioConstant => "φ黄金比例",

                // 常规数据类
                ModuleType.PipeLine => "平面管线",
                ModuleType.RiserPipe => "立管",
                ModuleType.PressureBox => "调压箱调压柜",
                ModuleType.Excavation => "开挖回填",
                ModuleType.Demolition => "破除恢复",
                ModuleType.AntiCorrosion => "防腐",
                ModuleType.LightningProtection => "防雷防静电",
                ModuleType.WarningBand => "警示带示踪线",
                ModuleType.WeldInspection => "焊口探伤",
                ModuleType.InstallationTeam => "安装台班",
                ModuleType.Measures => "措施",

                // 触发器类
                ModuleType.ClickTrigger => "点击触发器",
                ModuleType.AssociationTrigger => "关联触发器",
                ModuleType.TimedTrigger => "定时触发器",
                ModuleType.FileChangeTrigger => "文件变化触发器",
                ModuleType.EnvironmentTrigger => "环境触发器",

                // 处理类
                ModuleType.DataFilter => "数据过滤",
                ModuleType.TagSearch => "标签搜索",
                ModuleType.DataCalculation => "数据计算",
                ModuleType.DataValidation => "数据验证",
                ModuleType.DataTransform => "数据转换",
                ModuleType.DataCondition => "数据条件",
                ModuleType.ArrayExpansion => "数组展开",
                ModuleType.Other => "其他",

                // 整理类
                ModuleType.TableManager => "表格管理",
                ModuleType.GraphicsAPI => "图形API",
                ModuleType.ExcelCSV => "Excel/CSV",
                ModuleType.WordProcessor => "Word处理",

                // 输出类
                ModuleType.FileGeneration => "文件生成",
                ModuleType.ManualLocation => "手动定位",
                ModuleType.SpecifiedPath => "指定路径",
                ModuleType.ThirdPartyAPI => "第三方API",
                ModuleType.CADExport => "CAD导出",
                ModuleType.ExcelExport => "Excel导出",
                ModuleType.CSVExport => "CSV导出",
                ModuleType.WordExport => "Word导出",
                ModuleType.PPTExport => "PPT导出",
                ModuleType.ImageExport => "图片导出",
                ModuleType.PublishRelease => "发布释放",
                ModuleType.NotificationAlert => "通知警报",
                ModuleType.DialogChat => "对话聊天",
                ModuleType.OtherOutput => "其他输出",

                // 控制类
                ModuleType.ConditionalBranch => "条件分支",
                ModuleType.LoopProcessor => "循环处理",
                ModuleType.ErrorHandler => "错误处理",
                ModuleType.FlowControl => "流程控制",
                ModuleType.ScriptExecutor => "脚本执行",
                ModuleType.VariableManager => "变量管理",
                ModuleType.StateManager => "状态管理",
                ModuleType.AIAgent => "智能体",

                _ => "未知模块"
            };
        }

        #region 自动布局功能

        /// <summary>
        /// 执行层次布局
        /// </summary>
        public async Task ApplyHierarchicalLayoutAsync()
        {
            System.Diagnostics.Debug.WriteLine("=== ApplyHierarchicalLayoutAsync 开始执行 ===");

            // 强化防护措施，防止Stack overflow
            if (_isLayoutInProgress)
            {
                System.Diagnostics.Debug.WriteLine("布局已在进行中，跳过重复调用");
                return;
            }

            if (_isAnimating)
            {
                System.Diagnostics.Debug.WriteLine("动画正在进行中，跳过布局调用");
                return;
            }

            if (!Nodes.Any())
            {
                System.Diagnostics.Debug.WriteLine($"层次布局：没有节点可以布局，当前节点数量: {Nodes.Count}");
                return;
            }

            // 设置布局进行中标志
            _isLayoutInProgress = true;
            System.Diagnostics.Debug.WriteLine("设置 _isLayoutInProgress = true");

            try
            {
                // 统计锁定和未锁定的节点
                var lockedNodes = Nodes.Where(n => n.IsLocked).ToList();
                var unlockedNodes = Nodes.Where(n => !n.IsLocked).ToList();

                System.Diagnostics.Debug.WriteLine($"开始层次布局：总节点数量 {Nodes.Count}, 锁定节点 {lockedNodes.Count}, 未锁定节点 {unlockedNodes.Count}, 连接数量 {Connections.Count}");

                // 如果没有未锁定的节点，无需布局
                if (!unlockedNodes.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"层次布局：所有节点都已锁定，无需布局。锁定节点: {lockedNodes.Count}, 未锁定节点: {unlockedNodes.Count}");
                    return;
                }

                // 使用实际画布可见区域大小，确保布局在可见范围内
                var canvasWidth = Math.Max(800, ActualCanvasSize.Width); // 使用实际画布宽度
                var canvasHeight = Math.Max(600, ActualCanvasSize.Height); // 使用实际画布高度

                // 考虑左侧面板占用的空间，调整可用宽度
                var leftPanelWidth = 250; // 左侧模板面板宽度
                var availableWidth = Math.Max(600, canvasWidth - leftPanelWidth);

                // 确保起始位置在可见区域内
                var startX = Math.Max(leftPanelWidth + 50, ActualViewport.X + 50);
                var startY = Math.Max(50, ActualViewport.Y + 50);

                var options = new LayoutOptions
                {
                    HorizontalSpacing = 300, // 增加水平间距，让节点不那么拥挤
                    VerticalSpacing = 160,   // 增加垂直间距
                    StartX = startX, // 从可见区域开始布局
                    StartY = startY,
                    EnableAnimation = true,
                    AnimationDuration = 1200, // 增加动画时间，让过渡更明显
                    CanvasWidth = availableWidth, // 使用可用宽度进行换行计算
                    CanvasHeight = canvasHeight,
                    NodeWidth = 250,
                    NodeHeight = 120,
                    RowSpacing = 200 // 增加行间距，让布局更清晰
                };

                System.Diagnostics.Debug.WriteLine($"实际画布尺寸: {canvasWidth} x {canvasHeight}");
                System.Diagnostics.Debug.WriteLine($"可用布局宽度: {availableWidth}");
                System.Diagnostics.Debug.WriteLine($"布局起始位置: ({startX}, {startY})");

                System.Diagnostics.Debug.WriteLine($"准备调用ApplyLayoutAsync！节点数: {Nodes.Count}, 未锁定: {unlockedNodes.Count}");

                // 使用带动画的布局应用方法
                await ApplyBalancedGridLayoutAsync(options);

                System.Diagnostics.Debug.WriteLine("ApplyLayoutAsync 调用完成！");

                // 布局完成后，延迟调整视图以避免布局循环
                await Task.Delay(options.AnimationDuration + 200); // 等待动画完成

                // 只有在没有其他布局操作进行时才调整视图
                if (!_isAnimating && _isLayoutInProgress)
                {
                    FitToNodes();
                }

                // 如果有锁定节点，显示用户反馈
                if (lockedNodes.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"布局完成：{lockedNodes.Count} 个节点因锁定而未参与布局");
                    // 这里可以添加用户通知，比如状态栏消息或临时提示
                    ShowLayoutCompletionMessage(lockedNodes.Count, unlockedNodes.Count);
                }
            }
            finally
            {
                _isLayoutInProgress = false;
            }
        }

        /// <summary>
        /// Auto 策略：优先识别树（或森林），否则使用力导向；多分量在服务内打包
        /// </summary>
        public async Task ApplyAutoLayoutAsync()
        {
            if (_isLayoutInProgress || _isAnimating)
                return;

            if (!Nodes.Any()) return;

            _isLayoutInProgress = true;
            try
            {
                var canvasWidth = Math.Max(800, ActualCanvasSize.Width);
                var canvasHeight = Math.Max(600, ActualCanvasSize.Height);
                var leftPanelWidth = 250;
                var availableWidth = Math.Max(600, canvasWidth - leftPanelWidth);
                var startX = Math.Max(leftPanelWidth + 50, ActualViewport.X + 50);
                var startY = Math.Max(50, ActualViewport.Y + 50);

                var options = new LayoutOptions
                {
                    StartX = startX,
                    StartY = startY,
                    CanvasWidth = availableWidth,
                    CanvasHeight = canvasHeight,
                    NodeWidth = 250,
                    NodeHeight = 120,
                    HorizontalSpacing = 280,
                    VerticalSpacing = 160,
                    EnableAnimation = true,
                    AnimationDuration = 1100,
                    Iterations = 260,
                    InitialTemperature = Math.Max(availableWidth, canvasHeight) * 0.45,
                    MinDistance = 140,
                    PreventOverlap = true
                };

                var nodePositions = ConvertNodesToPositions(Nodes).ToList();
                var connectionInfos = ConvertConnectionsToInfo(Connections).ToList();

                var algo = SelectAutoAlgorithm(nodePositions, connectionInfos);
                await ApplyLayoutAsync(algo, options);

                await Task.Delay(options.AnimationDuration + 200);
                if (!_isAnimating)
                    FitToNodes();
            }
            finally
            {
                _isLayoutInProgress = false;
            }
        }

        private LayoutAlgorithm SelectAutoAlgorithm(IEnumerable<NodePosition> nodes, IEnumerable<ConnectionInfo> connections)
        {
            var nodeList = nodes.ToList();
            var idSet = new HashSet<string>(nodeList.Select(n => n.NodeId));
            var indeg = nodeList.ToDictionary(n => n.NodeId, n => 0);
            var outdeg = nodeList.ToDictionary(n => n.NodeId, n => 0);
            int edgeCount = 0;
            foreach (var e in connections)
            {
                if (!idSet.Contains(e.SourceNodeId) || !idSet.Contains(e.TargetNodeId)) continue;
                edgeCount++;
                outdeg[e.SourceNodeId]++;
                indeg[e.TargetNodeId]++;
            }

            // 如果每个节点入度<=1，且边数不超过节点数-分量数，近似视作森林
            bool allInLE1 = indeg.Values.All(v => v <= 1);
            if (allInLE1)
            {
                // 粗略估计分量数：节点数-边数（森林条件下成立）
                int compEst = Math.Max(1, nodeList.Count - edgeCount);
                if (edgeCount <= nodeList.Count - compEst)
                {
                    // 如果根的最大分支较多，做径向树
                    // 统计可能根（入度=0）的最大出度
                    int maxRootOut = 0;
                    foreach (var n in nodeList)
                    {
                        if (indeg[n.NodeId] == 0) maxRootOut = Math.Max(maxRootOut, outdeg[n.NodeId]);
                    }
                    if (maxRootOut >= 6 || nodeList.Count >= 24)
                        return LayoutAlgorithm.RadialTree;
                    return LayoutAlgorithm.TreeTidy;
                }
            }

            return LayoutAlgorithm.ForceDirected;
        }
        /// <summary>
        /// 力导向自动布局
        /// </summary>
        public async Task ApplyForceDirectedLayoutAsync()
        {
            if (_isLayoutInProgress || _isAnimating)
            {
                System.Diagnostics.Debug.WriteLine("布局/动画进行中，跳过力导向布局");
                return;
            }
            if (!Nodes.Any())
                return;

            _isLayoutInProgress = true;
            try
            {
                var canvasWidth = Math.Max(800, ActualCanvasSize.Width);
                var canvasHeight = Math.Max(600, ActualCanvasSize.Height);
                var leftPanelWidth = 250;
                var availableWidth = Math.Max(600, canvasWidth - leftPanelWidth);
                var startX = Math.Max(leftPanelWidth + 50, ActualViewport.X + 50);
                var startY = Math.Max(50, ActualViewport.Y + 50);

                var options = new LayoutOptions
                {
                    StartX = startX,
                    StartY = startY,
                    CanvasWidth = availableWidth,
                    CanvasHeight = canvasHeight,
                    NodeWidth = 250,
                    NodeHeight = 120,
                    EnableAnimation = true,
                    AnimationDuration = 1000,
                    Iterations = 200,
                    InitialTemperature = Math.Max(availableWidth, canvasHeight) / 2.0,
                    MinDistance = 140,
                    PreventOverlap = true
                };

                await ApplyLayoutAsync(LayoutAlgorithm.ForceDirected, options);

                await Task.Delay(options.AnimationDuration + 200);
                if (!_isAnimating)
                {
                    FitToNodes();
                }
            }
            finally
            {
                _isLayoutInProgress = false;
            }
        }

        /// <summary>
        /// 显示布局完成消息
        /// </summary>
        /// <param name="lockedCount">锁定节点数量</param>
        /// <param name="layoutedCount">参与布局的节点数量</param>
        private void ShowLayoutCompletionMessage(int lockedCount, int layoutedCount)
        {
            // 这里可以通过事件或属性来通知UI显示消息
            // 暂时使用Debug输出，后续可以扩展为状态栏消息或通知
            var message = $"自动布局完成：{layoutedCount} 个节点已重新排列，{lockedCount} 个节点因锁定而保持原位置";
            System.Diagnostics.Debug.WriteLine(message);

            // TODO: 可以添加一个事件来通知UI显示这个消息
            // LayoutCompletionMessage?.Invoke(message);
        }

        /// <summary>
        /// 测试直接布局方法 - 基于历史有效代码
        /// </summary>
        private async Task TestDirectLayoutAsync(List<ModuleNodeViewModel> unlockedNodes, LayoutOptions options)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始测试直接布局，节点数量: {unlockedNodes.Count}");

                // 创建简单的位置字典，模拟历史有效代码的返回值
                var newPositions = new Dictionary<ModuleNodeViewModel, Point>();

                // 简单的网格布局
                for (int i = 0; i < unlockedNodes.Count; i++)
                {
                    var x = options.StartX + (i % 3) * options.HorizontalSpacing; // 每行3个节点
                    var y = options.StartY + (i / 3) * options.VerticalSpacing;
                    newPositions[unlockedNodes[i]] = new Point(x, y);
                }

                System.Diagnostics.Debug.WriteLine($"生成了 {newPositions.Count} 个位置");

                if (newPositions.Any())
                {
                    // 过滤出需要移动的节点（排除锁定节点）
                    var nodesToMove = newPositions.Where(kvp => !kvp.Key.IsLocked).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                    if (nodesToMove.Any())
                    {
                        if (options.EnableAnimation)
                        {
                            System.Diagnostics.Debug.WriteLine($"开始动画过渡，移动 {nodesToMove.Count} 个节点");
                            // 使用历史有效代码的动画方法
                            await AnimateNodesToPositionsAsync(nodesToMove, options.AnimationDuration);
                            System.Diagnostics.Debug.WriteLine("动画过渡完成");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"直接设置位置，移动 {nodesToMove.Count} 个节点");
                            // 直接设置新位置（只对未锁定的节点）
                            foreach (var kvp in nodesToMove)
                            {
                                kvp.Key.Location = kvp.Value;
                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("所有节点都已锁定，无需移动");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"测试直接布局完成，节点数量: {newPositions.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"测试直接布局失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 使用正确的Nodify方法应用布局
        /// </summary>
        private async Task ApplyLayoutWithNodifyAsync(List<ModuleNodeViewModel> unlockedNodes, LayoutOptions options)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始应用布局！节点数量: {unlockedNodes.Count}");

                // 调用布局服务获取新位置
                var nodePositions = ConvertNodesToPositions(unlockedNodes);
                var connectionInfos = ConvertConnectionsToInfo(Connections.ToList());

                var layoutService = new ProjectDigitizer.Application.Services.NodeLayoutService();
                var layoutResult = layoutService.CalculateLayout(nodePositions, connectionInfos, ProjectDigitizer.Core.ValueObjects.LayoutAlgorithm.Hierarchical, options);

                if (!layoutResult.Success)
                {
                    System.Diagnostics.Debug.WriteLine($"布局计算失败: {layoutResult.ErrorMessage}");
                    return;
                }

                var newPositions = layoutResult.NodePositions.Values.ToList();

                System.Diagnostics.Debug.WriteLine($"布局服务返回了 {newPositions.Count} 个位置");

                // 在UI线程中应用新位置
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // 查找主窗口和NodifyEditor
                    var mainWindow = System.Windows.Application.Current.MainWindow;
                    if (mainWindow == null) return;

                    var nodifyEditor = FindChild<Nodify.NodifyEditor>(mainWindow, "Canvas");
                    if (nodifyEditor == null) return;

                    var itemContainers = FindChildren<Nodify.ItemContainer>(nodifyEditor);

                    // 应用新位置到ItemContainer
                    for (int i = 0; i < Math.Min(itemContainers.Count, newPositions.Count); i++)
                    {
                        var container = itemContainers[i];
                        var newPosition = newPositions[i];

                        // 使用正确的Nodify方法设置位置
                        container.Location = new System.Windows.Point(newPosition.X, newPosition.Y);

                        System.Diagnostics.Debug.WriteLine($"应用布局：节点 {i} 移动到 ({newPosition.X}, {newPosition.Y})");
                    }

                    // 同时更新ViewModel（保持数据一致性）
                    for (int i = 0; i < Math.Min(unlockedNodes.Count, newPositions.Count); i++)
                    {
                        var node = unlockedNodes[i];
                        var newPosition = newPositions[i];
                        node.Location = new System.Windows.Point(newPosition.X, newPosition.Y);
                    }
                });

                System.Diagnostics.Debug.WriteLine("布局应用完成！");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"布局应用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试直接UI操作方法 - 绕过ViewModel直接操作UI元素
        /// </summary>
        private async Task TestDirectUIManipulationAsync(List<ModuleNodeViewModel> unlockedNodes, LayoutOptions options)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始直接UI操作测试！节点数量: {unlockedNodes.Count}");

                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // 查找主窗口
                    var mainWindow = System.Windows.Application.Current.MainWindow;
                    if (mainWindow == null)
                    {
                        System.Diagnostics.Debug.WriteLine("找不到主窗口！");
                        return;
                    }

                    // 查找NodifyEditor控件
                    var nodifyEditor = FindChild<Nodify.NodifyEditor>(mainWindow, "Canvas");
                    if (nodifyEditor == null)
                    {
                        System.Diagnostics.Debug.WriteLine("找不到NodifyEditor控件！");
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"找到NodifyEditor控件！子元素数量: {System.Windows.Media.VisualTreeHelper.GetChildrenCount(nodifyEditor)}");

                    // 查找所有ItemContainer
                    var itemContainers = FindChildren<Nodify.ItemContainer>(nodifyEditor);
                    System.Diagnostics.Debug.WriteLine($"找到 {itemContainers.Count} 个ItemContainer");

                    // 直接设置前两个ItemContainer的位置
                    for (int i = 0; i < Math.Min(itemContainers.Count, 2); i++)
                    {
                        var container = itemContainers[i];
                        var newX = 100 + i * 300;
                        var newY = 100;

                        try
                        {
                            // 根据Nodify文档，直接设置Location属性是正确的方法
                            var newLocation = new System.Windows.Point(newX, newY);
                            container.Location = newLocation;

                            System.Diagnostics.Debug.WriteLine($"设置ItemContainer {i} Location为: ({newX}, {newY})");
                            System.Windows.MessageBox.Show($"设置ItemContainer {i} Location为: ({newX}, {newY})", "调试信息",
                      System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                            // 强制更新布局
                            container.InvalidateArrange();
                            container.InvalidateMeasure();
                            container.UpdateLayout();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置ItemContainer {i} 位置失败: {ex.Message}");
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("直接UI操作完成！");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"直接UI操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 查找指定类型的子控件
        /// </summary>
        private T FindChild<T>(System.Windows.DependencyObject parent, string childName = null) where T : System.Windows.DependencyObject
        {
            if (parent == null) return null;

            T foundChild = null;
            int childrenCount = System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent);

            for (int i = 0; i < childrenCount; i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);

                if (child is T typedChild)
                {
                    if (string.IsNullOrEmpty(childName) || (child is System.Windows.FrameworkElement fe && fe.Name == childName))
                    {
                        foundChild = typedChild;
                        break;
                    }
                }

                foundChild = FindChild<T>(child, childName);
                if (foundChild != null) break;
            }

            return foundChild;
        }

        /// <summary>
        /// 查找指定类型的所有子控件
        /// </summary>
        private List<T> FindChildren<T>(System.Windows.DependencyObject parent) where T : System.Windows.DependencyObject
        {
            var result = new List<T>();
            if (parent == null) return result;

            int childrenCount = System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childrenCount; i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);

                if (child is T typedChild)
                {
                    result.Add(typedChild);
                }

                result.AddRange(FindChildren<T>(child));
            }

            return result;
        }

        /// <summary>
        /// 应用层次布局算法
        /// </summary>
        private async Task ApplyLayoutAsync(LayoutAlgorithm algorithm, LayoutOptions options)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始计算布局位置: {algorithm}");

                // 允许：层级/网格/力导向/整齐树/径向树
                if (algorithm != LayoutAlgorithm.Hierarchical &&
                    algorithm != LayoutAlgorithm.BalancedGrid &&
                    algorithm != LayoutAlgorithm.ForceDirected &&
                    algorithm != LayoutAlgorithm.TreeTidy &&
                    algorithm != LayoutAlgorithm.RadialTree)
                {
                    throw new ArgumentException($"不支持的布局算法: {algorithm}");
                }

                // 计算新的布局位置
                var nodePositions = ConvertNodesToPositions(Nodes);
                var connectionInfos = ConvertConnectionsToInfo(Connections);

                System.Diagnostics.Debug.WriteLine($"输入数据: {nodePositions.Count()} 个节点, {connectionInfos.Count()} 个连接");
                foreach (var node in nodePositions)
                {
                    System.Diagnostics.Debug.WriteLine($"  节点 {node.NodeId}: ({node.X}, {node.Y}) 锁定={node.IsLocked}");
                }

                var layoutResult = _layoutService.CalculateLayout(nodePositions, connectionInfos, algorithm, options);

                System.Diagnostics.Debug.WriteLine($"布局计算完成，获得 {layoutResult.NodePositions.Count} 个位置，成功={layoutResult.Success}");
                if (!string.IsNullOrEmpty(layoutResult.ErrorMessage))
                {
                    System.Diagnostics.Debug.WriteLine($"布局错误信息: {layoutResult.ErrorMessage}");
                }

                // 调试信息
                System.Diagnostics.Debug.WriteLine($"布局服务结果: 成功={layoutResult.Success}, 位置数量={layoutResult.NodePositions.Count}, 错误={layoutResult.ErrorMessage ?? "无"}");

                if (layoutResult.Success && layoutResult.NodePositions.Any())
                {
                    // 过滤出需要移动的节点（排除锁定节点）
                    var nodesToMove = layoutResult.NodePositions.Where(kvp => !kvp.Value.IsLocked).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                    System.Diagnostics.Debug.WriteLine($"布局结果分析: 总位置 {layoutResult.NodePositions.Count}, 需要移动 {nodesToMove.Count}");
                    foreach (var kvp in nodesToMove)
                    {
                        System.Diagnostics.Debug.WriteLine($"  将移动节点 {kvp.Key} 到位置 ({kvp.Value.X}, {kvp.Value.Y})");
                    }

                    if (nodesToMove.Any())
                    {
                        if (options.EnableAnimation)
                        {
                            System.Diagnostics.Debug.WriteLine($"开始动画过渡，移动 {nodesToMove.Count} 个节点");
                            // 使用动画过渡到新位置（只对未锁定的节点）
                            var viewModelPositions = ConvertPositionsToViewModels(nodesToMove);
                            System.Diagnostics.Debug.WriteLine($"转换后的ViewModel位置数量: {viewModelPositions.Count}");

                            System.Diagnostics.Debug.WriteLine($"准备执行动画: 需要移动 {nodesToMove.Count} 个节点, 转换后 {viewModelPositions.Count} 个ViewModel");

                            await AnimateNodesToPositionsAsync(viewModelPositions, options.AnimationDuration);
                            System.Diagnostics.Debug.WriteLine("动画过渡完成");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"直接设置位置，移动 {nodesToMove.Count} 个节点");
                            // 直接设置新位置（只对未锁定的节点）
                            var viewModelPositions = ConvertPositionsToViewModels(nodesToMove);
                            foreach (var kvp in viewModelPositions)
                            {
                                System.Diagnostics.Debug.WriteLine($"  设置节点 {kvp.Key.Title} 位置为 ({kvp.Value.X}, {kvp.Value.Y})");
                                kvp.Key.Location = kvp.Value;
                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("所有节点都已锁定，无需移动");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"布局失败或无位置数据: Success={layoutResult.Success}, 位置数量={layoutResult.NodePositions.Count}");
                }

                System.Diagnostics.Debug.WriteLine($"自动布局完成: {algorithm}, 节点数量: {layoutResult.NodePositions.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动布局失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw; // 重新抛出异常以便UI层处理
            }
        }

        /// <summary>
        /// 动画过渡节点到新位置
        /// </summary>
        private async Task AnimateNodesToPositionsAsync(Dictionary<ModuleNodeViewModel, Point> targetPositions, int duration)
        {
            if (!targetPositions.Any())
                return;

            System.Diagnostics.Debug.WriteLine($"开始动画: {targetPositions.Count} 个节点, 时长: {duration}ms");

            _isAnimating = true;
            try
            {
                var tcs = new TaskCompletionSource<bool>();
                var startPositions = targetPositions.ToDictionary(kvp => kvp.Key, kvp => kvp.Key.Location);
                var distances = targetPositions.ToDictionary(kvp => kvp.Key,
                    kvp => Math.Sqrt(Math.Pow(kvp.Value.X - startPositions[kvp.Key].X, 2) + Math.Pow(kvp.Value.Y - startPositions[kvp.Key].Y, 2)));
                double maxDelay = duration * 0.25;
                double maxDist = distances.Values.DefaultIfEmpty(0).Max();
                var delays = targetPositions.ToDictionary(kvp => kvp.Key,
                    kvp => maxDist > 0 ? maxDelay * (distances[kvp.Key] / maxDist) : 0);

                var sw = System.Diagnostics.Stopwatch.StartNew();

                void OnRender(object? s, EventArgs e)
                {
                    double elapsed = sw.Elapsed.TotalMilliseconds;
                    bool allDone = true;
                    foreach (var kv in targetPositions)
                    {
                        var node = kv.Key; var targetPos = kv.Value; var startPos = startPositions[node];
                        double delay = delays[node];
                        double localT = Math.Max(0, Math.Min(1.0, (elapsed - delay) / Math.Max(1.0, duration - delay)));
                        // 三次缓动：easeInOutCubic
                        double p = localT < 0.5 ? 4 * localT * localT * localT : 1 - Math.Pow(-2 * localT + 2, 3) / 2.0;
                        double x = startPos.X + (targetPos.X - startPos.X) * p;
                        double y = startPos.Y + (targetPos.Y - startPos.Y) * p;
                        node.Location = new Point(x, y);
                        if (localT < 1.0) allDone = false;
                    }

                    if (allDone || elapsed >= duration + maxDelay + 32)
                    {
                        System.Windows.Media.CompositionTarget.Rendering -= OnRender;
                        foreach (var kv in targetPositions) kv.Key.Location = kv.Value;
                        tcs.TrySetResult(true);
                    }
                }

                System.Windows.Media.CompositionTarget.Rendering += OnRender;
                await tcs.Task;
            }
            finally
            {
                _isAnimating = false;
            }
        }

        /// <summary>
        /// 缓动函数 - 更明显的EaseInOut效果
        /// </summary>
        private double EaseInOut(double t)
        {
            // 保留旧接口，改为三次 easeInOut（更柔和）
            return t < 0.5 ? 4 * t * t * t : 1 - Math.Pow(-2 * t + 2, 3) / 2.0;
        }



        #endregion
        /// <summary>
        /// 将ModuleNodeViewModel转换为NodePosition
        /// </summary>
        private IEnumerable<NodePosition> ConvertNodesToPositions(IEnumerable<ModuleNodeViewModel> nodes)
        {
            // 采集每个可见节点的实际尺寸（Nodify.ItemContainer.ActualWidth/Height）
            var sizeMap = new Dictionary<ModuleNodeViewModel, Size>();
            try
            {
                var mainWindow = System.Windows.Application.Current.MainWindow;
                if (mainWindow != null)
                {
                    var editor = FindChild<NodifyEditor>(mainWindow, "Canvas");
                    if (editor != null)
                    {
                        var containers = FindChildren<Nodify.ItemContainer>(editor);
                        foreach (var c in containers)
                        {
                            if (c.DataContext is ModuleNodeViewModel vm)
                            {
                                var w = c.ActualWidth;
                                var h = c.ActualHeight;
                                if (double.IsFinite(w) && w > 0 && double.IsFinite(h) && h > 0)
                                {
                                    sizeMap[vm] = new Size(w, h);
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // 忽略尺寸采集失败，使用默认尺寸由 LayoutOptions 兜底
            }

            return nodes.Select(node => new NodePosition
            {
                NodeId = node.Module?.Id ?? Guid.NewGuid().ToString(),
                X = node.Location.X,
                Y = node.Location.Y,
                Width = sizeMap.TryGetValue(node, out var s) ? s.Width : 0,
                Height = sizeMap.TryGetValue(node, out s) ? s.Height : 0,
                IsLocked = node.IsLocked,
                Title = node.Title ?? string.Empty,
                NodeType = node.NodeType
            });
        }

        /// <summary>
        /// 将ConnectionViewModel转换为ConnectionInfo
        /// </summary>
        private IEnumerable<ConnectionInfo> ConvertConnectionsToInfo(IEnumerable<ConnectionViewModel> connections)
        {
            return connections.Select(conn => new ConnectionInfo
            {
                Id = conn.Id.ToString(),
                SourceNodeId = (conn.Source?.Node as ModuleNodeViewModel)?.Module?.Id ?? string.Empty,
                SourceConnectorId = conn.Source?.Id.ToString() ?? string.Empty,
                TargetNodeId = (conn.Target?.Node as ModuleNodeViewModel)?.Module?.Id ?? string.Empty,
                TargetConnectorId = conn.Target?.Id.ToString() ?? string.Empty,
                IsInputConnection = conn.Target?.IsInput ?? false
            });
        }

        /// <summary>
        /// 将NodePosition字典转换为ModuleNodeViewModel字典
        /// </summary>
        private Dictionary<ModuleNodeViewModel, Point> ConvertPositionsToViewModels(Dictionary<string, NodePosition> positions)
        {
            var result = new Dictionary<ModuleNodeViewModel, Point>();

            System.Diagnostics.Debug.WriteLine($"=== ConvertPositionsToViewModels 开始转换 ===");
            System.Diagnostics.Debug.WriteLine($"输入位置数量: {positions.Count}");
            System.Diagnostics.Debug.WriteLine($"当前节点数量: {Nodes.Count}");

            foreach (var kvp in positions)
            {
                var nodeId = kvp.Key;
                var position = kvp.Value;

                System.Diagnostics.Debug.WriteLine($"查找节点ID: {nodeId}");

                // 根据NodeId找到对应的ViewModel
                var viewModel = Nodes.FirstOrDefault(n => n.Module?.Id == nodeId);
                if (viewModel != null)
                {
                    result[viewModel] = new Point(position.X, position.Y);
                    System.Diagnostics.Debug.WriteLine($"  找到匹配节点: {viewModel.Title} -> ({position.X}, {position.Y})");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"  未找到匹配节点ID: {nodeId}");
                    // 列出所有节点的ID用于调试
                    foreach (var node in Nodes)
                    {
                        System.Diagnostics.Debug.WriteLine($"    现有节点: {node.Title} ID={node.Module?.Id ?? "NULL"}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"转换结果: {result.Count} 个节点位置");
            return result;
        }

        /// <summary>
        /// 追加来自插件的模板项到“处理类”分组；仅添加存在且未重复的节点。
        /// 为后续按项目属性筛选预留 context 参数。
        /// </summary>
        private void AppendPluginTemplatesToProcessCategory(TemplateCategory processCategory, ProjectContext? context)
        {
            try
            {
                var registered = _nodeRegistry.GetRegisteredModuleTypes();
                if (registered == null || registered.Count == 0)
                    return;

                var existing = new HashSet<ModuleType>(processCategory.Items.Select(i => i.ModuleType));

                foreach (var moduleType in registered)
                {
                    var template = _nodeRegistry.GetTemplate(moduleType);
                    if (template == null)
                        continue;

                    // 优先读取接口元数据，其次读取类特性
                    string? group = null; string? iconPath = null; int? order = null; string[]? tags = null;
                    if (template is INodeListItemMetadata meta)
                    {
                        group = meta.Group;
                        order = meta.Order;
                        iconPath = meta.IconPath;
                        tags = meta.Tags;
                    }
                    var attr = template.GetType().GetCustomAttribute<NodeListItemAttribute>();
                    if (attr != null)
                    {
                        group ??= attr.Group;
                        order ??= attr.Order;
                        iconPath ??= attr.IconPath;
                        tags ??= attr.Tags;
                    }

                    if (string.IsNullOrWhiteSpace(group))
                        continue; // 无分组信息，跳过
                    if (!string.Equals(group, "处理类", StringComparison.Ordinal) &&
                        !string.Equals(group, "处理器", StringComparison.Ordinal))
                        continue; // 仅加入处理分组

                    if (existing.Contains(moduleType))
                        continue; // 避免重复

                    // 预留：根据 context 进行可见性筛选（暂未实现策略）
                    processCategory.Items.Add(new TemplateItem
                    {
                        Name = attr?.DisplayName ?? template.Name,
                        Description = attr?.Description ?? template.Description,
                        ModuleType = moduleType,
                        IconPath = iconPath ?? string.Empty,
                        IsEnabled = true
                    });
                }
            }
            catch
            {
                // 忽略插件发现错误，避免影响主程序
            }
        }

    }
}
