using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Studio.Controls.Properties.Widgets
{
    /// <summary>
    /// 文本属性编辑器
    /// </summary>
    public class TextPropertyWidget : IPropertyWidget
    {
        private readonly TextBox _textBox;
        private PropertyDefinition _propertyDefinition = new();
        private object? _value;

        public TextPropertyWidget()
        {
            _textBox = new TextBox
            {
                Margin = new Thickness(0, 8, 0, 16),
                FontSize = 14,
                Padding = new Thickness(8),
                BorderThickness = new Thickness(1),
                BorderBrush = System.Windows.Media.Brushes.LightGray
            };

            // 使用项目通用样式（在全局 ResourceDictionary 中已定义）

            _textBox.TextChanged += OnTextChanged;
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    _textBox.Text = value?.ToString() ?? string.Empty;
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _textBox.IsEnabled;
            set => _textBox.IsEnabled = value;
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Core.ValueObjects.ValidationResult Validate()
        {
            var result = new Core.ValueObjects.ValidationResult();
            var text = _textBox.Text;

            // 必填验证
            if (PropertyDefinition.Required && string.IsNullOrWhiteSpace(text))
            {
                result.AddError($"{PropertyDefinition.Title} 是必填项");
                return result;
            }

            // 正则表达式验证
            foreach (var rule in PropertyDefinition.ValidationRules)
            {
                if (rule.Type == "pattern" && !string.IsNullOrEmpty(rule.Pattern))
                {
                    if (!Regex.IsMatch(text, rule.Pattern))
                    {
                        result.AddError(rule.ErrorMessage);
                        return result;
                    }
                }
            }

            return result;
        }

        public FrameworkElement GetElement()
        {
            return _textBox;
        }

        private void UpdateUI()
        {
            _textBox.Text = PropertyDefinition.DefaultValue?.ToString() ?? string.Empty;

            // 设置 ToolTip 作为帮助文本
            if (!string.IsNullOrEmpty(PropertyDefinition.Description))
            {
                _textBox.ToolTip = PropertyDefinition.Description;
            }
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            var oldValue = _value;
            _value = _textBox.Text;
            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, oldValue, _value));
        }
    }
}
