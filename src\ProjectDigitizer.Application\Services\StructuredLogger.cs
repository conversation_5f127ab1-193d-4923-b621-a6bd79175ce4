using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Services;

/// <summary>
/// 结构化日志记录器实现
/// </summary>
public class StructuredLogger : IStructuredLogger
{
    private readonly ILogger<StructuredLogger> _logger;
    private readonly Dictionary<string, IOperationTracker> _activeOperations;

    public StructuredLogger(ILogger<StructuredLogger> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _activeOperations = new Dictionary<string, IOperationTracker>();
    }

    public void LogOperation(string operationName, object? parameters, TimeSpan duration)
    {
        _logger.LogInformation("Operation completed: {OperationName} in {Duration}ms with parameters {@Parameters}",
            operationName, duration.TotalMilliseconds, parameters);
    }

    public void LogPerformance(string metricName, double value, Dictionary<string, object>? tags = null)
    {
        var logData = new Dictionary<string, object>
        {
            ["MetricName"] = metricName,
            ["Value"] = value,
            ["Timestamp"] = DateTime.UtcNow
        };

        if (tags != null)
        {
            foreach (var tag in tags)
            {
                logData[$"Tag_{tag.Key}"] = tag.Value;
            }
        }

        _logger.LogInformation("Performance metric: {MetricName} = {Value} {@Tags}",
            metricName, value, tags);
    }

    public void LogError(Exception exception, string context, object? additionalData = null)
    {
        var errorData = new Dictionary<string, object>
        {
            ["Context"] = context,
            ["ExceptionType"] = exception.GetType().Name,
            ["Timestamp"] = DateTime.UtcNow
        };

        if (additionalData != null)
        {
            errorData["AdditionalData"] = additionalData;
        }

        _logger.LogError(exception, "Error in {Context}: {Message} {@ErrorData}",
            context, exception.Message, errorData);
    }

    public void LogInformation(string message, Dictionary<string, object>? properties = null)
    {
        if (properties != null && properties.Count > 0)
        {
            _logger.LogInformation("{Message} {@Properties}", message, properties);
        }
        else
        {
            _logger.LogInformation("{Message}", message);
        }
    }

    public void LogWarning(string message, Dictionary<string, object>? properties = null)
    {
        if (properties != null && properties.Count > 0)
        {
            _logger.LogWarning("{Message} {@Properties}", message, properties);
        }
        else
        {
            _logger.LogWarning("{Message}", message);
        }
    }

    public void LogDebug(string message, Dictionary<string, object>? properties = null)
    {
        if (properties != null && properties.Count > 0)
        {
            _logger.LogDebug("{Message} {@Properties}", message, properties);
        }
        else
        {
            _logger.LogDebug("{Message}", message);
        }
    }

    public IOperationTracker StartOperation(string operationName, Dictionary<string, object>? properties = null)
    {
        var tracker = new OperationTracker(operationName, this, properties);
        _activeOperations[tracker.OperationId] = tracker;

        LogInformation($"Operation started: {operationName}", new Dictionary<string, object>
        {
            ["OperationId"] = tracker.OperationId,
            ["OperationName"] = operationName,
            ["StartTime"] = DateTime.UtcNow
        });

        return tracker;
    }

    public async Task LogOperationAsync(string operationName, object? parameters, TimeSpan duration)
    {
        await Task.Run(() => LogOperation(operationName, parameters, duration));
    }

    internal void CompleteOperation(string operationId, bool success, Exception? exception = null, object? result = null)
    {
        if (_activeOperations.TryGetValue(operationId, out var tracker))
        {
            _activeOperations.Remove(operationId);

            var logData = new Dictionary<string, object>
            {
                ["OperationId"] = operationId,
                ["OperationName"] = tracker.OperationName,
                ["Success"] = success,
                ["EndTime"] = DateTime.UtcNow
            };

            if (result != null)
            {
                logData["Result"] = result;
            }

            if (success)
            {
                LogInformation($"Operation completed successfully: {tracker.OperationName}", logData);
            }
            else
            {
                LogError(exception ?? new InvalidOperationException("Operation failed"),
                    $"Operation failed: {tracker.OperationName}", logData);
            }
        }
    }
}

/// <summary>
/// 操作跟踪器实现
/// </summary>
internal class OperationTracker : IOperationTracker
{
    private readonly StructuredLogger _logger;
    private readonly Dictionary<string, object> _properties;
    private readonly Stopwatch _stopwatch;
    private bool _disposed;

    public string OperationId { get; }
    public string OperationName { get; }

    public OperationTracker(string operationName, StructuredLogger logger, Dictionary<string, object>? initialProperties = null)
    {
        OperationId = Guid.NewGuid().ToString();
        OperationName = operationName;
        _logger = logger;
        _properties = initialProperties ?? new Dictionary<string, object>();
        _stopwatch = Stopwatch.StartNew();
    }

    public void AddProperty(string key, object value)
    {
        _properties[key] = value;
    }

    public void LogProgress(double progress, string? message = null)
    {
        var progressData = new Dictionary<string, object>
        {
            ["OperationId"] = OperationId,
            ["Progress"] = progress,
            ["ElapsedTime"] = _stopwatch.Elapsed
        };

        if (!string.IsNullOrEmpty(message))
        {
            progressData["Message"] = message;
        }

        _logger.LogInformation($"Operation progress: {OperationName} - {progress:F1}%", progressData);
    }

    public void Complete(object? result = null)
    {
        if (!_disposed)
        {
            _stopwatch.Stop();
            _logger.CompleteOperation(OperationId, true, null, result);
            _disposed = true;
        }
    }

    public void Fail(Exception exception)
    {
        if (!_disposed)
        {
            _stopwatch.Stop();
            _logger.CompleteOperation(OperationId, false, exception);
            _disposed = true;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _stopwatch.Stop();
            _logger.CompleteOperation(OperationId, true);
            _disposed = true;
        }
    }
}
