using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

using Plugins.DataCalculation.Models;
using Plugins.DataCalculation.Services;

namespace Plugins.DataCalculation.Controls.Functions
{
    /// <summary>
    /// 公式模板选择器：提供搜索、按分类与复杂度筛选、选择回调等功能。
    /// </summary>
    public partial class FormulaTemplateSelector : UserControl, INotifyPropertyChanged
    {
        private readonly FormulaTemplateService _formulaService;
        private List<FormulaCategory> _allCategories = new();
        private List<FormulaCategory> _filteredCategories = new();
        private FormulaTemplate? _selectedTemplate;
        private string _searchKeyword = string.Empty;
        private string? _selectedCategoryId;
        private FormulaComplexity? _selectedComplexity;
        private Border? _lastHighlightedBorder;

        /// <summary>当前选中的模板。</summary>
        public FormulaTemplate? SelectedTemplate
        {
            get => _selectedTemplate;
            private set => SetProperty(ref _selectedTemplate, value);
        }

        /// <summary>选择某个模板时触发。</summary>
        public event EventHandler<FormulaTemplate>? FormulaSelected;
        /// <summary>取消选择时触发。</summary>
        public event EventHandler? SelectionCancelled;

        /// <summary>构造函数：初始化服务与 UI。</summary>
        public FormulaTemplateSelector()
        {
            InitializeComponent();
            _formulaService = new FormulaTemplateService();
            DataContext = this;
            InitializeAsync();
        }

        /// <summary>异步初始化模板与过滤器。</summary>
        private async void InitializeAsync()
        {
            try
            {
                await _formulaService.InitializeAsync();
                LoadFormulas();
                InitializeFilters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化公式模板选择器失败: {ex.Message}");
                MessageBox.Show($"加载公式模板失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>加载模板数据。</summary>
        private void LoadFormulas()
        {
            _allCategories = _formulaService.Categories.ToList();
            ApplyFilters();
        }

        /// <summary>初始化分类与复杂度过滤器。</summary>
        private void InitializeFilters()
        {
            var categoryItems = new List<ComboBoxItem>
            {
                new ComboBoxItem { Content = "全部分类", Tag = null }
            };
            foreach (var category in _allCategories)
            {
                categoryItems.Add(new ComboBoxItem { Content = category.Name, Tag = category.Id });
            }
            CategoryFilterComboBox.ItemsSource = categoryItems;
            CategoryFilterComboBox.SelectedIndex = 0;

            var complexityItems = new List<ComboBoxItem>
            {
                new ComboBoxItem { Content = "全部复杂度", Tag = null },
                new ComboBoxItem { Content = "简单", Tag = FormulaComplexity.Simple },
                new ComboBoxItem { Content = "中等", Tag = FormulaComplexity.Medium },
                new ComboBoxItem { Content = "复杂", Tag = FormulaComplexity.Complex },
                new ComboBoxItem { Content = "高级", Tag = FormulaComplexity.Advanced }
            };
            ComplexityFilterComboBox.ItemsSource = complexityItems;
            ComplexityFilterComboBox.SelectedIndex = 0;
        }

        /// <summary>应用搜索关键字、分类与复杂度过滤，刷新展示。</summary>
        private void ApplyFilters()
        {
            var filteredTemplates = _formulaService.Templates.AsEnumerable();
            if (!string.IsNullOrWhiteSpace(_searchKeyword))
            {
                filteredTemplates = _formulaService.SearchTemplates(_searchKeyword);
            }
            if (!string.IsNullOrEmpty(_selectedCategoryId))
            {
                filteredTemplates = filteredTemplates.Where(t => t.Category == _selectedCategoryId);
            }
            if (_selectedComplexity.HasValue)
            {
                filteredTemplates = filteredTemplates.Where(t => t.Complexity == _selectedComplexity.Value);
            }

            var grouped = filteredTemplates
                .GroupBy(t => _allCategories.FirstOrDefault(c => c.Id == t.Category)?.Name ?? "未分类")
                .Select(g => new FormulaCategory
                {
                    Id = g.Key,
                    Name = g.Key,
                    Templates = g.ToList()
                })
                .OrderBy(c => c.Name)
                .ToList();

            _filteredCategories = grouped;
            CategoriesItemsControl.ItemsSource = _filteredCategories;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchKeyword = SearchTextBox.Text;
            ApplyFilters();
        }

        private void CategoryFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var item = CategoryFilterComboBox.SelectedItem as ComboBoxItem;
            _selectedCategoryId = item?.Tag?.ToString();
            ApplyFilters();
        }

        private void ComplexityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var item = ComplexityFilterComboBox.SelectedItem as ComboBoxItem;
            _selectedComplexity = item?.Tag as FormulaComplexity?;
            ApplyFilters();
        }

        /// <summary>点击模板项时，更新当前选中并高亮。</summary>
        private void FormulaItem_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border && border.Tag is FormulaTemplate template)
            {
                SelectedTemplate = template;
                HighlightSelectedItem(border);
            }
        }

        private void HighlightSelectedItem(Border border)
        {
            // 安全高亮：不刷新 ItemsSource，避免引发 InvalidOperationException
            if (_lastHighlightedBorder != null)
            {
                _lastHighlightedBorder.ClearValue(Border.BorderBrushProperty);
                _lastHighlightedBorder.ClearValue(Border.BorderThicknessProperty);
            }

            border.BorderBrush = (Brush)Application.Current.FindResource("Brush.Primary");
            border.BorderThickness = new Thickness(2);
            _lastHighlightedBorder = border;
        }

        private void RemoveAllHighlights()
        {
            if (_lastHighlightedBorder != null)
            {
                _lastHighlightedBorder.ClearValue(Border.BorderBrushProperty);
                _lastHighlightedBorder.ClearValue(Border.BorderThicknessProperty);
                _lastHighlightedBorder = null;
            }
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedTemplate != null)
            {
                FormulaSelected?.Invoke(this, SelectedTemplate);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            SelectionCancelled?.Invoke(this, EventArgs.Empty);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            SelectionCancelled?.Invoke(this, EventArgs.Empty);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value; OnPropertyChanged(propertyName); return true;
        }
    }
}


