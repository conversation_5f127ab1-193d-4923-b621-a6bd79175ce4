using System.Collections.Generic;

using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Application.Interfaces
{
    /// <summary>
    /// 节点自动布局服务接口
    /// </summary>
    public interface INodeLayoutService
    {
        /// <summary>
        /// 执行自动布局
        /// </summary>
        /// <param name="nodes">要布局的节点集合</param>
        /// <param name="connections">连接关系</param>
        /// <param name="algorithm">布局算法</param>
        /// <param name="options">布局选项</param>
        /// <returns>布局结果</returns>
        LayoutResult CalculateLayout(
            IEnumerable<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutAlgorithm algorithm,
            LayoutOptions? options = null);
    }
}
