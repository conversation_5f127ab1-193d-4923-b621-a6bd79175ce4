using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;

using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.CodeCompletion;
using ICSharpCode.AvalonEdit.Document;
using ICSharpCode.AvalonEdit.Editing;
using ICSharpCode.AvalonEdit.Rendering;

namespace ProjectDigitizer.Studio.Controls.Functions
{
    public partial class AvalonFormulaEditor : UserControl
    {
        public static readonly DependencyProperty TextProperty = DependencyProperty.Register(
            nameof(Text), typeof(string), typeof(AvalonFormulaEditor),
            new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnTextChanged));

        private CompletionWindow? _completionWindow;
        private readonly HashSet<string> _keywords = new(new[] { "IF", "AND", "OR", "NOT", "TRUE", "FALSE" }, StringComparer.OrdinalIgnoreCase);
        private readonly HashSet<string> _functions = new(new[] {
            "SUM", "AVG", "AVERAGE", "COUNT", "MAX", "MIN",
            "ABS", "ROUND", "ROUNDUP", "ROUNDDOWN",
            "TEXT", "LEN", "CONCAT",
            "LEFT", "RIGHT", "MID", "FIND", "SUBSTITUTE", "TRIM", "UPPER", "LOWER",
            "IF", "IFERROR", "IFNA", "AND", "OR"
        }, StringComparer.OrdinalIgnoreCase);
        // 函数签名与中文说明（标题行）
        private readonly Dictionary<string, string> _functionSignatures = new(StringComparer.OrdinalIgnoreCase)
        {
            ["IF"] = "IF(condition, trueValue, falseValue)  如果条件为真返回trueValue，否则返回falseValue",
            ["AND"] = "AND(arg1, arg2, ...)  所有参数为真返回TRUE",
            ["OR"] = "OR(arg1, arg2, ...)  任一参数为真返回TRUE",
            ["SUM"] = "SUM(number1, number2, ...)  求和",
            ["AVG"] = "AVG(number1, number2, ...)  平均值（AVERAGE 同义）",
            ["AVERAGE"] = "AVERAGE(number1, number2, ...)  平均值",
            ["MAX"] = "MAX(number1, number2, ...)  最大值",
            ["MIN"] = "MIN(number1, number2, ...)  最小值",
            ["ABS"] = "ABS(number)  绝对值",
            ["ROUND"] = "ROUND(number, [digits])  四舍五入到指定位数",
            ["ROUNDUP"] = "ROUNDUP(number, [digits])  向上取整（远离0）",
            ["ROUNDDOWN"] = "ROUNDDOWN(number, [digits])  向下取整（趋向0）",
            ["TEXT"] = "TEXT(number, format)  数字格式化为文本",
            ["LEN"] = "LEN(text)  文本长度",
            ["CONCAT"] = "CONCAT(text1, text2, ...)  文本拼接",
            ["LEFT"] = "LEFT(text, [num_chars])  左侧子串",
            ["RIGHT"] = "RIGHT(text, [num_chars])  右侧子串",
            ["MID"] = "MID(text, start, num_chars)  指定位置子串（start 从1开始）",
            ["FIND"] = "FIND(find_text, within_text, [start])  区分大小写查找，返回位置(1基)",
            ["SUBSTITUTE"] = "SUBSTITUTE(text, old_text, new_text, [instance])  文本替换",
            ["TRIM"] = "TRIM(text)  去首尾空白并压缩中间空白",
            ["UPPER"] = "UPPER(text)  转大写",
            ["LOWER"] = "LOWER(text)  转小写",
            ["IFERROR"] = "IFERROR(value, value_if_error)  出错时返回备用值",
            ["IFNA"] = "IFNA(value, value_if_na)  值为NA时返回备用值（本实现简化为空串/NaN判定）"
        };

        // 函数参数名（中文）与示例：用于底部信息条详细说明
        private readonly Dictionary<string, (string[] paramNames, string example)> _functionDetails = new(StringComparer.OrdinalIgnoreCase)
        {
            ["IF"] = (new[] { "条件", "为真时的值", "为假时的值" }, "示例: IF(A>0, \"正\", \"负\")"),
            ["AND"] = (new[] { "参数1", "参数2", "…" }, "示例: AND(A>0, B<10)"),
            ["OR"] = (new[] { "参数1", "参数2", "…" }, "示例: OR(A>0, B<10)"),
            ["SUM"] = (new[] { "数值1", "数值2", "…" }, "示例: SUM(1,2,3)"),
            ["AVG"] = (new[] { "数值1", "数值2", "…" }, "示例: AVG(1,2,3)"),
            ["AVERAGE"] = (new[] { "数值1", "数值2", "…" }, "示例: AVERAGE(1,2,3)"),
            ["MAX"] = (new[] { "数值1", "数值2", "…" }, "示例: MAX(1,9,3)"),
            ["MIN"] = (new[] { "数值1", "数值2", "…" }, "示例: MIN(1,9,3)"),
            ["ABS"] = (new[] { "数值" }, "示例: ABS(-3.5)"),
            ["ROUND"] = (new[] { "数值", "小数位(可选)" }, "示例: ROUND(3.14159, 2) -> 3.14"),
            ["ROUNDUP"] = (new[] { "数值", "小数位(可选)" }, "示例: ROUNDUP(3.141, 2) -> 3.15"),
            ["ROUNDDOWN"] = (new[] { "数值", "小数位(可选)" }, "示例: ROUNDDOWN(3.149, 2) -> 3.14"),
            ["TEXT"] = (new[] { "数值", "格式字符串" }, "示例: TEXT(12.3, \"F2\") -> 12.30"),
            ["LEN"] = (new[] { "文本" }, "示例: LEN(\"你好\") -> 2"),
            ["CONCAT"] = (new[] { "文本1", "文本2", "…" }, "示例: CONCAT(\"a\",\"b\") -> ab"),
            ["LEFT"] = (new[] { "文本", "字符数(可选)" }, "示例: LEFT(\"Hello\",2) -> He"),
            ["RIGHT"] = (new[] { "文本", "字符数(可选)" }, "示例: RIGHT(\"Hello\",2) -> lo"),
            ["MID"] = (new[] { "文本", "起始位置(从1)", "长度" }, "示例: MID(\"Hello\",2,3) -> ell"),
            ["FIND"] = (new[] { "要找的文本", "目标文本", "起始位置(可选)" }, "示例: FIND(\"lo\",\"Hello\") -> 4"),
            ["SUBSTITUTE"] = (new[] { "文本", "旧文本", "新文本", "第N次(可选)" }, "示例: SUBSTITUTE(\"a-a\",\"-\",\"+\") -> a+a"),
            ["TRIM"] = (new[] { "文本" }, "示例: TRIM(\" a  b \") -> \"a b\""),
            ["UPPER"] = (new[] { "文本" }, "示例: UPPER(\"ab\") -> AB"),
            ["LOWER"] = (new[] { "文本" }, "示例: LOWER(\"Ab\") -> ab"),
            ["IFERROR"] = (new[] { "表达式", "出错时的值" }, "示例: IFERROR(1/0, 0) -> 0"),
            ["IFNA"] = (new[] { "值", "为NA时的值" }, "示例: IFNA(\"\", 0) -> 0")
        };

        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        public AvalonFormulaEditor()
        {
            InitializeComponent();

            // 事件绑定
            Editor.TextChanged += Editor_TextChanged;
            Editor.TextArea.TextEntered += TextArea_TextEntered;
            Editor.TextArea.TextEntering += TextArea_TextEntering;
            Editor.TextArea.PreviewKeyDown += TextArea_PreviewKeyDown;
            Editor.LostKeyboardFocus += (s, e) => _completionWindow?.Close();

            // 光标移动时更新下方信息条（函数签名与中文说明）
            Editor.TextArea.Caret.PositionChanged += (s, e) => UpdateSignatureInfo();

            // 简单的语法高亮
            Editor.TextArea.TextView.LineTransformers.Add(new FormulaHighlightColorizer(_keywords, _functions));
        }

        private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is AvalonFormulaEditor ctl)
            {
                var newText = e.NewValue as string ?? string.Empty;
                if (ctl.Editor.Text != newText)
                {
                    // 保留光标位置
                    var caret = ctl.Editor.CaretOffset;
                    ctl.Editor.Text = newText;
                    ctl.Editor.CaretOffset = Math.Min(caret, ctl.Editor.Text.Length);
                }
            }
        }

        /// <summary>
        /// 根据光标上下文更新底部信息条：
        /// - 若光标位于函数名或其括号参数内，显示对应函数签名与中文说明。
        /// - 同时根据当前参数位置附带“当前第 N 个参数”的提示。
        /// </summary>
        private void UpdateSignatureInfo()
        {
            try
            {
                if (InfoPanel == null || InfoTextBlock == null) return;
                var text = Editor.Text ?? string.Empty;
                var offset = Editor.CaretOffset;
                if (string.IsNullOrEmpty(text) || offset < 0 || offset > text.Length)
                {
                    InfoPanel.Visibility = Visibility.Collapsed;
                    return;
                }

                // 回溯找到最近的左括号，以识别函数调用
                int left = offset - 1;
                int depth = 0;
                while (left >= 0)
                {
                    char c = text[left];
                    if (c == ')') depth++;
                    else if (c == '(')
                    {
                        if (depth == 0) break;
                        depth--;
                    }
                    left--;
                }
                if (left < 1) { InfoPanel.Visibility = Visibility.Collapsed; return; }

                // 解析函数名
                int nameEnd = left;
                int nameStart = nameEnd - 1;
                while (nameStart >= 0 && (char.IsLetterOrDigit(text[nameStart]) || text[nameStart] == '_')) nameStart--;
                nameStart++;
                if (nameStart >= nameEnd) { InfoPanel.Visibility = Visibility.Collapsed; return; }
                var name = text.Substring(nameStart, nameEnd - nameStart);
                if (!_functions.Contains(name)) { InfoPanel.Visibility = Visibility.Collapsed; return; }

                // 计算当前参数索引
                int i = left + 1;
                depth = 1;
                int argIndex = 0;
                while (i < text.Length && i < offset && depth > 0)
                {
                    char c = text[i];
                    if (c == '(') depth++;
                    else if (c == ')') depth--;
                    else if (c == ',' && depth == 1) argIndex++;
                    i++;
                }

                _functionSignatures.TryGetValue(name, out var sig);
                var title = sig ?? name;
                InfoTextBlock.Text = title;

                // 使用 Inlines 方式在“参数”行中加粗当前参数，中文更规整
                try
                {
                    if (_functionDetails.TryGetValue(name, out var d))
                    {
                        var names = d.paramNames;
                        var idx = Math.Min(argIndex, Math.Max(0, names.Length - 1));
                        InfoParamsTextBlock.Inlines.Clear();
                        InfoParamsTextBlock.Inlines.Add(new Run("参数: "));
                        for (int pi = 0; pi < names.Length; pi++)
                        {
                            if (pi > 0)
                                InfoParamsTextBlock.Inlines.Add(new Run("，"));

                            if (pi == idx)
                                InfoParamsTextBlock.Inlines.Add(new Bold(new Run(names[pi])));
                            else
                                InfoParamsTextBlock.Inlines.Add(new Run(names[pi]));
                        }
                        // 同步示例文本
                        InfoExampleTextBlock.Text = d.example;
                    }
                    else
                    {
                        InfoParamsTextBlock.Inlines.Clear();
                        InfoExampleTextBlock.Text = string.Empty;
                    }
                }
                catch { /* non-fatal UI update issue */ }

                // 参数描述：高亮当前参数名
                if (_functionDetails.TryGetValue(name, out var details))
                {
                    var names = details.paramNames;
                    var idx = Math.Min(argIndex, Math.Max(0, names.Length - 1));
                    // 拼接参数名并对当前参数名加粗标记（用简单星号语义后续可替换为 Run）
                    var parts = names.Select((n, i) => i == idx ? $"【{n}】" : n);
                    InfoParamsTextBlock.Text = "参数: " + string.Join("，", parts);
                    InfoExampleTextBlock.Text = details.example;
                }
                else
                {
                    InfoParamsTextBlock.Text = string.Empty;
                    InfoExampleTextBlock.Text = string.Empty;
                }

                InfoPanel.Visibility = Visibility.Visible;
            }
            catch
            {
                InfoPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void Editor_TextChanged(object? sender, EventArgs e)
        {
            if (Text != Editor.Text)
            {
                Text = Editor.Text;
            }
        }

        private void TextArea_TextEntered(object? sender, TextCompositionEventArgs e)
        {
            // 当输入字母时，显示智能提示
            if (!string.IsNullOrEmpty(e.Text) && char.IsLetter(e.Text[0]))
            {
                ShowCompletion();
            }
            // 例如刚输入“(”进入参数区后，刷新底部签名说明
            UpdateSignatureInfo();
        }

        private void TextArea_PreviewKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Space && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                // 按 Ctrl+Space 强制显示智能提示
                ShowCompletion(force: true);
                e.Handled = true;
            }

            // 输入回车、Esc、分号等时，关闭智能提示窗口
            if (_completionWindow != null)
            {
                if (e.Key == Key.Escape)
                {
                    _completionWindow.Close();
                }
                // 左右方向键移动光标时关闭
                if (e.Key == Key.Left || e.Key == Key.Right)
                {
                    _completionWindow.Close();
                }
            }

            // 常见按键下也刷新签名信息条（等光标定位完成后）
            if (e.Key is Key.Left or Key.Right or Key.Up or Key.Down or Key.Back or Key.Delete or Key.Home or Key.End)
            {
                Dispatcher.BeginInvoke(new Action(UpdateSignatureInfo));
            }
        }

        private void TextArea_TextEntering(object? sender, TextCompositionEventArgs e)
        {
            if (_completionWindow == null)
                return;

            if (string.IsNullOrEmpty(e.Text))
                return;

            var ch = e.Text[0];
            // 当输入不是字母或数字（例如空格、运算符等），关闭提示窗口
            if (!char.IsLetterOrDigit(ch))
            {
                _completionWindow.Close();
                return;
            }

            // 简单过滤：若当前词与候选不再匹配，则关闭
            var caretOffset = Editor.TextArea.Caret.Offset;
            var pendingWord = GetCurrentWord(Editor.Text, caretOffset) + e.Text;
            var hasMatch = _keywords.Any(k => k.StartsWith(pendingWord, StringComparison.OrdinalIgnoreCase))
                           || _functions.Any(f => f.StartsWith(pendingWord, StringComparison.OrdinalIgnoreCase));
            if (!hasMatch)
            {
                _completionWindow.Close();
            }
        }

        private void ShowCompletion(bool force = false)
        {
            var caret = Editor.TextArea.Caret;
            var offset = caret.Offset;
            var word = GetCurrentWord(Editor.Text, offset);

            if (!force && (string.IsNullOrEmpty(word) || word.Length < 2))
            {
                return;
            }

            _completionWindow = new CompletionWindow(Editor.TextArea);
            // 适当放宽宽度，尽量容纳中文描述，减少水平滚动
            if (double.IsNaN(_completionWindow.Width) || _completionWindow.Width < 560)
                _completionWindow.Width = 560;
            var data = _completionWindow.CompletionList.CompletionData;

            IEnumerable<string> candidates = _keywords.Concat(_functions);
            if (!string.IsNullOrEmpty(word))
            {
                candidates = candidates.Where(c => c.StartsWith(word, StringComparison.OrdinalIgnoreCase));
            }

            foreach (var item in candidates.Distinct(StringComparer.OrdinalIgnoreCase))
            {
                _functionSignatures.TryGetValue(item, out var sig);
                data.Add(new SimpleCompletionData(item, sig ?? item));
            }

            if (data.Count > 0)
            {
                // 让补全替换当前已输入的单词（例如 "if"），
                // 而不是在其后插入，避免出现 "ifIF" 的重复问题。
                int replaceLength = string.IsNullOrEmpty(word) ? 0 : word.Length;
                _completionWindow.StartOffset = Math.Max(0, offset - replaceLength);
                _completionWindow.EndOffset = offset;

                _completionWindow.Show();
                // 避免点击滚动条导致候选立即关闭（保守策略：仅阻断列表上的鼠标按下冒泡）
                try { _completionWindow.CompletionList.PreviewMouseDown += (s, e) => { /* no-op to prevent close by parent */ }; } catch { }
                _completionWindow.Closed += (s, e) => _completionWindow = null;
            }
        }

        private static string GetCurrentWord(string text, int offset)
        {
            if (offset <= 0 || offset > text.Length) return string.Empty;
            int start = offset - 1;
            while (start > 0 && char.IsLetterOrDigit(text[start - 1])) start--;
            return text.Substring(start, offset - start);
        }
    }

    /// <summary>
    /// 智能提示项：左侧显示函数名，右侧显示中文签名/说明。
    /// 插入时仅插入函数名本身，不包含描述。
    /// </summary>
    internal class SimpleCompletionData : ICSharpCode.AvalonEdit.CodeCompletion.ICompletionData
    {
        public SimpleCompletionData(string text, string? description = null)
        {
            Text = text;
            Description = string.IsNullOrEmpty(description) ? text : description;
        }

        public ImageSource? Image => null;
        public string Text { get; }
        public object Content
        {
            get
            {
                // 两列布局：左列函数名，右列中文签名/描述（自动换行）
                var grid = new Grid
                {
                    Margin = new Thickness(0)
                };
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                var nameTb = new TextBlock
                {
                    Text = Text,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 12, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetColumn(nameTb, 0);
                grid.Children.Add(nameTb);

                var desc = Description?.ToString() ?? string.Empty;
                var descTb = new TextBlock
                {
                    Text = desc,
                    Foreground = new SolidColorBrush(Color.FromRgb(120, 120, 120)),
                    FontSize = 11,
                    TextWrapping = TextWrapping.Wrap,
                    TextTrimming = TextTrimming.CharacterEllipsis
                };
                Grid.SetColumn(descTb, 1);
                grid.Children.Add(descTb);

                return grid;
            }
        }
        public object Description { get; }
        public double Priority => 0;

        public void Complete(TextArea textArea, ISegment completionSegment, EventArgs insertionRequestEventArgs)
        {
            // 使用 AvalonEdit 提供的 completionSegment 进行替换
            textArea.Document.Replace(completionSegment, Text);
        }
    }

    internal class FormulaHighlightColorizer : DocumentColorizingTransformer
    {
        private readonly HashSet<string> _keywords;
        private readonly HashSet<string> _functions;

        public FormulaHighlightColorizer(HashSet<string> keywords, HashSet<string> functions)
        {
            _keywords = keywords;
            _functions = functions;
        }

        protected override void ColorizeLine(DocumentLine line)
        {
            var text = CurrentContext.Document.GetText(line);
            int lineStart = line.Offset;

            // 高亮字段占位符 {Field}
            for (int i = 0; i < text.Length; i++)
            {
                if (text[i] == '{')
                {
                    int j = i + 1;
                    while (j < text.Length && text[j] != '}') j++;
                    if (j < text.Length && text[j] == '}')
                    {
                        ChangeLinePart(lineStart + i, lineStart + j + 1, (elem) =>
                        {
                            elem.TextRunProperties.SetForegroundBrush(new SolidColorBrush(Color.FromRgb(30, 136, 229))); // 蓝色
                            elem.TextRunProperties.SetTypeface(new Typeface(elem.TextRunProperties.Typeface.FontFamily, FontStyles.Normal, FontWeights.SemiBold, FontStretches.Normal));
                        });
                        i = j;
                    }
                }
            }

            // 关键字/函数的简单匹配（着色）
            int idx = 0;
            while (idx < text.Length)
            {
                if (char.IsLetter(text[idx]))
                {
                    int start = idx;
                    while (idx < text.Length && char.IsLetter(text[idx])) idx++;
                    var word = text.Substring(start, idx - start);
                    if (_keywords.Contains(word))
                    {
                        ChangeLinePart(lineStart + start, lineStart + idx, (elem) =>
                        {
                            elem.TextRunProperties.SetForegroundBrush(new SolidColorBrush(Color.FromRgb(156, 39, 176))); // 紫色
                            elem.TextRunProperties.SetTypeface(new Typeface(elem.TextRunProperties.Typeface.FontFamily, FontStyles.Normal, FontWeights.Bold, FontStretches.Normal));
                        });
                    }
                    else if (_functions.Contains(word))
                    {
                        ChangeLinePart(lineStart + start, lineStart + idx, (elem) =>
                        {
                            elem.TextRunProperties.SetForegroundBrush(new SolidColorBrush(Color.FromRgb(46, 125, 50))); // 绿色
                        });
                    }
                }
                else idx++;
            }
        }
    }
}
