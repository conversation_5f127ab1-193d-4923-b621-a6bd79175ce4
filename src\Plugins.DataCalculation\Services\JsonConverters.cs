using System;
using System.Text.Json;
using System.Text.Json.Serialization;

using Plugins.DataCalculation.Models;

namespace Plugins.DataCalculation.Services
{
    /// <summary>
    /// 将 JSON 中的中文复杂度（"简单/中等/复杂/高级"）映射到枚举 FormulaComplexity。
    /// 也兼容英文（Simple/Medium/Complex/Advanced），以便旧数据仍可读取。
    /// 序列化时输出中文。
    /// </summary>
    public sealed class FormulaComplexityJsonConverter : JsonConverter<FormulaComplexity>
    {
        public override FormulaComplexity Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var s = reader.GetString()?.Trim() ?? string.Empty;
            return s switch
            {
                "简单" or "Simple" => FormulaComplexity.Simple,
                "中等" or "Medium" => FormulaComplexity.Medium,
                "复杂" or "Complex" => FormulaComplexity.Complex,
                "高级" or "Advanced" => FormulaComplexity.Advanced,
                _ => FormulaComplexity.Simple
            };
        }

        public override void Write(Utf8JsonWriter writer, FormulaComplexity value, JsonSerializerOptions options)
        {
            var s = value switch
            {
                FormulaComplexity.Simple => "简单",
                FormulaComplexity.Medium => "中等",
                FormulaComplexity.Complex => "复杂",
                FormulaComplexity.Advanced => "高级",
                _ => "简单"
            };
            writer.WriteStringValue(s);
        }
    }
}


