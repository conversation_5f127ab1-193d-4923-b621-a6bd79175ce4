using System;
using System.Threading.Tasks;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 错误恢复策略接口
/// </summary>
public interface IErrorRecoveryStrategy
{
    /// <summary>
    /// 检查是否可以恢复指定的异常
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <returns>是否可以恢复</returns>
    bool CanRecover(Exception exception);

    /// <summary>
    /// 尝试从异常中恢复
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="context">恢复上下文</param>
    /// <returns>恢复结果</returns>
    Task<RecoveryResult> RecoverAsync(Exception exception, object? context);

    /// <summary>
    /// 获取策略优先级
    /// </summary>
    int Priority { get; }
}

/// <summary>
/// 恢复结果
/// </summary>
public class RecoveryResult
{
    /// <summary>
    /// 是否成功恢复
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 恢复消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 恢复后的数据
    /// </summary>
    public object? RecoveredData { get; set; }

    /// <summary>
    /// 是否需要用户干预
    /// </summary>
    public bool RequiresUserIntervention { get; set; }

    /// <summary>
    /// 创建成功的恢复结果
    /// </summary>
    /// <param name="message">恢复消息</param>
    /// <param name="recoveredData">恢复的数据</param>
    /// <returns>恢复结果</returns>
    public static RecoveryResult Success(string? message = null, object? recoveredData = null)
    {
        return new RecoveryResult
        {
            IsSuccessful = true,
            Message = message,
            RecoveredData = recoveredData
        };
    }

    /// <summary>
    /// 创建失败的恢复结果
    /// </summary>
    /// <param name="message">失败消息</param>
    /// <param name="requiresUserIntervention">是否需要用户干预</param>
    /// <returns>恢复结果</returns>
    public static RecoveryResult Failure(string? message = null, bool requiresUserIntervention = false)
    {
        return new RecoveryResult
        {
            IsSuccessful = false,
            Message = message,
            RequiresUserIntervention = requiresUserIntervention
        };
    }
}
