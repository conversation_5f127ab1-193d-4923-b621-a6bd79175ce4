using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 布尔值到GridLength转换器
    /// </summary>
    public class BooleanToGridLengthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isVisible)
            {
                // 使用全局 UI 配置的默认属性面板宽度
                return isVisible ? new GridLength(ProjectDigitizer.Studio.Configuration.UIConfiguration.DefaultPropertyPanelWidth) : new GridLength(0);
            }
            return new GridLength(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
