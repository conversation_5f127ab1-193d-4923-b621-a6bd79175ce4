using ProjectDigitizer.Core.Exceptions;

namespace ProjectDigitizer.Infrastructure.Exceptions;

/// <summary>
/// 基础设施层异常基类
/// </summary>
public class InfrastructureException : ProjectDigitizerException
{
    public InfrastructureException(string message) : base(message)
    {
    }

    public InfrastructureException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public InfrastructureException(string message, string? errorCode) : base(message, errorCode)
    {
    }

    public InfrastructureException(string message, string? errorCode, Exception innerException) : base(message, errorCode, innerException)
    {
    }
}
