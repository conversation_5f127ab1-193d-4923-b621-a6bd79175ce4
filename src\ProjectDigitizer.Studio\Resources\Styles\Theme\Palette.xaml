<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 统一色板（支持后续暗色主题扩展） -->
    <!-- 品牌主色与对比色 -->
    <!-- 主色：蓝色系，接近 Figma/现代产品的强调色 -->
    <Color x:Key="Color.Primary">#2563EB</Color>
    <Color x:Key="Color.PrimaryVariant">#1D4ED8</Color>
    <Color x:Key="Color.Secondary">#06B6D4</Color>
    <Color x:Key="Color.SecondaryVariant">#0891B2</Color>
    
    <!-- AppBar/TopBar 更偏商务、中性深蓝，提高可读性 -->
    <Color x:Key="Color.AppBar">#FFF7F7F9</Color>

    <!-- 中性色（灰阶） -->
    <Color x:Key="Color.Surface">#FFFFFFFF</Color>
    <Color x:Key="Color.SurfaceVariant">#FFF8F9FA</Color>
    <Color x:Key="Color.Outline">#FFE5E7EB</Color>
    <Color x:Key="Color.Border">#FFDDDDDD</Color>
    <Color x:Key="Color.Text">#FF333333</Color>
    <Color x:Key="Color.TextSecondary">#FF444444</Color>
    <Color x:Key="Color.Placeholder">#FF666666</Color>

    <!-- 反馈色 -->
    <Color x:Key="Color.Success">#10B981</Color>
    <Color x:Key="Color.Warning">#F59E0B</Color>
    <Color x:Key="Color.Danger">#EF4444</Color>
    <Color x:Key="Color.Info">#3B82F6</Color>

    <!-- 阴影色（用于阴影与分隔线） -->
    <Color x:Key="Color.Shadow">#33000000</Color>
    <Color x:Key="Color.Divider">#14000000</Color>

    <!-- 透明辅助色 -->
    <Color x:Key="Color.Primary/08">#144F46E5</Color>
    <Color x:Key="Color.Primary/16">#294F46E5</Color>
    <Color x:Key="Color.Text/60">#99111827</Color>
    <Color x:Key="Color.Text/40">#66111827</Color>
    <Color x:Key="Color.Text/24">#3D111827</Color>
</ResourceDictionary>
