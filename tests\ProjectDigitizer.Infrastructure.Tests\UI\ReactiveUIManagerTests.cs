using System;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

using ProjectDigitizer.Infrastructure.UI;

using Xunit;

namespace ProjectDigitizer.Infrastructure.Tests.UI
{
    /// <summary>
    /// 响应式UI管理器测试
    /// </summary>
    public class ReactiveUIManagerTests : IDisposable
    {
        private readonly ReactiveUIManager _uiManager;

        public ReactiveUIManagerTests()
        {
            _uiManager = new ReactiveUIManager();
            _uiManager.StartReactiveUI(); // 启动UI管理器
        }

        [Fact]
        public void Constructor_ShouldInitializeCorrectly()
        {
            // Assert
            Assert.NotNull(_uiManager.CurrentConfig);
            Assert.NotNull(_uiManager.UpdateStats);
            Assert.True(_uiManager.CurrentConfig.EnableBatchUpdates);
            Assert.True(_uiManager.CurrentConfig.EnableThrottling);
            Assert.True(_uiManager.CurrentConfig.EnableDebouncing);
        }

        [Fact]
        public void StartReactiveUI_ShouldBeginUIManagement()
        {
            // Act
            _uiManager.StartReactiveUI();

            // Assert - 应该能够正常启动，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void StopReactiveUI_ShouldStopUIManagement()
        {
            // Arrange
            _uiManager.StartReactiveUI();

            // Act
            _uiManager.StopReactiveUI();

            // Assert - 应该能够正常停止，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void RegisterViewModel_ShouldAddToManagedViewModels()
        {
            // Arrange
            var viewModel = new TestViewModel();

            // Act
            _uiManager.RegisterViewModel(viewModel);

            // Assert
            var metrics = _uiManager.GetPerformanceMetrics();
            Assert.Equal(1, metrics.RegisteredViewModelsCount);
        }

        [Fact]
        public void UnregisterViewModel_ShouldRemoveFromManagedViewModels()
        {
            // Arrange
            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);

            // Act
            _uiManager.UnregisterViewModel(viewModel);

            // Assert
            var metrics = _uiManager.GetPerformanceMetrics();
            Assert.Equal(0, metrics.RegisteredViewModelsCount);
        }

        [Fact]
        public void BatchUpdateProperties_ShouldProcessUpdatesInBatch()
        {
            // Arrange
            var viewModel1 = new TestViewModel();
            var viewModel2 = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel1);
            _uiManager.RegisterViewModel(viewModel2);

            var updates = new[]
            {
                new PropertyUpdate
                {
                    ViewModel = viewModel1,
                    PropertyName = "TestProperty",
                    NewValue = "Value1",
                    Priority = UpdatePriority.Normal
                },
                new PropertyUpdate
                {
                    ViewModel = viewModel2,
                    PropertyName = "TestProperty",
                    NewValue = "Value2",
                    Priority = UpdatePriority.High
                }
            };

            bool batchCompleted = false;
            _uiManager.BatchUpdateCompleted += (args) => batchCompleted = true;

            // Act
            _uiManager.BatchUpdateProperties(updates);

            // Assert
            Assert.True(batchCompleted);
        }

        [Fact]
        public void DelayedUpdate_ShouldExecuteAfterDelay()
        {
            // Arrange
            bool updateExecuted = false;
            Action updateAction = () => updateExecuted = true;

            // Act
            _uiManager.DelayedUpdate(updateAction, 50);

            // Assert - 立即检查应该还没有执行
            Assert.False(updateExecuted);

            // 等待延迟时间后再检查
            System.Threading.Thread.Sleep(100);
            Assert.True(updateExecuted);
        }

        [Fact]
        public void ThrottledUpdate_ShouldLimitUpdateFrequency()
        {
            // Arrange
            int updateCount = 0;
            Action updateAction = () => updateCount++;

            // Act - 快速连续调用多次
            for (int i = 0; i < 5; i++)
            {
                _uiManager.ThrottledUpdate("test-key", updateAction, 100);
            }

            // Assert - 由于节流，应该只执行一次
            Assert.Equal(1, updateCount);
        }

        [Fact]
        public void DebouncedUpdate_ShouldDelayExecutionUntilQuiet()
        {
            // Arrange
            int updateCount = 0;
            Action updateAction = () => updateCount++;

            // Act - 快速连续调用多次
            for (int i = 0; i < 3; i++)
            {
                _uiManager.DebouncedUpdate("test-key", updateAction, 50);
                System.Threading.Thread.Sleep(10); // 短暂延迟，但小于防抖时间
            }

            // Assert - 立即检查应该还没有执行
            Assert.Equal(0, updateCount);

            // 等待防抖时间后再检查
            System.Threading.Thread.Sleep(100);
            Assert.Equal(1, updateCount); // 应该只执行最后一次
        }

        [Fact]
        public void SetPropertyPriority_ShouldUpdatePropertyConfig()
        {
            // Arrange
            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);

            // Act
            _uiManager.SetPropertyPriority(viewModel, "TestProperty", UpdatePriority.High);

            // Assert - 应该能够正常设置，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void SuspendUpdates_ShouldPauseViewModelUpdates()
        {
            // Arrange
            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);
            _uiManager.StartReactiveUI();

            // Act
            _uiManager.SuspendUpdates(viewModel);

            // Assert - 应该能够正常暂停，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void ResumeUpdates_ShouldResumeViewModelUpdates()
        {
            // Arrange
            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);
            _uiManager.SuspendUpdates(viewModel);

            // Act
            _uiManager.ResumeUpdates(viewModel);

            // Assert - 应该能够正常恢复，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void FlushPendingUpdates_ShouldProcessAllPendingUpdates()
        {
            // Act
            _uiManager.FlushPendingUpdates();

            // Assert - 应该能够正常刷新，不抛出异常
            Assert.True(true);
        }

        [Fact]
        public void GetPerformanceMetrics_ShouldReturnValidMetrics()
        {
            // Arrange
            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);

            // Act
            var metrics = _uiManager.GetPerformanceMetrics();

            // Assert
            Assert.NotNull(metrics);
            Assert.Equal(1, metrics.RegisteredViewModelsCount);
            Assert.True(metrics.UIResponseTimeMs >= 0);
            Assert.True(metrics.UpdateFrequency >= 0);
            Assert.True(metrics.BatchingEfficiency >= 0 && metrics.BatchingEfficiency <= 1);
            Assert.True(metrics.ThrottlingEfficiency >= 0 && metrics.ThrottlingEfficiency <= 1);
            Assert.True(metrics.OverallUIPerformanceScore >= 0 && metrics.OverallUIPerformanceScore <= 100);
        }

        [Fact]
        public void ApplyConfig_ShouldUpdateCurrentConfig()
        {
            // Arrange
            var newConfig = new ReactiveUIConfig
            {
                EnableBatchUpdates = false,
                BatchSize = 50,
                BatchDelayMs = 100,
                EnableThrottling = false,
                DefaultThrottleMs = 200
            };

            // Act
            _uiManager.ApplyConfig(newConfig);

            // Assert
            Assert.Equal(newConfig.EnableBatchUpdates, _uiManager.CurrentConfig.EnableBatchUpdates);
            Assert.Equal(newConfig.BatchSize, _uiManager.CurrentConfig.BatchSize);
            Assert.Equal(newConfig.BatchDelayMs, _uiManager.CurrentConfig.BatchDelayMs);
            Assert.Equal(newConfig.EnableThrottling, _uiManager.CurrentConfig.EnableThrottling);
            Assert.Equal(newConfig.DefaultThrottleMs, _uiManager.CurrentConfig.DefaultThrottleMs);
        }

        [Fact]
        public void BatchUpdateCompleted_EventShouldTriggerOnBatchProcessing()
        {
            // Arrange
            bool eventTriggered = false;
            UIBatchUpdateEventArgs? eventArgs = null;
            _uiManager.BatchUpdateCompleted += (args) =>
            {
                eventTriggered = true;
                eventArgs = args;
            };

            var viewModel = new TestViewModel();
            _uiManager.RegisterViewModel(viewModel);

            var updates = new[]
            {
                new PropertyUpdate
                {
                    ViewModel = viewModel,
                    PropertyName = "TestProperty",
                    NewValue = "TestValue",
                    Priority = UpdatePriority.Normal
                }
            };

            // Act
            _uiManager.BatchUpdateProperties(updates);

            // Assert
            Assert.True(eventTriggered);
            Assert.NotNull(eventArgs);
            Assert.Equal(1, eventArgs.UpdateCount);
            Assert.Equal(1, eventArgs.ViewModelCount);
        }

        [Fact]
        public void PerformanceWarning_EventShouldBeConfigurable()
        {
            // Arrange
            bool eventTriggered = false;
            _uiManager.PerformanceWarning += (warning) => eventTriggered = true;

            // Assert
            // 事件已经被正确订阅，测试通过
        }

        public void Dispose()
        {
            _uiManager?.Dispose();
        }

        /// <summary>
        /// 测试用的ViewModel
        /// </summary>
        private class TestViewModel : INotifyPropertyChanged
        {
            private string _testProperty = string.Empty;

            public string TestProperty
            {
                get => _testProperty;
                set
                {
                    if (_testProperty != value)
                    {
                        _testProperty = value;
                        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(TestProperty)));
                    }
                }
            }

            public event PropertyChangedEventHandler? PropertyChanged;
        }
    }
}
