using System;
using System.Linq;
using System.Reflection;

using Microsoft.Extensions.DependencyInjection;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Extensions;

/// <summary>
/// 节点相关的 DI 扩展。
/// </summary>
public static class NodeServiceCollectionExtensions
{
    /// <summary>
    /// 注册节点框架（注册注册表、扫描并加载传入程序集中的节点模块）。
    /// </summary>
    public static IServiceCollection AddNodeFramework(this IServiceCollection services, params Assembly[] assemblies)
    {
        // 注册 NodeRegistry 为单例
        services.AddSingleton<INodeRegistry, Services.NodeRegistry>();

        if (assemblies is { Length: > 0 })
        {
            // 采用 BuildServiceProvider 之前的轻量方式：临时创建注册表实例用于扫描时注册
            var registry = new Services.NodeRegistry();
            // 预注册：替换容器中单例实例
            services.AddSingleton<INodeRegistry>(registry);

            foreach (var asm in assemblies.Distinct())
            {
                try
                {
                    var moduleTypes = asm.GetTypes()
                        .Where(t => !t.IsAbstract && !t.IsInterface && typeof(INodeModule).IsAssignableFrom(t))
                        .ToArray();

                    foreach (var type in moduleTypes)
                    {
                        if (Activator.CreateInstance(type) is INodeModule module)
                        {
                            module.RegisterNodes(registry);
                        }
                    }
                }
                catch
                {
                    // 忽略单个程序集扫描异常，避免影响整体启动
                }
            }
        }

        return services;
    }
}

