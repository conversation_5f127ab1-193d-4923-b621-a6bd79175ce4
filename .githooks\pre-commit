#!/usr/bin/env bash
set -euo pipefail

# Pre-commit: verify formatting of staged files ONLY (no writes)
export DOTNET_NOLOGO=1

# Collect staged C#/XAML/CSPROJ files (Added, Copied, Modified, Renamed)
mapfile -t files < <(git diff --cached --name-only --diff-filter=ACMR | grep -E '\.(cs|xaml|csproj)$' || true)
if [[ ${#files[@]} -eq 0 ]]; then
  exit 0
fi

echo "[pre-commit] Verifying dotnet format on staged files (style+whitespace)..."

args=()
for f in "${files[@]}"; do
  [[ -f "$f" ]] || continue
  args+=(--include "$f")
done

if [[ ${#args[@]} -eq 0 ]]; then
  exit 0
fi

# Check only; do NOT modify files to avoid post-commit diffs
dotnet format whitespace ProjectDigitizer.sln --no-restore --verbosity minimal --verify-no-changes "${args[@]}"
code_ws=$?
dotnet format style ProjectDigitizer.sln --no-restore --verbosity minimal --verify-no-changes "${args[@]}"
code_style=$?
if [[ $code_ws -ne 0 || $code_style -ne 0 ]]; then
  echo "[pre-commit] 需要先格式化：请运行以下命令后重试提交" >&2
  echo "  dotnet format whitespace ProjectDigitizer.sln --no-restore ${args[*]}" >&2
  echo "  dotnet format style ProjectDigitizer.sln --no-restore ${args[*]}" >&2
  exit 1
fi

exit 0
