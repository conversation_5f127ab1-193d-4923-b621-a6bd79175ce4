using System;
using System.Collections.Generic;
using System.Linq;

using ProjectDigitizer.Core.DomainServices;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Application.Services
{
    /// <summary>
    /// 函数注册表 - 管理所有可用的函数提供者
    /// </summary>
    public class FunctionRegistry
    {
        private readonly Dictionary<string, IFunctionProvider> _functions;
        private readonly Dictionary<string, List<IFunctionProvider>> _categories;
        private static FunctionRegistry? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static FunctionRegistry Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new FunctionRegistry();
                        }
                    }
                }
                return _instance;
            }
        }

        private FunctionRegistry()
        {
            _functions = new Dictionary<string, IFunctionProvider>(StringComparer.OrdinalIgnoreCase);
            _categories = new Dictionary<string, List<IFunctionProvider>>();
            RegisterBuiltInFunctions();
        }

        /// <summary>
        /// 注册内置函数
        /// </summary>
        private void RegisterBuiltInFunctions()
        {
            // 注册基础数学函数
            RegisterFunction(new SumFunction());
            RegisterFunction(new AverageFunction());
            RegisterFunction(new CountFunction());
            RegisterFunction(new MaxFunction());
            RegisterFunction(new MinFunction());

            // 注册其他数学函数
            RegisterFunction(new AbsFunction());
            RegisterFunction(new RoundFunction());
            RegisterFunction(new PowerFunction());
            RegisterFunction(new SqrtFunction());

            // 注册字符串函数
            RegisterFunction(new ConcatFunction());
            RegisterFunction(new LengthFunction());
            RegisterFunction(new UpperFunction());
            RegisterFunction(new LowerFunction());

            // 注册逻辑函数
            RegisterFunction(new IfFunction());
            RegisterFunction(new AndFunction());
            RegisterFunction(new OrFunction());
            RegisterFunction(new NotFunction());
        }

        /// <summary>
        /// 注册函数提供者
        /// </summary>
        /// <param name="functionProvider">函数提供者</param>
        public void RegisterFunction(IFunctionProvider functionProvider)
        {
            if (functionProvider == null)
                throw new ArgumentNullException(nameof(functionProvider));

            var name = functionProvider.Name.ToUpper();
            _functions[name] = functionProvider;

            // 按分类组织
            var category = functionProvider.Category;
            if (!_categories.ContainsKey(category))
            {
                _categories[category] = new List<IFunctionProvider>();
            }

            if (!_categories[category].Contains(functionProvider))
            {
                _categories[category].Add(functionProvider);
            }

            System.Diagnostics.Debug.WriteLine($"已注册函数: {functionProvider.Name} ({category})");
        }

        /// <summary>
        /// 注销函数提供者
        /// </summary>
        /// <param name="functionName">函数名称</param>
        public void UnregisterFunction(string functionName)
        {
            if (string.IsNullOrEmpty(functionName))
                return;

            var name = functionName.ToUpper();
            if (_functions.TryGetValue(name, out var function))
            {
                _functions.Remove(name);

                // 从分类中移除
                var category = function.Category;
                if (_categories.ContainsKey(category))
                {
                    _categories[category].Remove(function);
                    if (!_categories[category].Any())
                    {
                        _categories.Remove(category);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已注销函数: {functionName}");
            }
        }

        /// <summary>
        /// 获取函数提供者
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <returns>函数提供者，如果不存在则返回null</returns>
        public IFunctionProvider? GetFunction(string functionName)
        {
            if (string.IsNullOrEmpty(functionName))
                return null;

            var name = functionName.ToUpper();
            return _functions.TryGetValue(name, out var function) ? function : null;
        }

        /// <summary>
        /// 检查函数是否存在
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <returns>是否存在</returns>
        public bool HasFunction(string functionName)
        {
            if (string.IsNullOrEmpty(functionName))
                return false;

            var name = functionName.ToUpper();
            return _functions.ContainsKey(name);
        }

        /// <summary>
        /// 获取所有函数提供者
        /// </summary>
        /// <returns>函数提供者列表</returns>
        public List<IFunctionProvider> GetAllFunctions()
        {
            return _functions.Values.ToList();
        }

        /// <summary>
        /// 按分类获取函数提供者
        /// </summary>
        /// <param name="category">分类名称</param>
        /// <returns>函数提供者列表</returns>
        public List<IFunctionProvider> GetFunctionsByCategory(string category)
        {
            return _categories.TryGetValue(category, out var functions)
                ? functions.ToList()
                : new List<IFunctionProvider>();
        }

        /// <summary>
        /// 获取所有分类
        /// </summary>
        /// <returns>分类列表</returns>
        public List<string> GetCategories()
        {
            return _categories.Keys.ToList();
        }

        /// <summary>
        /// 按类型获取函数提供者
        /// </summary>
        /// <param name="functionType">函数类型</param>
        /// <returns>函数提供者列表</returns>
        public List<IFunctionProvider> GetFunctionsByType(FunctionType functionType)
        {
            return _functions.Values.Where(f => f.Type == functionType).ToList();
        }

        /// <summary>
        /// 搜索函数
        /// </summary>
        /// <param name="searchTerm">搜索词</param>
        /// <returns>匹配的函数提供者列表</returns>
        public List<IFunctionProvider> SearchFunctions(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return GetAllFunctions();

            var term = searchTerm.ToLower();
            return _functions.Values.Where(f =>
                f.Name.ToLower().Contains(term) ||
                f.DisplayName.ToLower().Contains(term) ||
                f.Description.ToLower().Contains(term) ||
                f.Category.ToLower().Contains(term)).ToList();
        }

        /// <summary>
        /// 执行函数
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>执行结果</returns>
        public FunctionResult ExecuteFunction(string functionName, Dictionary<string, object> parameters)
        {
            var function = GetFunction(functionName);
            if (function == null)
            {
                return FunctionResult.Error($"未找到函数: {functionName}");
            }

            try
            {
                // 验证参数
                var validation = function.ValidateParameters(parameters);
                if (!validation.IsValid)
                {
                    var errors = string.Join(", ", validation.Errors);
                    return FunctionResult.Error($"参数验证失败: {errors}");
                }

                // 执行函数
                return function.Execute(parameters);
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"函数执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取函数统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public FunctionStatistics GetStatistics()
        {
            var totalFunctions = _functions.Count;
            var categoryCounts = _categories.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.Count);

            var typeCounts = new Dictionary<FunctionType, int>();
            foreach (FunctionType type in Enum.GetValues<FunctionType>())
            {
                typeCounts[type] = _functions.Values.Count(f => f.Type == type);
            }

            return new FunctionStatistics
            {
                TotalFunctions = totalFunctions,
                CategoryCounts = categoryCounts,
                TypeCounts = typeCounts
            };
        }

        /// <summary>
        /// 清空所有函数（除了内置函数）
        /// </summary>
        public void ClearCustomFunctions()
        {
            var customFunctions = _functions.Values
                .Where(f => !IsBuiltInFunction(f))
                .ToList();

            foreach (var function in customFunctions)
            {
                UnregisterFunction(function.Name);
            }
        }

        /// <summary>
        /// 检查是否为内置函数
        /// </summary>
        /// <param name="function">函数提供者</param>
        /// <returns>是否为内置函数</returns>
        private bool IsBuiltInFunction(IFunctionProvider function)
        {
            // 简单实现：检查命名空间
            return function.GetType().Namespace?.Contains("ProjectDigitizer.Studio.Functions") == true;
        }
    }

    /// <summary>
    /// 函数统计信息
    /// </summary>
    public class FunctionStatistics
    {
        public int TotalFunctions { get; set; }
        public Dictionary<string, int> CategoryCounts { get; set; } = new();
        public Dictionary<FunctionType, int> TypeCounts { get; set; } = new();
    }
}
