namespace CalcEngine
{
    /// <summary>
    /// CalcEngine：解析字符串表达式并生成可求值的 <see cref="Expression"/>，支持变量、对象属性与自定义函数。
    /// </summary>
    /// <remarks>
    /// 可扩展点：
    /// 1) 通过 <b>DataContext</b> 将对象的公有属性暴露到表达式作用域中（支持多级属性，如 Address.Street）。
    /// 2) 通过 <b>RegisterFunction</b> 注册自定义函数。
    /// 3) 重写 <b>GetExternalObject</b> 以按需动态提供外部对象/变量（如单元格范围、命名区域等）。
    /// </remarks>
    public partial class CalcEngine { }
}

