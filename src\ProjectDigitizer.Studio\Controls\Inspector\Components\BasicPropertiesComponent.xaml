<local:InspectorComponent x:Class="ProjectDigitizer.Studio.Controls.Inspector.Components.BasicPropertiesComponent"
                          xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                          xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                          xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                          xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                          xmlns:local="clr-namespace:ProjectDigitizer.Studio.Controls.Inspector"
                          mc:Ignorable="d"
                          d:DesignHeight="300"
                          d:DesignWidth="320">

    <UserControl.Resources>
        <!-- 属性标签样式 -->
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="FontWeight"
                    Value="Medium"/>
            <Setter Property="Margin"
                    Value="0,8,0,4"/>
            <Setter Property="Foreground"
                    Value="{DynamicResource Brush.Text}"/>
        </Style>

        <!-- 属性编辑器样式 -->
        <Style x:Key="PropertyEditorStyle" TargetType="FrameworkElement">
            <Setter Property="Margin"
                    Value="0,0,0,12"/>
        </Style>
    </UserControl.Resources>

    <StackPanel>
        <!-- 基本信息 -->
        <GroupBox Header="基本信息"
                  Margin="0,0,0,16">
            <StackPanel>
                <!-- 节点名称 -->
                <TextBlock Text="节点名称"
                           Style="{StaticResource PropertyLabelStyle}"/>
                <TextBox x:Name="NodeNameTextBox"
                         Style="{StaticResource PropertyEditorStyle}"/>

                <!-- 节点描述 -->
                <TextBlock Text="描述"
                           Style="{StaticResource PropertyLabelStyle}"/>
                <TextBox x:Name="NodeDescriptionTextBox"
                         Style="{StaticResource PropertyEditorStyle}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinHeight="60"/>

                <!-- 节点类型（只读） -->
                <TextBlock Text="节点类型"
                           Style="{StaticResource PropertyLabelStyle}"/>
                <TextBox x:Name="NodeTypeTextBox"
                         Style="{StaticResource PropertyEditorStyle}"
                         IsReadOnly="True"
                         Background="{DynamicResource Brush.SurfaceVariant}"/>
            </StackPanel>
        </GroupBox>

        <!-- 高级设置 -->
        <GroupBox Header="高级设置">
            <StackPanel>
                <!-- 启用状态 -->
                <CheckBox x:Name="EnabledCheckBox"
                          Content="启用此节点"
                          Margin="0,8,0,8"
                          IsChecked="True"/>

                <!-- 调试模式 -->
                <CheckBox x:Name="DebugModeCheckBox"
                          Content="调试模式"
                          Margin="0,0,0,8"
                          ToolTip="启用后将输出详细的调试信息"/>

                <!-- 超时设置 -->
                <TextBlock Text="超时时间 (秒)"
                           Style="{StaticResource PropertyLabelStyle}"/>
                <Slider x:Name="TimeoutSlider"
                        Style="{StaticResource PropertyEditorStyle}"
                        Minimum="1"
                        Maximum="300"
                        Value="30"
                        TickFrequency="30"
                        IsSnapToTickEnabled="True"
                        TickPlacement="BottomRight"/>
                <TextBlock Text="{Binding Value, ElementName=TimeoutSlider, StringFormat=\{0\} 秒}"
                           FontSize="11"
                           Opacity="0.7"
                           Margin="0,-8,0,8"/>
            </StackPanel>
        </GroupBox>
    </StackPanel>
</local:InspectorComponent>


