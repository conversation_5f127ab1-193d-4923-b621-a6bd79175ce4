namespace ProjectDigitizer.Core.Enums
{
    /// <summary>
    /// 连接器数据类型
    /// </summary>
    public enum ConnectorDataType
    {
        /// <summary>通用类型 - 可连接任何数据类型</summary>
        Any,

        /// <summary>数值类型 - 整数、小数等数值数据</summary>
        Number,

        /// <summary>文本类型 - 字符串、文本数据</summary>
        Text,

        /// <summary>布尔类型 - 真/假逻辑值</summary>
        <PERSON><PERSON><PERSON>,

        /// <summary>文件类型 - 文件路径或文件数据</summary>
        File,

        /// <summary>几何类型 - 几何图形、坐标数据</summary>
        Geometry,

        /// <summary>控制流 - 程序执行控制信号</summary>
        Control
    }
}
