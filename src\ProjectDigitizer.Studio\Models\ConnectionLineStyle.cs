using System.ComponentModel;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 连接线样式枚举
    /// 定义画布中连接线的不同显示样式
    /// </summary>
    public enum ConnectionLineStyle
    {
        /// <summary>
        /// 直线/正交线连接
        /// 使用 nodify:LineConnection，支持圆角和正交路径
        /// </summary>
        [Description("正交线")]
        Line = 0,

        /// <summary>
        /// 贝塞尔曲线连接
        /// 使用 nodify:Connection，提供平滑的曲线连接
        /// </summary>
        [Description("贝塞尔曲线")]
        Bezier = 1,

        /// <summary>
        /// 阶梯线连接
        /// 使用 nodify:StepConnection，提供阶梯式的连接路径
        /// </summary>
        [Description("阶梯线")]
        Step = 2,

        /// <summary>
        /// 电路线连接
        /// 使用 nodify:CircuitConnection，提供电路图风格的连接
        /// </summary>
        [Description("电路线")]
        Circuit = 3
    }

    /// <summary>
    /// 连接线样式扩展方法
    /// </summary>
    public static class ConnectionLineStyleExtensions
    {
        /// <summary>
        /// 获取连接线样式的显示名称
        /// </summary>
        /// <param name="style">连接线样式</param>
        /// <returns>显示名称</returns>
        public static string GetDisplayName(this ConnectionLineStyle style)
        {
            var field = style.GetType().GetField(style.ToString());
            var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            return attribute?.Description ?? style.ToString();
        }

        /// <summary>
        /// 获取连接线样式对应的Nodify控件类型名称
        /// </summary>
        /// <param name="style">连接线样式</param>
        /// <returns>Nodify控件类型名称</returns>
        public static string GetNodifyControlType(this ConnectionLineStyle style)
        {
            return style switch
            {
                ConnectionLineStyle.Line => "LineConnection",
                ConnectionLineStyle.Bezier => "Connection",
                ConnectionLineStyle.Step => "StepConnection",
                ConnectionLineStyle.Circuit => "CircuitConnection",
                _ => "LineConnection"
            };
        }

        /// <summary>
        /// 获取连接线样式的图标资源键
        /// </summary>
        /// <param name="style">连接线样式</param>
        /// <returns>图标资源键</returns>
        public static string GetIconResourceKey(this ConnectionLineStyle style)
        {
            return style switch
            {
                ConnectionLineStyle.Line => "LineConnectionIcon",
                ConnectionLineStyle.Bezier => "BezierConnectionIcon",
                ConnectionLineStyle.Step => "StepConnectionIcon",
                ConnectionLineStyle.Circuit => "CircuitConnectionIcon",
                _ => "LineConnectionIcon"
            };
        }
    }
}
