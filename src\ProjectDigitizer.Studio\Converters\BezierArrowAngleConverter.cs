using System;
using System.Globalization;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 贝塞尔曲线箭头角度转换器
    /// 根据贝塞尔曲线终点的切线方向计算箭头的旋转角度
    /// </summary>
    public class BezierArrowAngleConverter : IMultiValueConverter
    {
        /// <summary>
        /// 将源点和目标点转换为贝塞尔曲线箭头角度
        /// </summary>
        /// <param name="values">values[0]: Source.X, values[1]: Source.Y, values[2]: Target.X, values[3]: Target.Y</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数（可选）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>箭头的旋转角度（度）</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 4)
                return 0.0;

            // 尝试解析坐标值
            if (!TryParseDouble(values[0], out double sourceX) ||
                !TryParseDouble(values[1], out double sourceY) ||
                !TryParseDouble(values[2], out double targetX) ||
                !TryParseDouble(values[3], out double targetY))
            {
                return 0.0;
            }

            // 计算贝塞尔曲线的控制点（模拟Nodify的默认行为）
            double spacing = 15.0; // 与XAML中的Spacing保持一致

            // 计算控制点
            double controlPoint1X = sourceX + spacing;
            double controlPoint1Y = sourceY;
            double controlPoint2X = targetX - spacing;
            double controlPoint2Y = targetY;

            // 计算贝塞尔曲线在终点处的切线方向
            // 对于三次贝塞尔曲线，终点处的切线方向是从倒数第二个控制点指向终点
            double tangentX = targetX - controlPoint2X;
            double tangentY = targetY - controlPoint2Y;

            // 如果切线长度太小，回退到直线方向
            double tangentLength = Math.Sqrt(tangentX * tangentX + tangentY * tangentY);
            if (tangentLength < 0.001)
            {
                tangentX = targetX - sourceX;
                tangentY = targetY - sourceY;
                tangentLength = Math.Sqrt(tangentX * tangentX + tangentY * tangentY);
            }

            // 避免除零错误
            if (tangentLength < 0.001)
                return 0.0;

            // 计算角度（弧度）
            double angleRadians = Math.Atan2(tangentY, tangentX);

            // 转换为度数
            double angleDegrees = angleRadians * 180.0 / Math.PI;

            // 确保角度在0-360度范围内
            if (angleDegrees < 0)
                angleDegrees += 360;

            System.Diagnostics.Debug.WriteLine($"BezierArrowAngleConverter: Source=({sourceX},{sourceY}), Target=({targetX},{targetY}), " +
                                             $"ControlPoint2=({controlPoint2X},{controlPoint2Y}), Tangent=({tangentX},{tangentY}), " +
                                             $"Angle={angleDegrees}°");

            return angleDegrees;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("BezierArrowAngleConverter does not support ConvertBack");
        }

        /// <summary>
        /// 安全解析double值
        /// </summary>
        private static bool TryParseDouble(object value, out double result)
        {
            result = 0.0;

            if (value == null)
                return false;

            if (value is double d)
            {
                result = d;
                return true;
            }

            if (value is float f)
            {
                result = f;
                return true;
            }

            if (value is int i)
            {
                result = i;
                return true;
            }

            return double.TryParse(value.ToString(), out result);
        }
    }
}
