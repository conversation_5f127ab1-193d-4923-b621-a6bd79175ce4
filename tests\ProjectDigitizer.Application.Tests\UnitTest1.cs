using ProjectDigitizer.Application.Tests.Fixtures;

namespace ProjectDigitizer.Application.Tests;

/// <summary>
/// Application层基础功能测试
/// </summary>
[Collection("Application Tests")]
public class ApplicationBasicTests
{
    private readonly ApplicationTestFixture _fixture;

    public ApplicationBasicTests(ApplicationTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public void ServiceProvider_Should_BeConfigured()
    {
        // Arrange & Act
        var serviceProvider = _fixture.ServiceProvider;

        // Assert
        serviceProvider.Should().NotBeNull();
    }

    [Fact]
    public void ApplicationServices_Should_BeRegistered()
    {
        // Arrange & Act
        using var scope = _fixture.CreateScope();

        // Assert
        scope.Should().NotBeNull();
        scope.ServiceProvider.Should().NotBeNull();
    }
}
