namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 实体基接口
/// </summary>
public interface IEntity
{
    /// <summary>
    /// 实体ID
    /// </summary>
    object Id { get; }
}

/// <summary>
/// 泛型实体接口
/// </summary>
/// <typeparam name="TId">ID类型</typeparam>
public interface IEntity<out TId> : IEntity
{
    /// <summary>
    /// 强类型实体ID
    /// </summary>
    new TId Id { get; }
}

/// <summary>
/// 可审计实体接口
/// </summary>
public interface IAuditableEntity : IEntity
{
    /// <summary>
    /// 创建时间
    /// </summary>
    DateTime CreatedAt { get; }

    /// <summary>
    /// 创建者
    /// </summary>
    string? CreatedBy { get; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    DateTime? LastModifiedAt { get; }

    /// <summary>
    /// 最后修改者
    /// </summary>
    string? LastModifiedBy { get; }
}

/// <summary>
/// 软删除实体接口
/// </summary>
public interface ISoftDeletableEntity : IEntity
{
    /// <summary>
    /// 是否已删除
    /// </summary>
    bool IsDeleted { get; }

    /// <summary>
    /// 删除时间
    /// </summary>
    DateTime? DeletedAt { get; }

    /// <summary>
    /// 删除者
    /// </summary>
    string? DeletedBy { get; }

    /// <summary>
    /// 标记为已删除
    /// </summary>
    /// <param name="deletedBy">删除者</param>
    void MarkAsDeleted(string? deletedBy = null);

    /// <summary>
    /// 恢复删除
    /// </summary>
    void Restore();
}

/// <summary>
/// 聚合根接口
/// </summary>
public interface IAggregateRoot : IEntity
{
    /// <summary>
    /// 领域事件集合
    /// </summary>
    IReadOnlyCollection<IDomainEvent> DomainEvents { get; }

    /// <summary>
    /// 添加领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    void AddDomainEvent(IDomainEvent domainEvent);

    /// <summary>
    /// 移除领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    void RemoveDomainEvent(IDomainEvent domainEvent);

    /// <summary>
    /// 清除所有领域事件
    /// </summary>
    void ClearDomainEvents();
}
