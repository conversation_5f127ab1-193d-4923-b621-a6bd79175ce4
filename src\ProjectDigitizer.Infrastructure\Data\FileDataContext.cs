using System.IO;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Repositories;

namespace ProjectDigitizer.Infrastructure.Data;

/// <summary>
/// 基于文件系统的数据上下文实现
/// 适用于桌面应用程序的文件存储场景
/// </summary>
public class FileDataContext : IDataContext
{
    private readonly ILogger<FileDataContext> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<Type, object> _repositories;
    private readonly string _dataDirectory;
    private bool _disposed = false;

    public FileDataContext(ILogger<FileDataContext> logger, IServiceProvider serviceProvider, string dataDirectory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _dataDirectory = dataDirectory ?? throw new ArgumentNullException(nameof(dataDirectory));
        _repositories = new Dictionary<Type, object>();

        // 确保数据目录存在
        if (!Directory.Exists(_dataDirectory))
        {
            Directory.CreateDirectory(_dataDirectory);
            _logger.LogDebug("创建数据目录: {DataDirectory}", _dataDirectory);
        }
    }

    /// <summary>
    /// 获取指定类型的存储库
    /// </summary>
    public IRepository<T> GetRepository<T>() where T : class
    {
        var entityType = typeof(T);

        if (_repositories.TryGetValue(entityType, out var existingRepository))
        {
            return (IRepository<T>)existingRepository;
        }

        // 创建新的文件存储库实例
        var fileName = $"{entityType.Name}.json";
        var filePath = Path.Combine(_dataDirectory, fileName);

        var logger = _serviceProvider.GetService(typeof(ILogger<Repository<T>>)) as ILogger<Repository<T>>;
        if (logger == null)
        {
            throw new InvalidOperationException($"无法获取类型 {typeof(ILogger<Repository<T>>)} 的日志记录器");
        }

        var repository = new FileRepository<T>(filePath, logger);
        _repositories[entityType] = repository;

        _logger.LogDebug("创建存储库: {EntityType} -> {FilePath}", entityType.Name, filePath);

        return repository;
    }

    /// <summary>
    /// 保存所有更改
    /// 对于文件存储库，这个操作是自动的，因为每次操作都会立即保存
    /// </summary>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("保存数据上下文更改");

            // 对于文件存储库，更改是立即保存的
            // 这里可以添加额外的验证或清理逻辑

            await Task.CompletedTask;
            return 0; // 文件存储库不跟踪更改数量
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存数据上下文更改时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 开始事务
    /// 对于文件存储库，实现简单的事务模拟
    /// </summary>
    public async Task<IDataTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("开始数据事务");

        var transaction = new FileDataTransaction(_logger, _dataDirectory);
        await Task.CompletedTask;

        return transaction;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _logger.LogDebug("释放文件数据上下文资源");

            // 清理存储库实例
            foreach (var repository in _repositories.Values)
            {
                if (repository is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }

            _repositories.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 文件数据事务实现
/// 提供简单的事务语义支持
/// </summary>
internal class FileDataTransaction : IDataTransaction
{
    private readonly ILogger _logger;
    private readonly string _dataDirectory;
    private readonly string _backupDirectory;
    private bool _committed = false;
    private bool _disposed = false;

    public FileDataTransaction(ILogger logger, string dataDirectory)
    {
        _logger = logger;
        _dataDirectory = dataDirectory;
        _backupDirectory = Path.Combine(dataDirectory, $"backup_{Guid.NewGuid():N}");

        // 创建备份目录
        if (Directory.Exists(_dataDirectory))
        {
            Directory.CreateDirectory(_backupDirectory);

            // 备份现有文件
            foreach (var file in Directory.GetFiles(_dataDirectory, "*.json"))
            {
                var fileName = Path.GetFileName(file);
                var backupPath = Path.Combine(_backupDirectory, fileName);
                File.Copy(file, backupPath);
            }

            _logger.LogDebug("创建事务备份: {BackupDirectory}", _backupDirectory);
        }
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        if (_committed)
            return;

        try
        {
            _logger.LogDebug("提交事务");

            // 删除备份目录
            if (Directory.Exists(_backupDirectory))
            {
                Directory.Delete(_backupDirectory, true);
            }

            _committed = true;
            await Task.CompletedTask;

            _logger.LogDebug("事务提交成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "提交事务时发生错误");
            throw;
        }
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        if (_committed)
            return;

        try
        {
            _logger.LogDebug("回滚事务");

            // 恢复备份文件
            if (Directory.Exists(_backupDirectory))
            {
                foreach (var backupFile in Directory.GetFiles(_backupDirectory, "*.json"))
                {
                    var fileName = Path.GetFileName(backupFile);
                    var originalPath = Path.Combine(_dataDirectory, fileName);
                    File.Copy(backupFile, originalPath, true);
                }

                Directory.Delete(_backupDirectory, true);
            }

            await Task.CompletedTask;

            _logger.LogDebug("事务回滚成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "回滚事务时发生错误");
            throw;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            if (!_committed)
            {
                // 如果事务未提交，自动回滚
                try
                {
                    RollbackAsync().GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "自动回滚事务时发生错误");
                }
            }

            _disposed = true;
        }
    }
}
