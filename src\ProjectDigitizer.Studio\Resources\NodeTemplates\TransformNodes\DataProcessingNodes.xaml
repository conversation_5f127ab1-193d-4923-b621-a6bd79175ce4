<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 数据处理转换节点模板集合 ==========  -->
    <!--  用于 DataFilter、TagSearch、DataTransform、DataValidation 等数据处理节点  -->

    <!--  通用数据处理节点内容模板  -->
    <DataTemplate x:Key="DataProcessingNodeContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  数据处理图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="Filter" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataFilter}">
                                                <Setter Property="Kind" Value="Filter" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TagSearch}">
                                                <Setter Property="Kind" Value="TagSearch" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataTransform}">
                                                <Setter Property="Kind" Value="SwapHorizontal" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataValidation}">
                                                <Setter Property="Kind" Value="CheckCircle" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataCondition}">
                                                <Setter Property="Kind" Value="CallSplit" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataMerge}">
                                                <Setter Property="Kind" Value="CallMerge" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataSort}">
                                                <Setter Property="Kind" Value="Sort" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataGroup}">
                                                <Setter Property="Kind" Value="Group" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：处理类型信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  处理类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    Background="#2196F3"
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="数据" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataFilter}">
                                                        <Setter Property="Text" Value="过滤" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TagSearch}">
                                                        <Setter Property="Text" Value="搜索" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataTransform}">
                                                        <Setter Property="Text" Value="转换" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataValidation}">
                                                        <Setter Property="Text" Value="验证" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataCondition}">
                                                        <Setter Property="Text" Value="条件" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataMerge}">
                                                        <Setter Property="Text" Value="合并" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataSort}">
                                                        <Setter Property="Text" Value="排序" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataGroup}">
                                                        <Setter Property="Text" Value="分组" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    MaxWidth="100"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.ProcessType, FallbackValue='标准处理'}"
                                    TextTrimming="CharacterEllipsis"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  规则配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="规则配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  预览结果按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="预览结果">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Eye"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Fill="#4CAF50"
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="处理状态"
                                    VerticalAlignment="Center"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#E3F2FD"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  处理规则  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="规则:"
                                VerticalAlignment="Top" />

                            <TextBlock
                                FontFamily="Consolas"
                                FontSize="9"
                                Foreground="#2196F3"
                                Grid.Column="1"
                                MaxHeight="30"
                                TextTrimming="CharacterEllipsis"
                                TextWrapping="Wrap">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="{Binding NodeProperties.FilterExpression, FallbackValue='field > 0'}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.TagSearch}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.SearchPattern, FallbackValue='tag:*'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataTransform}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.TransformExpression, FallbackValue='field1 -> field2'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataValidation}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.ValidationRules, FallbackValue='required, numeric'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataCondition}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.ConditionExpression, FallbackValue='if field > 10'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataSort}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.SortFields, FallbackValue='field1 ASC, field2 DESC'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DataGroup}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.GroupByFields, FallbackValue='field1, field2'}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  目标字段  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="字段:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#2196F3"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White"
                                    Text="{Binding NodeProperties.TargetFields, FallbackValue='全部字段'}" />
                            </Border>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  输入记录数  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="输入" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#2196F3"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.InputRecords, FallbackValue=0}" />
                            </StackPanel>

                            <!--  输出记录数  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="输出" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#2196F3"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.OutputRecords, FallbackValue=0}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  数据过滤节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataFilterNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  标签搜索节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="TagSearchNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据转换节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataTransformNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据验证节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataValidationNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据条件节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataConditionNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据合并节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataMergeNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据排序节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataSortNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  数据分组节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DataGroupNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataProcessingNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
