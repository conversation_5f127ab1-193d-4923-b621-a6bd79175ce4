using System;
using System.Collections.Generic;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 画布性能优化器接口
    /// </summary>
    public interface ICanvasPerformanceOptimizer : IDisposable
    {
        /// <summary>
        /// 当前优化配置
        /// </summary>
        CanvasPerformanceConfig CurrentConfig { get; }

        /// <summary>
        /// 渲染统计信息
        /// </summary>
        CanvasRenderingStats RenderingStats { get; }

        /// <summary>
        /// 建议的优化方案事件
        /// </summary>
        event Action<PerformanceOptimizationSuggestion>? OptimizationSuggested;

        /// <summary>
        /// 渲染性能告警事件
        /// </summary>
        event Action<RenderingPerformanceWarning>? PerformanceWarning;

        /// <summary>
        /// 开始优化
        /// </summary>
        void StartOptimization();

        /// <summary>
        /// 停止优化
        /// </summary>
        void StopOptimization();

        /// <summary>
        /// 更新节点数量信息
        /// </summary>
        /// <param name="totalNodes">总节点数</param>
        /// <param name="visibleNodes">可见节点数</param>
        void UpdateNodeCount(int totalNodes, int visibleNodes);

        /// <summary>
        /// 记录渲染开始
        /// </summary>
        void BeginRender();

        /// <summary>
        /// 记录渲染结束
        /// </summary>
        void EndRender();

        /// <summary>
        /// 记录批次渲染
        /// </summary>
        /// <param name="batchSize">批大小</param>
        void RecordBatchRender(int batchSize);

        /// <summary>
        /// 获取推荐渲染配置
        /// </summary>
        /// <param name="nodeCount">节点数量</param>
        /// <param name="viewportSize">视口大小</param>
        /// <returns>推荐配置</returns>
        CanvasRenderingConfig GetRecommendedRenderingConfig(int nodeCount, ViewportSize viewportSize);

        /// <summary>
        /// 应用性能配置
        /// </summary>
        /// <param name="config">配置</param>
        void ApplyPerformanceConfig(CanvasPerformanceConfig config);

        /// <summary>
        /// 获取当前性能指标
        /// </summary>
        /// <returns>指标</returns>
        CanvasPerformanceMetrics GetCurrentMetrics();

        /// <summary>
        /// 针对大规模节点优化
        /// </summary>
        /// <param name="nodeCount">节点数量</param>
        /// <returns>优化结果</returns>
        LargeScaleOptimizationResult OptimizeForLargeScale(int nodeCount);

        /// <summary>
        /// 设置渲染质量
        /// </summary>
        /// <param name="quality">质量</param>
        void SetRenderingQuality(RenderingQuality quality);

        /// <summary>
        /// 启用/配置批量渲染
        /// </summary>
        /// <param name="enabled">是否启用</param>
        /// <param name="batchSize">批大小</param>
        void SetBatchRendering(bool enabled, int batchSize = 50);

        /// <summary>
        /// 设置帧率上限
        /// </summary>
        /// <param name="maxFPS">最大帧率</param>
        void SetFrameRateLimit(int maxFPS);
    }

    /// <summary>
    /// 画布性能配置
    /// </summary>
    public class CanvasPerformanceConfig
    {
        /// <summary>
        /// 是否启用虚拟化
        /// </summary>
        public bool EnableVirtualization { get; set; } = true;

        /// <summary>
        /// 是否启用批量渲染
        /// </summary>
        public bool EnableBatchRendering { get; set; } = true;

        /// <summary>
        /// 批次大小
        /// </summary>
        public int BatchSize { get; set; } = 50;

        /// <summary>
        /// 最大帧率
        /// </summary>
        public int MaxFrameRate { get; set; } = 60;

        /// <summary>
        /// 渲染质量
        /// </summary>
        public RenderingQuality RenderingQuality { get; set; } = RenderingQuality.High;

        /// <summary>
        /// 是否启用 LOD
        /// </summary>
        public bool EnableLevelOfDetail { get; set; } = true;

        /// <summary>
        /// LOD 距离阈值
        /// </summary>
        public double LODDistanceThreshold { get; set; } = 500.0;

        /// <summary>
        /// 是否启用遮挡剔除
        /// </summary>
        public bool EnableOcclusionCulling { get; set; } = true;

        /// <summary>
        /// 视口边距（用于预加载）
        /// </summary>
        public double ViewportMargin { get; set; } = 100.0;
    }

    /// <summary>
    /// 画布渲染统计
    /// </summary>
    public class CanvasRenderingStats
    {
        public long TotalRenders { get; set; }
        public double AverageRenderTimeMs { get; set; }
        public double MaxRenderTimeMs { get; set; }
        public double CurrentFPS { get; set; }
        public double AverageFPS { get; set; }
        public long BatchRenderCount { get; set; }
        public long SkippedRenderCount { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// 渲染配置建议
    /// </summary>
    public class CanvasRenderingConfig
    {
        public int RecommendedBatchSize { get; set; }
        public RenderingQuality RecommendedQuality { get; set; }
        public int RecommendedMaxFPS { get; set; }
        public bool ShouldEnableVirtualization { get; set; }
        public bool ShouldEnableLOD { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 当前性能指标
    /// </summary>
    public class CanvasPerformanceMetrics
    {
        public int CurrentNodeCount { get; set; }
        public int VisibleNodeCount { get; set; }
        public double RenderingEfficiency { get; set; }
        public double MemoryEfficiency { get; set; }
        public double OverallPerformanceScore { get; set; }
        public IEnumerable<PerformanceBottleneck> Bottlenecks { get; set; } = new List<PerformanceBottleneck>();
    }

    /// <summary>
    /// 大规模优化结果
    /// </summary>
    public class LargeScaleOptimizationResult
    {
        public bool NeedsOptimization { get; set; }
        public IEnumerable<OptimizationStrategy> RecommendedStrategies { get; set; } = new List<OptimizationStrategy>();
        public double ExpectedPerformanceGain { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 优化建议
    /// </summary>
    public class PerformanceOptimizationSuggestion
    {
        public OptimizationType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public double ExpectedImprovement { get; set; }
        public CanvasPerformanceConfig SuggestedConfig { get; set; } = new();
    }

    /// <summary>
    /// 性能告警
    /// </summary>
    public class RenderingPerformanceWarning
    {
        public PerformanceWarningLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public CanvasPerformanceMetrics CurrentMetrics { get; set; } = new();
        public IEnumerable<string> SuggestedSolutions { get; set; } = new List<string>();
    }

    public enum RenderingQuality
    {
        Low,
        Medium,
        High,
        Ultra
    }

    public enum OptimizationType
    {
        EnableVirtualization,
        AdjustBatchSize,
        ReduceRenderingQuality,
        LimitFrameRate,
        EnableLevelOfDetail,
        EnableOcclusionCulling
    }

    public enum PerformanceWarningLevel
    {
        Info,
        Warning,
        Critical
    }

    public class PerformanceBottleneck
    {
        public BottleneckType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public double Impact { get; set; }
        public string SuggestedSolution { get; set; } = string.Empty;
    }

    public enum BottleneckType
    {
        CPU,
        Memory,
        Rendering,
        IO
    }

    public class OptimizationStrategy
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double ExpectedEffect { get; set; }
        public int ImplementationDifficulty { get; set; }
        public Action? ExecuteAction { get; set; }
    }
}
