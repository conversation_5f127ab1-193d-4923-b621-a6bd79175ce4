using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Core.Entities
{
    /// <summary>
    /// 数据来源模式
    /// </summary>
    public enum DataSourceMode
    {
        /// <summary>画布节点输出数据</summary>
        NodeOutput,
        /// <summary>外部导入的数据文件</summary>
        ExternalFile
    }

    /// <summary>
    /// 字段数据类型
    /// </summary>
    public enum FieldDataType
    {
        /// <summary>任意类型</summary>
        Any,
        /// <summary>数值型</summary>
        Number,
        /// <summary>文本型</summary>
        Text,
        /// <summary>布尔型</summary>
        Boolean,
        /// <summary>日期时间型</summary>
        DateTime,
        /// <summary>文件路径</summary>
        File,
        /// <summary>几何数据</summary>
        Geometry,
        /// <summary>数组/列表</summary>
        Array,
        /// <summary>对象</summary>
        Object
    }

    /// <summary>
    /// 字段引用状态
    /// </summary>
    public enum FieldReferenceState
    {
        /// <summary>可用但未使用</summary>
        Available,
        /// <summary>已连接但未引用</summary>
        Connected,
        /// <summary>已引用但未连接</summary>
        Referenced,
        /// <summary>已连接且已引用（激活状态）</summary>
        Active
    }

    /// <summary>
    /// 函数类型
    /// </summary>
    public enum FunctionType
    {
        /// <summary>数学函数</summary>
        Math,
        /// <summary>统计函数</summary>
        Statistical,
        /// <summary>字符串函数</summary>
        String,
        /// <summary>日期时间函数</summary>
        DateTime,
        /// <summary>逻辑函数</summary>
        Logical,
        /// <summary>聚合函数</summary>
        Aggregation,
        /// <summary>自定义函数</summary>
        Custom
    }

    /// <summary>
    /// 字段信息模型
    /// </summary>
    public class FieldInfo : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _displayName = string.Empty;
        private FieldDataType _dataType = FieldDataType.Any;
        private bool _isReferenced = false;
        private bool _isConnected = false;
        private string _sourceType = string.Empty;
        private string _sourceId = string.Empty;
        private string _description = string.Empty;
        private object? _sampleValue;
        private bool _isDragging = false;

        /// <summary>字段名称（用于表达式引用）</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>显示名称（用于UI展示）</summary>
        public string DisplayName
        {
            get => _displayName;
            set => SetProperty(ref _displayName, value);
        }

        /// <summary>字段数据类型</summary>
        public FieldDataType DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>是否被表达式引用</summary>
        public bool IsReferenced
        {
            get => _isReferenced;
            set
            {
                if (SetProperty(ref _isReferenced, value))
                {
                    OnPropertyChanged(nameof(State));
                }
            }
        }

        /// <summary>是否有画布连接</summary>
        public bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (SetProperty(ref _isConnected, value))
                {
                    OnPropertyChanged(nameof(State));
                }
            }
        }

        /// <summary>数据来源类型（"node" 或 "file"）</summary>
        public string SourceType
        {
            get => _sourceType;
            set => SetProperty(ref _sourceType, value);
        }

        /// <summary>数据来源ID</summary>
        public string SourceId
        {
            get => _sourceId;
            set => SetProperty(ref _sourceId, value);
        }

        /// <summary>字段描述</summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>示例值（用于预览）</summary>
        public object? SampleValue
        {
            get => _sampleValue;
            set => SetProperty(ref _sampleValue, value);
        }

        /// <summary>是否正在拖拽</summary>
        public bool IsDragging
        {
            get => _isDragging;
            set => SetProperty(ref _isDragging, value);
        }

        /// <summary>字段引用状态</summary>
        public FieldReferenceState State =>
            IsReferenced && IsConnected ? FieldReferenceState.Active :
            IsReferenced ? FieldReferenceState.Referenced :
            IsConnected ? FieldReferenceState.Connected :
            FieldReferenceState.Available;

        /// <summary>是否有示例值</summary>
        public bool HasSampleValue => SampleValue != null;

        /// <summary>示例值文本</summary>
        public string SampleValueText => SampleValue?.ToString() ?? "";

        /// <summary>子字段列表（用于树形结构）</summary>
        public ObservableCollection<FieldInfo> Children { get; set; } = new();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            // 临时禁用PropertyChanged事件，防止Stack overflow
            System.Diagnostics.Debug.WriteLine($"FieldInfo PropertyChanged事件已禁用: {propertyName}");
            // PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 函数表达式模型
    /// </summary>
    public class FunctionExpression : INotifyPropertyChanged
    {
        private string _id = Guid.NewGuid().ToString();
        private string _name = string.Empty;
        private string _expression = string.Empty;
        private FunctionType _type = FunctionType.Math;
        private List<string> _referencedFields = new();
        private object? _result;
        private bool _isEnabled = true;
        private string _description = string.Empty;
        private ValidationResult _validationResult = ValidationResult.Success();

        /// <summary>表达式唯一标识</summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>表达式名称</summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>表达式内容</summary>
        public string Expression
        {
            get => _expression;
            set => SetProperty(ref _expression, value);
        }

        /// <summary>函数类型</summary>
        public FunctionType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>引用的字段列表</summary>
        public List<string> ReferencedFields
        {
            get => _referencedFields;
            set
            {
                if (SetProperty(ref _referencedFields, value))
                {
                    OnPropertyChanged(nameof(HasReferencedFields));
                }
            }
        }

        /// <summary>执行结果</summary>
        public object? Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        /// <summary>是否启用</summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>表达式描述</summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>验证结果</summary>
        public ValidationResult ValidationResult
        {
            get => _validationResult;
            set => SetProperty(ref _validationResult, value);
        }

        /// <summary>是否有引用字段</summary>
        public bool HasReferencedFields => ReferencedFields.Any();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 函数节点配置模型
    /// </summary>
    public class FunctionNodeConfig : INotifyPropertyChanged
    {
        private DataSourceMode _dataSourceMode = DataSourceMode.NodeOutput;
        private ObservableCollection<FieldInfo> _availableFields = new();
        private ObservableCollection<FieldInfo> _selectedFields = new();
        private ObservableCollection<FunctionExpression> _functions = new();
        private string _externalFilePath = string.Empty;
        private Dictionary<string, object> _globalParameters = new();

        /// <summary>数据来源模式</summary>
        public DataSourceMode DataSourceMode
        {
            get => _dataSourceMode;
            set => SetProperty(ref _dataSourceMode, value);
        }

        /// <summary>可用字段列表</summary>
        public ObservableCollection<FieldInfo> AvailableFields
        {
            get => _availableFields;
            set => SetProperty(ref _availableFields, value);
        }

        /// <summary>已选字段列表</summary>
        public ObservableCollection<FieldInfo> SelectedFields
        {
            get => _selectedFields;
            set => SetProperty(ref _selectedFields, value);
        }

        /// <summary>函数表达式列表</summary>
        public ObservableCollection<FunctionExpression> Functions
        {
            get => _functions;
            set => SetProperty(ref _functions, value);
        }

        /// <summary>可用的源节点集合</summary>
        public ObservableCollection<SourceNodeInfo> AvailableSourceNodes { get; set; } = new();

        /// <summary>函数执行结果集合</summary>
        public ObservableCollection<FunctionResult> FunctionResults { get; set; } = new();

        /// <summary>外部文件路径</summary>
        public string ExternalFilePath
        {
            get => _externalFilePath;
            set => SetProperty(ref _externalFilePath, value);
        }

        /// <summary>全局参数</summary>
        public Dictionary<string, object> GlobalParameters
        {
            get => _globalParameters;
            set => SetProperty(ref _globalParameters, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 字段引用变化事件参数
    /// </summary>
    public class FieldReferenceChangedEventArgs : EventArgs
    {
        public string FieldId { get; }
        public bool IsReferenced { get; }
        public string ExpressionId { get; }

        public FieldReferenceChangedEventArgs(string fieldId, bool isReferenced, string expressionId)
        {
            FieldId = fieldId;
            IsReferenced = isReferenced;
            ExpressionId = expressionId;
        }
    }

    /// <summary>
    /// 函数执行结果事件参数
    /// </summary>
    public class FunctionExecutedEventArgs : EventArgs
    {
        public string FunctionId { get; }
        public object? Result { get; }
        public bool IsSuccess { get; }
        public string? ErrorMessage { get; }

        public FunctionExecutedEventArgs(string functionId, object? result, bool isSuccess, string? errorMessage = null)
        {
            FunctionId = functionId;
            Result = result;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 数据来源变化事件参数
    /// </summary>
    public class DataSourceChangedEventArgs : EventArgs
    {
        public DataSourceMode OldMode { get; }
        public DataSourceMode NewMode { get; }
        public List<FieldInfo> AvailableFields { get; }

        public DataSourceChangedEventArgs(DataSourceMode oldMode, DataSourceMode newMode, List<FieldInfo> availableFields)
        {
            OldMode = oldMode;
            NewMode = newMode;
            AvailableFields = availableFields;
        }
    }
}
