using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Constants
{
    /// <summary>
    /// π常量节点属性面板交互逻辑
    /// </summary>
    public partial class PiConstantPanel : UserControl, INotifyPropertyChanged
    {
        private PiConstantConfig _config = null!;

        /// <summary>当前节点依赖属性</summary>
        public static readonly DependencyProperty CurrentNodeProperty = DependencyProperty.Register(
            nameof(CurrentNode), typeof(ModuleNodeViewModel), typeof(PiConstantPanel),
            new PropertyMetadata(null, OnCurrentNodeChanged));

        /// <summary>当前节点</summary>
        public ModuleNodeViewModel? CurrentNode
        {
            get => (ModuleNodeViewModel?)GetValue(CurrentNodeProperty);
            set => SetValue(CurrentNodeProperty, value);
        }

        /// <summary>画布视图模型依赖属性</summary>
        public static readonly DependencyProperty CanvasViewModelProperty = DependencyProperty.Register(
            nameof(CanvasViewModel), typeof(CanvasViewModel), typeof(PiConstantPanel),
            new PropertyMetadata(null));

        /// <summary>画布视图模型</summary>
        public CanvasViewModel? CanvasViewModel
        {
            get => (CanvasViewModel?)GetValue(CanvasViewModelProperty);
            set => SetValue(CanvasViewModelProperty, value);
        }

        public PiConstantPanel()
        {
            InitializeComponent();
            InitializeConfig();
        }

        /// <summary>
        /// 初始化配置
        /// </summary>
        private void InitializeConfig()
        {
            _config = new PiConstantConfig();
            DataContext = _config;

            // 订阅配置变化事件
            _config.PropertyChanged += OnConfigPropertyChanged;
        }

        /// <summary>
        /// 当前节点变化处理
        /// </summary>
        private static void OnCurrentNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is PiConstantPanel panel)
            {
                panel.OnCurrentNodeChanged();
            }
        }

        /// <summary>
        /// 当前节点变化处理
        /// </summary>
        private void OnCurrentNodeChanged()
        {
            if (CurrentNode != null)
            {
                LoadNodeConfig();
            }
        }

        /// <summary>
        /// 加载节点配置
        /// </summary>
        private void LoadNodeConfig()
        {
            if (CurrentNode?.PropertyValues == null)
                return;

            try
            {
                // 从节点属性中加载精度设置
                var precisionValue = CurrentNode.PropertyValues.GetValue("precision");
                if (precisionValue != null && int.TryParse(precisionValue.ToString(), out var precision))
                {
                    _config.Precision = Math.Max(2, Math.Min(15, precision));
                }
                else
                {
                    _config.Precision = 6; // 默认精度
                }

                // 更新节点输出字段
                UpdateNodeOutputFields();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载π常量节点配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置属性变化处理
        /// </summary>
        private void OnConfigPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PiConstantConfig.Precision))
            {
                // 同步精度设置到节点属性
                OnPropertyValueChanged("precision", _config.Precision);

                // 更新输出字段
                UpdateNodeOutputFields();
            }
        }

        /// <summary>
        /// 更新节点输出字段
        /// </summary>
        private void UpdateNodeOutputFields()
        {
            if (CurrentNode?.Module == null)
                return;

            try
            {
                // 设置输出字段信息
                CurrentNode.Module.Parameters["output_field_name"] = "pi_value";
                CurrentNode.Module.Parameters["output_field_type"] = "Number";
                CurrentNode.Module.Parameters["output_field_description"] = "数学常量π的值";
                CurrentNode.Module.Parameters["pi_precision"] = _config.Precision;
                CurrentNode.Module.Parameters["pi_value"] = _config.PiValue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新π常量节点输出字段失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 属性值变化通知
        /// </summary>
        private void OnPropertyValueChanged(string propertyName, object value)
        {
            if (CurrentNode?.PropertyValues != null)
            {
                CurrentNode.PropertyValues.SetValue(propertyName, value);
            }
        }

        /// <summary>
        /// 设置精度按钮点击处理
        /// </summary>
        private void SetPrecision_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string tagValue)
            {
                if (int.TryParse(tagValue, out var precision))
                {
                    _config.Precision = precision;
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// π常量节点配置模型
    /// </summary>
    public class PiConstantConfig : INotifyPropertyChanged
    {
        private int _precision = 6;
        private const double PI_VALUE = Math.PI;

        /// <summary>精度（小数位数）</summary>
        public int Precision
        {
            get => _precision;
            set
            {
                if (_precision != value && value >= 2 && value <= 15)
                {
                    _precision = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PiValue));
                }
            }
        }

        /// <summary>π值（根据精度格式化）</summary>
        public double PiValue => Math.Round(PI_VALUE, _precision);

        /// <summary>π值字符串（用于显示）</summary>
        public string PiValueString => PiValue.ToString($"F{_precision}");

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
