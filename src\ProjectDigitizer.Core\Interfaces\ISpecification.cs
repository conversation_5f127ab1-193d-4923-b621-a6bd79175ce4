using System.Linq.Expressions;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 规约模式接口，用于封装业务规则和查询条件
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface ISpecification<T>
{
    /// <summary>
    /// 获取规约表达式
    /// </summary>
    Expression<Func<T, bool>> Criteria { get; }

    /// <summary>
    /// 包含的导航属性
    /// </summary>
    List<Expression<Func<T, object>>> Includes { get; }

    /// <summary>
    /// 包含的字符串导航属性
    /// </summary>
    List<string> IncludeStrings { get; }

    /// <summary>
    /// 排序表达式
    /// </summary>
    Expression<Func<T, object>>? OrderBy { get; }

    /// <summary>
    /// 降序排序表达式
    /// </summary>
    Expression<Func<T, object>>? OrderByDescending { get; }

    /// <summary>
    /// 分组表达式
    /// </summary>
    Expression<Func<T, object>>? GroupBy { get; }

    /// <summary>
    /// 是否启用分页
    /// </summary>
    bool IsPagingEnabled { get; }

    /// <summary>
    /// 跳过的记录数
    /// </summary>
    int Take { get; }

    /// <summary>
    /// 获取的记录数
    /// </summary>
    int Skip { get; }

    /// <summary>
    /// 检查实体是否满足规约
    /// </summary>
    /// <param name="entity">要检查的实体</param>
    /// <returns>如果满足规约返回true，否则返回false</returns>
    bool IsSatisfiedBy(T entity);
}

/// <summary>
/// 规约基类，提供规约的基本实现
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public abstract class BaseSpecification<T> : ISpecification<T>
{
    protected BaseSpecification(Expression<Func<T, bool>>? criteria = null)
    {
        Criteria = criteria ?? (x => true);
    }

    public Expression<Func<T, bool>> Criteria { get; }
    public List<Expression<Func<T, object>>> Includes { get; } = new();
    public List<string> IncludeStrings { get; } = new();
    public Expression<Func<T, object>>? OrderBy { get; private set; }
    public Expression<Func<T, object>>? OrderByDescending { get; private set; }
    public Expression<Func<T, object>>? GroupBy { get; private set; }
    public bool IsPagingEnabled { get; private set; }
    public int Take { get; private set; }
    public int Skip { get; private set; }

    /// <summary>
    /// 添加包含的导航属性
    /// </summary>
    /// <param name="includeExpression">包含表达式</param>
    protected virtual void AddInclude(Expression<Func<T, object>> includeExpression)
    {
        Includes.Add(includeExpression);
    }

    /// <summary>
    /// 添加包含的字符串导航属性
    /// </summary>
    /// <param name="includeString">包含字符串</param>
    protected virtual void AddInclude(string includeString)
    {
        IncludeStrings.Add(includeString);
    }

    /// <summary>
    /// 应用排序
    /// </summary>
    /// <param name="orderByExpression">排序表达式</param>
    protected virtual void ApplyOrderBy(Expression<Func<T, object>> orderByExpression)
    {
        OrderBy = orderByExpression;
    }

    /// <summary>
    /// 应用降序排序
    /// </summary>
    /// <param name="orderByDescendingExpression">降序排序表达式</param>
    protected virtual void ApplyOrderByDescending(Expression<Func<T, object>> orderByDescendingExpression)
    {
        OrderByDescending = orderByDescendingExpression;
    }

    /// <summary>
    /// 应用分组
    /// </summary>
    /// <param name="groupByExpression">分组表达式</param>
    protected virtual void ApplyGroupBy(Expression<Func<T, object>> groupByExpression)
    {
        GroupBy = groupByExpression;
    }

    /// <summary>
    /// 应用分页
    /// </summary>
    /// <param name="skip">跳过的记录数</param>
    /// <param name="take">获取的记录数</param>
    protected virtual void ApplyPaging(int skip, int take)
    {
        Skip = skip;
        Take = take;
        IsPagingEnabled = true;
    }

    /// <summary>
    /// 检查实体是否满足规约
    /// </summary>
    /// <param name="entity">要检查的实体</param>
    /// <returns>如果满足规约返回true，否则返回false</returns>
    public virtual bool IsSatisfiedBy(T entity)
    {
        return Criteria.Compile()(entity);
    }
}
