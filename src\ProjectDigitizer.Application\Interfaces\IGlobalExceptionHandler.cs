namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 全局异常处理器接口
/// </summary>
public interface IGlobalExceptionHandler
{
    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文</param>
    void HandleException(Exception exception, string context);

    /// <summary>
    /// 异步处理异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文</param>
    /// <returns>处理任务</returns>
    Task HandleExceptionAsync(Exception exception, string context);

    /// <summary>
    /// 注册异常处理器
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    /// <param name="handler">处理器</param>
    void RegisterExceptionHandler<T>(Func<T, bool> handler) where T : Exception;
}
