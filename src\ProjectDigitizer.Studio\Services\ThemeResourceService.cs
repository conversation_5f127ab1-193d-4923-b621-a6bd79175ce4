using System;
using System.Linq;
// 注意：避免与 ProjectDigitizer.Application 命名空间冲突，使用完全限定名

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 负责在浅色/深色之间切换本地色板资源（Palette.xaml / Palette.Dark.xaml）。
    /// 不影响 WPFUI 的控件主题，仅更新我们自定义的颜色键。
    /// </summary>
    internal static class ThemeResourceService
    {
        private static readonly Uri LightPalette = new("/ProjectDigitizer.Studio;component/Resources/Styles/Theme/Palette.xaml", UriKind.Relative);
        private static readonly Uri DarkPalette = new("/ProjectDigitizer.Studio;component/Resources/Styles/Theme/Palette.Dark.xaml", UriKind.Relative);

        public static void ApplyDark(bool isDark)
        {
            var app = System.Windows.Application.Current;
            if (app == null) return;

            var merged = app.Resources.MergedDictionaries;
            // 查找当前已加载的 Palette 资源
            var current = merged.FirstOrDefault(d => d.Source != null && d.Source.OriginalString.EndsWith("Palette.xaml"));
            if (current != null)
            {
                merged.Remove(current);
            }

            var newDict = new System.Windows.ResourceDictionary
            {
                Source = isDark ? DarkPalette : LightPalette
            };
            // 将 Brushes.xaml 等依赖该键的资源放后面，所以插入到 Palette 原来位置或前部
            merged.Insert(0, newDict);
        }
    }
}
