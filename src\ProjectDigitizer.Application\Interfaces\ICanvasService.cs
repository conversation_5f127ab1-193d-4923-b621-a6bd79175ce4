using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 画布操作服务接口
/// </summary>
public interface ICanvasService : IApplicationService
{
    /// <summary>
    /// 应用自动布局
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <param name="connections">连接列表</param>
    Task ApplyAutoLayoutAsync(IEnumerable<object> nodes, IEnumerable<object> connections);

    /// <summary>
    /// 验证节点连接
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <returns>是否可以连接</returns>
    bool CanConnect(object sourceNode, object targetNode);

    /// <summary>
    /// 优化画布性能
    /// </summary>
    /// <param name="canvasData">画布数据</param>
    void OptimizeCanvas(object canvasData);
}
