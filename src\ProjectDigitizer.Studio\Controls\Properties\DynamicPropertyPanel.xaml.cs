using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;
using ProjectDigitizer.Studio.Adapters;
using ProjectDigitizer.Studio.Controls.Properties.Widgets;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Properties
{
    /// <summary>
    /// 动态属性面板
    /// </summary>
    public partial class DynamicPropertyPanel : UserControl
    {
        private ModuleNodeViewModel? _currentNode;
        private NodePropertySchema? _currentSchema;
        private NodePropertyValues? _currentValues;
        private readonly List<IPropertyWidget> _widgets = new();

        public static readonly DependencyProperty CurrentNodeProperty = DependencyProperty.Register(
            nameof(CurrentNode), typeof(ModuleNodeViewModel), typeof(DynamicPropertyPanel),
            new PropertyMetadata(null, OnCurrentNodeChanged));

        public static readonly DependencyProperty CanvasViewModelProperty = DependencyProperty.Register(
            nameof(CanvasViewModel), typeof(CanvasViewModel), typeof(DynamicPropertyPanel),
            new PropertyMetadata(null));

        public ModuleNodeViewModel? CurrentNode
        {
            get => (ModuleNodeViewModel?)GetValue(CurrentNodeProperty);
            set => SetValue(CurrentNodeProperty, value);
        }

        public CanvasViewModel? CanvasViewModel
        {
            get => (CanvasViewModel?)GetValue(CanvasViewModelProperty);
            set => SetValue(CanvasViewModelProperty, value);
        }

        private static void OnCurrentNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is DynamicPropertyPanel panel)
            {
                panel.SetCurrentNode(e.NewValue as ModuleNodeViewModel);
            }
        }

        public DynamicPropertyPanel()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 设置当前编辑的节点
        /// </summary>
        public void SetCurrentNode(ModuleNodeViewModel? node)
        {
            if (_currentNode == node) return;

            _currentNode = node;

            try
            {
                LoadNodeProperties();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadNodeProperties error: {ex.Message}");
                ShowEmptyState($"加载属性时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载节点属性
        /// </summary>
        private void LoadNodeProperties()
        {
            ClearProperties();

            if (_currentNode?.Module == null)
            {
                ShowEmptyState();
                return;
            }

            // 优先尝试由插件提供的属性面板（Provider 或 约定资源键）
            try
            {
                var pluginPanel = PropertyPanelAdapter.Resolve(_currentNode.Module.Type, _currentNode);
                if (pluginPanel != null)
                {
                    PropertyContainer.Children.Clear();
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                    PropertyContainer.Children.Add(pluginPanel);
                    return;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DynamicPropertyPanel] Plugin panel resolve failed: {ex.Message}");
            }

            // 检查是否有专用的属性面板控件（DataCalculation类型使用Inspector系统）
            if (HasCustomPropertyPanel(_currentNode.Module.Type))
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Creating custom property panel for {_currentNode.Module.Type}");

                    var customPanel = CreateCustomPropertyPanel(_currentNode);
                    if (customPanel != null)
                    {
                        // 清空PropertyContainer并隐藏空状态
                        PropertyContainer.Children.Clear();
                        EmptyStatePanel.Visibility = Visibility.Collapsed;

                        PropertyContainer.Children.Add(customPanel);

                        System.Diagnostics.Debug.WriteLine($"[DEBUG] Custom property panel added successfully");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Error creating custom property panel: {ex.Message}");
                    // 如果专用面板创建失败，继续使用Inspector系统
                }
            }

            // 使用新的Inspector系统（适用于没有专用面板的节点类型）
            try
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Creating Inspector panel for {_currentNode.Module.Type}");

                var inspectorPanel = new ProjectDigitizer.Studio.Controls.Inspector.InspectorPanel();
                inspectorPanel.CurrentNode = _currentNode;

                // 清空PropertyContainer并隐藏空状态
                PropertyContainer.Children.Clear();
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                // 隐藏DynamicPropertyPanel的标题栏，避免与InspectorPanel重复
                TitleBar.Visibility = Visibility.Collapsed;

                PropertyContainer.Children.Add(inspectorPanel);

                System.Diagnostics.Debug.WriteLine($"[DEBUG] Inspector panel added successfully");
                return;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Error creating Inspector panel: {ex.Message}");
                // 如果Inspector创建失败，继续使用通用属性架构系统
            }

            // 获取节点的属性架构
            _currentSchema = GetPropertySchema(_currentNode.Module.Type);
            if (_currentSchema == null)
            {
                ShowEmptyState("该节点类型暂不支持属性配置");
                return;
            }

            // 初始化属性值容器
            _currentValues = new NodePropertyValues();

            // 加载节点已保存的属性值
            LoadSavedPropertyValues();

            // 创建属性编辑器
            CreatePropertyEditors();

            HideEmptyState();
        }

        /// <summary>
        /// 获取属性架构（基于燃气工程业务需求）
        /// </summary>
        private NodePropertySchema? GetPropertySchema(ModuleType moduleType)
        {
            return moduleType switch
            {
                // 常规数据类 - 平面管线
                ModuleType.PipeLine => new NodePropertySchema
                {
                    NodeType = "PipeLine",
                    DisplayName = "平面管线",
                    Description = "燃气平面管线配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "pipelineName", new PropertyDefinition
                            {
                                Name = "pipelineName",
                                Title = "管线名称",
                                Description = "管线的标识名称",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "基本信息",
                                Order = 1
                            }
                        },
                        {
                            "diameter", new PropertyDefinition
                            {
                                Name = "diameter",
                                Title = "管径(mm)",
                                Description = "管道直径，单位毫米",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 20,
                                Maximum = 1000,
                                DefaultValue = 100,
                                DataSource = DataSourceType.CADInfo,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "pressure", new PropertyDefinition
                            {
                                Name = "pressure",
                                Title = "压力等级",
                                Description = "管道压力等级",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "low", Label = "低压(≤0.005MPa)" },
                                    new() { Value = "medium", Label = "中压(0.005-0.4MPa)" },
                                    new() { Value = "high", Label = "高压(0.4-1.6MPa)" }
                                },
                                DefaultValue = "medium",
                                DataSource = DataSourceType.Manual,
                                Group = "技术参数",
                                Order = 3
                            }
                        },
                        {
                            "material", new PropertyDefinition
                            {
                                Name = "material",
                                Title = "管道材质",
                                Description = "管道材质类型",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "pe", Label = "PE管" },
                                    new() { Value = "steel", Label = "钢管" },
                                    new() { Value = "cast_iron", Label = "铸铁管" }
                                },
                                DefaultValue = "pe",
                                DataSource = DataSourceType.MaterialLibrary,
                                Group = "材料信息",
                                Order = 4
                            }
                        },
                        {
                            "length", new PropertyDefinition
                            {
                                Name = "length",
                                Title = "管线长度(m)",
                                Description = "管线总长度，单位米",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = false,
                                Minimum = 0,
                                DataSource = DataSourceType.CADInfo,
                                Group = "计算参数",
                                Order = 5
                            }
                        }
                    }
                },

                // 常规数据类 - 立管
                ModuleType.RiserPipe => new NodePropertySchema
                {
                    NodeType = "RiserPipe",
                    DisplayName = "立管",
                    Description = "燃气立管配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "riserNumber", new PropertyDefinition
                            {
                                Name = "riserNumber",
                                Title = "立管编号",
                                Description = "立管的唯一编号",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "基本信息",
                                Order = 1
                            }
                        },
                        {
                            "floors", new PropertyDefinition
                            {
                                Name = "floors",
                                Title = "楼层数",
                                Description = "立管服务的楼层数量",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 1,
                                Maximum = 50,
                                DefaultValue = 6,
                                DataSource = DataSourceType.ProjectInfo,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "userCount", new PropertyDefinition
                            {
                                Name = "userCount",
                                Title = "用户数量",
                                Description = "立管服务的用户数量",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 1,
                                DataSource = DataSourceType.CADInfo,
                                Group = "技术参数",
                                Order = 3
                            }
                        }
                    }
                },

                // 常规数据类 - 调压箱调压柜
                ModuleType.PressureBox => new NodePropertySchema
                {
                    NodeType = "PressureBox",
                    DisplayName = "调压箱调压柜",
                    Description = "燃气调压设备配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "equipmentType", new PropertyDefinition
                            {
                                Name = "equipmentType",
                                Title = "设备类型",
                                Description = "调压设备的类型",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "box", Label = "调压箱" },
                                    new() { Value = "cabinet", Label = "调压柜" },
                                    new() { Value = "station", Label = "调压站" }
                                },
                                DefaultValue = "box",
                                DataSource = DataSourceType.Manual,
                                Group = "基本信息",
                                Order = 1
                            }
                        },
                        {
                            "inletPressure", new PropertyDefinition
                            {
                                Name = "inletPressure",
                                Title = "进口压力(MPa)",
                                Description = "设备进口压力",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0.005,
                                Maximum = 1.6,
                                DefaultValue = 0.4,
                                DataSource = DataSourceType.Manual,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "outletPressure", new PropertyDefinition
                            {
                                Name = "outletPressure",
                                Title = "出口压力(MPa)",
                                Description = "设备出口压力",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0.002,
                                Maximum = 0.4,
                                DefaultValue = 0.005,
                                DataSource = DataSourceType.Manual,
                                Group = "技术参数",
                                Order = 3
                            }
                        },
                        {
                            "capacity", new PropertyDefinition
                            {
                                Name = "capacity",
                                Title = "调压能力(m³/h)",
                                Description = "设备的调压能力",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 10,
                                DataSource = DataSourceType.MaterialLibrary,
                                Group = "技术参数",
                                Order = 4
                            }
                        }
                    }
                },

                // 数据衍生关联类 - 焊口探伤
                ModuleType.WeldInspection => new NodePropertySchema
                {
                    NodeType = "WeldInspection",
                    DisplayName = "焊口探伤",
                    Description = "焊口探伤检测配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "inspectionMethod", new PropertyDefinition
                            {
                                Name = "inspectionMethod",
                                Title = "探伤方法",
                                Description = "焊口探伤的检测方法",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "rt", Label = "射线探伤(RT)" },
                                    new() { Value = "ut", Label = "超声波探伤(UT)" },
                                    new() { Value = "mt", Label = "磁粉探伤(MT)" },
                                    new() { Value = "pt", Label = "渗透探伤(PT)" }
                                },
                                DefaultValue = "rt",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "检测参数",
                                Order = 1
                            }
                        },
                        {
                            "inspectionRatio", new PropertyDefinition
                            {
                                Name = "inspectionRatio",
                                Title = "探伤比例(%)",
                                Description = "需要进行探伤检测的焊口比例",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0,
                                Maximum = 100,
                                DefaultValue = 100,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "检测参数",
                                Order = 2
                            }
                        }
                    }
                },

                // 常规数据类 - 开挖回填
                ModuleType.Excavation => new NodePropertySchema
                {
                    NodeType = "Excavation",
                    DisplayName = "开挖回填",
                    Description = "管道开挖回填工程配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "excavationMethod", new PropertyDefinition
                            {
                                Name = "excavationMethod",
                                Title = "开挖方式",
                                Description = "管道开挖的施工方式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "manual", Label = "人工开挖" },
                                    new() { Value = "mechanical", Label = "机械开挖" },
                                    new() { Value = "trenchless", Label = "非开挖" }
                                },
                                DefaultValue = "mechanical",
                                DataSource = DataSourceType.Manual,
                                Group = "施工参数",
                                Order = 1
                            }
                        },
                        {
                            "excavationDepth", new PropertyDefinition
                            {
                                Name = "excavationDepth",
                                Title = "开挖深度(m)",
                                Description = "管道埋设深度",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0.5,
                                Maximum = 10,
                                DefaultValue = 1.2,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "trenchWidth", new PropertyDefinition
                            {
                                Name = "trenchWidth",
                                Title = "沟槽宽度(m)",
                                Description = "开挖沟槽的宽度",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0.3,
                                Maximum = 5,
                                DefaultValue = 0.8,
                                DataSource = DataSourceType.Formula,
                                Group = "技术参数",
                                Order = 3
                            }
                        },
                        {
                            "backfillMaterial", new PropertyDefinition
                            {
                                Name = "backfillMaterial",
                                Title = "回填材料",
                                Description = "管道回填使用的材料",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "sand", Label = "中粗砂" },
                                    new() { Value = "soil", Label = "原土" },
                                    new() { Value = "gravel", Label = "砂石" }
                                },
                                DefaultValue = "sand",
                                DataSource = DataSourceType.MaterialLibrary,
                                Group = "材料信息",
                                Order = 4
                            }
                        }
                    }
                },

                // 常规数据类 - 破除恢复
                ModuleType.Demolition => new NodePropertySchema
                {
                    NodeType = "Demolition",
                    DisplayName = "破除恢复",
                    Description = "路面破除与恢复工程配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "pavementType", new PropertyDefinition
                            {
                                Name = "pavementType",
                                Title = "路面类型",
                                Description = "需要破除的路面类型",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "asphalt", Label = "沥青路面" },
                                    new() { Value = "concrete", Label = "混凝土路面" },
                                    new() { Value = "brick", Label = "砖石路面" },
                                    new() { Value = "soil", Label = "土路面" }
                                },
                                DefaultValue = "asphalt",
                                DataSource = DataSourceType.CADInfo,
                                Group = "基本信息",
                                Order = 1
                            }
                        },
                        {
                            "pavementThickness", new PropertyDefinition
                            {
                                Name = "pavementThickness",
                                Title = "路面厚度(cm)",
                                Description = "路面结构层厚度",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 5,
                                Maximum = 50,
                                DefaultValue = 20,
                                DataSource = DataSourceType.CADInfo,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "restorationStandard", new PropertyDefinition
                            {
                                Name = "restorationStandard",
                                Title = "恢复标准",
                                Description = "路面恢复的质量标准",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "original", Label = "原状恢复" },
                                    new() { Value = "improved", Label = "提升恢复" },
                                    new() { Value = "temporary", Label = "临时恢复" }
                                },
                                DefaultValue = "original",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "质量要求",
                                Order = 3
                            }
                        }
                    }
                },

                // 常规数据类 - 防腐
                ModuleType.AntiCorrosion => new NodePropertySchema
                {
                    NodeType = "AntiCorrosion",
                    DisplayName = "防腐",
                    Description = "管道防腐工程配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "coatingType", new PropertyDefinition
                            {
                                Name = "coatingType",
                                Title = "防腐层类型",
                                Description = "管道防腐涂层的类型",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "3pe", Label = "三层PE防腐" },
                                    new() { Value = "2pe", Label = "二层PE防腐" },
                                    new() { Value = "epoxy", Label = "环氧煤沥青" },
                                    new() { Value = "polyethylene", Label = "聚乙烯胶带" }
                                },
                                DefaultValue = "3pe",
                                DataSource = DataSourceType.MaterialLibrary,
                                Group = "防腐材料",
                                Order = 1
                            }
                        },
                        {
                            "coatingThickness", new PropertyDefinition
                            {
                                Name = "coatingThickness",
                                Title = "防腐层厚度(mm)",
                                Description = "防腐涂层的厚度",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 1,
                                Maximum = 10,
                                DefaultValue = 3,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "cathodicProtection", new PropertyDefinition
                            {
                                Name = "cathodicProtection",
                                Title = "阴极保护",
                                Description = "是否采用阴极保护",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                Required = false,
                                DefaultValue = true,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "保护措施",
                                Order = 3
                            }
                        }
                    }
                },

                // 常规数据类 - 防雷防静电
                ModuleType.LightningProtection => new NodePropertySchema
                {
                    NodeType = "LightningProtection",
                    DisplayName = "防雷防静电",
                    Description = "防雷防静电系统配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "groundingType", new PropertyDefinition
                            {
                                Name = "groundingType",
                                Title = "接地方式",
                                Description = "防雷接地的方式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "rod", Label = "接地棒" },
                                    new() { Value = "grid", Label = "接地网" },
                                    new() { Value = "ring", Label = "环形接地" }
                                },
                                DefaultValue = "rod",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "接地系统",
                                Order = 1
                            }
                        },
                        {
                            "groundingResistance", new PropertyDefinition
                            {
                                Name = "groundingResistance",
                                Title = "接地电阻(Ω)",
                                Description = "接地电阻值要求",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 1,
                                Maximum = 30,
                                DefaultValue = 10,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "staticElectricityProtection", new PropertyDefinition
                            {
                                Name = "staticElectricityProtection",
                                Title = "静电保护",
                                Description = "是否设置静电保护措施",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                Required = false,
                                DefaultValue = true,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "保护措施",
                                Order = 3
                            }
                        }
                    }
                },

                // 数据衍生关联类 - 警示带示踪线
                ModuleType.WarningBand => new NodePropertySchema
                {
                    NodeType = "WarningBand",
                    DisplayName = "警示带示踪线",
                    Description = "管道警示带和示踪线配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "warningBandType", new PropertyDefinition
                            {
                                Name = "warningBandType",
                                Title = "警示带类型",
                                Description = "警示带的材质和规格",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "plastic", Label = "塑料警示带" },
                                    new() { Value = "metal", Label = "金属警示带" },
                                    new() { Value = "composite", Label = "复合警示带" }
                                },
                                DefaultValue = "plastic",
                                DataSource = DataSourceType.MaterialLibrary,
                                Group = "警示材料",
                                Order = 1
                            }
                        },
                        {
                            "installationDepth", new PropertyDefinition
                            {
                                Name = "installationDepth",
                                Title = "敷设深度(m)",
                                Description = "警示带距离地面的深度",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0.3,
                                Maximum = 2,
                                DefaultValue = 0.5,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "技术参数",
                                Order = 2
                            }
                        },
                        {
                            "tracerWire", new PropertyDefinition
                            {
                                Name = "tracerWire",
                                Title = "示踪线",
                                Description = "是否敷设示踪线",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                Required = false,
                                DefaultValue = true,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "示踪系统",
                                Order = 3
                            }
                        }
                    }
                },

                _ => GetExtendedPropertySchema(moduleType)
            };
        }

        /// <summary>
        /// 获取扩展的属性架构（剩余节点类型）
        /// </summary>
        private NodePropertySchema? GetExtendedPropertySchema(ModuleType moduleType)
        {
            return moduleType switch
            {
                // 数据衍生关联类 - 安装台班
                ModuleType.InstallationTeam => new NodePropertySchema
                {
                    NodeType = "InstallationTeam",
                    DisplayName = "安装台班",
                    Description = "安装施工台班配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "teamSize", new PropertyDefinition
                            {
                                Name = "teamSize",
                                Title = "台班人数",
                                Description = "施工台班的人员数量",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 2,
                                Maximum = 20,
                                DefaultValue = 6,
                                DataSource = DataSourceType.Manual,
                                Group = "人员配置",
                                Order = 1
                            }
                        },
                        {
                            "workingHours", new PropertyDefinition
                            {
                                Name = "workingHours",
                                Title = "工作时长(小时)",
                                Description = "每日工作时长",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 4,
                                Maximum = 12,
                                DefaultValue = 8,
                                DataSource = DataSourceType.Manual,
                                Group = "工作安排",
                                Order = 2
                            }
                        },
                        {
                            "skillLevel", new PropertyDefinition
                            {
                                Name = "skillLevel",
                                Title = "技能等级",
                                Description = "施工人员的技能等级",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "junior", Label = "初级工" },
                                    new() { Value = "intermediate", Label = "中级工" },
                                    new() { Value = "senior", Label = "高级工" },
                                    new() { Value = "technician", Label = "技师" }
                                },
                                DefaultValue = "intermediate",
                                DataSource = DataSourceType.Manual,
                                Group = "人员配置",
                                Order = 3
                            }
                        }
                    }
                },

                // 数据衍生关联类 - 措施
                ModuleType.Measures => new NodePropertySchema
                {
                    NodeType = "Measures",
                    DisplayName = "措施",
                    Description = "施工安全和技术措施配置",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "safetyMeasures", new PropertyDefinition
                            {
                                Name = "safetyMeasures",
                                Title = "安全措施",
                                Description = "施工安全防护措施",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "standard", Label = "标准安全措施" },
                                    new() { Value = "enhanced", Label = "加强安全措施" },
                                    new() { Value = "special", Label = "特殊安全措施" }
                                },
                                DefaultValue = "standard",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "安全管理",
                                Order = 1
                            }
                        },
                        {
                            "environmentalProtection", new PropertyDefinition
                            {
                                Name = "environmentalProtection",
                                Title = "环境保护",
                                Description = "是否采取环境保护措施",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                Required = false,
                                DefaultValue = true,
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "环境管理",
                                Order = 2
                            }
                        },
                        {
                            "qualityControl", new PropertyDefinition
                            {
                                Name = "qualityControl",
                                Title = "质量控制等级",
                                Description = "施工质量控制等级",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "basic", Label = "基本质控" },
                                    new() { Value = "standard", Label = "标准质控" },
                                    new() { Value = "strict", Label = "严格质控" }
                                },
                                DefaultValue = "standard",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "质量管理",
                                Order = 3
                            }
                        }
                    }
                },

                _ => PropertySchemaExtensions.GetProcessingAndOutputSchema(moduleType)
            };
        }

        /// <summary>
        /// 创建属性编辑器（支持分组和数据来源标识）
        /// </summary>
        private void CreatePropertyEditors()
        {
            if (_currentSchema == null) return;

            // 按分组和排序权重组织属性
            var groupedProperties = _currentSchema.Properties.Values
                .OrderBy(p => p.Order)
                .GroupBy(p => p.Group ?? "其他")
                .OrderBy(g => g.Key);

            foreach (var group in groupedProperties)
            {
                // 添加分组标题（如果有分组）
                if (!string.IsNullOrEmpty(group.Key) && group.Key != "其他")
                {
                    var groupHeader = new TextBlock
                    {
                        Text = group.Key,
                        FontWeight = FontWeights.Bold,
                        FontSize = 16,
                        Margin = new Thickness(0, 20, 0, 8),
                        Foreground = System.Windows.Media.Brushes.DarkSlateGray
                    };
                    PropertyContainer.Children.Add(groupHeader);

                    // 添加分组分隔线
                    var separator = new System.Windows.Shapes.Rectangle
                    {
                        Height = 1,
                        Fill = System.Windows.Media.Brushes.LightGray,
                        Margin = new Thickness(0, 0, 0, 12)
                    };
                    PropertyContainer.Children.Add(separator);
                }

                foreach (var property in group)
                {
                    // 创建属性容器（包含数据来源图标）
                    var propertyPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(0, 8, 0, 4)
                    };

                    // 添加数据来源图标
                    var sourceIcon = CreateDataSourceIcon(property.DataSource);
                    propertyPanel.Children.Add(sourceIcon);

                    // 创建属性标签
                    var label = new TextBlock
                    {
                        Text = property.Title + (property.Required ? " *" : ""),
                        FontWeight = FontWeights.SemiBold,
                        FontSize = 14,
                        Margin = new Thickness(8, 0, 0, 0),
                        VerticalAlignment = VerticalAlignment.Center,
                        Foreground = property.Required ? System.Windows.Media.Brushes.DarkBlue : System.Windows.Media.Brushes.Black
                    };
                    propertyPanel.Children.Add(label);

                    PropertyContainer.Children.Add(propertyPanel);

                    // 创建属性编辑器
                    var widget = PropertyWidgetFactory.CreateWidget(property);
                    widget.ValueChanged += OnPropertyValueChanged;

                    // 设置初始值：优先使用已保存的值，否则使用默认值
                    var savedValue = _currentValues?.GetValue(property.Name);
                    if (savedValue != null)
                    {
                        widget.Value = savedValue;
                    }
                    else if (property.DefaultValue != null)
                    {
                        widget.Value = property.DefaultValue;
                        _currentValues?.SetValue(property.Name, property.DefaultValue);
                    }

                    _widgets.Add(widget);
                    PropertyContainer.Children.Add(widget.GetElement());

                    // 添加描述文本（如果有）
                    if (!string.IsNullOrEmpty(property.Description))
                    {
                        var description = new TextBlock
                        {
                            Text = property.Description,
                            FontSize = 12,
                            Foreground = System.Windows.Media.Brushes.Gray,
                            Margin = new Thickness(24, 0, 0, 12), // 左边距对齐图标
                            TextWrapping = TextWrapping.Wrap
                        };

                        PropertyContainer.Children.Add(description);
                    }
                }
            }
        }

        /// <summary>
        /// 创建数据来源图标
        /// </summary>
        private FrameworkElement CreateDataSourceIcon(DataSourceType dataSource)
        {
            var icon = new System.Windows.Shapes.Ellipse
            {
                Width = 16,
                Height = 16,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 根据数据来源类型设置颜色和工具提示
            var (color, tooltip) = dataSource switch
            {
                DataSourceType.ProjectInfo => (System.Windows.Media.Brushes.Purple, "项目信息关联"),
                DataSourceType.CADInfo => (System.Windows.Media.Brushes.Orange, "CAD信息关联"),
                DataSourceType.Manual => (System.Windows.Media.Brushes.Green, "手动输入参数"),
                DataSourceType.Fixed => (System.Windows.Media.Brushes.Gray, "固定默认值"),
                DataSourceType.Formula => (System.Windows.Media.Brushes.Orange, "公式关联结果"),
                DataSourceType.MaterialLibrary => (System.Windows.Media.Brushes.Blue, "材料库（字典）"),
                DataSourceType.StandardLibrary => (System.Windows.Media.Brushes.Cyan, "规范与图库"),
                DataSourceType.Special => (System.Windows.Media.Brushes.Purple, "特殊关联"),
                DataSourceType.Error => (System.Windows.Media.Brushes.Red, "错误信息"),
                _ => (System.Windows.Media.Brushes.Gray, "未知来源")
            };

            icon.Fill = color;
            icon.ToolTip = tooltip;

            return icon;
        }

        /// <summary>
        /// 加载节点已保存的属性值
        /// </summary>
        private void LoadSavedPropertyValues()
        {
            if (_currentNode == null || _currentValues == null) return;

            try
            {
                // 从节点的PropertyValues中加载已保存的值
                var savedValues = _currentNode.PropertyValues.GetAllValues();
                foreach (var kvp in savedValues)
                {
                    _currentValues.SetValue(kvp.Key, kvp.Value);
                }

                System.Diagnostics.Debug.WriteLine($"已加载节点 '{_currentNode.Title}' 的 {savedValues.Count} 个属性值");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载属性值失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 属性值变化处理
        /// </summary>
        private void OnPropertyValueChanged(object? sender, PropertyValueChangedEventArgs e)
        {
            _currentValues?.SetValue(e.PropertyName, e.NewValue);
        }

        /// <summary>
        /// 清空属性
        /// </summary>
        private void ClearProperties()
        {
            PropertyContainer.Children.Clear();
            _widgets.Clear();
            _currentSchema = null;
            _currentValues = null;

            // 重置标题栏可见性
            TitleBar.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 显示空状态
        /// </summary>
        private void ShowEmptyState(string? message = null)
        {
            EmptyStateText.Text = message ?? "请选择一个节点以查看其属性";
            EmptyStatePanel.Visibility = Visibility.Visible;
            PropertyContentPresenter.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 隐藏空状态
        /// </summary>
        private void HideEmptyState()
        {
            EmptyStatePanel.Visibility = Visibility.Collapsed;
            PropertyContentPresenter.Visibility = Visibility.Visible;
            PropertyContentPresenter.Content = _currentNode;
        }

        /// <summary>
        /// 重置按钮点击
        /// </summary>
        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentSchema == null) return;

            // 重置所有属性为默认值
            foreach (var widget in _widgets)
            {
                var property = _currentSchema.Properties.Values
                    .FirstOrDefault(p => p.Name == widget.PropertyDefinition.Name);

                if (property != null)
                {
                    widget.Value = property.DefaultValue;
                }
            }
        }

        /// <summary>
        /// 应用按钮点击
        /// </summary>
        private void ApplyButton_Click(object sender, RoutedEventArgs e)
        {
            // 验证所有属性
            var errors = new List<string>();

            foreach (var widget in _widgets)
            {
                var result = widget.Validate();
                if (!result.IsValid)
                {
                    errors.AddRange(result.Errors);
                }
            }

            if (errors.Any())
            {
                MessageBox.Show($"属性验证失败：\n{string.Join("\n", errors)}",
                    "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 应用属性值到节点
            ApplyPropertiesToNode();
        }

        /// <summary>
        /// 应用属性到节点
        /// </summary>
        private void ApplyPropertiesToNode()
        {
            if (_currentNode == null || _currentValues == null) return;

            try
            {
                // 将属性值保存到节点的PropertyValues中
                var allValues = _currentValues.GetAllValues();
                foreach (var kvp in allValues)
                {
                    _currentNode.PropertyValues.SetValue(kvp.Key, kvp.Value);
                }

                // 更新节点的模块参数（如果需要）
                if (_currentNode.Module != null)
                {
                    // 将属性值同步到模块的Parameters字典
                    foreach (var kvp in allValues)
                    {
                        _currentNode.Module.Parameters[kvp.Key] = kvp.Value ?? "";
                    }
                }

                System.Diagnostics.Debug.WriteLine($"属性已应用到节点: {_currentNode.Title}, 共 {allValues.Count} 个属性");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用属性失败: {ex.Message}");
                MessageBox.Show($"应用属性失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 检查是否有专用的属性面板控件
        /// </summary>
        private bool HasCustomPropertyPanel(ModuleType moduleType)
        {
            return moduleType switch
            {
                // ModuleType.DataCalculation => true,  // 暂时禁用，使用新的Inspector系统
                ModuleType.PiConstant => true,
                ModuleType.EConstant => true,
                ModuleType.GoldenRatioConstant => true,
                // 可以在这里添加其他需要专用面板的模块类型
                _ => false
            };
        }

        /// <summary>
        /// 创建专用的属性面板控件
        /// </summary>
        private UIElement? CreateCustomPropertyPanel(ModuleNodeViewModel node)
        {
            System.Diagnostics.Debug.WriteLine($"[DEBUG] CreateCustomPropertyPanel - CanvasViewModel: {this.CanvasViewModel != null}");
            System.Diagnostics.Debug.WriteLine($"[DEBUG] CreateCustomPropertyPanel - CanvasViewModel.Nodes count: {this.CanvasViewModel?.Nodes?.Count ?? 0}");

            return node.Module.Type switch
            {
                // ModuleType.DataCalculation => new ProjectDigitizer.Studio.Controls.Functions.FunctionPropertyPanel
                // {
                //     CurrentNode = node,
                //     CanvasViewModel = this.CanvasViewModel,
                //     Margin = new Thickness(0)
                // },
                ModuleType.PiConstant or ModuleType.EConstant or ModuleType.GoldenRatioConstant =>
                    new ProjectDigitizer.Studio.Controls.Constants.PiConstantPanel
                    {
                        CurrentNode = node,
                        CanvasViewModel = this.CanvasViewModel,
                        Margin = new Thickness(0)
                    },
                // 可以在这里添加其他专用面板的创建逻辑
                _ => null
            };
        }
    }
}
