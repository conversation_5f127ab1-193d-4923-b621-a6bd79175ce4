using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 上游节点结果提供接口（可运行一次或返回缓存结果）。
    /// DataCalculation 节点在预览/执行前可通过它获取更真实的上游变量值。
    /// </summary>
    public interface IUpstreamResultProvider
    {
        /// <summary>
        /// 尝试获取上游节点的某个属性结果。
        /// 如无可用值，可内部触发一次轻量运行或返回缓存。
        /// </summary>
        /// <param name="node">上游节点</param>
        /// <param name="preferredProperty">首选属性名（如 Result/Value）</param>
        /// <returns>(isAvailable, value)</returns>
        (bool isAvailable, object? value) TryGetResult(ModuleNodeViewModel node, string? preferredProperty);
    }
}

