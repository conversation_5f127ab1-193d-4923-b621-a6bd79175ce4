<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 数组展开节点模板 ==========  -->
    <!--  专用于 ModuleType.ArrayExpansion 的节点模板  -->

    <!--  数组展开节点专用样式  -->
    <Style
        BasedOn="{StaticResource BaseNodeStyle}"
        TargetType="nodify:Node"
        x:Key="ArrayExpansionNodeStyle">
        <Setter Property="Width" Value="280" />
        <Setter Property="Height" Value="130" />
    </Style>

    <!--  数组展开节点内容模板  -->
    <DataTemplate x:Key="ArrayExpansionContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏 - 数组展开专用  -->
                <Border
                    Background="{StaticResource ModernHeaderGradientIndigo}"
                    CornerRadius="12,12,0,0"
                    Grid.Row="0"
                    Height="56"
                    Padding="8,4">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  数组展开图标  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Kind="UnfoldMoreHorizontal"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18" />

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：展开模式信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  展开模式信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="12"
                                    Kind="VectorArrangeBelow"
                                    Margin="0,0,4,0"
                                    Opacity="0.8"
                                    Width="12" />
                                <TextBlock
                                    FontSize="10"
                                    Foreground="White"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.ExpansionMode, FallbackValue='Flatten'}"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="展开配置" />

                                <!--  预览按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="预览结果">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Eye"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="展开状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Running">
                                                    <Setter Property="Fill" Value="#2196F3" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Processing">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域 - 数组展开专用  -->
                <Border
                    Background="#E8EAF6"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  数组路径  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="路径:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontFamily="Consolas"
                                FontSize="9"
                                Foreground="#3F51B5"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.ArrayPath, FallbackValue='未设置'}"
                                TextTrimming="CharacterEllipsis"
                                ToolTip="{Binding NodeProperties.ArrayPath}"
                                VerticalAlignment="Center" />
                        </Grid>

                        <!--  输出格式  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="格式:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#3F51B5"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White"
                                    Text="{Binding NodeProperties.OutputFormat, FallbackValue='Individual'}" />
                            </Border>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  最大深度  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="深度" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#3F51B5"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.MaxDepth, FallbackValue=10}" />
                            </StackPanel>

                            <!--  保持结构  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="结构" />
                                <ipack:PackIconMaterial
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Width="12">
                                    <ipack:PackIconMaterial.Style>
                                        <Style TargetType="ipack:PackIconMaterial">
                                            <Setter Property="Kind" Value="CheckboxMarkedOutline" />
                                            <Setter Property="Foreground" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding NodeProperties.PreserveStructure}" Value="False">
                                                    <Setter Property="Kind" Value="CheckboxBlankOutline" />
                                                    <Setter Property="Foreground" Value="#999" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ipack:PackIconMaterial.Style>
                                </ipack:PackIconMaterial>
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  数组展开节点主模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ArrayExpansionNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource ArrayExpansionNodeStyle}">

            <!--  使用高级连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  使用专用内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ArrayExpansionContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
