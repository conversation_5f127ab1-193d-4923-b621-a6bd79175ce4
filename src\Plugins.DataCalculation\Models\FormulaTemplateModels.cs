using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Plugins.DataCalculation.Models
{
    public class FormulaTemplate : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private string _expression = string.Empty;
        private string _category = string.Empty;
        private List<FormulaParameter> _parameters = new();
        private List<string> _examples = new();
        private string _unit = string.Empty;
        private FormulaComplexity _complexity = FormulaComplexity.Simple;
        private bool _isBuiltIn = true;

        public string Id { get => _id; set => SetProperty(ref _id, value); }
        public string Name { get => _name; set => SetProperty(ref _name, value); }
        public string Description { get => _description; set => SetProperty(ref _description, value); }
        public string Expression { get => _expression; set => SetProperty(ref _expression, value); }
        public string Category { get => _category; set => SetProperty(ref _category, value); }
        public List<FormulaParameter> Parameters { get => _parameters; set => SetProperty(ref _parameters, value); }
        public List<string> Examples { get => _examples; set => SetProperty(ref _examples, value); }
        public string Unit { get => _unit; set => SetProperty(ref _unit, value); }
        public FormulaComplexity Complexity { get => _complexity; set => SetProperty(ref _complexity, value); }
        public bool IsBuiltIn { get => _isBuiltIn; set => SetProperty(ref _isBuiltIn, value); }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        public List<string> Tags { get; set; } = new();

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false; field = value; OnPropertyChanged(propertyName); return true;
        }
    }

    public class FormulaParameter : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _displayName = string.Empty;
        private string _description = string.Empty;
        private FieldDataType _dataType = FieldDataType.Number;
        private bool _isRequired = true;
        private object? _defaultValue;
        private double? _minValue;
        private double? _maxValue;
        private string _unit = string.Empty;

        public string Name { get => _name; set => SetProperty(ref _name, value); }
        public string DisplayName { get => _displayName; set => SetProperty(ref _displayName, value); }
        public string Description { get => _description; set => SetProperty(ref _description, value); }
        public FieldDataType DataType { get => _dataType; set => SetProperty(ref _dataType, value); }
        public bool IsRequired { get => _isRequired; set => SetProperty(ref _isRequired, value); }
        public object? DefaultValue { get => _defaultValue; set => SetProperty(ref _defaultValue, value); }
        public double? MinValue { get => _minValue; set => SetProperty(ref _minValue, value); }
        public double? MaxValue { get => _maxValue; set => SetProperty(ref _maxValue, value); }
        public string Unit { get => _unit; set => SetProperty(ref _unit, value); }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false; field = value; OnPropertyChanged(propertyName); return true;
        }
    }

    public enum FieldDataType
    {
        Number,
        Text,
        Boolean,
        Object
    }

    public enum FormulaComplexity
    {
        Simple,
        Medium,
        Complex,
        Advanced
    }

    public class FormulaCategory : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private string _iconGlyph = string.Empty;
        private string _color = string.Empty;
        private int _order = 0;

        public string Id { get => _id; set => SetProperty(ref _id, value); }
        public string Name { get => _name; set => SetProperty(ref _name, value); }
        public string Description { get => _description; set => SetProperty(ref _description, value); }
        public string IconGlyph { get => _iconGlyph; set => SetProperty(ref _iconGlyph, value); }
        public string Color { get => _color; set => SetProperty(ref _color, value); }
        public int Order { get => _order; set => SetProperty(ref _order, value); }
        public List<FormulaTemplate> Templates { get; set; } = new();

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false; field = value; OnPropertyChanged(propertyName); return true;
        }
    }
}


