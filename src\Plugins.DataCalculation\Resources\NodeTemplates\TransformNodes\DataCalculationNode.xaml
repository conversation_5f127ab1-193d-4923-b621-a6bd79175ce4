<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:entities="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 数据计算节点模板 ==========  -->
    <!--  专用于 ModuleType.DataCalculation 的 GroupingNode 模板  -->

    <!--  数据计算节点专用样式  -->
    <Style TargetType="nodify:GroupingNode" x:Key="DataCalculationGroupingNodeStyle">
        <Setter Property="Width" Value="320" />
        <Setter Property="MinHeight" Value="180" />
        <Setter Property="MovementMode" Value="Self" />
        <Setter Property="CanResize" Value="False" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="nodify:GroupingNode">
                    <Border
                        Background="White"
                        BorderBrush="#7B1FA2"
                        BorderThickness="2"
                        CornerRadius="12"
                        Effect="{StaticResource StandardNodeShadow}"
                        x:Name="MainBorder">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <!--  Header区域 - 使用PART_Header支持拖拽  -->
                            <ContentPresenter
                                Content="{TemplateBinding Header}"
                                ContentTemplate="{TemplateBinding HeaderTemplate}"
                                Grid.Row="0"
                                x:Name="PART_Header" />

                            <!--  Content区域  -->
                            <ContentPresenter
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                Grid.Row="1"
                                Margin="12" />
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <!--  选中状态  -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" TargetName="MainBorder" Value="{StaticResource SelectedNodeGlow}" />
                        </DataTrigger>

                        <!--  锁定状态  -->
                        <DataTrigger Binding="{Binding IsLocked}" Value="True">
                            <Setter Property="Opacity" TargetName="MainBorder" Value="0.8" />
                            <Setter Property="Effect" TargetName="MainBorder" Value="{StaticResource LockedNodeShadow}" />
                        </DataTrigger>

                        <!--  禁用状态  -->
                        <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                            <Setter Property="Opacity" TargetName="MainBorder" Value="0.5" />
                            <Setter Property="BorderBrush" TargetName="MainBorder" Value="#BDBDBD" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  数据计算节点头部模板  -->
    <DataTemplate x:Key="DataCalculationHeaderTemplate">
        <Border
            Background="{StaticResource ModernHeaderGradientDarkPurple}"
            CornerRadius="10,10,0,0"
            Height="56"
            Margin="-2,-2,-2,0"
            Padding="0">
            <Grid>
                <!--  输入连接端点：使用 Nodify.NodeInput，确保锚点写回并可连接  -->
                <ItemsControl
                    HorizontalAlignment="Left"
                    ItemsSource="{Binding Inputs}"
                    Margin="-10,0,0,0"
                    VerticalAlignment="Center">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Vertical" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <nodify:NodeInput
                                Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                Header=""
                                IsConnected="{Binding IsConnected}"
                                ToolTip="{Binding Title}"
                                />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!--  输出连接端点：使用 Nodify.NodeOutput，确保锚点写回并可连接  -->
                <ItemsControl
                    HorizontalAlignment="Right"
                    ItemsSource="{Binding Outputs}"
                    Margin="0,0,-10,0"
                    VerticalAlignment="Center">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Vertical" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <nodify:NodeOutput
                                Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                Header=""
                                IsConnected="{Binding IsConnected}"
                                ToolTip="{Binding Title}"
                                />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!--  标题与状态（原样保留）  -->
                <TextBlock
                    FontSize="14"
                    FontWeight="SemiBold"
                    Foreground="White"
                    HorizontalAlignment="Center"
                    Text="数据计算"
                    VerticalAlignment="Center" />
            </Grid>
        </Border>
    </DataTemplate>

    <!--  数据计算节点内容模板（原样保留 ExpandedContent 呈现）  -->
    <DataTemplate x:Key="DataCalculationContentTemplate">
        <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <Grid>
                <ItemsControl x:Name="FunctionsList" ItemsSource="{Binding ExpandedContent}">
                    <ItemsControl.Resources>
                        <!--  展示函数显示项：名称 + 表达式  -->
                        <DataTemplate DataType="{x:Type entities:FunctionDisplayItem}">
                            <Border
                                Background="{DynamicResource Brush.SurfaceVariant}"
                                BorderBrush="{DynamicResource Brush.Divider}"
                                BorderThickness="1"
                                CornerRadius="6"
                                Margin="0,4,0,0"
                                Padding="8">
                                <StackPanel>
                                    <TextBlock FontWeight="SemiBold" Text="{Binding Name}" />
                                    <TextBlock
                                        FontFamily="Consolas"
                                        FontSize="12"
                                        Margin="0,2,0,0"
                                        Text="{Binding Expression}"
                                        TextWrapping="Wrap" />
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.Resources>
                </ItemsControl>

                <!-- 空状态占位：暂无公式 -->
                <Border
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Padding="16,12"
                    CornerRadius="8"
                    Background="{DynamicResource Brush.Surface}"
                    BorderBrush="{DynamicResource Brush.Divider}"
                    BorderThickness="1">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Visibility" Value="Visible" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=FunctionsList, Path=HasItems}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                        <ipack:PackIconMaterial
                            Width="16"
                            Height="16"
                            Margin="0,0,8,0"
                            Foreground="{DynamicResource Brush.TextSecondary}"
                            Kind="Function" />
                        <StackPanel>
                            <TextBlock
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                FontSize="12"
                                FontWeight="SemiBold"
                                Text="暂无公式" />
                            <TextBlock
                                Foreground="{DynamicResource Brush.TextSecondary}"
                                FontSize="11"
                                Opacity="0.85"
                                Text="请在右侧属性面板添加函数或导入模板" />
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
    </DataTemplate>

    <!--  数据计算节点包装模板  -->
    <DataTemplate x:Key="DataCalculationGroupingNodeTemplate">
        <nodify:GroupingNode Header="{Binding}" Style="{StaticResource DataCalculationGroupingNodeStyle}">

            <!--  头部模板  -->
            <nodify:GroupingNode.HeaderTemplate>
                <StaticResource ResourceKey="DataCalculationHeaderTemplate" />
            </nodify:GroupingNode.HeaderTemplate>

            <!--  内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataCalculationContentTemplate}" />
        </nodify:GroupingNode>
    </DataTemplate>

    <!--  为选择器提供约定键：NodeTemplate.ModuleType.DataCalculation  -->
    <DataTemplate x:Key="NodeTemplate.ModuleType.DataCalculation">
        <nodify:GroupingNode Header="{Binding}" Style="{StaticResource DataCalculationGroupingNodeStyle}">

            <nodify:GroupingNode.HeaderTemplate>
                <StaticResource ResourceKey="DataCalculationHeaderTemplate" />
            </nodify:GroupingNode.HeaderTemplate>

            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataCalculationContentTemplate}" />
        </nodify:GroupingNode>
    </DataTemplate>

</ResourceDictionary>

