namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 连接线样式选择项
    /// 用于ComboBox的数据绑定
    /// </summary>
    public class ConnectionLineStyleItem
    {
        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 样式值
        /// </summary>
        public ConnectionLineStyle Value { get; set; }

        /// <summary>
        /// 图标字符
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConnectionLineStyleItem()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="displayName">显示名称</param>
        /// <param name="value">样式值</param>
        /// <param name="icon">图标字符</param>
        public ConnectionLineStyleItem(string displayName, ConnectionLineStyle value, string icon)
        {
            DisplayName = displayName;
            Value = value;
            Icon = icon;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>显示名称</returns>
        public override string ToString()
        {
            return DisplayName;
        }
    }
}
