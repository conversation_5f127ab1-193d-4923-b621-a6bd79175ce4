using System.ComponentModel.DataAnnotations;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 应用程序主配置类
    /// </summary>
    public class ApplicationSettings
    {
        /// <summary>
        /// 应用程序基本信息
        /// </summary>
        [Required]
        public ApplicationInfo Application { get; set; } = new();

        /// <summary>
        /// 数据库配置
        /// </summary>
        [Required]
        public DatabaseSettings Database { get; set; } = new();

        /// <summary>
        /// 日志配置
        /// </summary>
        [Required]
        public LoggingSettings Logging { get; set; } = new();

        /// <summary>
        /// 外部服务配置
        /// </summary>
        public ExternalServicesSettings ExternalServices { get; set; } = new();

        /// <summary>
        /// UI配置
        /// </summary>
        public UISettings UI { get; set; } = new();

        /// <summary>
        /// 数据目录路径
        /// </summary>
        [Required]
        public string DataDirectory { get; set; } = "Data";
    }

    /// <summary>
    /// 应用程序基本信息配置
    /// </summary>
    public class ApplicationInfo
    {
        /// <summary>
        /// 应用程序名称
        /// </summary>
        [Required]
        public string Name { get; set; } = "ProjectDigitizer Studio";

        /// <summary>
        /// 应用程序版本
        /// </summary>
        [Required]
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 应用程序描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        public string Company { get; set; } = string.Empty;
    }
}
