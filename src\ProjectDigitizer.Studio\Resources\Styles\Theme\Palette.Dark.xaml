<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 深色主题色板（与 Palette.xaml 键一致） -->
    <!-- 品牌主色与对比色（保持一致） -->
    <Color x:Key="Color.Primary">#4F46E5</Color>
    <Color x:Key="Color.PrimaryVariant">#6366F1</Color>
    <Color x:Key="Color.Secondary">#06B6D4</Color>
    <Color x:Key="Color.SecondaryVariant">#22D3EE</Color>

    <!-- AppBar/TopBar 深色背景 -->
    <Color x:Key="Color.AppBar">#FF1C1D21</Color>

    <!-- 中性色（深色模式） -->
    <Color x:Key="Color.Surface">#FF121317</Color>
    <Color x:Key="Color.SurfaceVariant">#FF191B20</Color>
    <Color x:Key="Color.Outline">#FF2A2E36</Color>
    <Color x:Key="Color.Border">#FF2C3038</Color>
    <Color x:Key="Color.Text">#FFF3F4F6</Color>
    <Color x:Key="Color.TextSecondary">#FFA7ADB7</Color>
    <Color x:Key="Color.Placeholder">#FF6B7280</Color>

    <!-- 反馈色（适配深色背景，稍微提亮） -->
    <Color x:Key="Color.Success">#22C55E</Color>
    <Color x:Key="Color.Warning">#F59E0B</Color>
    <Color x:Key="Color.Danger">#EF4444</Color>
    <Color x:Key="Color.Info">#60A5FA</Color>

    <!-- 阴影/分隔 -->
    <Color x:Key="Color.Shadow">#99000000</Color>
    <Color x:Key="Color.Divider">#1FFFFFFF</Color>

    <!-- 透明辅助色（基于文本为白色系） -->
    <Color x:Key="Color.Primary/08">#144F46E5</Color>
    <Color x:Key="Color.Primary/16">#294F46E5</Color>
    <Color x:Key="Color.Text/60">#99FFFFFF</Color>
    <Color x:Key="Color.Text/40">#66FFFFFF</Color>
    <Color x:Key="Color.Text/24">#3DFFFFFF</Color>
</ResourceDictionary>

