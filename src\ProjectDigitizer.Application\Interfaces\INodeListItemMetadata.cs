using System.Diagnostics.CodeAnalysis;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// Optional metadata that a node template can provide for palette/list display.
/// Implement on your INodeTemplate to influence grouping and sort ordering.
/// </summary>
public interface INodeListItemMetadata
{
    /// <summary>
    /// Logical group name for the node in the palette, e.g. "处理器"、"输入"、"输出"、"控制".
    /// </summary>
    string Group { get; }

    /// <summary>
    /// Order within its group (ascending).
    /// </summary>
    int Order { get; }

    /// <summary>
    /// Optional icon path (pack URI or relative) for palette display.
    /// </summary>
    string? IconPath { get; }

    /// <summary>
    /// Optional capability tags for search/filter (e.g., ["公式", "计算"]).
    /// </summary>
    string[] Tags { get; }
}
