using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;

namespace CalcEngine
{
    /// <summary>
	/// 词法标记（Token），对应表达式语法树上的节点最小单元。
    /// </summary>
    internal sealed class Token
    {
        // ** fields
        public readonly TKID ID;
        public readonly TKTYPE Type;
        public readonly object Value;

        // ** ctor
        public Token(object value, TKID id, TKTYPE type)
        {
            Value = value;
            ID = id;
            Type = type;
        }
    }
    /// <summary>
    /// 标记类型（用于构建表达式，声明顺序体现运算优先级）。
    /// </summary>
    internal enum TKTYPE
    {
        COMPARE,	// < > = <= >=
        ADDSUB,		// + -
        MULDIV,		// * /
        POWER,		// ^
        GROUP,		// ( ) , .
        LITERAL,	// 123.32, "Hello", etc.
        IDENTIFIER  // functions, external objects, bindings
    }
    /// <summary>
    /// 标记 ID（用于求值阶段识别不同运算与字面量）。
    /// </summary>
    internal enum TKID
    {
        GT, LT, GE, LE, EQ, NE, // COMPARE
        ADD, SUB, // ADDSUB
        MUL, DIV, DIVINT, MOD, // MULDIV
        POWER, // POWER
        OPEN, CLOSE, END, COMMA, PERIOD, // GROUP
        ATOM, // LITERAL, IDENTIFIER
    }
}
