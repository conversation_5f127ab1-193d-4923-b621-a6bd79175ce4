using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Infrastructure.Performance
{
    /// <summary>
    /// 画布性能优化器实现
    /// </summary>
    public class CanvasPerformanceOptimizer : ICanvasPerformanceOptimizer
    {
        private readonly DispatcherTimer _performanceTimer;
        private readonly Queue<double> _renderTimeHistory;
        private readonly Queue<double> _fpsHistory;
        private readonly Stopwatch _renderStopwatch;
        private bool _isOptimizing;
        private bool _disposed;

        // 性能监控设置
        private const int MAX_HISTORY_SIZE = 60; // 保留60个样本
        private const int PERFORMANCE_CHECK_INTERVAL_MS = 1000; // 1秒检查一次
        private const double LOW_FPS_THRESHOLD = 30.0;
        private const double CRITICAL_FPS_THRESHOLD = 15.0;

        // 渲染统计
        private long _totalRenders;
        private long _batchRenderCount;
        private long _skippedRenderCount;
        private int _currentNodeCount;
        private int _visibleNodeCount;

        public CanvasPerformanceConfig CurrentConfig { get; private set; }
        public CanvasRenderingStats RenderingStats { get; private set; }

        public event Action<PerformanceOptimizationSuggestion>? OptimizationSuggested;
        public event Action<RenderingPerformanceWarning>? PerformanceWarning;

        public CanvasPerformanceOptimizer()
        {
            CurrentConfig = new CanvasPerformanceConfig();
            RenderingStats = new CanvasRenderingStats();

            _renderTimeHistory = new Queue<double>();
            _fpsHistory = new Queue<double>();
            _renderStopwatch = new Stopwatch();

            _performanceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(PERFORMANCE_CHECK_INTERVAL_MS)
            };
            _performanceTimer.Tick += OnPerformanceTimerTick;
        }

        public void StartOptimization()
        {
            if (_disposed) return;

            if (!_isOptimizing)
            {
                _isOptimizing = true;
                _performanceTimer.Start();
            }
        }

        public void StopOptimization()
        {
            if (_isOptimizing)
            {
                _isOptimizing = false;
                _performanceTimer.Stop();
            }
        }

        public void UpdateNodeCount(int totalNodes, int visibleNodes)
        {
            _currentNodeCount = totalNodes;
            _visibleNodeCount = visibleNodes;

            // 根据节点数量自动调整配置
            if (_isOptimizing)
            {
                AutoAdjustConfiguration();
            }
        }

        public void BeginRender()
        {
            _renderStopwatch.Restart();
        }

        public void EndRender()
        {
            _renderStopwatch.Stop();
            var renderTime = _renderStopwatch.ElapsedMilliseconds;

            _totalRenders++;
            RecordRenderTime(renderTime);
            UpdateRenderingStats();
        }

        public void RecordBatchRender(int batchSize)
        {
            _batchRenderCount++;
            UpdateRenderingStats();
        }

        public CanvasRenderingConfig GetRecommendedRenderingConfig(int nodeCount, ViewportSize viewportSize)
        {
            var config = new CanvasRenderingConfig();

            if (nodeCount < 50)
            {
                // 小规模场景 - 高质量渲染
                config.RecommendedQuality = RenderingQuality.High;
                config.RecommendedMaxFPS = 60;
                config.RecommendedBatchSize = 10;
                config.ShouldEnableVirtualization = false;
                config.ShouldEnableLOD = false;
                config.Reason = "节点数量较少，可以使用高质量渲染";
            }
            else if (nodeCount < 200)
            {
                // 中等规模场景 - 平衡质量和性能
                config.RecommendedQuality = RenderingQuality.Medium;
                config.RecommendedMaxFPS = 60;
                config.RecommendedBatchSize = 25;
                config.ShouldEnableVirtualization = true;
                config.ShouldEnableLOD = false;
                config.Reason = "中等节点数量，平衡质量和性能";
            }
            else if (nodeCount < 1000)
            {
                // 大规模场景 - 优先性能
                config.RecommendedQuality = RenderingQuality.Medium;
                config.RecommendedMaxFPS = 45;
                config.RecommendedBatchSize = 50;
                config.ShouldEnableVirtualization = true;
                config.ShouldEnableLOD = true;
                config.Reason = "大量节点，需要启用虚拟化和LOD优化";
            }
            else
            {
                // 超大规模场景 - 激进优化
                config.RecommendedQuality = RenderingQuality.Low;
                config.RecommendedMaxFPS = 30;
                config.RecommendedBatchSize = 100;
                config.ShouldEnableVirtualization = true;
                config.ShouldEnableLOD = true;
                config.Reason = "超大规模场景，需要激进的性能优化";
            }

            return config;
        }

        public void ApplyPerformanceConfig(CanvasPerformanceConfig config)
        {
            CurrentConfig = config ?? throw new ArgumentNullException(nameof(config));
        }

        public CanvasPerformanceMetrics GetCurrentMetrics()
        {
            var metrics = new CanvasPerformanceMetrics
            {
                CurrentNodeCount = _currentNodeCount,
                VisibleNodeCount = _visibleNodeCount,
                RenderingEfficiency = CalculateRenderingEfficiency(),
                MemoryEfficiency = CalculateMemoryEfficiency(),
                OverallPerformanceScore = CalculateOverallPerformanceScore(),
                Bottlenecks = IdentifyBottlenecks()
            };

            return metrics;
        }

        public LargeScaleOptimizationResult OptimizeForLargeScale(int nodeCount)
        {
            var result = new LargeScaleOptimizationResult();
            var strategies = new List<OptimizationStrategy>();

            if (nodeCount > 500)
            {
                result.NeedsOptimization = true;

                // 虚拟化策略
                if (!CurrentConfig.EnableVirtualization)
                {
                    strategies.Add(new OptimizationStrategy
                    {
                        Name = "启用虚拟化",
                        Description = "启用画布虚拟化以减少渲染的节点数量",
                        ExpectedEffect = 0.4, // 40%性能提升
                        ImplementationDifficulty = 2,
                        ExecuteAction = () =>
                        {
                            CurrentConfig.EnableVirtualization = true;
                        }
                    });
                }
                else
                {
                    // 即使已启用虚拟化，也可以提供进一步优化建议
                    strategies.Add(new OptimizationStrategy
                    {
                        Name = "优化虚拟化设置",
                        Description = "调整虚拟化参数以获得更好的性能",
                        ExpectedEffect = 0.1, // 10%性能提升
                        ImplementationDifficulty = 1,
                        ExecuteAction = () =>
                        {
                            // 可以在这里调整虚拟化参数
                        }
                    });
                }

                // 批处理策略
                if (CurrentConfig.BatchSize < 50)
                {
                    strategies.Add(new OptimizationStrategy
                    {
                        Name = "增加批处理大小",
                        Description = "增加渲染批处理大小以减少渲染调用次数",
                        ExpectedEffect = 0.2, // 20%性能提升
                        ImplementationDifficulty = 1,
                        ExecuteAction = () =>
                        {
                            CurrentConfig.BatchSize = Math.Min(100, nodeCount / 10);
                        }
                    });
                }

                // LOD策略
                if (!CurrentConfig.EnableLevelOfDetail && nodeCount > 1000)
                {
                    strategies.Add(new OptimizationStrategy
                    {
                        Name = "启用细节层次",
                        Description = "根据距离调整节点渲染细节",
                        ExpectedEffect = 0.3, // 30%性能提升
                        ImplementationDifficulty = 3,
                        ExecuteAction = () =>
                        {
                            CurrentConfig.EnableLevelOfDetail = true;
                            CurrentConfig.LODDistanceThreshold = 300.0;
                        }
                    });
                }

                // 质量降级策略
                if (CurrentConfig.RenderingQuality == RenderingQuality.High && nodeCount > 2000)
                {
                    strategies.Add(new OptimizationStrategy
                    {
                        Name = "降低渲染质量",
                        Description = "降低渲染质量以提高性能",
                        ExpectedEffect = 0.25, // 25%性能提升
                        ImplementationDifficulty = 1,
                        ExecuteAction = () =>
                        {
                            CurrentConfig.RenderingQuality = RenderingQuality.Medium;
                        }
                    });
                }

                result.RecommendedStrategies = strategies;
                result.ExpectedPerformanceGain = strategies.Sum(s => s.ExpectedEffect) * 100;
                result.Description = $"检测到{nodeCount}个节点，建议应用{strategies.Count}个优化策略";
            }
            else
            {
                result.NeedsOptimization = false;
                result.Description = "当前节点数量在可接受范围内，无需特殊优化";
            }

            return result;
        }

        public void SetRenderingQuality(RenderingQuality quality)
        {
            CurrentConfig.RenderingQuality = quality;
        }

        public void SetBatchRendering(bool enabled, int batchSize = 50)
        {
            CurrentConfig.EnableBatchRendering = enabled;
            CurrentConfig.BatchSize = batchSize;
        }

        public void SetFrameRateLimit(int maxFPS)
        {
            CurrentConfig.MaxFrameRate = Math.Max(15, Math.Min(120, maxFPS));
        }

        private void OnPerformanceTimerTick(object? sender, EventArgs e)
        {
            UpdatePerformanceMetrics();
            CheckPerformanceThresholds();
            GenerateOptimizationSuggestions();
        }

        private void RecordRenderTime(double renderTimeMs)
        {
            _renderTimeHistory.Enqueue(renderTimeMs);
            while (_renderTimeHistory.Count > MAX_HISTORY_SIZE)
            {
                _renderTimeHistory.Dequeue();
            }
        }

        private void UpdateRenderingStats()
        {
            if (_renderTimeHistory.Count > 0)
            {
                RenderingStats.AverageRenderTimeMs = _renderTimeHistory.Average();
                RenderingStats.MaxRenderTimeMs = _renderTimeHistory.Max();
            }

            RenderingStats.TotalRenders = _totalRenders;
            RenderingStats.BatchRenderCount = _batchRenderCount;
            RenderingStats.SkippedRenderCount = _skippedRenderCount;
            RenderingStats.LastUpdated = DateTime.Now;
        }

        private void UpdatePerformanceMetrics()
        {
            // 计算当前FPS
            if (_renderTimeHistory.Count > 0)
            {
                var avgRenderTime = _renderTimeHistory.Average();
                var currentFPS = avgRenderTime > 0 ? 1000.0 / avgRenderTime : 0;

                _fpsHistory.Enqueue(currentFPS);
                while (_fpsHistory.Count > MAX_HISTORY_SIZE)
                {
                    _fpsHistory.Dequeue();
                }

                RenderingStats.CurrentFPS = currentFPS;
                RenderingStats.AverageFPS = _fpsHistory.Average();
            }
        }

        private void CheckPerformanceThresholds()
        {
            var currentFPS = RenderingStats.CurrentFPS;

            if (currentFPS < CRITICAL_FPS_THRESHOLD)
            {
                TriggerPerformanceWarning(PerformanceWarningLevel.Critical,
                    $"帧率严重过低: {currentFPS:F1} FPS");
            }
            else if (currentFPS < LOW_FPS_THRESHOLD)
            {
                TriggerPerformanceWarning(PerformanceWarningLevel.Warning,
                    $"帧率较低: {currentFPS:F1} FPS");
            }
        }

        private void GenerateOptimizationSuggestions()
        {
            var currentFPS = RenderingStats.CurrentFPS;

            if (currentFPS < LOW_FPS_THRESHOLD && _currentNodeCount > 100)
            {
                var suggestion = new PerformanceOptimizationSuggestion
                {
                    Type = OptimizationType.EnableVirtualization,
                    Description = "检测到性能问题，建议启用虚拟化优化",
                    ExpectedImprovement = 0.3,
                    SuggestedConfig = GetOptimizedConfig()
                };

                OptimizationSuggested?.Invoke(suggestion);
            }
        }

        private void AutoAdjustConfiguration()
        {
            var recommendedConfig = GetRecommendedRenderingConfig(_currentNodeCount, new ViewportSize(1920, 1080));

            // 自动应用推荐配置
            CurrentConfig.EnableVirtualization = recommendedConfig.ShouldEnableVirtualization;
            CurrentConfig.EnableLevelOfDetail = recommendedConfig.ShouldEnableLOD;
            CurrentConfig.BatchSize = recommendedConfig.RecommendedBatchSize;
            CurrentConfig.MaxFrameRate = recommendedConfig.RecommendedMaxFPS;
            CurrentConfig.RenderingQuality = recommendedConfig.RecommendedQuality;
        }

        private double CalculateRenderingEfficiency()
        {
            if (_totalRenders == 0) return 1.0;

            var efficiency = (double)_visibleNodeCount / Math.Max(_currentNodeCount, 1);
            return Math.Max(0.0, Math.Min(1.0, efficiency));
        }

        private double CalculateMemoryEfficiency()
        {
            // 简化的内存效率计算
            var virtualizedRatio = CurrentConfig.EnableVirtualization ? 0.8 : 0.4;
            var lodRatio = CurrentConfig.EnableLevelOfDetail ? 0.9 : 0.7;
            return virtualizedRatio * lodRatio;
        }

        private double CalculateOverallPerformanceScore()
        {
            var fpsScore = Math.Min(100.0, RenderingStats.CurrentFPS / 60.0 * 100.0);
            var efficiencyScore = CalculateRenderingEfficiency() * 100.0;
            var memoryScore = CalculateMemoryEfficiency() * 100.0;

            return (fpsScore + efficiencyScore + memoryScore) / 3.0;
        }

        private IEnumerable<PerformanceBottleneck> IdentifyBottlenecks()
        {
            var bottlenecks = new List<PerformanceBottleneck>();

            if (RenderingStats.CurrentFPS < LOW_FPS_THRESHOLD)
            {
                bottlenecks.Add(new PerformanceBottleneck
                {
                    Type = BottleneckType.Rendering,
                    Description = "渲染帧率过低",
                    Impact = 1.0 - (RenderingStats.CurrentFPS / 60.0),
                    SuggestedSolution = "启用虚拟化或降低渲染质量"
                });
            }

            if (_currentNodeCount > 1000 && !CurrentConfig.EnableVirtualization)
            {
                bottlenecks.Add(new PerformanceBottleneck
                {
                    Type = BottleneckType.CPU,
                    Description = "大量节点未启用虚拟化",
                    Impact = 0.6,
                    SuggestedSolution = "启用画布虚拟化"
                });
            }

            return bottlenecks;
        }

        private void TriggerPerformanceWarning(PerformanceWarningLevel level, string message)
        {
            var warning = new RenderingPerformanceWarning
            {
                Level = level,
                Message = message,
                CurrentMetrics = GetCurrentMetrics(),
                SuggestedSolutions = GetPerformanceSolutions()
            };

            PerformanceWarning?.Invoke(warning);
        }

        private IEnumerable<string> GetPerformanceSolutions()
        {
            var solutions = new List<string>();

            if (!CurrentConfig.EnableVirtualization)
                solutions.Add("启用画布虚拟化");

            if (CurrentConfig.RenderingQuality == RenderingQuality.High)
                solutions.Add("降低渲染质量");

            if (CurrentConfig.BatchSize < 50)
                solutions.Add("增加批处理大小");

            return solutions;
        }

        private CanvasPerformanceConfig GetOptimizedConfig()
        {
            return new CanvasPerformanceConfig
            {
                EnableVirtualization = true,
                EnableBatchRendering = true,
                BatchSize = Math.Max(50, _currentNodeCount / 20),
                MaxFrameRate = 45,
                RenderingQuality = _currentNodeCount > 500 ? RenderingQuality.Medium : RenderingQuality.High,
                EnableLevelOfDetail = _currentNodeCount > 1000,
                LODDistanceThreshold = 400.0,
                EnableOcclusionCulling = true,
                ViewportMargin = 150.0
            };
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopOptimization();
                _performanceTimer?.Stop();
                _disposed = true;
            }
        }
    }
}
