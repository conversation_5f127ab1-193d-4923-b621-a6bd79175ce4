using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace Plugins.DataCalculation;

/// <summary>
/// 数据计算 节点模板：定义节点的基本元数据与默认属性架构。
/// </summary>
[NodeListItem(Group = "处理器", Order = 100, IconPath = null, Tags = new[] { "公式", "计算" })]
public class DataCalculationTemplate : INodeTemplate, INodeListItemMetadata
{
    public ModuleType ModuleType => ModuleType.DataCalculation;
    public string Name => "数据计算";
    public string Description => "对上游数据进行表达式计算，输出结果";
    public string IconGlyph => "\uE1D1";
    // INodeListItemMetadata
    public string Group => "处理器";
    public int Order => 100;
    public string? IconPath => null;
    public string[] Tags => new[] { "公式", "计算" };

    /// <summary>默认属性架构：提供表达式编辑字段。</summary>
    public NodePropertySchema PropertySchema => new()
    {
        NodeType = nameof(ModuleType.DataCalculation),
        DisplayName = "数据计算",
        Description = "数据计算节点的基本属性",
        Properties =
        {
            ["Expression"] = new PropertyDefinition
            {
                Name = "Expression",
                Title = "表达式",
                Description = "支持 +, -, *, / 与括号（示例：a + b*2）",
                Type = ProjectDigitizer.Core.ValueObjects.PropertyType.String,
                UiWidget = "textarea",
                DefaultValue = string.Empty,
                Required = false,
                Group = "General",
                Order = 0
            }
        }
    };

    public string DefaultCodeTemplate => string.Empty;

    /// <summary>
    /// 创建节点实例（用于画布添加时的默认值）。
    /// </summary>
    public INode CreateInstance()
    {
        return new SimpleNode
        {
            Id = System.Guid.NewGuid().ToString(),
            Name = "数据计算",
            Description = "通过表达式对数据进行计算",
            ModuleType = ModuleType.DataCalculation,
            NodeType = NodeType.Transform,
            IsEnabled = true,
            IsVisible = true,
            Properties = new TransformNodeProperties(),
            CodeTemplate = string.Empty
        };
    }
}

/// <summary>
/// 简化的节点实现（仅用于模板创建），真实节点由运行时工厂管理。
/// </summary>
internal class SimpleNode : INode
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ModuleType ModuleType { get; set; }
    public NodeType NodeType { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsVisible { get; set; }
    public INodeProperties Properties { get; set; } = new TransformNodeProperties();
    public string CodeTemplate { get; set; } = string.Empty;

    public ValidationResult Validate() => Properties.ValidateProperties();
    public IEnumerable<IPortDefinition> GetInputPorts() => Array.Empty<IPortDefinition>();
    public IEnumerable<IPortDefinition> GetOutputPorts() => Array.Empty<IPortDefinition>();
    public INode Clone() => (INode)MemberwiseClone();
    public event System.ComponentModel.PropertyChangedEventHandler? PropertyChanged
    {
        add { }
        remove { }
    }
}


