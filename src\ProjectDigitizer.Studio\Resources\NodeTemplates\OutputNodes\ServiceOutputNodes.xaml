<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 服务输出节点模板集合 ==========  -->
    <!--  用于 DatabaseOutput、EmailSender、PrintOutput、WebServiceOutput、FTPUpload、CloudStorage 等服务输出节点  -->

    <!--  通用服务输出节点内容模板  -->
    <DataTemplate x:Key="ServiceOutputNodeContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  服务输出图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="Export" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DatabaseOutput}">
                                                <Setter Property="Kind" Value="Database" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EmailSender}">
                                                <Setter Property="Kind" Value="Email" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.PrintOutput}">
                                                <Setter Property="Kind" Value="Printer" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.WebServiceOutput}">
                                                <Setter Property="Kind" Value="Web" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FTPUpload}">
                                                <Setter Property="Kind" Value="Upload" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CloudStorage}">
                                                <Setter Property="Kind" Value="Cloud" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.NotificationAlert}">
                                                <Setter Property="Kind" Value="Bell" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ReportGeneration}">
                                                <Setter Property="Kind" Value="FileChart" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：服务类型信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  服务类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    Background="#FF9800"
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="服务" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DatabaseOutput}">
                                                        <Setter Property="Text" Value="数据库" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EmailSender}">
                                                        <Setter Property="Text" Value="邮件" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.PrintOutput}">
                                                        <Setter Property="Text" Value="打印" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.WebServiceOutput}">
                                                        <Setter Property="Text" Value="WEB" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FTPUpload}">
                                                        <Setter Property="Text" Value="FTP" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CloudStorage}">
                                                        <Setter Property="Text" Value="云存储" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.NotificationAlert}">
                                                        <Setter Property="Text" Value="通知" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ReportGeneration}">
                                                        <Setter Property="Text" Value="报表" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    Opacity="0.8"
                                    VerticalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.ServiceEndpoint, FallbackValue='未配置'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EmailSender}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.SmtpServer, FallbackValue='smtp.example.com'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.PrintOutput}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.PrinterName, FallbackValue='默认打印机'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.DatabaseOutput}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.DatabaseName, FallbackValue='数据库'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  测试连接按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="测试连接">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Connection"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="服务配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  连接状态  -->
                                <Ellipse
                                    Fill="#4CAF50"
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="连接状态"
                                    VerticalAlignment="Center"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#FFF3E0"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  服务地址  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="地址:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontFamily="Consolas"
                                FontSize="9"
                                Foreground="#FF9800"
                                Grid.Column="1"
                                TextTrimming="CharacterEllipsis"
                                VerticalAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="{Binding NodeProperties.ServerAddress, FallbackValue='localhost'}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.EmailSender}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.SmtpServer, FallbackValue='smtp.example.com:587'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.WebServiceOutput}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.WebServiceUrl, FallbackValue='https://api.example.com'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.FTPUpload}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.FtpServer, FallbackValue='ftp.example.com'}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CloudStorage}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.CloudProvider, FallbackValue='阿里云OSS'}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  认证信息  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="认证:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#FF9800"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.AuthType, FallbackValue='用户名密码'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CloudStorage}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.AccessKey, FallbackValue='AccessKey'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.WebServiceOutput}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.ApiKey, FallbackValue='API Key'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Border>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  超时时间  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="超时" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#FF9800"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.TimeoutSeconds, FallbackValue=30, StringFormat={}{0}s}" />
                            </StackPanel>

                            <!--  重试次数  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="重试" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#FF9800"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.RetryCount, FallbackValue=3}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  数据库输出节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DatabaseOutputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  邮件发送节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="EmailSenderNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  打印输出节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="PrintOutputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  Web服务输出节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="WebServiceOutputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  FTP上传节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="FTPUploadNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  云存储节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="CloudStorageNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  通知警报节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="NotificationAlertNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  报表生成节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ReportGenerationNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ServiceOutputNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
