<UserControl x:Class="ProjectDigitizer.Studio.Controls.Constants.PiConstantPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="400"
             d:DesignWidth="350"
             Background="{DynamicResource Brush.Surface}">

    <UserControl.Resources>
        <!-- 标题样式 -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource Brush.Primary}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 卡片样式 -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource Brush.Surface}"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="1" Direction="270" Color="Black" Opacity="0.2" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 数值显示样式 -->
        <Style x:Key="ValueDisplayStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{StaticResource Brush.PrimaryVariant}"/>
            <Setter Property="Background" Value="{StaticResource Brush.SurfaceVariant}"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 节点标题 -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="4,4,4,2">
            <StackPanel>
                <TextBlock Text="π 常量节点" Style="{StaticResource SectionHeaderStyle}" HorizontalAlignment="Center"/>
                <TextBlock Text="提供数学常量π的精确值" FontSize="12" Foreground="{StaticResource Brush.TextSecondary}" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- π值显示 -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="4,2,4,2">
            <StackPanel>
                <TextBlock Text="当前π值" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBlock x:Name="PiValueDisplay" 
                          Text="{Binding PiValue, StringFormat=F15}" 
                          Style="{StaticResource ValueDisplayStyle}"
                          TextWrapping="Wrap"/>
                <TextBlock Text="{Binding PiValue, StringFormat='科学计数法: {0:E15}'}" 
                          FontSize="10" 
                          Foreground="{StaticResource Brush.TextSecondary}" 
                          HorizontalAlignment="Center" 
                          Margin="0,4,0,0"/>
            </StackPanel>
        </Border>

        <!-- 精度设置 -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="4,2,4,2">
            <StackPanel>
                <TextBlock Text="输出精度设置" Style="{StaticResource SectionHeaderStyle}"/>
                
                <!-- 精度滑块 -->
                <Grid Margin="0,8,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="2" FontSize="12" VerticalAlignment="Center"/>
                    <Slider x:Name="PrecisionSlider" 
                           Grid.Column="1"
                           Minimum="2" 
                           Maximum="15" 
                           Value="{Binding Precision, Mode=TwoWay}"
                           TickFrequency="1" 
                           IsSnapToTickEnabled="True"
                           Margin="8,0"/>
                    <TextBlock Grid.Column="2" Text="15" FontSize="12" VerticalAlignment="Center"/>
                </Grid>
                
                <!-- 当前精度显示 -->
                <TextBlock Text="{Binding Precision, StringFormat='当前精度: {0} 位小数'}" 
                          FontSize="12" 
                          Foreground="{StaticResource Brush.TextSecondary}" 
                          HorizontalAlignment="Center" 
                          Margin="0,4,0,0"/>
                
                <!-- 预设精度按钮 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8,0,0">
                    <Button Content="2位" Click="SetPrecision_Click" Tag="2" Margin="2" Padding="8,2" FontSize="10"/>
                    <Button Content="4位" Click="SetPrecision_Click" Tag="4" Margin="2" Padding="8,2" FontSize="10"/>
                    <Button Content="6位" Click="SetPrecision_Click" Tag="6" Margin="2" Padding="8,2" FontSize="10"/>
                    <Button Content="10位" Click="SetPrecision_Click" Tag="10" Margin="2" Padding="8,2" FontSize="10"/>
                    <Button Content="15位" Click="SetPrecision_Click" Tag="15" Margin="2" Padding="8,2" FontSize="10"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 输出信息 -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="4,2,4,4">
            <StackPanel>
                <TextBlock Text="输出信息" Style="{StaticResource SectionHeaderStyle}"/>
                
                <!-- 输出字段信息 -->
                <Border Background="{StaticResource Brush.SurfaceVariant}" BorderBrush="{StaticResource Brush.Border}" BorderThickness="1" CornerRadius="4" Padding="8" Margin="0,4,0,0">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="字段名:" FontWeight="Medium" FontSize="12"/>
                            <TextBlock Grid.Column="1" Text="pi_value" FontFamily="Consolas" FontSize="12" Margin="8,0,0,0"/>
                        </Grid>
                        <Grid Margin="0,4,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="数据类型:" FontWeight="Medium" FontSize="12"/>
                            <TextBlock Grid.Column="1" Text="Number (Double)" FontSize="12" Margin="8,0,0,0"/>
                        </Grid>
                        <Grid Margin="0,4,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="描述:" FontWeight="Medium" FontSize="12"/>
                            <TextBlock Grid.Column="1" Text="数学常量π的值" FontSize="12" Margin="8,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 使用说明 -->
                <TextBlock Text="💡 提示：此节点可连接到数据计算节点或其他需要π值的节点" 
                          FontSize="11" 
                          Foreground="{StaticResource Brush.TextSecondary}" 
                          TextWrapping="Wrap" 
                          Margin="0,8,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>


