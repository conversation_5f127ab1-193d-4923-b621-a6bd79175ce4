<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:nodify="https://miroiu.github.io/nodify"
                    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels">

    <!-- ========== 直线连接模板 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的直线连接相关模板 -->

    <!-- 直线/正交线连接模板 -->
    <DataTemplate x:Key="LineConnectionTemplate" DataType="{x:Type viewmodels:ConnectionViewModel}">
        <!-- 使用Grid来叠加多层效果 -->
        <Grid>
            <!-- 主连接线 -->
            <nodify:LineConnection x:Name="MainConnection"
                                   Source="{Binding Source.Anchor}"
                                   Target="{Binding Target.Anchor}"
                                   Stroke="{Binding ConnectionColor}"
                                   StrokeThickness="2.5"
                                   IsEnabled="{Binding IsEnabled}"
                                   CornerRadius="5"
                                   Spacing="20"
                                   Cursor="Hand"/>

            <!-- 光效流动层 -->
            <nodify:LineConnection x:Name="FlowEffect"
                                   Source="{Binding Source.Anchor}"
                                   Target="{Binding Target.Anchor}"
                                   StrokeThickness="4"
                                   IsEnabled="{Binding IsEnabled}"
                                   CornerRadius="5"
                                   Spacing="20"
                                   IsHitTestVisible="False"
                                   Opacity="0"
                                   StrokeDashArray="12 30"
                                   StrokeDashOffset="0">
                <!-- 光效渐变画刷 -->
                <nodify:LineConnection.Stroke>
                    <SolidColorBrush Color="#80FFFFFF" x:Name="FlowBrush"/>
                </nodify:LineConnection.Stroke>
            </nodify:LineConnection>
        </Grid>

        <DataTemplate.Triggers>
            <!-- 禁用状态 -->
            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                <Setter TargetName="MainConnection" Property="Opacity" Value="0.5"/>
                <Setter TargetName="MainConnection" Property="StrokeDashArray" Value="5,5"/>
            </DataTrigger>

            <!-- 鼠标悬停效果 -->
            <DataTrigger Binding="{Binding ElementName=MainConnection, Path=IsMouseOver}" Value="True">
                <Setter TargetName="MainConnection" Property="StrokeThickness" Value="3"/>
            </DataTrigger>

            <!-- 慢速光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Slow">
                <Setter TargetName="FlowEffect" Property="Opacity" Value="0.8"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="FlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:2"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 正常速度光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Normal">
                <Setter TargetName="FlowEffect" Property="Opacity" Value="0.9"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="FlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:1"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 快速光效流动 -->
            <DataTrigger Binding="{Binding FlowState}" Value="Fast">
                <Setter TargetName="FlowEffect" Property="Opacity" Value="1.0"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="FlowEffect"
                                             Storyboard.TargetProperty="StrokeDashOffset"
                                             From="42"
                                             To="0"
                                             Duration="0:0:0.5"
                                             AccelerationRatio="0"
                                             DecelerationRatio="0"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>

    <!-- 默认连接线模板（回退到直线连接） -->
    <DataTemplate x:Key="DefaultConnectionTemplate" DataType="{x:Type viewmodels:ConnectionViewModel}">
        <!-- 使用Grid来叠加多层效果 -->
        <Grid>
            <!-- 基础连接线 -->
            <nodify:LineConnection Source="{Binding Source.Anchor}"
                                   Target="{Binding Target.Anchor}"
                                   Stroke="{Binding ConnectionColor}"
                                   StrokeThickness="2.5"
                                   IsEnabled="{Binding IsEnabled}"
                                   CornerRadius="5"
                                   Spacing="15">
                <nodify:LineConnection.Style>
                    <Style TargetType="nodify:LineConnection">
                        <Setter Property="Cursor" Value="Hand"/>
                        <Style.Triggers>
                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                                <Setter Property="StrokeDashArray" Value="5,5"/>
                            </Trigger>
                            <!-- 鼠标悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="StrokeThickness" Value="3"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </nodify:LineConnection.Style>
            </nodify:LineConnection>

            <!-- 光效层 -->
            <nodify:LineConnection x:Name="DefaultFlowEffect"
                                   Source="{Binding Source.Anchor}"
                                   Target="{Binding Target.Anchor}"
                                   StrokeThickness="2.5"
                                   IsEnabled="{Binding IsEnabled}"
                                   CornerRadius="5"
                                   Spacing="15"
                                   StrokeDashArray="12 30"
                                   StrokeDashOffset="0">
                <nodify:LineConnection.Stroke>
                    <SolidColorBrush Color="#C0FFFFFF" x:Name="DefaultFlowBrush"/>
                </nodify:LineConnection.Stroke>
                <nodify:LineConnection.Style>
                    <Style TargetType="nodify:LineConnection">
                        <Style.Triggers>
                            <!-- 禁用状态时隐藏光效 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <!-- 慢速流动动画 -->
                            <DataTrigger Binding="{Binding FlowState}" Value="Slow">
                                <DataTrigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Storyboard.TargetProperty="StrokeDashOffset"
                                                             From="42"
                                                             To="0"
                                                             Duration="0:0:2"
                                                             AccelerationRatio="0"
                                                             DecelerationRatio="0"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </DataTrigger.EnterActions>
                            </DataTrigger>
                            <!-- 正常速度流动动画 -->
                            <DataTrigger Binding="{Binding FlowState}" Value="Normal">
                                <DataTrigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Storyboard.TargetProperty="StrokeDashOffset"
                                                             From="42"
                                                             To="0"
                                                             Duration="0:0:1"
                                                             AccelerationRatio="0"
                                                             DecelerationRatio="0"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </DataTrigger.EnterActions>
                            </DataTrigger>
                            <!-- 快速流动动画 -->
                            <DataTrigger Binding="{Binding FlowState}" Value="Fast">
                                <DataTrigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Storyboard.TargetProperty="StrokeDashOffset"
                                                             From="42"
                                                             To="0"
                                                             Duration="0:0:0.5"
                                                             AccelerationRatio="0"
                                                             DecelerationRatio="0"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </DataTrigger.EnterActions>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </nodify:LineConnection.Style>
            </nodify:LineConnection>
        </Grid>
    </DataTemplate>

</ResourceDictionary>
