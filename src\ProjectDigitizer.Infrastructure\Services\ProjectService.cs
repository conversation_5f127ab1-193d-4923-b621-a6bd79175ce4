using System.IO;
using System.Text.Json;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 项目管理服务实现
/// 负责项目文件的保存、加载和验证
/// </summary>
public class ProjectService : IProjectService
{
    private readonly ILogger<ProjectService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public ProjectService(ILogger<ProjectService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };
    }

    /// <summary>
    /// 保存项目到文件
    /// </summary>
    public async Task SaveProjectAsync(object projectData, string filePath)
    {
        try
        {
            if (projectData == null)
                throw new ArgumentNullException(nameof(projectData));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            _logger.LogInformation("开始保存项目到文件: {FilePath}", filePath);

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogDebug("创建目录: {Directory}", directory);
            }

            // 序列化项目数据
            var jsonString = JsonSerializer.Serialize(projectData, _jsonOptions);

            // 写入文件
            await File.WriteAllTextAsync(filePath, jsonString);

            _logger.LogInformation("项目保存成功: {FilePath}", filePath);
        }
        catch (Exception ex) when (!(ex is ArgumentNullException || ex is ArgumentException))
        {
            _logger.LogError(ex, "保存项目时发生错误: {FilePath}", filePath);
            throw new FileOperationException($"保存项目文件失败: {ex.Message}", filePath, ex);
        }
    }

    /// <summary>
    /// 从文件加载项目
    /// </summary>
    public async Task<object> LoadProjectAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"项目文件不存在: {filePath}");

            _logger.LogInformation("开始加载项目文件: {FilePath}", filePath);

            // 读取文件内容
            var jsonString = await File.ReadAllTextAsync(filePath);

            if (string.IsNullOrWhiteSpace(jsonString))
                throw new InvalidOperationException("项目文件内容为空");

            // 反序列化项目数据
            var projectData = JsonSerializer.Deserialize<object>(jsonString, _jsonOptions);

            if (projectData == null)
                throw new InvalidOperationException("无法解析项目文件内容");

            _logger.LogInformation("项目加载成功: {FilePath}", filePath);

            return projectData;
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is FileNotFoundException))
        {
            _logger.LogError(ex, "加载项目时发生错误: {FilePath}", filePath);
            throw new FileOperationException($"加载项目文件失败: {ex.Message}", filePath, ex);
        }
    }

    /// <summary>
    /// 验证项目数据
    /// </summary>
    public bool ValidateProject(object projectData)
    {
        try
        {
            if (projectData == null)
            {
                _logger.LogWarning("项目数据为空");
                return false;
            }

            _logger.LogDebug("开始验证项目数据");

            // 基本验证：检查是否可以序列化
            try
            {
                var jsonString = JsonSerializer.Serialize(projectData, _jsonOptions);
                if (string.IsNullOrWhiteSpace(jsonString))
                {
                    _logger.LogWarning("项目数据序列化后为空");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "项目数据序列化失败");
                return false;
            }

            // TODO: 添加更多具体的验证逻辑
            // 例如：检查必需字段、数据格式、业务规则等

            _logger.LogDebug("项目数据验证通过");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证项目数据时发生错误");
            return false;
        }
    }
}
