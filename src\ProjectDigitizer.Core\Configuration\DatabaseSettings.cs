using System.ComponentModel.DataAnnotations;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// 数据库配置类
    /// </summary>
    public class DatabaseSettings
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        [Required]
        public string ConnectionString { get; set; } = "Data Source=ProjectDigitizer.db";

        /// <summary>
        /// 数据库提供程序类型
        /// </summary>
        [Required]
        public DatabaseProvider Provider { get; set; } = DatabaseProvider.SQLite;

        /// <summary>
        /// 命令超时时间（秒）
        /// </summary>
        [Range(1, 300)]
        public int CommandTimeout { get; set; } = 30;

        /// <summary>
        /// 连接池最大大小
        /// </summary>
        [Range(1, 1000)]
        public int MaxPoolSize { get; set; } = 100;

        /// <summary>
        /// 是否启用敏感数据日志记录
        /// </summary>
        public bool EnableSensitiveDataLogging { get; set; } = false;

        /// <summary>
        /// 是否启用详细错误信息
        /// </summary>
        public bool EnableDetailedErrors { get; set; } = false;

        /// <summary>
        /// 是否自动迁移数据库
        /// </summary>
        public bool AutoMigrate { get; set; } = true;

        /// <summary>
        /// 备份配置
        /// </summary>
        public DatabaseBackupSettings Backup { get; set; } = new();
    }

    /// <summary>
    /// 数据库提供程序枚举
    /// </summary>
    public enum DatabaseProvider
    {
        SQLite,
        SqlServer,
        PostgreSQL,
        MySQL
    }

    /// <summary>
    /// 数据库备份配置
    /// </summary>
    public class DatabaseBackupSettings
    {
        /// <summary>
        /// 是否启用自动备份
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 备份目录
        /// </summary>
        public string BackupDirectory { get; set; } = "Backups";

        /// <summary>
        /// 备份间隔（小时）
        /// </summary>
        [Range(1, 168)] // 1小时到1周
        public int IntervalHours { get; set; } = 24;

        /// <summary>
        /// 保留备份文件数量
        /// </summary>
        [Range(1, 100)]
        public int RetainedBackupCount { get; set; } = 7;
    }
}
