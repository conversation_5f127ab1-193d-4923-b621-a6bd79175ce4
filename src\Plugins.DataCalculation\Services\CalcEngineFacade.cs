using System;
using System.Collections.Generic;
using System.Globalization;

namespace Plugins.DataCalculation.Services;

/// <summary>
/// CalcEngine 门面：统一变量注入、文化设置与错误转换，隔离第三方实现细节。
/// </summary>
internal static class CalcEngineFacade
{
    /// <summary>
    /// 计算表达式值。
    /// </summary>
    /// <param name="expression">表达式字符串，支持以 '=' 开头。</param>
    /// <param name="variables">变量字典，注入至 CalcEngine 变量作用域。</param>
    /// <param name="culture">可选文化，默认 InvariantCulture。</param>
    /// <returns>表达式求值结果。</returns>
    public static object? Evaluate(string expression, IReadOnlyDictionary<string, object> variables, CultureInfo? culture = null)
    {
        try
        {
            var engine = new CalcEngine.CalcEngine
            {
                CultureInfo = culture ?? CultureInfo.InvariantCulture
            };

            foreach (var kv in variables)
            {
                engine.Variables[kv.Key] = kv.Value;
            }

            return engine.Evaluate(expression);
        }
        catch (CalcEngine.CalcEngineParseException ex)
        {
            throw new DataCalculationException($"表达式解析错误：{ex.Message}", ex);
        }
        catch (Exception ex)
        {
            throw new DataCalculationException($"表达式计算失败：{ex.Message}", ex);
        }
    }
}

