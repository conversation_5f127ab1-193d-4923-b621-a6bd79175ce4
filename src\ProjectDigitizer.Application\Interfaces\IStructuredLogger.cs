using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 结构化日志记录器接口
/// 支持结构化日志记录和性能监控
/// </summary>
public interface IStructuredLogger
{
    /// <summary>
    /// 记录操作日志
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="parameters">操作参数</param>
    /// <param name="duration">执行时长</param>
    void LogOperation(string operationName, object? parameters, TimeSpan duration);

    /// <summary>
    /// 记录性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="tags">标签信息</param>
    void LogPerformance(string metricName, double value, Dictionary<string, object>? tags = null);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="context">错误上下文</param>
    /// <param name="additionalData">附加数据</param>
    void LogError(Exception exception, string context, object? additionalData = null);

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="properties">结构化属性</param>
    void LogInformation(string message, Dictionary<string, object>? properties = null);

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">警告消息</param>
    /// <param name="properties">结构化属性</param>
    void LogWarning(string message, Dictionary<string, object>? properties = null);

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">调试消息</param>
    /// <param name="properties">结构化属性</param>
    void LogDebug(string message, Dictionary<string, object>? properties = null);

    /// <summary>
    /// 开始操作跟踪
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="properties">初始属性</param>
    /// <returns>操作跟踪器</returns>
    IOperationTracker StartOperation(string operationName, Dictionary<string, object>? properties = null);

    /// <summary>
    /// 异步记录操作日志
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="parameters">操作参数</param>
    /// <param name="duration">执行时长</param>
    /// <returns>记录任务</returns>
    Task LogOperationAsync(string operationName, object? parameters, TimeSpan duration);
}

/// <summary>
/// 操作跟踪器接口
/// 用于跟踪长时间运行的操作
/// </summary>
public interface IOperationTracker : IDisposable
{
    /// <summary>
    /// 操作ID
    /// </summary>
    string OperationId { get; }

    /// <summary>
    /// 操作名称
    /// </summary>
    string OperationName { get; }

    /// <summary>
    /// 添加属性
    /// </summary>
    /// <param name="key">属性键</param>
    /// <param name="value">属性值</param>
    void AddProperty(string key, object value);

    /// <summary>
    /// 记录进度
    /// </summary>
    /// <param name="progress">进度百分比 (0-100)</param>
    /// <param name="message">进度消息</param>
    void LogProgress(double progress, string? message = null);

    /// <summary>
    /// 标记操作成功完成
    /// </summary>
    /// <param name="result">操作结果</param>
    void Complete(object? result = null);

    /// <summary>
    /// 标记操作失败
    /// </summary>
    /// <param name="exception">异常信息</param>
    void Fail(Exception exception);
}
