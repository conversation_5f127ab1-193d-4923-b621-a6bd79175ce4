using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Studio.ViewModels
{
    public class PluginInfoItem
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string[] Modules { get; set; } = Array.Empty<string>();
    }

    public class PluginManagerViewModel
    {
        private readonly IPluginCatalog _catalog;

        public ObservableCollection<PluginInfoItem> Plugins { get; } = new();

        public PluginManagerViewModel(IPluginCatalog catalog)
        {
            _catalog = catalog ?? throw new ArgumentNullException(nameof(catalog));
            Load();
        }

        private void Load()
        {
            Plugins.Clear();
            if (_catalog.Assemblies is not { Count: > 0 }) return;

            foreach (var asm in _catalog.Assemblies)
            {
                try
                {
                    var name = asm.GetName();
                    var modules = asm
                        .GetTypes()
                        .Where(t => !t.IsAbstract && !t.IsInterface && typeof(INodeModule).IsAssignableFrom(t))
                        .Select(t => t.Name)
                        .Distinct()
                        .OrderBy(s => s)
                        .ToArray();

                    var codeBase = asm.Location;
                    Plugins.Add(new PluginInfoItem
                    {
                        Name = name.Name ?? "(unknown)",
                        Version = name.Version?.ToString() ?? string.Empty,
                        Location = codeBase,
                        Modules = modules
                    });
                }
                catch
                {
                    // skip broken plugin
                }
            }
        }
    }
}

