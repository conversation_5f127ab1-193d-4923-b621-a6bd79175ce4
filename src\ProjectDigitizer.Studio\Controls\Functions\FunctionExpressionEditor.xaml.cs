using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.Services;
using ProjectDigitizer.Core.ValueObjects;
using ProjectDigitizer.Studio.Controls.Properties.Services;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls.Functions
{
    /// <summary>
    /// 函数表达式编辑器交互逻辑
    /// </summary>
    public partial class FunctionExpressionEditor : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<FunctionExpression> _functions = new();
        private ObservableCollection<FunctionType> _availableFunctionTypes = new();
        private ObservableCollection<FieldInfo> _availableFields = new();
        private ObservableCollection<IntelliSenseItem> _intelliSenseItems = new();
        private FieldReferenceTracker? _referenceTracker;
        private bool _isIntelliSenseVisible = false;

        /// <summary>函数表达式列表</summary>
        public ObservableCollection<FunctionExpression> Functions
        {
            get => _functions;
            set
            {
                if (SetProperty(ref _functions, value))
                {
                    OnPropertyChanged(nameof(HasFunctions));
                    SubscribeToFunctionChanges();
                }
            }
        }

        /// <summary>可用函数类型</summary>
        public ObservableCollection<FunctionType> AvailableFunctionTypes
        {
            get => _availableFunctionTypes;
            set => SetProperty(ref _availableFunctionTypes, value);
        }

        /// <summary>可用字段列表</summary>
        public ObservableCollection<FieldInfo> AvailableFields
        {
            get => _availableFields;
            set => SetProperty(ref _availableFields, value);
        }

        /// <summary>智能提示项列表</summary>
        public ObservableCollection<IntelliSenseItem> IntelliSenseItems
        {
            get => _intelliSenseItems;
            set => SetProperty(ref _intelliSenseItems, value);
        }

        /// <summary>是否有函数</summary>
        public bool HasFunctions => _functions.Any();

        /// <summary>字段引用追踪器</summary>
        public FieldReferenceTracker? ReferenceTracker
        {
            get => _referenceTracker;
            set => _referenceTracker = value;
        }

        /// <summary>函数变化事件</summary>
        public event EventHandler<FunctionExpression>? FunctionAdded;
        public event EventHandler<FunctionExpression>? FunctionRemoved;
        public event EventHandler<FunctionExpression>? FunctionChanged;

        public FunctionExpressionEditor()
        {
            InitializeComponent();
            DataContext = this;
            InitializeData();
            InitializeEventHandlers();
            // 用更完整的 Excel 函数目录刷新智能提示
            RefreshIntelliSenseFromCatalog();
        }

        private void RefreshIntelliSenseFromCatalog()
        {
            try
            {
                _intelliSenseItems.Clear();
                var items = new List<IntelliSenseItem>();
                foreach (var f in ExcelFunctionCatalog.Functions.OrderBy(x => x.Category).ThenBy(x => x.Name))
                {
                    var icon = "Function";
                    items.Add(new IntelliSenseItem(f.Name, f.DisplayName, f.Description, icon));
                    foreach (var alias in f.Aliases)
                    {
                        items.Add(new IntelliSenseItem(alias, f.DisplayName, f.Description, icon));
                    }
                }
                foreach (var it in items)
                {
                    _intelliSenseItems.Add(it);
                }
            }
            catch
            {
                // 失败时保留原有的基础项
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化可用函数类型
            _availableFunctionTypes = new ObservableCollection<FunctionType>(
                Enum.GetValues<FunctionType>());

            // 初始化智能提示项
            InitializeIntelliSenseItems();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            AddFunctionButton.Click += OnAddFunctionClick;
            ImportTemplateButton.Click += OnImportTemplateClick;
            ValidateAllButton.Click += OnValidateAllClick;
            FormatButton.Click += OnFormatClick;
            HelpButton.Click += OnHelpClick;
        }

        /// <summary>
        /// 订阅函数变化事件
        /// </summary>
        private void SubscribeToFunctionChanges()
        {
            _functions.CollectionChanged += (s, e) =>
            {
                if (e.NewItems != null)
                {
                    foreach (FunctionExpression function in e.NewItems)
                    {
                        function.PropertyChanged += OnFunctionPropertyChanged;
                        FunctionAdded?.Invoke(this, function);
                    }
                }
                if (e.OldItems != null)
                {
                    foreach (FunctionExpression function in e.OldItems)
                    {
                        function.PropertyChanged -= OnFunctionPropertyChanged;
                        FunctionRemoved?.Invoke(this, function);
                    }
                }
                OnPropertyChanged(nameof(HasFunctions));
            };
        }

        /// <summary>
        /// 函数属性变化处理
        /// </summary>
        private void OnFunctionPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (sender is FunctionExpression function)
            {
                if (e.PropertyName == nameof(FunctionExpression.Expression))
                {
                    // 更新字段引用追踪
                    _referenceTracker?.UpdateExpressionReferences(function.Id, function.Expression);

                    // 验证表达式
                    ValidateFunction(function);
                }

                FunctionChanged?.Invoke(this, function);
            }
        }

        /// <summary>
        /// 添加函数
        /// </summary>
        public void AddFunction(FunctionExpression? function = null)
        {
            var newFunction = function ?? new FunctionExpression
            {
                Name = $"函数{_functions.Count + 1}",
                Expression = "",
                Type = FunctionType.Math,
                IsEnabled = true
            };

            _functions.Add(newFunction);

            // 选中新添加的函数标签页
            FunctionTabControl.SelectedItem = newFunction;
        }

        /// <summary>
        /// 移除函数
        /// </summary>
        public void RemoveFunction(FunctionExpression function)
        {
            if (_functions.Contains(function))
            {
                // 移除字段引用追踪
                _referenceTracker?.RemoveExpressionReferences(function.Id);

                _functions.Remove(function);
            }
        }

        /// <summary>
        /// 验证函数
        /// </summary>
        private void ValidateFunction(FunctionExpression function)
        {
            try
            {
                var result = new Core.ValueObjects.ValidationResult();

                // 基本验证
                if (string.IsNullOrWhiteSpace(function.Expression))
                {
                    result.AddWarning("表达式不能为空");
                }
                else
                {
                    // TODO: 实现表达式语法验证
                    // 这里可以集成表达式解析器进行语法检查
                }

                function.ValidationResult = result;
            }
            catch (Exception ex)
            {
                function.ValidationResult = Core.ValueObjects.ValidationResult.Error($"验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证所有函数
        /// </summary>
        private void ValidateAllFunctions()
        {
            foreach (var function in _functions)
            {
                ValidateFunction(function);
            }
        }

        /// <summary>
        /// 初始化智能提示项
        /// </summary>
        private void InitializeIntelliSenseItems()
        {
            _intelliSenseItems.Clear();

            // 添加基础数学函数
            var mathFunctions = new[]
            {
                new IntelliSenseItem("SUM", "求和函数", "计算数值的总和", "Function"),
                new IntelliSenseItem("AVG", "平均值函数", "计算数值的平均值", "Function"),
                new IntelliSenseItem("COUNT", "计数函数", "计算项目数量", "Function"),
                new IntelliSenseItem("MAX", "最大值函数", "找出最大值", "Function"),
                new IntelliSenseItem("MIN", "最小值函数", "找出最小值", "Function"),
                new IntelliSenseItem("ABS", "绝对值函数", "计算绝对值", "Function"),
                new IntelliSenseItem("ROUND", "四舍五入函数", "数值四舍五入", "Function")
            };

            foreach (var item in mathFunctions)
            {
                _intelliSenseItems.Add(item);
            }
        }

        /// <summary>
        /// 显示智能提示
        /// </summary>
        private void ShowIntelliSense(TextBox textBox, string filter = "")
        {
            var filteredItems = string.IsNullOrEmpty(filter)
                ? _intelliSenseItems.ToList()
                : _intelliSenseItems.Where(item =>
                    item.Name.ToLower().Contains(filter.ToLower()) ||
                    item.Description.ToLower().Contains(filter.ToLower())).ToList();

            if (filteredItems.Any())
            {
                IntelliSenseListBox.ItemsSource = filteredItems;
                IntelliSensePopup.PlacementTarget = textBox;
                IntelliSensePopup.IsOpen = true;
                _isIntelliSenseVisible = true;
            }
        }

        /// <summary>
        /// 隐藏智能提示
        /// </summary>
        private void HideIntelliSense()
        {
            IntelliSensePopup.IsOpen = false;
            _isIntelliSenseVisible = false;
        }

        /// <summary>
        /// 添加函数按钮点击
        /// </summary>
        private void OnAddFunctionClick(object sender, RoutedEventArgs e)
        {
            AddFunction();
        }

        /// <summary>
        /// 删除函数按钮点击
        /// </summary>
        private void OnDeleteFunctionClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is FunctionExpression function)
            {
                if (MessageBox.Show($"确定要删除函数 '{function.Name}' 吗？", "确认删除",
                    MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
                {
                    RemoveFunction(function);
                }
            }
        }

        /// <summary>
        /// 导入模板按钮点击
        /// </summary>
        private void OnImportTemplateClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var selector = new FormulaTemplateSelector();
                var window = new Window
                {
                    Title = "选择公式模板",
                    Content = selector,
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.CanResize
                };

                selector.FormulaSelected += (s, template) =>
                {
                    // 将模板转换为一条函数表达式并添加
                    var func = new FunctionExpression
                    {
                        Name = template.Name,
                        Expression = template.Expression,
                        Type = FunctionType.Math,
                        IsEnabled = true
                    };
                    AddFunction(func);
                    window.Close();
                };

                selector.SelectionCancelled += (s, _)
                    => window.Close();

                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开公式模板选择器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 验证所有按钮点击
        /// </summary>
        private void OnValidateAllClick(object sender, RoutedEventArgs e)
        {
            ValidateAllFunctions();
            System.Diagnostics.Debug.WriteLine("验证完成");
        }

        /// <summary>
        /// 格式化按钮点击
        /// </summary>
        private void OnFormatClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现表达式格式化功能
            System.Diagnostics.Debug.WriteLine("表达式格式化功能开发中...");
        }

        /// <summary>
        /// 帮助按钮点击
        /// </summary>
        private void OnHelpClick(object sender, RoutedEventArgs e)
        {
            // TODO: 显示函数帮助文档
            var helpText = "函数表达式帮助:\n\n" +
                          "1. 基础数学函数: SUM, AVG, COUNT, MAX, MIN\n" +
                          "2. 字段引用: 使用 {字段名} 或 [字段名]\n" +
                          "3. 运算符: +, -, *, /, %, ^, ==, !=, <, >, <=, >=\n" +
                          "4. 逻辑运算: AND, OR, NOT\n" +
                          "5. 条件表达式: IF(条件, 真值, 假值)\n\n" +
                          "示例:\n" +
                          "SUM({数量} * {单价})\n" +
                          "IF({分数} > 60, \"及格\", \"不及格\")\n" +
                          "AVG({value1}, {value2}, {value3})";

            System.Diagnostics.Debug.WriteLine($"函数帮助: {helpText}");
        }

        /// <summary>
        /// 表达式文本变化处理
        /// </summary>
        private void OnExpressionTextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 检查是否需要显示智能提示
                var caretIndex = textBox.CaretIndex;
                var text = textBox.Text;

                if (caretIndex > 0 && text.Length > caretIndex - 1)
                {
                    var currentChar = text[caretIndex - 1];
                    if (char.IsLetter(currentChar))
                    {
                        // 获取当前单词
                        var wordStart = caretIndex - 1;
                        while (wordStart > 0 && char.IsLetterOrDigit(text[wordStart - 1]))
                        {
                            wordStart--;
                        }

                        var currentWord = text.Substring(wordStart, caretIndex - wordStart);
                        if (currentWord.Length >= 2)
                        {
                            ShowIntelliSense(textBox, currentWord);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 表达式按键处理
        /// </summary>
        private void OnExpressionKeyDown(object sender, KeyEventArgs e)
        {
            if (_isIntelliSenseVisible)
            {
                switch (e.Key)
                {
                    case Key.Escape:
                        HideIntelliSense();
                        e.Handled = true;
                        break;
                    case Key.Enter:
                    case Key.Tab:
                        // 插入选中的智能提示项
                        if (IntelliSenseListBox.SelectedItem is IntelliSenseItem selectedItem)
                        {
                            InsertIntelliSenseItem(sender as TextBox, selectedItem);
                        }
                        HideIntelliSense();
                        e.Handled = true;
                        break;
                    case Key.Up:
                    case Key.Down:
                        // 将按键传递给智能提示列表
                        IntelliSenseListBox.Focus();
                        var keyEventArgs = new KeyEventArgs(e.KeyboardDevice, e.InputSource, e.Timestamp, e.Key)
                        {
                            RoutedEvent = KeyDownEvent
                        };
                        IntelliSenseListBox.RaiseEvent(keyEventArgs);
                        e.Handled = true;
                        break;
                }
            }
            else if (e.Key == Key.F1)
            {
                OnHelpClick(sender, e);
                e.Handled = true;
            }
        }

        /// <summary>
        /// 智能提示按键处理
        /// </summary>
        private void OnIntelliSenseKeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Enter:
                case Key.Tab:
                    if (IntelliSenseListBox.SelectedItem is IntelliSenseItem selectedItem)
                    {
                        InsertIntelliSenseItem(FunctionTabControl.SelectedContent as TextBox, selectedItem);
                    }
                    HideIntelliSense();
                    e.Handled = true;
                    break;
                case Key.Escape:
                    HideIntelliSense();
                    e.Handled = true;
                    break;
            }
        }

        /// <summary>
        /// 鼠标双击插入函数
        /// </summary>
        private void OnIntelliSenseMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (IntelliSenseListBox.SelectedItem is IntelliSenseItem selectedItem)
            {
                InsertIntelliSenseItem(GetCurrentTextBox(), selectedItem);
                HideIntelliSense();
                e.Handled = true;
            }
        }

        /// <summary>
        /// 单击选择（不关闭弹窗，便于滚动与再次选择）；再次点击或按回车可插入
        /// </summary>
        private void OnIntelliSenseMouseUp(object sender, MouseButtonEventArgs e)
        {
            // 仅设置选中，不立即关闭
            if (sender is ListBox lb && lb.SelectedItem is IntelliSenseItem item)
            {
                // 将焦点回到文本框，便于继续输入
                GetCurrentTextBox()?.Focus();
            }
        }

        /// <summary>
        /// 输入框筛选（来自弹窗内的搜索框）
        /// </summary>
        private void OnIntelliSenseSearchTextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox tb)
            {
                var keyword = tb.Text?.Trim() ?? string.Empty;
                var items = string.IsNullOrEmpty(keyword)
                    ? _intelliSenseItems.ToList()
                    : _intelliSenseItems.Where(x =>
                        x.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                        x.DisplayName.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                        x.Description.Contains(keyword, StringComparison.OrdinalIgnoreCase)).ToList();
                IntelliSenseListBox.ItemsSource = items;
            }
        }

        /// <summary>
        /// 插入智能提示项
        /// </summary>
        private void InsertIntelliSenseItem(TextBox? textBox, IntelliSenseItem item)
        {
            if (textBox == null) return;

            var caretIndex = textBox.CaretIndex;
            var text = textBox.Text;

            // 找到当前单词的开始位置
            var wordStart = caretIndex;
            while (wordStart > 0 && char.IsLetterOrDigit(text[wordStart - 1]))
            {
                wordStart--;
            }

            // 替换当前单词
            var newText = text.Substring(0, wordStart) + item.Name + text.Substring(caretIndex);
            textBox.Text = newText;
            textBox.CaretIndex = wordStart + item.Name.Length;
        }

        /// <summary>
        /// 插入函数按钮点击
        /// </summary>
        private void OnInsertFunctionClick(object sender, RoutedEventArgs e)
        {
            var textBox = GetCurrentTextBox();
            if (textBox != null)
                ShowIntelliSense(textBox);
        }

        /// <summary>
        /// 插入字段按钮点击
        /// </summary>
        private void OnInsertFieldClick(object sender, RoutedEventArgs e)
        {
            var textBox = GetCurrentTextBox();
            if (textBox != null && _availableFields.Any())
            {
                // 创建字段选择菜单
                var contextMenu = new ContextMenu();
                foreach (var field in _availableFields)
                {
                    var menuItem = new MenuItem
                    {
                        Header = field.DisplayName,
                        Tag = field
                    };
                    menuItem.Click += (s, args) =>
                    {
                        if (s is MenuItem item && item.Tag is FieldInfo selectedField)
                        {
                            InsertFieldReference(textBox, selectedField);
                        }
                    };
                    contextMenu.Items.Add(menuItem);
                }

                contextMenu.PlacementTarget = textBox;
                contextMenu.IsOpen = true;
            }
        }

        /// <summary>
        /// 插入括号按钮点击
        /// </summary>
        private void OnInsertParenthesesClick(object sender, RoutedEventArgs e)
        {
            var textBox = GetCurrentTextBox();
            if (textBox != null)
            {
                var caretIndex = textBox.CaretIndex;
                textBox.Text = textBox.Text.Insert(caretIndex, "()");
                textBox.CaretIndex = caretIndex + 1;
            }
        }

        /// <summary>
        /// 测试表达式按钮点击
        /// </summary>
        private void OnTestExpressionClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现表达式测试功能
            System.Diagnostics.Debug.WriteLine("表达式测试功能开发中...");
        }

        /// <summary>
        /// 获取当前活动的文本框
        /// </summary>
        private TextBox? GetCurrentTextBox()
        {
            // 从当前选中的标签页中查找表达式文本框
            if (FunctionTabControl.SelectedContent is FrameworkElement content)
            {
                return FindChild<TextBox>(content, "ExpressionTextBox");
            }
            return null;
        }

        /// <summary>
        /// 查找子控件
        /// </summary>
        private T? FindChild<T>(DependencyObject parent, string childName) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T typedChild && (child as FrameworkElement)?.Name == childName)
                {
                    return typedChild;
                }

                var foundChild = FindChild<T>(child, childName);
                if (foundChild != null)
                {
                    return foundChild;
                }
            }

            return null;
        }

        /// <summary>
        /// 插入字段引用
        /// </summary>
        private void InsertFieldReference(TextBox textBox, FieldInfo field)
        {
            var caretIndex = textBox.CaretIndex;
            var fieldReference = $"{{{field.Name}}}";
            textBox.Text = textBox.Text.Insert(caretIndex, fieldReference);
            textBox.CaretIndex = caretIndex + fieldReference.Length;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 智能提示项
    /// </summary>
    public class IntelliSenseItem
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;

        public IntelliSenseItem(string name, string displayName, string description, string icon)
        {
            Name = name;
            DisplayName = displayName;
            Description = description;
            Icon = icon;
        }
    }
}
