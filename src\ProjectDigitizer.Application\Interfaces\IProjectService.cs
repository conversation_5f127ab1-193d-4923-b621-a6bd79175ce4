using System.Threading.Tasks;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 项目管理服务接口
/// </summary>
public interface IProjectService : IApplicationService
{
    /// <summary>
    /// 保存项目
    /// </summary>
    /// <param name="projectData">项目数据</param>
    /// <param name="filePath">文件路径</param>
    Task SaveProjectAsync(object projectData, string filePath);

    /// <summary>
    /// 加载项目
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>项目数据</returns>
    Task<object> LoadProjectAsync(string filePath);

    /// <summary>
    /// 验证项目数据
    /// </summary>
    /// <param name="projectData">项目数据</param>
    /// <returns>验证结果</returns>
    bool ValidateProject(object projectData);
}
