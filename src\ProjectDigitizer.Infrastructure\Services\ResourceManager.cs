using System.Collections.Concurrent;

using Microsoft.Extensions.Logging;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 资源管理器接口
/// </summary>
public interface IResourceManager : IDisposable
{
    /// <summary>
    /// 注册可释放资源
    /// </summary>
    /// <param name="resource">资源</param>
    /// <param name="resourceName">资源名称</param>
    void RegisterResource(IDisposable resource, string? resourceName = null);

    /// <summary>
    /// 注册资源释放回调
    /// </summary>
    /// <param name="disposeAction">释放动作</param>
    /// <param name="resourceName">资源名称</param>
    void RegisterDisposeAction(Action disposeAction, string? resourceName = null);

    /// <summary>
    /// 释放指定资源
    /// </summary>
    /// <param name="resource">资源</param>
    void DisposeResource(IDisposable resource);

    /// <summary>
    /// 释放所有资源
    /// </summary>
    void DisposeAllResources();

    /// <summary>
    /// 获取资源统计信息
    /// </summary>
    /// <returns>资源统计</returns>
    ResourceStatistics GetStatistics();
}

/// <summary>
/// 资源统计信息
/// </summary>
public class ResourceStatistics
{
    /// <summary>
    /// 注册的资源数量
    /// </summary>
    public int RegisteredResourcesCount { get; set; }

    /// <summary>
    /// 已释放的资源数量
    /// </summary>
    public int DisposedResourcesCount { get; set; }

    /// <summary>
    /// 释放失败的资源数量
    /// </summary>
    public int FailedDisposalsCount { get; set; }

    /// <summary>
    /// 资源类型统计
    /// </summary>
    public Dictionary<string, int> ResourceTypeCount { get; set; } = new();
}

/// <summary>
/// 资源管理器实现
/// </summary>
public class ResourceManager : IResourceManager
{
    private readonly ILogger<ResourceManager> _logger;
    private readonly ConcurrentDictionary<IDisposable, string> _resources;
    private readonly ConcurrentQueue<(Action Action, string Name)> _disposeActions;
    private readonly object _lockObject = new();
    private int _disposedCount;
    private int _failedCount;
    private bool _disposed;

    public ResourceManager(ILogger<ResourceManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _resources = new ConcurrentDictionary<IDisposable, string>();
        _disposeActions = new ConcurrentQueue<(Action, string)>();
    }

    /// <inheritdoc />
    public void RegisterResource(IDisposable resource, string? resourceName = null)
    {
        if (resource == null)
            throw new ArgumentNullException(nameof(resource));

        if (_disposed)
        {
            _logger.LogWarning("尝试在已释放的资源管理器中注册资源 {ResourceName}", resourceName ?? resource.GetType().Name);
            return;
        }

        var name = resourceName ?? resource.GetType().Name;
        _resources.TryAdd(resource, name);

        _logger.LogDebug("已注册资源 {ResourceName} ({ResourceType})", name, resource.GetType().Name);
    }

    /// <inheritdoc />
    public void RegisterDisposeAction(Action disposeAction, string? resourceName = null)
    {
        if (disposeAction == null)
            throw new ArgumentNullException(nameof(disposeAction));

        if (_disposed)
        {
            _logger.LogWarning("尝试在已释放的资源管理器中注册释放动作 {ResourceName}", resourceName ?? "Unknown");
            return;
        }

        var name = resourceName ?? "CustomAction";
        _disposeActions.Enqueue((disposeAction, name));

        _logger.LogDebug("已注册释放动作 {ActionName}", name);
    }

    /// <inheritdoc />
    public void DisposeResource(IDisposable resource)
    {
        if (resource == null)
            return;

        if (_resources.TryRemove(resource, out var resourceName))
        {
            DisposeResourceInternal(resource, resourceName);
        }
    }

    /// <inheritdoc />
    public void DisposeAllResources()
    {
        lock (_lockObject)
        {
            if (_disposed)
                return;

            _logger.LogInformation("开始释放所有资源，共 {ResourceCount} 个资源和 {ActionCount} 个释放动作",
                _resources.Count, _disposeActions.Count);

            // 释放注册的资源
            var resources = _resources.ToArray();
            foreach (var kvp in resources)
            {
                DisposeResourceInternal(kvp.Key, kvp.Value);
                _resources.TryRemove(kvp.Key, out _);
            }

            // 执行释放动作
            while (_disposeActions.TryDequeue(out var actionInfo))
            {
                try
                {
                    actionInfo.Action();
                    Interlocked.Increment(ref _disposedCount);
                    _logger.LogDebug("已执行释放动作 {ActionName}", actionInfo.Name);
                }
                catch (Exception ex)
                {
                    Interlocked.Increment(ref _failedCount);
                    _logger.LogError(ex, "执行释放动作 {ActionName} 时发生错误", actionInfo.Name);
                }
            }

            _logger.LogInformation("资源释放完成，成功: {SuccessCount}，失败: {FailedCount}",
                _disposedCount, _failedCount);
        }
    }

    /// <inheritdoc />
    public ResourceStatistics GetStatistics()
    {
        var resourceTypes = _resources.Values
            .GroupBy(name => name)
            .ToDictionary(g => g.Key, g => g.Count());

        return new ResourceStatistics
        {
            RegisteredResourcesCount = _resources.Count + _disposeActions.Count,
            DisposedResourcesCount = _disposedCount,
            FailedDisposalsCount = _failedCount,
            ResourceTypeCount = resourceTypes
        };
    }

    /// <summary>
    /// 内部资源释放方法
    /// </summary>
    private void DisposeResourceInternal(IDisposable resource, string resourceName)
    {
        try
        {
            resource.Dispose();
            Interlocked.Increment(ref _disposedCount);
            _logger.LogDebug("已释放资源 {ResourceName}", resourceName);
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _failedCount);
            _logger.LogError(ex, "释放资源 {ResourceName} 时发生错误", resourceName);
        }
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        DisposeAllResources();
        _disposed = true;

        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~ResourceManager()
    {
        if (!_disposed)
        {
            _logger.LogWarning("ResourceManager 未正确释放，在析构函数中进行清理");
            Dispose();
        }
    }
}
