namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 命令处理器接口
/// </summary>
/// <typeparam name="T">命令类型</typeparam>
public interface ICommandHandler<in T> where T : class
{
    /// <summary>
    /// 处理命令
    /// </summary>
    /// <param name="command">命令对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleAsync(T command, CancellationToken cancellationToken = default);
}
