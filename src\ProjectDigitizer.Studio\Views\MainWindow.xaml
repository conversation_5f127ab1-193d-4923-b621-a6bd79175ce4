<ui:FluentWindow
    Background="{StaticResource Brush.SurfaceVariant}"
    Height="768"
    Title="ProjectDigitizer Studio"
    Width="1220"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d"
    x:Class="ProjectDigitizer.Studio.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:canvasControls="clr-namespace:ProjectDigitizer.Studio.Controls.Canvas"
    xmlns:commonControls="clr-namespace:ProjectDigitizer.Studio.Controls.Common"
    xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:ProjectDigitizer.Studio.Models"
    xmlns:nodify="clr-namespace:Nodify;assembly=Nodify"
    xmlns:propertiesControls="clr-namespace:ProjectDigitizer.Studio.Controls.Properties"
    xmlns:selectors="clr-namespace:ProjectDigitizer.Studio.Selectors"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:viewModels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ui:FluentWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/NodeTemplates.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  ToolBar 在深色下默认样式过亮，这里统一背景和边框  -->
            <Style TargetType="ToolBar">
                <Setter Property="Background" Value="{StaticResource Brush.SurfaceVariant}" />
                <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}" />
                <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
            </Style>
            <!--  Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:BooleanToGridLengthConverter x:Key="BooleanToGridLengthConverter" />
            <converters:BooleanToAngleConverter x:Key="BooleanToAngleConverter" />
            <converters:MidPointXConverter x:Key="MidPointXConverter" />
            <converters:MidPointYConverter x:Key="MidPointYConverter" />
            <converters:GreaterThanConverter x:Key="GreaterThanConverter" />

            <!--  Node/Connection template selectors  -->
            <selectors:NodeTypeDataTemplateSelector
                ControlNodeTemplate="{StaticResource NodeTemplate}"
                DefaultTemplate="{StaticResource NodeTemplate}"
                InputNodeTemplate="{StaticResource NodeTemplate}"
                OutputNodeTemplate="{StaticResource NodeTemplate}"
                TransformNodeTemplate="{StaticResource NodeTemplate}"
                x:Key="NodeTemplateSelector" />
            <selectors:ConnectionTemplateSelector
                BezierConnectionTemplate="{StaticResource BezierConnectionTemplate}"
                CircuitConnectionTemplate="{StaticResource CircuitConnectionTemplate}"
                DefaultTemplate="{StaticResource DefaultConnectionTemplate}"
                LineConnectionTemplate="{StaticResource LineConnectionTemplate}"
                StepConnectionTemplate="{StaticResource StepConnectionTemplate}"
                x:Key="ConnectionTemplateSelector" />

            <!--  Toolbar variants  -->
            <Style
                BasedOn="{StaticResource Button.Text}"
                TargetType="Button"
                x:Key="Toolbar.Button.Flat">
                <Setter Property="FontSize" Value="13" />
                <Setter Property="Padding" Value="12,6" />
                <Setter Property="MinHeight" Value="36" />
                <Setter Property="Margin" Value="0,0,8,0" />
            </Style>

            <!--  Grid brushes  -->
            <GeometryDrawing
                Brush="{StaticResource Brush.Text/40}"
                Geometry="M0,0 L0,1 0.03,1 0.03,0.03 1,0.03 1,0 Z"
                x:Key="SmallGridGeometry" />
            <GeometryDrawing
                Brush="{StaticResource Brush.Text/60}"
                Geometry="M0,0 L0,1 0.015,1 0.015,0.015 1,0.015 1,0 Z"
                x:Key="LargeGridGeometry" />
            <DrawingBrush
                Drawing="{StaticResource SmallGridGeometry}"
                TileMode="Tile"
                Viewport="0 0 20 20"
                ViewportUnits="Absolute"
                x:Key="SmallGridDrawingBrush">
                <DrawingBrush.Transform>
                    <Binding ElementName="Canvas" Path="ViewportTransform" />
                </DrawingBrush.Transform>
            </DrawingBrush>
            <DrawingBrush
                Drawing="{StaticResource LargeGridGeometry}"
                Opacity="0.5"
                TileMode="Tile"
                Viewport="0 0 100 100"
                ViewportUnits="Absolute"
                x:Key="LargeGridDrawingBrush">
                <DrawingBrush.Transform>
                    <Binding ElementName="Canvas" Path="ViewportTransform" />
                </DrawingBrush.Transform>
            </DrawingBrush>
        </ResourceDictionary>
    </ui:FluentWindow.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <!--  TitleBar  -->
        <ui:TitleBar Grid.Row="0" Title="ProjectDigitizer Studio" />

        <!--  Ribbon-like menu bar below TitleBar  -->
        <Border
            Background="{StaticResource Brush.SurfaceVariant}"
            BorderBrush="{StaticResource Brush.Border}"
            BorderThickness="0,0,0,1"
            Grid.Row="1"
            Padding="4,2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Menu
                    Background="Transparent"
                    Foreground="{StaticResource Brush.Text}"
                    Grid.Column="0"
                    VerticalAlignment="Center">
                    <Menu.ItemContainerStyle>
                        <Style BasedOn="{StaticResource {x:Type MenuItem}}" TargetType="MenuItem">
                            <Setter Property="Padding" Value="12,6" />
                            <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
                        </Style>
                    </Menu.ItemContainerStyle>
                    <MenuItem Header="文件">
                        <MenuItem Header="新建项目">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="DocumentAdd24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="保存项目">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Save24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="另存为项目">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="SaveCopy24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <Separator />
                        <MenuItem Header="退出">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Dismiss24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="模板">
                        <MenuItem Header="导入本地模板">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowUpload24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="导入云端模板">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="CloudArrowDown24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="存为本地模板">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Save24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="存为云端模板">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="CloudArrowUp24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="数据">
                        <MenuItem Header="CAD数据导入">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Cube24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="数据库数据导入">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Database24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="表格数据导入">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Table24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="项目信息">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Info24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="材料表">
                        <MenuItem Header="导出准备">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="ArrowExport24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="生成材料表">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Table24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="导出到CAD">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Cube24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="导出为excel文件">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="DocumentArrowDown24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="工具">
                        <MenuItem Header="导入批注">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="CommentAdd24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <Separator />
                        <MenuItem Click="OpenPluginManager_Click" Header="插件管理...">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="PuzzleCube24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <Separator />
                        <MenuItem Click="OpenTelerikTemplateDesigner_Click" Header="Telerik模板设计器">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Document24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                    <MenuItem Header="帮助">
                        <MenuItem Header="用户手册">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="BookOpen24" />
                            </MenuItem.Icon>
                        </MenuItem>
                        <MenuItem Header="关于">
                            <MenuItem.Icon>
                                <ui:SymbolIcon Symbol="Info24" />
                            </MenuItem.Icon>
                        </MenuItem>
                    </MenuItem>
                </Menu>
                <!--  Right side: theme toggle  -->
                <StackPanel
                    Grid.Column="1"
                    Orientation="Horizontal"
                    VerticalAlignment="Center">
                    <TextBlock
                        Foreground="{StaticResource Brush.TextSecondary}"
                        Margin="0,0,6,0"
                        Text="深色"
                        VerticalAlignment="Center" />
                    <ui:ToggleSwitch
                        Checked="ThemeToggle_Checked"
                        Unchecked="ThemeToggle_Unchecked"
                        x:Name="ThemeToggle" />
                </StackPanel>
            </Grid>
        </Border>

        <!--  Main content with three columns  -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="{Binding IsPropertyPanelVisible, Converter={StaticResource BooleanToGridLengthConverter}}" />
            </Grid.ColumnDefinitions>

            <!--  Left: module library  -->
            <Border
                Background="{StaticResource Brush.Surface}"
                CornerRadius="6"
                Grid.Column="0"
                Margin="8">
                <DockPanel Background="{StaticResource Brush.Surface}">
                    <Border
                        Background="{StaticResource Brush.Surface}"
                        BorderBrush="{StaticResource Brush.Border}"
                        BorderThickness="0,0,0,1"
                        DockPanel.Dock="Top"
                        Padding="12">
                        <StackPanel>
                            <TextBlock
                                FontSize="18"
                                FontWeight="Medium"
                                Foreground="{StaticResource Brush.Text}"
                                Text="工作流模块库" />
                            <TextBlock
                                FontSize="12"
                                Foreground="{StaticResource Brush.TextSecondary}"
                                Margin="0,4,0,0"
                                Opacity="0.7"
                                Text="拖拽模块到画布创建工作流" />
                            <ui:TextBox
                                Icon="Search24"
                                Margin="0,8,0,0"
                                PlaceholderText="搜索模块" />
                        </StackPanel>
                    </Border>

                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding TemplateCategories}" Margin="12">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        Background="{StaticResource Brush.Surface}"
                                        CornerRadius="6"
                                        Margin="0,0,0,16">
                                        <StackPanel>
                                            <Border
                                                Background="{StaticResource Brush.SurfaceVariant}"
                                                BorderBrush="{StaticResource Brush.Border}"
                                                BorderThickness="0,0,0,1"
                                                Padding="12,8">
                                                <StackPanel Orientation="Horizontal">
                                                    <ipack:PackIconMaterial
                                                        Foreground="{StaticResource Brush.Text}"
                                                        Height="20"
                                                        Kind="Widgets"
                                                        Width="20" />
                                                    <TextBlock
                                                        FontSize="14"
                                                        FontWeight="Medium"
                                                        Foreground="{StaticResource Brush.Text}"
                                                        Text="{Binding Name}" />
                                                </StackPanel>
                                            </Border>
                                            <ItemsControl ItemsSource="{Binding Items}" Margin="8">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border
                                                            Background="Transparent"
                                                            Cursor="Hand"
                                                            Margin="0,2"
                                                            MouseDown="TemplateItem_MouseDown"
                                                            MouseMove="TemplateItem_MouseMove"
                                                            MouseUp="TemplateItem_MouseUp"
                                                            Padding="12,8"
                                                            ToolTip="{Binding Description}">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="Background" Value="Transparent" />
                                                                    <Style.Triggers>
                                                                        <Trigger Property="IsMouseOver" Value="True">
                                                                            <Setter Property="Background" Value="{StaticResource Brush.Primary/08}" />
                                                                        </Trigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="*" />
                                                                </Grid.ColumnDefinitions>
                                                                <Border
                                                                    CornerRadius="4,0,0,4"
                                                                    Grid.Column="0"
                                                                    Height="32"
                                                                    Margin="0,0,12,0"
                                                                    Width="8">
                                                                    <Border.Background>
                                                                        <SolidColorBrush Color="{Binding DataContext.Color, RelativeSource={RelativeSource AncestorType=ItemsControl}}" />
                                                                    </Border.Background>
                                                                </Border>
                                                                <StackPanel Grid.Column="1">
                                                                    <TextBlock
                                                                        FontSize="13"
                                                                        FontWeight="Medium"
                                                                        Text="{Binding Name}" />
                                                                    <TextBlock
                                                                        FontSize="11"
                                                                        Foreground="{StaticResource Brush.TextSecondary}"
                                                                        Margin="0,2,0,0"
                                                                        Text="{Binding Description}"
                                                                        TextWrapping="Wrap" />
                                                                </StackPanel>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </DockPanel>
            </Border>

            <!--  Center: canvas tabs  -->
            <TabControl Grid.Column="1" Margin="5">
                <TabItem Header="模板">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <ToolBar Grid.Row="0">
                            <Button
                                Click="BtnEnableAll_Click"
                                Content="全部启用"
                                Style="{StaticResource Toolbar.Button.Flat}"
                                ToolTip="启用所有模板"
                                x:Name="BtnEnableAllCanvas" />
                            <Button
                                Click="BtnDisableAll_Click"
                                Content="全部关闭"
                                Style="{StaticResource Toolbar.Button.Flat}"
                                ToolTip="关闭所有模板"
                                x:Name="BtnDisableAllCanvas" />
                            <Button
                                Click="BtnClearCanvas_Click"
                                Content="清空画布"
                                Style="{StaticResource Toolbar.Button.Flat}"
                                ToolTip="清空当前画布"
                                x:Name="BtnClearCanvasArea" />
                            <Button
                                Click="BtnDeleteSelected_Click"
                                Content="删除选中"
                                Style="{StaticResource Toolbar.Button.Flat}"
                                ToolTip="删除所选项"
                                x:Name="BtnDeleteSelectedCanvas" />
                            <Separator />
                            <Button
                                Click="BtnHideClosedModules_Click"
                                Content="隐藏关闭的模板"
                                ToolTip="隐藏所有关闭的模板"
                                x:Name="BtnHideClosedModulesCanvas" />
                            <Separator />
                            <Button
                                Click="BtnAutoLayoutHierarchical_Click"
                                Content="自动布局"
                                Style="{StaticResource Button.Secondary}"
                                ToolTip="按照数据流方向自动排列节点，支持多行换行"
                                x:Name="BtnAutoLayoutHierarchical" />
                            <Separator />
                            <Label
                                Content="连线样式:"
                                Margin="5,0"
                                VerticalAlignment="Center" />
                            <ComboBox
                                SelectedValue="{Binding CurrentConnectionLineStyle, Mode=TwoWay}"
                                SelectedValuePath="Value"
                                ToolTip="选择连接线的显示样式"
                                VerticalAlignment="Center"
                                Width="120"
                                x:Name="ConnectionStyleComboBox">
                                <ComboBox.ItemsSource>
                                    <x:Array Type="{x:Type models:ConnectionLineStyleItem}">
                                        <models:ConnectionLineStyleItem
                                            DisplayName="贝塞尔曲线"
                                            Icon="〰️"
                                            Value="Bezier" />
                                        <models:ConnectionLineStyleItem
                                            DisplayName="阶梯线"
                                            Icon="🔲"
                                            Value="Step" />
                                    </x:Array>
                                </ComboBox.ItemsSource>
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock
                                                Margin="0,0,5,0"
                                                Text="{Binding Icon}"
                                                VerticalAlignment="Center" />
                                            <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center" />
                                        </StackPanel>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </ToolBar>

                        <Grid Background="{StaticResource Brush.Surface}" Grid.Row="1">
                            <Grid Background="{StaticResource SmallGridDrawingBrush}" />
                            <Grid Background="{StaticResource LargeGridDrawingBrush}" />

                            <nodify:NodifyEditor
                                AllowDrop="True"
                                Background="Transparent"
                                ConnectionCompletedCommand="{Binding CreateConnectionCommand}"
                                Connections="{Binding Connections}"
                                DisconnectConnectorCommand="{Binding DisconnectConnectorCommand}"
                                DragOver="Canvas_DragOver"
                                Drop="Canvas_Drop"
                                EnableRealtimeSelection="True"
                                ItemTemplateSelector="{StaticResource NodeTemplateSelector}"
                                ItemsSource="{Binding Nodes}"
                                PendingConnection="{Binding PendingConnection}"
                                PreviewMouseDown="Canvas_PreviewMouseDown"
                                RemoveConnectionCommand="{Binding RemoveConnectionCommand}"
                                SelectedItems="{x:Null}"
                                SelectionChanged="Canvas_SelectionChanged"
                                ViewportUpdated="Canvas_ViewportUpdated"
                                x:Name="Canvas">
                                <nodify:NodifyEditor.ConnectionTemplate>
                                    <DataTemplate>
                                        <Canvas>
                                            <nodify:LineConnection
                                                DirectionalArrowsCount="0"
                                                MouseRightButtonDown="Connection_MouseRightButtonDown"
                                                Source="{Binding Source.Anchor}"
                                                Stroke="Transparent"
                                                StrokeThickness="8"
                                                Target="{Binding Target.Anchor}"
                                                x:Name="HitTestConnection" />
                                            <ContentPresenter Content="{Binding}" ContentTemplateSelector="{StaticResource ConnectionTemplateSelector}" />
                                            <nodify:LineConnection
                                                DirectionalArrowsCount="0"
                                                IsHitTestVisible="False"
                                                Opacity="0.85"
                                                Source="{Binding Source.Anchor}"
                                                Stroke="{Binding ConnectionColor}"
                                                StrokeThickness="2.5"
                                                Target="{Binding Target.Anchor}"
                                                Visibility="Collapsed"
                                                x:Name="VisualConnection">
                                                <nodify:LineConnection.Style>
                                                    <Style TargetType="nodify:LineConnection">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                <Setter Property="Stroke" Value="{StaticResource Brush.Text/60}" />
                                                                <Setter Property="Opacity" Value="0.4" />
                                                                <Setter Property="StrokeDashArray" Value="6,4" />
                                                            </DataTrigger>
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}" Value="True" />
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsMouseOver}" Value="True" />
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke" Value="{StaticResource Brush.PrimaryVariant}" />
                                                                <Setter Property="StrokeThickness" Value="3.5" />
                                                                <Setter Property="Opacity" Value="1.0" />
                                                            </MultiDataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </nodify:LineConnection.Style>
                                            </nodify:LineConnection>
                                            <Border
                                                Background="{Binding ConnectionColor}"
                                                BorderBrush="White"
                                                BorderThickness="2"
                                                CornerRadius="8"
                                                Height="16"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Visibility="{Binding Source.HasMultipleConnections, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                Width="16"
                                                x:Name="ConnectionIndexBadge">
                                                <TextBlock
                                                    FontSize="9"
                                                    FontWeight="Bold"
                                                    Foreground="White"
                                                    HorizontalAlignment="Center"
                                                    Text="{Binding ConnectionIndex}"
                                                    VerticalAlignment="Center" />
                                            </Border>
                                            <Border
                                                Background="{Binding ConnectionColor}"
                                                BorderBrush="White"
                                                BorderThickness="2"
                                                CornerRadius="8"
                                                Height="16"
                                                Width="16">
                                                <Border.RenderTransform>
                                                    <TranslateTransform>
                                                        <TranslateTransform.X>
                                                            <MultiBinding Converter="{StaticResource MidPointXConverter}">
                                                                <Binding Path="Source.Anchor.X" />
                                                                <Binding Path="Target.Anchor.X" />
                                                            </MultiBinding>
                                                        </TranslateTransform.X>
                                                        <TranslateTransform.Y>
                                                            <MultiBinding Converter="{StaticResource MidPointYConverter}">
                                                                <Binding Path="Source.Anchor.Y" />
                                                                <Binding Path="Target.Anchor.Y" />
                                                            </MultiBinding>
                                                        </TranslateTransform.Y>
                                                    </TranslateTransform>
                                                </Border.RenderTransform>
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Visibility" Value="Collapsed" />
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Source.ConnectionCount, Converter={StaticResource GreaterThanConverter}, ConverterParameter=1}" Value="True">
                                                                <Setter Property="Visibility" Value="Visible" />
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock
                                                    FontSize="9"
                                                    FontWeight="Bold"
                                                    Foreground="White"
                                                    HorizontalAlignment="Center"
                                                    Text="{Binding ConnectionIndex}"
                                                    VerticalAlignment="Center" />
                                            </Border>
                                        </Canvas>
                                    </DataTemplate>
                                </nodify:NodifyEditor.ConnectionTemplate>
                                <nodify:NodifyEditor.PendingConnectionTemplate>
                                    <DataTemplate>
                                        <nodify:PendingConnection
                                            AllowOnlyConnectors="True"
                                            CompletedCommand="{Binding FinishCommand}"
                                            Opacity="0.8"
                                            StartedCommand="{Binding StartCommand}"
                                            Stroke="{StaticResource Brush.Success}"
                                            StrokeDashArray="8,4"
                                            StrokeThickness="2.5" />
                                    </DataTemplate>
                                </nodify:NodifyEditor.PendingConnectionTemplate>
                                <nodify:NodifyEditor.ItemContainerStyle>
                                    <Style TargetType="nodify:ItemContainer">
                                        <Setter Property="Location" Value="{Binding Location}" />
                                        <Setter Property="SelectedBrush" Value="Transparent" />
                                        <Setter Property="SelectedBorderThickness" Value="0" />
                                        <Setter Property="BorderBrush" Value="Transparent" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="nodify:ItemContainer">
                                                    <ContentPresenter />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <EventSetter Event="Selected" Handler="ItemContainer_Selected" />
                                        <EventSetter Event="MouseDown" Handler="ItemContainer_MouseDown" />
                                        <EventSetter Event="MouseDoubleClick" Handler="ItemContainer_MouseDoubleClick" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                                <Setter Property="Cursor" Value="Hand" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </nodify:NodifyEditor.ItemContainerStyle>
                            </nodify:NodifyEditor>

                            <canvasControls:CanvasZoomControl
                                FitToCanvasRequested="ZoomControl_FitToCanvas"
                                HorizontalAlignment="Right"
                                Margin="16"
                                ResetZoomRequested="ZoomControl_ResetZoom"
                                VerticalAlignment="Bottom"
                                ZoomChanged="ZoomControl_ZoomChanged"
                                x:Name="ZoomControl" />

                            <!--  Floating quick actions  -->
                            <StackPanel
                                HorizontalAlignment="Right"
                                Margin="16,16,72,16"
                                Orientation="Vertical"
                                VerticalAlignment="Bottom">
                                <Button
                                    Click="QuickFitButton_Click"
                                    Content="适应画布"
                                    Margin="0,0,0,6"
                                    Style="{StaticResource Button.Secondary}"
                                    Width="110" />
                                <Button
                                    Click="BtnAutoLayoutHierarchical_Click"
                                    Content="自动布局"
                                    Style="{StaticResource Button.Primary}"
                                    Width="110" />
                            </StackPanel>
                        </Grid>
                    </Grid>
                </TabItem>
                <TabItem Header="说明">
                    <TextBlock Margin="10" Text="说明内容" />
                </TabItem>
                <TabItem Header="项目信息">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            Grid.Row="0"
                            Margin="0,0,10,5"
                            Text="项目名称:" />
                        <TextBox
                            Grid.Column="1"
                            Grid.Row="0"
                            Margin="0,0,0,5" />
                        <TextBlock
                            Grid.Column="0"
                            Grid.Row="1"
                            Margin="0,0,10,0"
                            Text="项目描述:"
                            VerticalAlignment="Top" />
                        <TextBox
                            AcceptsReturn="True"
                            Grid.Column="1"
                            Grid.Row="1"
                            TextWrapping="Wrap"
                            VerticalAlignment="Stretch" />
                    </Grid>
                </TabItem>
                <TabItem Header="材料偏好">
                    <TextBlock Margin="10" Text="材料偏好设置" />
                </TabItem>
                <TabItem Header="材料表">
                    <TextBlock Margin="10" Text="材料表内容" />
                </TabItem>
            </TabControl>

            <!--  Right: property panel with tabs  -->
            <Grid Grid.Column="2">
                <TabControl Visibility="{Binding IsPropertyPanelVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TabItem Header="基础">
                        <propertiesControls:DynamicPropertyPanel
                            CanvasViewModel="{Binding}"
                            CurrentNode="{Binding SelectedNode}"
                            x:Name="PropertyPanel" />
                    </TabItem>
                    <TabItem Header="高级">
                        <TextBlock
                            Foreground="{StaticResource Brush.TextSecondary}"
                            Margin="12"
                            Text="后续提供高级属性编辑。" />
                    </TabItem>
                    <TabItem Header="函数">
                        <TextBlock
                            Foreground="{StaticResource Brush.TextSecondary}"
                            Margin="12"
                            Text="后续提供函数编辑器。" />
                    </TabItem>
                </TabControl>
            </Grid>

            <!--  Progress overlay  -->
            <commonControls:ProgressIndicator
                Grid.ColumnSpan="3"
                HorizontalAlignment="Right"
                Margin="20"
                MaxWidth="400"
                Panel.ZIndex="1000"
                VerticalAlignment="Top"
                x:Name="ProgressIndicator" />
        </Grid>

        <!--  Status bar  -->
        <Border
            Background="{StaticResource Brush.Surface}"
            BorderBrush="{StaticResource Brush.Border}"
            BorderThickness="0,1,0,0"
            Grid.Row="3"
            Padding="16,4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <ipack:PackIconMaterial
                        Foreground="{StaticResource Brush.Success}"
                        Height="16"
                        Kind="CheckCircle"
                        VerticalAlignment="Center"
                        Width="16" />
                    <TextBlock
                        Foreground="{StaticResource Brush.Text}"
                        Margin="6,0,0,0"
                        Text="就绪"
                        VerticalAlignment="Center" />
                </StackPanel>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock
                        Foreground="{StaticResource Brush.TextSecondary}"
                        Text="节点数量: "
                        VerticalAlignment="Center" />
                    <TextBlock
                        Foreground="{StaticResource Brush.Text}"
                        Margin="0,0,16,0"
                        Text="{Binding Nodes.Count}"
                        VerticalAlignment="Center" />
                    <TextBlock
                        Foreground="{StaticResource Brush.TextSecondary}"
                        Text="连接数量: "
                        VerticalAlignment="Center" />
                    <TextBlock
                        Foreground="{StaticResource Brush.Text}"
                        Text="{Binding Connections.Count}"
                        VerticalAlignment="Center" />
                </StackPanel>
            </Grid>
        </Border>

        <!--  Snackbar Presenter (可按需接入服务)  -->
        <!--  若使用 Wpf.Ui 的 SnackbarService，可在此添加 Presenter 占位容器  -->
        <!--  预留：Snackbar Presenter（WPFUI 4 可通过服务注册后接入）  -->
    </Grid>
</ui:FluentWindow>



