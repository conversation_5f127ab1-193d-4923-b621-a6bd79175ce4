<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <PackageId>Plugins.DataCalculation</PackageId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectDigitizer.Application\ProjectDigitizer.Application.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Core\ProjectDigitizer.Core.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Studio\ProjectDigitizer.Studio.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AvalonEdit" />
    <PackageReference Include="wpf-ui" />
    <PackageReference Include="MahApps.Metro.IconPacks.Material" />
    <PackageReference Include="Nodify" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Data\BuiltinFormulas.json" />
  </ItemGroup>

  <!-- 排除第三方 CalcEngine 测试文件，仅作为源码包含不参与编译 -->
  <ItemGroup>
    <Compile Remove="ThirdParty\CalcEngine\Tester.cs" />
    <None Include="ThirdParty\CalcEngine\Tester.cs" />
    <None Update="Data\BuiltinFormulas.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- 构建后复制到 Studio 插件目录，便于运行时加载 -->
  <Target Name="CopyPluginToStudioOutput" AfterTargets="Build">
    <PropertyGroup>
      <StudioOutput>$(MSBuildProjectDirectory)\..\ProjectDigitizer.Studio\bin\$(Configuration)\net8.0-windows\Plugins\</StudioOutput>
    </PropertyGroup>
    <MakeDir Directories="$(StudioOutput)" />
    <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(StudioOutput)" SkipUnchangedFiles="true" />
  </Target>

</Project>
