<UserControl
    Height="50"
    x:Class="ProjectDigitizer.Studio.Controls.NodeEditor.NodeEditorToolbar"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  工具栏按钮样式  -->
        <Style TargetType="Button" x:Key="ToolbarButtonStyle">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="8" />
            <Setter Property="Margin" Value="2" />
            <Setter Property="Width" Value="36" />
            <Setter Property="Height" Value="36" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}"
                            x:Name="Border">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{StaticResource Brush.Primary/08}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="Border" Value="{StaticResource Brush.Primary/16}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--  分隔符样式  -->
        <Style TargetType="Rectangle" x:Key="ToolbarSeparatorStyle">
            <Setter Property="Width" Value="1" />
            <Setter Property="Height" Value="24" />
            <Setter Property="Fill" Value="#E0E0E0" />
            <Setter Property="Margin" Value="4,0" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </UserControl.Resources>

    <Border
        Background="{StaticResource Brush.Surface}"
        BorderBrush="{StaticResource Brush.Border}"
        BorderThickness="0,0,0,1">
        <Border.Effect>
            <DropShadowEffect
                BlurRadius="3"
                Color="Black"
                Opacity="0.1"
                ShadowDepth="1" />
        </Border.Effect>

        <StackPanel
            Margin="12,7"
            Orientation="Horizontal"
            VerticalAlignment="Center">

            <!--  选择工具  -->
            <Button
                Command="{Binding SelectToolCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="选择工具 (V)">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="CursorDefault"
                    Width="18" />
            </Button>

            <!--  手动输入节点  -->
            <Button
                Command="{Binding AddManualInputNodeCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="手动输入数据节点">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Secondary}"
                    Height="18"
                    Kind="Keyboard"
                    Width="18" />
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}" />

            <!--  连接工具  -->
            <Button
                Command="{Binding ConnectToolCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="连接工具">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="VectorLine"
                    Width="18" />
            </Button>

            <!--  数组展开节点  -->
            <Button
                Command="{Binding AddArrayExpansionNodeCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="数组展开节点">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Primary}"
                    Height="18"
                    Kind="UnfoldMoreHorizontal"
                    Width="18" />
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}" />

            <!--  智能体节点  -->
            <Button
                Command="{Binding AddAIAgentNodeCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="智能体节点">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.PrimaryVariant}"
                    Height="18"
                    Kind="Robot"
                    Width="18" />
            </Button>

            <!--  属性面板  -->
            <Button
                Command="{Binding TogglePropertiesCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="属性面板">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="Cog"
                    Width="18" />
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}" />

            <!--  自动布局  -->
            <Button
                Command="{Binding AutoLayoutCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="自动布局">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Warning}"
                    Height="18"
                    Kind="AutoFix"
                    Width="18" />
            </Button>

            <!--  锁定/解锁  -->
            <Button
                Command="{Binding ToggleLockCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="锁定选中节点">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="Lock"
                    Width="18" />
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}" />

            <!--  撤销  -->
            <Button
                Command="{Binding UndoCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="撤销 (Ctrl+Z)">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="Undo"
                    Width="18" />
            </Button>

            <!--  重做  -->
            <Button
                Command="{Binding RedoCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="重做 (Ctrl+Y)">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="Redo"
                    Width="18" />
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}" />

            <!--  缩放适应  -->
            <Button
                Command="{Binding FitToScreenCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="缩放适应">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="FitToPage"
                    Width="18" />
            </Button>

            <!--  重置视图  -->
            <Button
                Command="{Binding ResetViewCommand}"
                Style="{StaticResource ToolbarButtonStyle}"
                ToolTip="重置视图">
                <ipack:PackIconMaterial
                    Foreground="{StaticResource Brush.Text}"
                    Height="18"
                    Kind="Refresh"
                    Width="18" />
            </Button>

        </StackPanel>
    </Border>
</UserControl>
