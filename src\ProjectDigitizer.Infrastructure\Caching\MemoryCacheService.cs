using System.Collections.Concurrent;
using System.Text.Json;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Infrastructure.Caching;

/// <summary>
/// 基于内存的缓存服务实现
/// 适用于单机应用程序的缓存需求
/// </summary>
public class MemoryCacheService : ICacheService, IDisposable
{
    private readonly ILogger<MemoryCacheService> _logger;
    private readonly ConcurrentDictionary<string, CacheItem> _cache;
    private readonly Timer _cleanupTimer;
    private readonly JsonSerializerOptions _jsonOptions;
    private bool _disposed = false;

    public MemoryCacheService(ILogger<MemoryCacheService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cache = new ConcurrentDictionary<string, CacheItem>();

        // 每5分钟清理一次过期缓存
        _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        _logger.LogDebug("内存缓存服务已初始化");
    }

    /// <summary>
    /// 获取缓存值
    /// </summary>
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));

            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    _logger.LogDebug("缓存项已过期并被移除: {Key}", key);
                    return null;
                }

                _logger.LogDebug("缓存命中: {Key}", key);

                if (cacheItem.Value is string jsonValue)
                {
                    var result = JsonSerializer.Deserialize<T>(jsonValue, _jsonOptions);
                    return result;
                }

                return cacheItem.Value as T;
            }

            _logger.LogDebug("缓存未命中: {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存值时发生错误: {Key}", key);
            return null;
        }
        finally
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 设置缓存值
    /// </summary>
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));

            if (value == null)
                throw new ArgumentNullException(nameof(value));

            var expirationTime = expiration.HasValue
                ? DateTime.UtcNow.Add(expiration.Value)
                : DateTime.MaxValue;

            // 序列化复杂对象
            object cacheValue = value;
            if (typeof(T) != typeof(string) && !typeof(T).IsPrimitive)
            {
                cacheValue = JsonSerializer.Serialize(value, _jsonOptions);
            }

            var cacheItem = new CacheItem
            {
                Value = cacheValue,
                ExpirationTime = expirationTime,
                CreatedTime = DateTime.UtcNow
            };

            _cache.AddOrUpdate(key, cacheItem, (k, v) => cacheItem);

            _logger.LogDebug("缓存项已设置: {Key}, 过期时间: {ExpirationTime}", key,
                expirationTime == DateTime.MaxValue ? "永不过期" : expirationTime.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置缓存值时发生错误: {Key}", key);
            throw;
        }
        finally
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 移除缓存值
    /// </summary>
    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            var removed = _cache.TryRemove(key, out _);

            if (removed)
            {
                _logger.LogDebug("缓存项已移除: {Key}", key);
            }
            else
            {
                _logger.LogDebug("缓存项不存在，无需移除: {Key}", key);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除缓存值时发生错误: {Key}", key);
        }
        finally
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 检查缓存键是否存在
    /// </summary>
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    return false;
                }
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查缓存键存在性时发生错误: {Key}", key);
            return false;
        }
        finally
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    public async Task ClearAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var count = _cache.Count;
            _cache.Clear();

            _logger.LogInformation("已清空所有缓存，共 {Count} 项", count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空缓存时发生错误");
            throw;
        }
        finally
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 获取或设置缓存值
    /// </summary>
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // 先尝试获取缓存值
            var cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // 缓存未命中，调用工厂函数获取值
            _logger.LogDebug("缓存未命中，调用工厂函数: {Key}", key);

            var value = await factory();
            if (value != null)
            {
                await SetAsync(key, value, expiration, cancellationToken);
            }

            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取或设置缓存值时发生错误: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// 清理过期缓存项
    /// </summary>
    private void CleanupExpiredItems(object? state)
    {
        try
        {
            var expiredKeys = new List<string>();
            var now = DateTime.UtcNow;

            foreach (var kvp in _cache)
            {
                if (kvp.Value.ExpirationTime <= now)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("清理了 {Count} 个过期缓存项", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期缓存项时发生错误");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _cache?.Clear();
            _disposed = true;

            _logger.LogDebug("内存缓存服务已释放");
        }
    }
}

/// <summary>
/// 缓存项
/// </summary>
internal class CacheItem
{
    public object? Value { get; set; }
    public DateTime ExpirationTime { get; set; }
    public DateTime CreatedTime { get; set; }

    public bool IsExpired => DateTime.UtcNow > ExpirationTime;
}
