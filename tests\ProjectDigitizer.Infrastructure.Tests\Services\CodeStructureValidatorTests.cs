using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Services;

using Xunit;
using Xunit.Abstractions;

namespace ProjectDigitizer.Infrastructure.Tests.Services;

/// <summary>
/// 代码结构验证器测试
/// </summary>
public class CodeStructureValidatorTests
{
    private readonly ICodeStructureValidator _structureValidator;
    private readonly ITestOutputHelper _output;

    public CodeStructureValidatorTests(ITestOutputHelper output)
    {
        _output = output;
        var mockLogger = new Mock<ILogger<CodeStructureValidator>>();
        _structureValidator = new CodeStructureValidator(mockLogger.Object);
    }

    [Theory]
    [InlineData("ProjectDigitizer.Core.Entities", ArchitectureLayer.Core)]
    [InlineData("ProjectDigitizer.Core.ValueObjects", ArchitectureLayer.Core)]
    [InlineData("ProjectDigitizer.Application.Services", ArchitectureLayer.Application)]
    [InlineData("ProjectDigitizer.Application.DTOs", ArchitectureLayer.Application)]
    [InlineData("ProjectDigitizer.Infrastructure.Data", ArchitectureLayer.Infrastructure)]
    [InlineData("ProjectDigitizer.Infrastructure.Services", ArchitectureLayer.Infrastructure)]
    [InlineData("ProjectDigitizer.Studio.ViewModels", ArchitectureLayer.Presentation)]
    [InlineData("ProjectDigitizer.Studio.Views", ArchitectureLayer.Presentation)]
    [InlineData("ProjectDigitizer.Core.Tests", ArchitectureLayer.Tests)]
    [InlineData("ProjectDigitizer.Application.Tests", ArchitectureLayer.Tests)]
    public void GetArchitectureLayer_ShouldIdentifyCorrectLayer(string namespaceName, ArchitectureLayer expectedLayer)
    {
        // Act
        var result = _structureValidator.GetArchitectureLayer("", namespaceName);

        // Assert
        _output.WriteLine($"命名空间: {namespaceName} -> 架构层: {result}");

        Assert.Equal(expectedLayer, result);
    }

    [Theory]
    [InlineData("", ArchitectureLayer.Core)] // 空命名空间默认为Core
    [InlineData("SomeOtherProject.Core", ArchitectureLayer.Core)] // 其他项目也识别为Core
    [InlineData("ProjectDigitizer.Unknown", ArchitectureLayer.Core)] // 未知层默认为Core
    public void GetArchitectureLayer_WithEdgeCases_ShouldReturnDefault(string namespaceName, ArchitectureLayer expectedLayer)
    {
        // Act
        var result = _structureValidator.GetArchitectureLayer("", namespaceName);

        // Assert
        _output.WriteLine($"边界情况命名空间: '{namespaceName}' -> 架构层: {result}");

        Assert.Equal(expectedLayer, result);
    }

    [Theory]
    // 允许的依赖关系
    [InlineData(ArchitectureLayer.Application, ArchitectureLayer.Core, true)]
    [InlineData(ArchitectureLayer.Infrastructure, ArchitectureLayer.Core, true)]
    [InlineData(ArchitectureLayer.Infrastructure, ArchitectureLayer.Application, true)]
    [InlineData(ArchitectureLayer.Presentation, ArchitectureLayer.Application, true)]
    [InlineData(ArchitectureLayer.Presentation, ArchitectureLayer.Infrastructure, true)]
    [InlineData(ArchitectureLayer.Tests, ArchitectureLayer.Core, true)]
    [InlineData(ArchitectureLayer.Tests, ArchitectureLayer.Application, true)]
    [InlineData(ArchitectureLayer.Tests, ArchitectureLayer.Infrastructure, true)]
    [InlineData(ArchitectureLayer.Tests, ArchitectureLayer.Presentation, true)]
    // 同层依赖
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Core, true)]
    [InlineData(ArchitectureLayer.Application, ArchitectureLayer.Application, true)]
    // 不允许的依赖关系
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Application, false)]
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Infrastructure, false)]
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Presentation, false)]
    [InlineData(ArchitectureLayer.Application, ArchitectureLayer.Infrastructure, false)]
    [InlineData(ArchitectureLayer.Application, ArchitectureLayer.Presentation, false)]
    public void ValidateLayerDependency_ShouldFollowCleanArchitectureRules(
        ArchitectureLayer sourceLayer,
        ArchitectureLayer targetLayer,
        bool shouldBeValid)
    {
        // Act
        var result = _structureValidator.ValidateLayerDependency(sourceLayer, targetLayer);

        // Assert
        _output.WriteLine($"层依赖验证: {sourceLayer} -> {targetLayer} = {(result.IsValid ? "允许" : "禁止")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(shouldBeValid, result.IsValid);
    }

    [Theory]
    [InlineData("UserService", "ProjectDigitizer.Application.Services", ClassType.Service, true)]
    [InlineData("UserEntity", "ProjectDigitizer.Core.Entities", ClassType.Entity, true)]
    [InlineData("UserRepository", "ProjectDigitizer.Infrastructure.Data", ClassType.Service, true)]
    [InlineData("UserViewModel", "ProjectDigitizer.Studio.ViewModels", ClassType.ViewModel, true)]
    [InlineData("UserDto", "ProjectDigitizer.Application.DTOs", ClassType.Dto, true)]
    public void ValidateClassPlacement_WithCorrectPlacement_ShouldReturnValid(
        string className,
        string classNamespace,
        ClassType classType,
        bool expectedValid)
    {
        // Act
        var result = _structureValidator.ValidateClassPlacement(className, classNamespace, classType);

        // Assert
        _output.WriteLine($"类放置验证: {className} ({classType}) 在 {classNamespace} = {(result.IsValid ? "正确" : "错误")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("UserEntity", "ProjectDigitizer.Application.Services", ClassType.Entity, false)] // Entity应该在Core层
    [InlineData("UserService", "ProjectDigitizer.Core.Entities", ClassType.Service, false)] // Service应该在Application层
    [InlineData("UserViewModel", "ProjectDigitizer.Infrastructure.Data", ClassType.ViewModel, false)] // ViewModel应该在Presentation层
    public void ValidateClassPlacement_WithIncorrectPlacement_ShouldReturnInvalid(
        string className,
        string classNamespace,
        ClassType classType,
        bool expectedValid)
    {
        // Act
        var result = _structureValidator.ValidateClassPlacement(className, classNamespace, classType);

        // Assert
        _output.WriteLine($"错误类放置验证: {className} ({classType}) 在 {classNamespace} = {(result.IsValid ? "正确" : "错误")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Fact]
    public void ValidateClassDependencies_WithValidDependencies_ShouldReturnValid()
    {
        // Arrange
        var className = "UserService";
        var classNamespace = "ProjectDigitizer.Application.Services";
        var dependencies = new[]
        {
            "ProjectDigitizer.Core.Entities.User",
            "ProjectDigitizer.Core.Interfaces.IUserRepository",
            "ProjectDigitizer.Infrastructure.Data.UserRepository"
        };

        // Act
        var result = _structureValidator.ValidateClassDependencies(className, classNamespace, dependencies);

        // Assert
        _output.WriteLine($"类依赖验证: {className} 依赖 {dependencies.Length} 个类型");

        foreach (var dependency in dependencies)
        {
            _output.WriteLine($"  依赖: {dependency}");
        }

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        // Application层可以依赖Core和Infrastructure层，所以应该有效
        Assert.True(result.IsValid);
    }

    [Fact]
    public void ValidateClassDependencies_WithInvalidDependencies_ShouldReturnInvalid()
    {
        // Arrange
        var className = "UserEntity";
        var classNamespace = "ProjectDigitizer.Core.Entities";
        var dependencies = new[]
        {
            "ProjectDigitizer.Application.Services.UserService", // Core不应依赖Application
            "ProjectDigitizer.Infrastructure.Data.UserRepository" // Core不应依赖Infrastructure
        };

        // Act
        var result = _structureValidator.ValidateClassDependencies(className, classNamespace, dependencies);

        // Assert
        _output.WriteLine($"无效类依赖验证: {className} 依赖 {dependencies.Length} 个类型");

        foreach (var dependency in dependencies)
        {
            _output.WriteLine($"  依赖: {dependency}");
        }

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        // Core层不应依赖Application和Infrastructure层
        Assert.False(result.IsValid);
    }

    [Fact]
    public void ValidateProjectArchitecture_WithValidProjectPath_ShouldValidateStructure()
    {
        // Arrange
        var projectPath = GetProjectRootPath();

        // Act
        var result = _structureValidator.ValidateProjectArchitecture(projectPath);

        // Assert
        _output.WriteLine($"项目架构验证: {projectPath}");
        _output.WriteLine($"结果: {(result.IsValid ? "有效" : "无效")}");
        _output.WriteLine($"错误数: {result.Errors.Count}, 警告数: {result.Warnings.Count}");

        foreach (var error in result.Errors)
        {
            _output.WriteLine($"  错误: {error}");
        }

        foreach (var warning in result.Warnings)
        {
            _output.WriteLine($"  警告: {warning}");
        }

        // 项目结构应该基本正确，允许一些警告但不应有严重错误
        Assert.True(result.Errors.Count <= 3, $"项目架构错误过多: {result.Errors.Count}");
    }

    [Fact]
    public void ValidateProjectArchitecture_WithInvalidPath_ShouldReturnError()
    {
        // Arrange
        var invalidPath = "/non/existent/path";

        // Act
        var result = _structureValidator.ValidateProjectArchitecture(invalidPath);

        // Assert
        _output.WriteLine($"无效路径验证: {invalidPath}");
        _output.WriteLine($"结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.False(result.IsValid);
        Assert.Contains("项目路径无效", result.Errors.FirstOrDefault() ?? "");
    }

    [Theory]
    [InlineData(ArchitectureLayer.Core, "Core")]
    [InlineData(ArchitectureLayer.Application, "Application")]
    [InlineData(ArchitectureLayer.Infrastructure, "Infrastructure")]
    [InlineData(ArchitectureLayer.Presentation, "Studio")]
    public void ValidateFolderStructure_WithValidLayer_ShouldValidateStructure(ArchitectureLayer layer, string layerName)
    {
        // Arrange
        var projectPath = GetProjectRootPath();

        // Act
        var result = _structureValidator.ValidateFolderStructure(projectPath, layer);

        // Assert
        _output.WriteLine($"文件夹结构验证: {layerName}层");
        _output.WriteLine($"结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        foreach (var warning in result.Warnings)
        {
            _output.WriteLine($"  警告: {warning}");
        }

        // 文件夹结构验证可能有警告，但不应有严重错误
        Assert.True(result.Errors.Count <= 1, $"{layerName}层文件夹结构错误: {string.Join(", ", result.Errors)}");
    }

    [Fact]
    public void CheckCircularDependencies_ShouldNotFindCircularDependencies()
    {
        // Arrange
        var projectPath = GetProjectRootPath();

        // Act
        var result = _structureValidator.CheckCircularDependencies(projectPath);

        // Assert
        _output.WriteLine($"循环依赖检查: {projectPath}");
        _output.WriteLine($"结果: {(result.IsValid ? "无循环依赖" : "发现循环依赖")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  循环依赖: {error}");
            }
        }

        // 项目不应有循环依赖
        Assert.True(result.IsValid, "项目中不应存在循环依赖");
    }

    private string GetProjectRootPath()
    {
        var currentDirectory = System.IO.Directory.GetCurrentDirectory();
        var projectRoot = currentDirectory;

        // 向上查找直到找到.sln文件或src文件夹
        while (!System.IO.Directory.Exists(System.IO.Path.Combine(projectRoot, "src")) &&
               !System.IO.Directory.GetFiles(projectRoot, "*.sln").Any())
        {
            var parent = System.IO.Directory.GetParent(projectRoot);
            if (parent == null)
                break;
            projectRoot = parent.FullName;
        }

        return projectRoot;
    }
}
