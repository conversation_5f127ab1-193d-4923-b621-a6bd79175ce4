using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Extensions;

using Xunit;
using Xunit.Abstractions;

namespace ProjectDigitizer.Infrastructure.Tests.CodeQuality;

/// <summary>
/// 代码质量验证测试
/// </summary>
public class CodeQualityTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ITestOutputHelper _output;

    public CodeQualityTests(ITestOutputHelper output)
    {
        _output = output;

        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());
        services.AddCodeQualityValidation();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task ProjectStructure_ShouldFollowCleanArchitecture()
    {
        // Arrange
        var projectPath = GetProjectRootPath();
        _output.WriteLine($"验证项目路径: {projectPath}");

        // Act
        var result = await _serviceProvider.ValidateProjectCodeQualityAsync(projectPath);

        // Assert
        _output.WriteLine($"验证结果: {result.Summary}");
        _output.WriteLine($"验证耗时: {result.Duration.TotalSeconds:F2}秒");

        if (result.Issues.Any())
        {
            _output.WriteLine("\n发现的问题:");
            foreach (var issue in result.Issues.Take(10))
            {
                _output.WriteLine($"[{issue.Severity}] {issue.Type}: {issue.Description}");
            }
        }

        // 允许警告，但不允许严重错误
        var criticalIssues = result.Issues.Where(i => i.Severity == IssueSeverity.Critical).ToList();
        Assert.Empty(criticalIssues);

        // 错误数量应该在合理范围内
        Assert.True(result.ErrorCount <= 5, $"错误数量过多: {result.ErrorCount}");
    }

    [Fact]
    public void ProjectArchitecture_ShouldHaveCorrectStructure()
    {
        // Arrange
        var structureValidator = _serviceProvider.GetRequiredService<ICodeStructureValidator>();
        var projectPath = GetProjectRootPath();

        // Act
        var result = structureValidator.ValidateProjectArchitecture(projectPath);

        // Assert
        _output.WriteLine($"架构验证结果: 错误{result.Errors.Count}个, 警告{result.Warnings.Count}个");

        foreach (var error in result.Errors)
        {
            _output.WriteLine($"错误: {error}");
        }

        foreach (var warning in result.Warnings)
        {
            _output.WriteLine($"警告: {warning}");
        }

        Assert.True(result.IsValid || result.Errors.Count <= 2,
            $"架构验证失败，错误过多: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("UserService", ClassType.Service)]
    [InlineData("DocumentEntity", ClassType.Entity)]
    [InlineData("ValidationResult", ClassType.ValueObject)]
    [InlineData("IUserRepository", ClassType.Interface)]
    [InlineData("UserController", ClassType.Controller)]
    [InlineData("UserViewModel", ClassType.ViewModel)]
    public void NamingConventions_ValidClassNames_ShouldPass(string className, ClassType classType)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateClassName(className, classType);

        // Assert
        _output.WriteLine($"验证类名 '{className}' (类型: {classType}): {(result.IsValid ? "通过" : "失败")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"错误: {error}");
            }
        }

        Assert.True(result.IsValid, $"类名 '{className}' 验证失败: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("userService", ClassType.Service)]
    [InlineData("User", ClassType.Interface)]
    [InlineData("usercontroller", ClassType.Controller)]
    [InlineData("USERSERVICE", ClassType.Service)]
    public void NamingConventions_InvalidClassNames_ShouldFail(string className, ClassType classType)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateClassName(className, classType);

        // Assert
        _output.WriteLine($"验证无效类名 '{className}' (类型: {classType}): {(result.IsValid ? "意外通过" : "正确失败")}");

        Assert.False(result.IsValid, $"无效类名 '{className}' 应该验证失败");
    }

    [Theory]
    [InlineData("GetUser")]
    [InlineData("SaveDocument")]
    [InlineData("ValidateInput")]
    [InlineData("ProcessDataAsync")]
    public void NamingConventions_ValidMethodNames_ShouldPass(string methodName)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateMethodName(methodName, MethodType.Public);

        // Assert
        _output.WriteLine($"验证方法名 '{methodName}': {(result.IsValid ? "通过" : "失败")}");

        Assert.True(result.IsValid, $"方法名 '{methodName}' 验证失败: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("Name")]
    [InlineData("UserId")]
    [InlineData("CreatedAt")]
    [InlineData("IsActive")]
    public void NamingConventions_ValidPropertyNames_ShouldPass(string propertyName)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidatePropertyName(propertyName, PropertyType.Public);

        // Assert
        _output.WriteLine($"验证属性名 '{propertyName}': {(result.IsValid ? "通过" : "失败")}");

        Assert.True(result.IsValid, $"属性名 '{propertyName}' 验证失败: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("ProjectDigitizer.Core.Entities", ArchitectureLayer.Core)]
    [InlineData("ProjectDigitizer.Application.Services", ArchitectureLayer.Application)]
    [InlineData("ProjectDigitizer.Infrastructure.Data", ArchitectureLayer.Infrastructure)]
    [InlineData("ProjectDigitizer.Studio.ViewModels", ArchitectureLayer.Presentation)]
    public void NamingConventions_ValidNamespaces_ShouldPass(string namespaceName, ArchitectureLayer expectedLayer)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateNamespace(namespaceName, expectedLayer);

        // Assert
        _output.WriteLine($"验证命名空间 '{namespaceName}' (期望层: {expectedLayer}): {(result.IsValid ? "通过" : "失败")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"错误: {error}");
            }
        }

        Assert.True(result.IsValid, $"命名空间 '{namespaceName}' 验证失败: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("IUserService")]
    [InlineData("IDocumentRepository")]
    [InlineData("IValidationService")]
    public void NamingConventions_ValidInterfaceNames_ShouldPass(string interfaceName)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateInterfaceName(interfaceName);

        // Assert
        _output.WriteLine($"验证接口名 '{interfaceName}': {(result.IsValid ? "通过" : "失败")}");

        Assert.True(result.IsValid, $"接口名 '{interfaceName}' 验证失败: {string.Join(", ", result.Errors)}");
    }

    [Theory]
    [InlineData("UserService")]
    [InlineData("DocumentRepository")]
    [InlineData("ValidationService")]
    public void NamingConventions_InvalidInterfaceNames_ShouldFail(string interfaceName)
    {
        // Arrange
        var namingService = _serviceProvider.GetRequiredService<INamingConventionService>();

        // Act
        var result = namingService.ValidateInterfaceName(interfaceName);

        // Assert
        _output.WriteLine($"验证无效接口名 '{interfaceName}': {(result.IsValid ? "意外通过" : "正确失败")}");

        Assert.False(result.IsValid, $"无效接口名 '{interfaceName}' 应该验证失败");
    }

    [Fact]
    public void ArchitectureLayer_ShouldBeIdentifiedCorrectly()
    {
        // Arrange
        var structureValidator = _serviceProvider.GetRequiredService<ICodeStructureValidator>();

        // Act & Assert
        Assert.Equal(ArchitectureLayer.Core,
            structureValidator.GetArchitectureLayer("", "ProjectDigitizer.Core.Entities"));

        Assert.Equal(ArchitectureLayer.Application,
            structureValidator.GetArchitectureLayer("", "ProjectDigitizer.Application.Services"));

        Assert.Equal(ArchitectureLayer.Infrastructure,
            structureValidator.GetArchitectureLayer("", "ProjectDigitizer.Infrastructure.Data"));

        Assert.Equal(ArchitectureLayer.Presentation,
            structureValidator.GetArchitectureLayer("", "ProjectDigitizer.Studio.ViewModels"));

        Assert.Equal(ArchitectureLayer.Tests,
            structureValidator.GetArchitectureLayer("", "ProjectDigitizer.Core.Tests"));

        _output.WriteLine("架构层识别测试通过");
    }

    [Theory]
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Application, false)] // Core不应依赖Application
    [InlineData(ArchitectureLayer.Core, ArchitectureLayer.Infrastructure, false)] // Core不应依赖Infrastructure
    [InlineData(ArchitectureLayer.Application, ArchitectureLayer.Core, true)] // Application可以依赖Core
    [InlineData(ArchitectureLayer.Infrastructure, ArchitectureLayer.Core, true)] // Infrastructure可以依赖Core
    [InlineData(ArchitectureLayer.Presentation, ArchitectureLayer.Application, true)] // Presentation可以依赖Application
    public void LayerDependency_ShouldFollowCleanArchitectureRules(
        ArchitectureLayer sourceLayer,
        ArchitectureLayer targetLayer,
        bool shouldBeValid)
    {
        // Arrange
        var structureValidator = _serviceProvider.GetRequiredService<ICodeStructureValidator>();

        // Act
        var result = structureValidator.ValidateLayerDependency(sourceLayer, targetLayer);

        // Assert
        _output.WriteLine($"验证层依赖 {sourceLayer} -> {targetLayer}: {(result.IsValid ? "允许" : "禁止")}");

        Assert.Equal(shouldBeValid, result.IsValid);
    }

    private string GetProjectRootPath()
    {
        var currentDirectory = System.IO.Directory.GetCurrentDirectory();
        var projectRoot = currentDirectory;

        // 向上查找直到找到.sln文件或src文件夹
        while (!System.IO.Directory.Exists(System.IO.Path.Combine(projectRoot, "src")) &&
               !System.IO.Directory.GetFiles(projectRoot, "*.sln").Any())
        {
            var parent = System.IO.Directory.GetParent(projectRoot);
            if (parent == null)
                break;
            projectRoot = parent.FullName;
        }

        return projectRoot;
    }

    public void Dispose()
    {
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
