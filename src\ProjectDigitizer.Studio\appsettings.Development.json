{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "System": "Information", "ProjectDigitizer": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "ProjectDigitizer": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/dev/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}, "DataDirectory": "Data/Development", "ExternalServices": {"BaseUrl": "https://dev-api.example.com", "TimeoutSeconds": 60, "EnableRetry": true, "MaxRetryAttempts": 5, "EnableCaching": false}, "Application": {"Name": "ProjectDigitizer Studio (Development)", "Version": "1.0.0-dev", "Environment": "Development"}, "Plugins": {"Directory": "bin/Debug/net8.0-windows/Plugins"}, "Database": {"ConnectionString": "Data Source=Data/Development/ProjectDigitizer.dev.db", "Provider": "SQLite", "CommandTimeout": 60, "EnableSensitiveDataLogging": true}, "UI": {"Theme": {"CurrentTheme": "Dark", "EnableAnimations": true}, "Window": {"DefaultWidth": 1400, "DefaultHeight": 900, "RememberPosition": true, "RememberSize": true}, "Canvas": {"DefaultZoom": 1.0, "MinimumZoom": 0.1, "MaximumZoom": 5.0, "EnableGrid": true, "GridSize": 20, "EnableSnapping": true}, "Performance": {"EnableVirtualization": true, "MaxVisibleNodes": 1000, "RenderingOptimization": "Development"}, "Language": {"CurrentLanguage": "zh-CN", "SupportedLanguages": ["zh-CN", "en-US"]}}}