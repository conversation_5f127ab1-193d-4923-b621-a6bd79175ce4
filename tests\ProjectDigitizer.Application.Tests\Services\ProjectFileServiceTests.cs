using System;
using System.IO;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Application.Tests.Fixtures;

namespace ProjectDigitizer.Application.Tests.Services;

/// <summary>
/// ProjectFileService 测试类
/// </summary>
[Collection("Application Tests")]
public class ProjectFileServiceTests : IDisposable
{
    private readonly ApplicationTestFixture _fixture;
    private readonly ProjectFileService _service;
    private readonly Mock<ILogger<ProjectFileService>> _loggerMock;
    private readonly string _testDirectory;

    public ProjectFileServiceTests(ApplicationTestFixture fixture)
    {
        _fixture = fixture;
        _loggerMock = new Mock<ILogger<ProjectFileService>>();
        _service = new ProjectFileService(_loggerMock.Object);

        // 创建测试目录
        _testDirectory = Path.Combine(Path.GetTempPath(), "ProjectDigitizerTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var exception = Assert.Throws<ArgumentNullException>(() => new ProjectFileService(null!));
        exception.ParamName.Should().Be("logger");
    }

    [Fact]
    public void CreateNewProject_WithValidName_ShouldReturnProjectFile()
    {
        // Arrange
        var projectName = "测试项目";
        var description = "这是一个测试项目";

        // Act
        var result = _service.CreateNewProject(projectName, description);

        // Assert
        result.Should().NotBeNull();
        result.ProjectInfo.Should().NotBeNull();
        result.ProjectInfo.Name.Should().Be(projectName);
        result.ProjectInfo.Description.Should().Be(description);
        result.Version.Should().Be("1.0.0");
        result.Templates.Should().NotBeNull();
        result.Templates.Should().BeEmpty();
        result.CreatedTime.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        result.LastModifiedTime.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void CreateNewProject_WithInvalidName_ShouldThrowArgumentException(string? projectName)
    {
        // Act & Assert
        var exception = Assert.Throws<ArgumentException>(() => _service.CreateNewProject(projectName!));
        exception.ParamName.Should().Be("projectName");
    }

    [Fact]
    public async Task SaveProjectAsync_WithValidData_ShouldSaveSuccessfully()
    {
        // Arrange
        var projectFile = _service.CreateNewProject("测试项目", "测试描述");
        var filePath = Path.Combine(_testDirectory, "test-project.json");

        // Act
        await _service.SaveProjectAsync(projectFile, filePath);

        // Assert
        File.Exists(filePath).Should().BeTrue();

        var fileContent = await File.ReadAllTextAsync(filePath);
        fileContent.Should().NotBeNullOrEmpty();
        // 检查JSON中包含项目名称（可能是Unicode转义形式）
        (fileContent.Contains("测试项目") || fileContent.Contains("\\u6D4B\\u8BD5\\u9879\\u76EE")).Should().BeTrue();
        (fileContent.Contains("测试描述") || fileContent.Contains("\\u6D4B\\u8BD5\\u63CF\\u8FF0")).Should().BeTrue();
    }

    [Fact]
    public async Task SaveProjectAsync_WithNullProjectFile_ShouldThrowArgumentNullException()
    {
        // Arrange
        var filePath = Path.Combine(_testDirectory, "test.json");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentNullException>(
            () => _service.SaveProjectAsync(null!, filePath));
        exception.ParamName.Should().Be("projectFile");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task SaveProjectAsync_WithInvalidFilePath_ShouldThrowArgumentException(string? filePath)
    {
        // Arrange
        var projectFile = _service.CreateNewProject("测试项目");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(
            () => _service.SaveProjectAsync(projectFile, filePath!));
        exception.ParamName.Should().Be("filePath");
    }

    [Fact]
    public async Task LoadProjectAsync_WithValidFile_ShouldLoadSuccessfully()
    {
        // Arrange
        var originalProject = _service.CreateNewProject("测试项目", "测试描述");
        var filePath = Path.Combine(_testDirectory, "test-project.json");
        await _service.SaveProjectAsync(originalProject, filePath);

        // Act
        var loadedProject = await _service.LoadProjectAsync(filePath);

        // Assert
        loadedProject.Should().NotBeNull();
        loadedProject.ProjectInfo.Name.Should().Be("测试项目");
        loadedProject.ProjectInfo.Description.Should().Be("测试描述");
        loadedProject.Version.Should().Be("1.0.0");
    }

    [Fact]
    public async Task LoadProjectAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var filePath = Path.Combine(_testDirectory, "non-existent.json");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<FileNotFoundException>(
            () => _service.LoadProjectAsync(filePath));
        exception.Message.Should().Contain(filePath);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task LoadProjectAsync_WithInvalidFilePath_ShouldThrowArgumentException(string? filePath)
    {
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(
            () => _service.LoadProjectAsync(filePath!));
        exception.ParamName.Should().Be("filePath");
    }

    [Fact]
    public async Task ValidateProjectFileAsync_WithValidFile_ShouldReturnTrue()
    {
        // Arrange
        var projectFile = _service.CreateNewProject("测试项目");
        var filePath = Path.Combine(_testDirectory, "valid-project.json");
        await _service.SaveProjectAsync(projectFile, filePath);

        // Act
        var result = await _service.ValidateProjectFileAsync(filePath);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ValidateProjectFileAsync_WithNonExistentFile_ShouldReturnFalse()
    {
        // Arrange
        var filePath = Path.Combine(_testDirectory, "non-existent.json");

        // Act
        var result = await _service.ValidateProjectFileAsync(filePath);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateProjectFileAsync_WithInvalidJson_ShouldReturnFalse()
    {
        // Arrange
        var filePath = Path.Combine(_testDirectory, "invalid.json");
        await File.WriteAllTextAsync(filePath, "{ invalid json }");

        // Act
        var result = await _service.ValidateProjectFileAsync(filePath);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetProjectInfoAsync_WithValidFile_ShouldReturnProjectInfo()
    {
        // Arrange
        var projectFile = _service.CreateNewProject("测试项目", "测试描述");
        var filePath = Path.Combine(_testDirectory, "info-test.json");
        await _service.SaveProjectAsync(projectFile, filePath);

        // Act
        var info = await _service.GetProjectInfoAsync(filePath);

        // Assert
        info.Should().NotBeNull();
        info.FilePath.Should().Be(filePath);
        info.ProjectName.Should().Be("测试项目");
        info.Description.Should().Be("测试描述");
        info.Version.Should().Be("1.0.0");
        info.FileSize.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetProjectInfoAsync_WithNonExistentFile_ShouldThrowFileNotFoundException()
    {
        // Arrange
        var filePath = Path.Combine(_testDirectory, "non-existent.json");

        // Act & Assert
        var exception = await Assert.ThrowsAsync<FileNotFoundException>(
            () => _service.GetProjectInfoAsync(filePath));
        exception.Message.Should().Contain(filePath);
    }

    public void Dispose()
    {
        // 清理测试目录
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }
}
