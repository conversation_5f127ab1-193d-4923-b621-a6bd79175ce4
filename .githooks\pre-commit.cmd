@echo off
REM Pre-commit (Windows): verify formatting of staged files ONLY (no writes)
setlocal EnableExtensions EnableDelayedExpansion
set DOTNET_NOLOGO=1

REM Collect staged C#/XAML/CSPROJ files (Added, Copied, Modified, Renamed)
set "FILES="
for /f "delims=" %%F in ('git diff --cached --name-only --diff-filter=ACMR') do (
  set "EXT=%%~xF"
  if /I "!EXT!"==".cs" (
    set FILES=!FILES! "%%F"
  ) else if /I "!EXT!"==".xaml" (
    set FILES=!FILES! "%%F"
  ) else if /I "!EXT!"==".csproj" (
    set FILES=!FILES! "%%F"
  )
)

if "%FILES%"=="" (
  exit /b 0
)

echo [pre-commit] Verifying dotnet format on staged files (style+whitespace)...

set "FAIL=0"
for %%F in (%FILES%) do (
  dotnet format whitespace ProjectDigitizer.sln --no-restore --verbosity minimal --verify-no-changes --include "%%~F"
  if not "!ERRORLEVEL!"=="0" (
    set "FAIL=1"
  )
  dotnet format style ProjectDigitizer.sln --no-restore --verbosity minimal --verify-no-changes --include "%%~F"
  if not "!ERRORLEVEL!"=="0" (
    set "FAIL=1"
  )
)

if "%FAIL%"=="1" (
  echo [pre-commit] 需要先格式化：请运行以下命令后重试提交 1>&2
  echo   dotnet format whitespace ProjectDigitizer.sln --no-restore --include %FILES% 1>&2
  echo   dotnet format style ProjectDigitizer.sln --no-restore --include %FILES% 1>&2
  exit /b 1
)

exit /b 0
