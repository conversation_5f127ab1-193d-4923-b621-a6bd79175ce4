<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========== 通用样式 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的可复用样式 -->

    <!-- 平面按钮样式 -->
    <Style x:Key="FlatButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- 节点功能按钮样式 -->
    <Style x:Key="NodeFunctionButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="22"/>
        <Setter Property="Height" Value="22"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Margin" Value="2,0,2,0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="3"
                            Padding="0">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#33FFFFFF"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 折叠/展开按钮样式 -->
    <Style x:Key="ExpandCollapseButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Margin" Value="0,0,2,0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="3"
                            Padding="0">
                        <ContentPresenter HorizontalAlignment="Center"
                                          VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#33FFFFFF"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 节点标题文本框样式 -->
    <Style x:Key="NodeTitleTextBoxStyle" TargetType="TextBox">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="2"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="Cursor" Value="Arrow"/>
        <Style.Triggers>
            <!-- 双击进入编辑模式 -->
            <EventTrigger RoutedEvent="MouseDoubleClick">
                <BeginStoryboard>
                    <Storyboard>
                        <BooleanAnimationUsingKeyFrames Storyboard.TargetProperty="IsReadOnly">
                            <DiscreteBooleanKeyFrame KeyTime="0" Value="False"/>
                        </BooleanAnimationUsingKeyFrames>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <!-- 失去焦点退出编辑模式 -->
            <EventTrigger RoutedEvent="LostFocus">
                <BeginStoryboard>
                    <Storyboard>
                        <BooleanAnimationUsingKeyFrames Storyboard.TargetProperty="IsReadOnly">
                            <DiscreteBooleanKeyFrame KeyTime="0" Value="True"/>
                        </BooleanAnimationUsingKeyFrames>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <!-- 编辑模式样式 -->
            <Trigger Property="IsReadOnly" Value="False">
                <Setter Property="Background" Value="#33FFFFFF"/>
                <Setter Property="BorderBrush" Value="White"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Cursor" Value="IBeam"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
