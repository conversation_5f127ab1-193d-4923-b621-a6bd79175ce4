using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Core.Entities
{
    /// <summary>
    /// 源节点信息 - 用于数据来源选择
    /// </summary>
    public class SourceNodeInfo : INotifyPropertyChanged
    {
        private string _nodeId = string.Empty;
        private string _nodeTitle = string.Empty;
        private string _nodeType = string.Empty;
        private bool _isSelected = false;
        private bool _isConnected = false;
        private bool _isAvailable = true;
        private string _outputDataType = string.Empty;
        private int _outputCount = 0;

        /// <summary>节点ID</summary>
        public string NodeId
        {
            get => _nodeId;
            set => SetProperty(ref _nodeId, value);
        }

        /// <summary>节点标题</summary>
        public string NodeTitle
        {
            get => _nodeTitle;
            set => SetProperty(ref _nodeTitle, value);
        }

        /// <summary>节点类型</summary>
        public string NodeType
        {
            get => _nodeType;
            set => SetProperty(ref _nodeType, value);
        }

        /// <summary>是否被选中</summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>是否已连接</summary>
        public bool IsConnected
        {
            get => _isConnected;
            set => SetProperty(ref _isConnected, value);
        }

        /// <summary>是否可用</summary>
        public bool IsAvailable
        {
            get => _isAvailable;
            set => SetProperty(ref _isAvailable, value);
        }

        /// <summary>输出数据类型</summary>
        public string OutputDataType
        {
            get => _outputDataType;
            set => SetProperty(ref _outputDataType, value);
        }

        /// <summary>输出端口数量</summary>
        public int OutputCount
        {
            get => _outputCount;
            set => SetProperty(ref _outputCount, value);
        }

        /// <summary>显示文本</summary>
        public string DisplayText => $"{NodeTitle} ({NodeType})";

        /// <summary>连接状态文本</summary>
        public string ConnectionStatusText => IsConnected ? "已连接" : "未连接";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

            // 当相关属性变化时，通知显示文本也变化了
            if (propertyName == nameof(NodeTitle) || propertyName == nameof(NodeType))
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(DisplayText)));
            }

            if (propertyName == nameof(IsConnected))
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ConnectionStatusText)));
            }
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
