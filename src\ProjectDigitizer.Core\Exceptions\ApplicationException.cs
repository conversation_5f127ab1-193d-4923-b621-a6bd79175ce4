namespace ProjectDigitizer.Core.Exceptions;

/// <summary>
/// 应用层异常基类
/// </summary>
public class ApplicationException : ProjectDigitizerException
{
    public ApplicationException(string message) : base(message)
    {
    }

    public ApplicationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ApplicationException(string message, string? errorCode) : base(message, errorCode)
    {
    }

    public ApplicationException(string message, string? errorCode, Exception innerException) : base(message, errorCode, innerException)
    {
    }
}

/// <summary>
/// 服务异常，表示应用服务执行失败
/// </summary>
public class ServiceException : ApplicationException
{
    public string ServiceName { get; }

    public ServiceException(string serviceName, string message) : base(message, "SERVICE_ERROR")
    {
        ServiceName = serviceName;
        WithDetail("ServiceName", serviceName);
    }

    public ServiceException(string serviceName, string message, Exception innerException) : base(message, "SERVICE_ERROR", innerException)
    {
        ServiceName = serviceName;
        WithDetail("ServiceName", serviceName);
    }
}

/// <summary>
/// 协调异常，表示多服务协调失败
/// </summary>
public class CoordinationException : ApplicationException
{
    public List<string> InvolvedServices { get; }

    public CoordinationException(List<string> involvedServices, string message) : base(message, "COORDINATION_ERROR")
    {
        InvolvedServices = involvedServices;
        WithDetail("InvolvedServices", involvedServices);
    }

    public CoordinationException(List<string> involvedServices, string message, Exception innerException) : base(message, "COORDINATION_ERROR", innerException)
    {
        InvolvedServices = involvedServices;
        WithDetail("InvolvedServices", involvedServices);
    }
}

/// <summary>
/// 权限异常，表示操作权限不足
/// </summary>
public class PermissionException : ApplicationException
{
    public string RequiredPermission { get; }
    public string? UserId { get; }

    public PermissionException(string requiredPermission, string message) : base(message, "PERMISSION_DENIED")
    {
        RequiredPermission = requiredPermission;
        WithDetail("RequiredPermission", requiredPermission);
    }

    public PermissionException(string requiredPermission, string? userId, string message) : base(message, "PERMISSION_DENIED")
    {
        RequiredPermission = requiredPermission;
        UserId = userId;
        WithDetail("RequiredPermission", requiredPermission);
        if (userId != null) WithDetail("UserId", userId);
    }

    public PermissionException(string requiredPermission, string message, Exception innerException) : base(message, "PERMISSION_DENIED", innerException)
    {
        RequiredPermission = requiredPermission;
        WithDetail("RequiredPermission", requiredPermission);
    }
}

/// <summary>
/// 操作超时异常
/// </summary>
public class OperationTimeoutException : ApplicationException
{
    public TimeSpan Timeout { get; }
    public string OperationName { get; }

    public OperationTimeoutException(string operationName, TimeSpan timeout)
        : base($"Operation '{operationName}' timed out after {timeout.TotalSeconds} seconds.", "OPERATION_TIMEOUT")
    {
        OperationName = operationName;
        Timeout = timeout;
        WithDetail("OperationName", operationName);
        WithDetail("Timeout", timeout);
    }

    public OperationTimeoutException(string operationName, TimeSpan timeout, Exception innerException)
        : base($"Operation '{operationName}' timed out after {timeout.TotalSeconds} seconds.", "OPERATION_TIMEOUT", innerException)
    {
        OperationName = operationName;
        Timeout = timeout;
        WithDetail("OperationName", operationName);
        WithDetail("Timeout", timeout);
    }
}
