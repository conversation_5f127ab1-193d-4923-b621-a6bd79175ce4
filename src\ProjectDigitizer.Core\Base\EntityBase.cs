using System.ComponentModel;
using System.Runtime.CompilerServices;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Core.Base;

/// <summary>
/// 实体基类
/// </summary>
/// <typeparam name="TId">ID类型</typeparam>
public abstract class EntityBase<TId> : IEntity<TId>, INotifyPropertyChanged, IValidatable
    where TId : notnull
{
    private TId _id;
    private DateTime _createdAt;
    private DateTime _updatedAt;
    private string _createdBy = string.Empty;
    private string _updatedBy = string.Empty;

    /// <summary>
    /// 实体ID
    /// </summary>
    public TId Id
    {
        get => _id;
        protected set => SetProperty(ref _id, value);
    }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt
    {
        get => _createdAt;
        protected set => SetProperty(ref _createdAt, value);
    }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt
    {
        get => _updatedAt;
        protected set => SetProperty(ref _updatedAt, value);
    }

    /// <summary>
    /// 创建者
    /// </summary>
    public string CreatedBy
    {
        get => _createdBy;
        protected set => SetProperty(ref _createdBy, value);
    }

    /// <summary>
    /// 更新者
    /// </summary>
    public string UpdatedBy
    {
        get => _updatedBy;
        protected set => SetProperty(ref _updatedBy, value);
    }

    /// <summary>
    /// 实体ID（非泛型接口实现）
    /// </summary>
    object IEntity.Id => Id;

    /// <summary>
    /// 属性变更事件
    /// </summary>
    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="id">实体ID</param>
    protected EntityBase(TId id)
    {
        _id = id;
        _createdAt = DateTime.UtcNow;
        _updatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 验证实体
    /// </summary>
    /// <returns>验证结果</returns>
    public virtual ValidationResult Validate()
    {
        var result = new ValidationResult();

        // 基本验证
        if (Id == null || Id.Equals(default(TId)))
        {
            result.AddError("实体ID不能为空");
        }

        if (CreatedAt == default)
        {
            result.AddError("创建时间不能为空");
        }

        if (UpdatedAt == default)
        {
            result.AddError("更新时间不能为空");
        }

        if (UpdatedAt < CreatedAt)
        {
            result.AddError("更新时间不能早于创建时间");
        }

        // 调用子类的验证逻辑
        ValidateCore(result);

        return result;
    }

    /// <summary>
    /// 核心验证逻辑（由子类实现）
    /// </summary>
    /// <param name="result">验证结果</param>
    protected virtual void ValidateCore(ValidationResult result)
    {
        // 子类可以重写此方法添加特定的验证逻辑
    }

    /// <summary>
    /// 更新实体的审计信息
    /// </summary>
    /// <param name="updatedBy">更新者</param>
    public virtual void UpdateAuditInfo(string updatedBy)
    {
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy ?? string.Empty;
    }

    /// <summary>
    /// 设置属性值并触发属性变更通知
    /// </summary>
    /// <typeparam name="T">属性类型</typeparam>
    /// <param name="field">字段引用</param>
    /// <param name="value">新值</param>
    /// <param name="propertyName">属性名称</param>
    /// <returns>是否发生了变更</returns>
    protected virtual bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);

        // 更新审计信息（除了审计字段本身）
        if (propertyName != nameof(UpdatedAt) && propertyName != nameof(UpdatedBy))
        {
            UpdatedAt = DateTime.UtcNow;
        }

        return true;
    }

    /// <summary>
    /// 触发属性变更通知
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 相等性比较
    /// </summary>
    /// <param name="obj">比较对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not EntityBase<TId> other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        return EqualityComparer<TId>.Default.Equals(Id, other.Id);
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return Id?.GetHashCode() ?? 0;
    }

    /// <summary>
    /// 相等性操作符
    /// </summary>
    public static bool operator ==(EntityBase<TId>? left, EntityBase<TId>? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// 不等性操作符
    /// </summary>
    public static bool operator !=(EntityBase<TId>? left, EntityBase<TId>? right)
    {
        return !Equals(left, right);
    }

    /// <summary>
    /// 字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{GetType().Name}[Id={Id}]";
    }
}

/// <summary>
/// 字符串ID实体基类
/// </summary>
public abstract class EntityBase : EntityBase<string>
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected EntityBase() : base(Guid.NewGuid().ToString())
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="id">实体ID</param>
    protected EntityBase(string id) : base(id)
    {
    }
}
