using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.Services;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Selectors
{
    // 属性面板模板选择器：优先按“PropertyPanel.ModuleType.{ModuleType}”从资源查找插件模板
    public class NodePropertyDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate? InputNodeTemplate { get; set; }
        public DataTemplate? TransformNodeTemplate { get; set; }
        public DataTemplate? OutputNodeTemplate { get; set; }
        public DataTemplate? ControlNodeTemplate { get; set; }
        public DataTemplate? DefaultTemplate { get; set; }

        private static DataTemplate? TryFindModuleTemplate(ModuleType moduleType, string prefix)
        {
            try
            {
                var key = $"{prefix}.{moduleType}";
                if (System.Windows.Application.Current != null && System.Windows.Application.Current.Resources.Contains(key))
                {
                    if (System.Windows.Application.Current.Resources[key] is DataTemplate dt)
                    {
                        return dt;
                    }
                }
            }
            catch
            {
                // 忽略资源查找异常
            }

            return null;
        }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ModuleNodeViewModel nodeViewModel && nodeViewModel.Module != null)
            {
                // 插件覆盖（按约定键）
                var pluginTemplate = TryFindModuleTemplate(nodeViewModel.Module.Type, "PropertyPanel.ModuleType");
                if (pluginTemplate != null)
                {
                    return pluginTemplate;
                }

                // 按节点大类回退
                var nodeType = NodeTypeRegistry.GetNodeType(nodeViewModel.Module.Type);
                return nodeType switch
                {
                    NodeType.Input => InputNodeTemplate ?? DefaultTemplate,
                    NodeType.Transform => TransformNodeTemplate ?? DefaultTemplate,
                    NodeType.Output => OutputNodeTemplate ?? DefaultTemplate,
                    NodeType.Control => ControlNodeTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }

    // 节点模板选择器（画布节点）：优先按“NodeTemplate.ModuleType.{ModuleType}”从资源查找插件模板
    public class NodeTypeDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate? InputNodeTemplate { get; set; }
        public DataTemplate? TransformNodeTemplate { get; set; }
        public DataTemplate? OutputNodeTemplate { get; set; }
        public DataTemplate? ControlNodeTemplate { get; set; }
        public DataTemplate? DefaultTemplate { get; set; }

        private static DataTemplate? TryFindNodeTemplate(ModuleType moduleType)
        {
            try
            {
                var key = $"NodeTemplate.ModuleType.{moduleType}";
                if (System.Windows.Application.Current != null && System.Windows.Application.Current.Resources.Contains(key))
                {
                    if (System.Windows.Application.Current.Resources[key] is DataTemplate dt)
                    {
                        return dt;
                    }
                }
            }
            catch
            {
            }

            return null;
        }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ModuleNodeViewModel nodeViewModel && nodeViewModel.Module != null)
            {
                // 插件覆盖（按约定键）
                var pluginTemplate = TryFindNodeTemplate(nodeViewModel.Module.Type);
                if (pluginTemplate != null)
                {
                    return pluginTemplate;
                }

                // 按节点大类回退
                var nodeType = NodeTypeRegistry.GetNodeType(nodeViewModel.Module.Type);
                return nodeType switch
                {
                    NodeType.Input => InputNodeTemplate ?? DefaultTemplate,
                    NodeType.Transform => TransformNodeTemplate ?? DefaultTemplate,
                    NodeType.Output => OutputNodeTemplate ?? DefaultTemplate,
                    NodeType.Control => ControlNodeTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }

    // 连接器模板选择器（保持不变）
    public class ConnectorTypeDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate? InputConnectorTemplate { get; set; }
        public DataTemplate? OutputConnectorTemplate { get; set; }
        public DataTemplate? DataConnectorTemplate { get; set; }
        public DataTemplate? ControlConnectorTemplate { get; set; }
        public DataTemplate? DefaultTemplate { get; set; }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ConnectorViewModel connectorViewModel)
            {
                return connectorViewModel.DataType switch
                {
                    "Input" => InputConnectorTemplate ?? DefaultTemplate,
                    "Output" => OutputConnectorTemplate ?? DefaultTemplate,
                    "Data" => DataConnectorTemplate ?? DefaultTemplate,
                    "Control" => ControlConnectorTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }
}
