namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 视口矩形（与 WPF Rect 解耦）
    /// </summary>
    public readonly struct ViewportRect
    {
        public double X { get; }
        public double Y { get; }
        public double Width { get; }
        public double Height { get; }

        public ViewportRect(double x, double y, double width, double height)
        {
            X = x;
            Y = y;
            Width = width;
            Height = height;
        }
    }

    /// <summary>
    /// 视口尺寸（与 WPF Size 解耦）
    /// </summary>
    public readonly struct ViewportSize
    {
        public double Width { get; }
        public double Height { get; }

        public ViewportSize(double width, double height)
        {
            Width = width;
            Height = height;
        }
    }
}

