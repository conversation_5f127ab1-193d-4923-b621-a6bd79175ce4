using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 多值：根据数据类型与输入/输出方向映射到主题颜色。
    /// </summary>
    public class ConnectorDataTypeToColorConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 2) return GetDefaultColor();

            var dataType = values[0] as ConnectorDataType? ?? ConnectorDataType.Any;
            var isInput = values[1] as bool? ?? false;

            return GetConnectorColor(dataType, isInput);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private Color GetConnectorColor(ConnectorDataType dataType, bool isInput)
        {
            // 基础色从资源读取，找不到则回退旧值
            var inputBase = Color.FromRgb(76, 175, 80);   // #4CAF50
            var outputBase = Color.FromRgb(33, 150, 243); // #2196F3
            Color baseColor = isInput
                ? GetColorFromBrushResource("InputConnectionBrush", inputBase)
                : GetColorFromBrushResource("OutputConnectionBrush", outputBase);

            switch (dataType)
            {
                case ConnectorDataType.Any:
                    return baseColor;

                case ConnectorDataType.Number:
                    return GetColorFromBrushResource("Connector.Number.Brush", Color.FromRgb(255, 152, 0));

                case ConnectorDataType.Text:
                    return GetColorFromBrushResource("Connector.Text.Brush", Color.FromRgb(156, 39, 176));

                case ConnectorDataType.Boolean:
                    return GetColorFromBrushResource("Connector.Boolean.Brush", Color.FromRgb(244, 67, 54));

                case ConnectorDataType.File:
                    return GetColorFromBrushResource("Connector.File.Brush", Color.FromRgb(56, 142, 60));

                case ConnectorDataType.Geometry:
                    return GetColorFromBrushResource("Connector.Geometry.Brush", Color.FromRgb(0, 188, 212));

                case ConnectorDataType.Control:
                    return GetColorFromBrushResource("Connector.Control.Brush", Color.FromRgb(123, 31, 162));

                default:
                    return baseColor;
            }
        }

        private Color GetDefaultColor() => Color.FromRgb(158, 158, 158); // #9E9E9E

        private static Color GetColorFromBrushResource(string key, Color fallback)
        {
            var brush = System.Windows.Application.Current?.TryFindResource(key) as SolidColorBrush;
            return brush?.Color ?? fallback;
        }
    }

    /// <summary>
    /// 单值：仅根据数据类型映射到主题颜色。
    /// </summary>
    public class ConnectorDataTypeToColorSimpleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ConnectorDataType dataType)
            {
                return GetDataTypeColor(dataType);
            }

            return GetDefaultColor();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private Color GetDataTypeColor(ConnectorDataType dataType)
        {
            switch (dataType)
            {
                case ConnectorDataType.Any:
                    return Color.FromRgb(158, 158, 158);

                case ConnectorDataType.Number:
                    return GetColorFromBrushResource("Connector.Number.Brush", Color.FromRgb(255, 152, 0));

                case ConnectorDataType.Text:
                    return GetColorFromBrushResource("Connector.Text.Brush", Color.FromRgb(156, 39, 176));

                case ConnectorDataType.Boolean:
                    return GetColorFromBrushResource("Connector.Boolean.Brush", Color.FromRgb(244, 67, 54));

                case ConnectorDataType.File:
                    return GetColorFromBrushResource("Connector.File.Brush", Color.FromRgb(56, 142, 60));

                case ConnectorDataType.Geometry:
                    return GetColorFromBrushResource("Connector.Geometry.Brush", Color.FromRgb(0, 188, 212));

                case ConnectorDataType.Control:
                    return GetColorFromBrushResource("Connector.Control.Brush", Color.FromRgb(123, 31, 162));

                default:
                    return GetDefaultColor();
            }
        }

        private Color GetDefaultColor() => Color.FromRgb(158, 158, 158); // #9E9E9E

        private static Color GetColorFromBrushResource(string key, Color fallback)
        {
            var brush = System.Windows.Application.Current?.TryFindResource(key) as SolidColorBrush;
            return brush?.Color ?? fallback;
        }
    }
}

