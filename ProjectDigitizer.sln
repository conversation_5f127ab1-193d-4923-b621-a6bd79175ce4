﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36109.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Core", "src\ProjectDigitizer.Core\ProjectDigitizer.Core.csproj", "{B1C2D3E4-F5A6-7890-BCDE-F12345678901}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Application", "src\ProjectDigitizer.Application\ProjectDigitizer.Application.csproj", "{C2D3E4F5-A6B7-8901-CDEF-123456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Infrastructure", "src\ProjectDigitizer.Infrastructure\ProjectDigitizer.Infrastructure.csproj", "{D3E4F5A6-B7C8-9012-DEF1-234567890123}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Studio", "src\ProjectDigitizer.Studio\ProjectDigitizer.Studio.csproj", "{E4F5A6B7-C8D9-0123-EF12-345678901234}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{F5A6B7C8-D9E0-1234-F123-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Doc", "Doc", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Core.Tests", "tests\ProjectDigitizer.Core.Tests\ProjectDigitizer.Core.Tests.csproj", "{875CF432-16AC-4C56-90B3-CCE12866B240}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Application.Tests", "tests\ProjectDigitizer.Application.Tests\ProjectDigitizer.Application.Tests.csproj", "{5FCE5848-7E30-48C7-BC1E-851C930B90C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Infrastructure.Tests", "tests\ProjectDigitizer.Infrastructure.Tests\ProjectDigitizer.Infrastructure.Tests.csproj", "{DC560003-E2F9-48A2-B407-AA2039F541FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Studio.Tests", "tests\ProjectDigitizer.Studio.Tests\ProjectDigitizer.Studio.Tests.csproj", "{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProjectDigitizer.Integration.Tests", "tests\ProjectDigitizer.Integration.Tests\ProjectDigitizer.Integration.Tests.csproj", "{68E4F962-00DF-4A05-A897-5220A5F44A24}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Plugins.DataCalculation", "src\Plugins.DataCalculation\Plugins.DataCalculation.csproj", "{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2D3E4F5-A6B7-8901-CDEF-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2D3E4F5-A6B7-8901-CDEF-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2D3E4F5-A6B7-8901-CDEF-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2D3E4F5-A6B7-8901-CDEF-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3E4F5A6-B7C8-9012-DEF1-234567890123}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3E4F5A6-B7C8-9012-DEF1-234567890123}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3E4F5A6-B7C8-9012-DEF1-234567890123}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3E4F5A6-B7C8-9012-DEF1-234567890123}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4F5A6B7-C8D9-0123-EF12-345678901234}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4F5A6B7-C8D9-0123-EF12-345678901234}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4F5A6B7-C8D9-0123-EF12-345678901234}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4F5A6B7-C8D9-0123-EF12-345678901234}.Release|Any CPU.Build.0 = Release|Any CPU
		{875CF432-16AC-4C56-90B3-CCE12866B240}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{875CF432-16AC-4C56-90B3-CCE12866B240}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{875CF432-16AC-4C56-90B3-CCE12866B240}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{875CF432-16AC-4C56-90B3-CCE12866B240}.Release|Any CPU.Build.0 = Release|Any CPU
		{5FCE5848-7E30-48C7-BC1E-851C930B90C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FCE5848-7E30-48C7-BC1E-851C930B90C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FCE5848-7E30-48C7-BC1E-851C930B90C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FCE5848-7E30-48C7-BC1E-851C930B90C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC560003-E2F9-48A2-B407-AA2039F541FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC560003-E2F9-48A2-B407-AA2039F541FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC560003-E2F9-48A2-B407-AA2039F541FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC560003-E2F9-48A2-B407-AA2039F541FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{68E4F962-00DF-4A05-A897-5220A5F44A24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68E4F962-00DF-4A05-A897-5220A5F44A24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68E4F962-00DF-4A05-A897-5220A5F44A24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68E4F962-00DF-4A05-A897-5220A5F44A24}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B1C2D3E4-F5A6-7890-BCDE-F12345678901} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{C2D3E4F5-A6B7-8901-CDEF-123456789012} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{D3E4F5A6-B7C8-9012-DEF1-234567890123} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{E4F5A6B7-C8D9-0123-EF12-345678901234} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{875CF432-16AC-4C56-90B3-CCE12866B240} = {F5A6B7C8-D9E0-1234-F123-************}
		{5FCE5848-7E30-48C7-BC1E-851C930B90C2} = {F5A6B7C8-D9E0-1234-F123-************}
		{DC560003-E2F9-48A2-B407-AA2039F541FE} = {F5A6B7C8-D9E0-1234-F123-************}
		{F5B9C1AA-F43C-487A-9F14-C4868BB02DDD} = {F5A6B7C8-D9E0-1234-F123-************}
		{68E4F962-00DF-4A05-A897-5220A5F44A24} = {F5A6B7C8-D9E0-1234-F123-************}
		{C7D6F0B2-6E0D-4E1E-B7C6-9D1E1DDE5F77} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8F73DCAA-E9CA-42DE-BE1C-8675B9C75DA1}
	EndGlobalSection
EndGlobal
