using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;

using Serilog;
using Serilog.Context;

using AppLogLevel = ProjectDigitizer.Application.Interfaces.LogLevel;
using ILogger = Microsoft.Extensions.Logging.ILogger;
using MsLogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace ProjectDigitizer.Infrastructure.Logging;

/// <summary>
/// 日志记录服务实现
/// 基于Microsoft.Extensions.Logging和Serilog的结构化日志记录
/// </summary>
public class LoggingService : ILoggingService
{
    private readonly ILogger<LoggingService> _logger;
    private readonly Serilog.ILogger _serilogLogger;

    public LoggingService(ILogger<LoggingService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serilogLogger = Log.Logger;
    }

    /// <summary>
    /// 记录信息日志
    /// </summary>
    public void LogInformation(string message, params object[] args)
    {
        try
        {
            _logger.LogInformation(message, args);
        }
        catch (Exception ex)
        {
            // 防止日志记录本身出错影响应用程序
            System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 记录警告日志
    /// </summary>
    public void LogWarning(string message, params object[] args)
    {
        try
        {
            _logger.LogWarning(message, args);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 记录错误日志
    /// </summary>
    public void LogError(Exception exception, string message, params object[] args)
    {
        try
        {
            _logger.LogError(exception, message, args);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 记录调试日志
    /// </summary>
    public void LogDebug(string message, params object[] args)
    {
        try
        {
            _logger.LogDebug(message, args);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    public void LogStructured(AppLogLevel level, string message, Dictionary<string, object> properties)
    {
        try
        {
            var serilogLevel = ConvertToSerilogLevel(level);

            using (LogContext.PushProperty("StructuredProperties", properties, true))
            {
                _serilogLogger.Write(serilogLevel, message);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"结构化日志记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 配置日志上下文
    /// </summary>
    public IDisposable BeginScope(Dictionary<string, object> contextProperties)
    {
        try
        {
            var scopeProperties = new List<IDisposable>();

            foreach (var property in contextProperties)
            {
                var scope = LogContext.PushProperty(property.Key, property.Value);
                scopeProperties.Add(scope);
            }

            return new CompositeDisposable(scopeProperties);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"创建日志上下文失败: {ex.Message}");
            return new EmptyDisposable();
        }
    }

    /// <summary>
    /// 转换日志级别
    /// </summary>
    private static Serilog.Events.LogEventLevel ConvertToSerilogLevel(AppLogLevel level)
    {
        return level switch
        {
            AppLogLevel.Trace => Serilog.Events.LogEventLevel.Verbose,
            AppLogLevel.Debug => Serilog.Events.LogEventLevel.Debug,
            AppLogLevel.Information => Serilog.Events.LogEventLevel.Information,
            AppLogLevel.Warning => Serilog.Events.LogEventLevel.Warning,
            AppLogLevel.Error => Serilog.Events.LogEventLevel.Error,
            AppLogLevel.Critical => Serilog.Events.LogEventLevel.Fatal,
            _ => Serilog.Events.LogEventLevel.Information
        };
    }
}

/// <summary>
/// 组合释放器
/// 用于管理多个IDisposable对象
/// </summary>
internal class CompositeDisposable : IDisposable
{
    private readonly List<IDisposable> _disposables;
    private bool _disposed = false;

    public CompositeDisposable(List<IDisposable> disposables)
    {
        _disposables = disposables ?? throw new ArgumentNullException(nameof(disposables));
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var disposable in _disposables)
            {
                try
                {
                    disposable?.Dispose();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"释放资源时发生错误: {ex.Message}");
                }
            }

            _disposables.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 空释放器
/// 用于异常情况下的占位符
/// </summary>
internal class EmptyDisposable : IDisposable
{
    public void Dispose()
    {
        // 空实现
    }
}
