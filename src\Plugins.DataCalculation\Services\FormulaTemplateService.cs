using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

using Plugins.DataCalculation.Models;

namespace Plugins.DataCalculation.Services
{
    public class FormulaTemplateService
    {
        private readonly List<FormulaCategory> _categories = new();
        private readonly List<FormulaTemplate> _templates = new();
        private readonly Dictionary<string, FormulaTemplate> _templateCache = new();
        private bool _isInitialized = false;

        public IReadOnlyList<FormulaCategory> Categories => _categories.AsReadOnly();
        public IReadOnlyList<FormulaTemplate> Templates => _templates.AsReadOnly();

        public async Task InitializeAsync()
        {
            if (_isInitialized) return;
            await LoadBuiltinFormulasAsync();
            _isInitialized = true;
        }

        public IEnumerable<FormulaTemplate> SearchTemplates(string keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword)) return _templates;
            var k = keyword.ToLowerInvariant();
            return _templates.Where(t =>
                t.Name.ToLowerInvariant().Contains(k) ||
                t.Description.ToLowerInvariant().Contains(k) ||
                t.Tags.Any(tag => tag.ToLowerInvariant().Contains(k))
            );
        }

        private async Task LoadBuiltinFormulasAsync()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceName = "Plugins.DataCalculation.Data.BuiltinFormulas.json";
                using var stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    CreateDefaultTemplates();
                    return;
                }
                using var reader = new StreamReader(stream);
                var jsonContent = await reader.ReadToEndAsync();
                var formulaData = JsonSerializer.Deserialize<FormulaData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    Converters =
                    {
                        new FormulaComplexityJsonConverter(),
                        new JsonStringEnumConverter() // parse enum values like "Number", "Text" etc.
                    }
                });
                if (formulaData != null)
                {
                    _categories.Clear();
                    _categories.AddRange(formulaData.Categories);
                    _templates.Clear();
                    _templateCache.Clear();
                    foreach (var t in formulaData.Templates)
                    {
                        _templates.Add(t);
                        _templateCache[t.Id] = t;
                        var cat = _categories.FirstOrDefault(c => c.Id == t.Category);
                        cat?.Templates.Add(t);
                    }
                }
                else
                {
                    CreateDefaultTemplates();
                }
            }
            catch
            {
                CreateDefaultTemplates();
            }
        }

        private void CreateDefaultTemplates()
        {
            var geometry = new FormulaCategory { Id = "geometry", Name = "几何计算", Description = "常见图形计算", Color = "#4CAF50", Order = 1 };
            _categories.Add(geometry);
            var circle = new FormulaTemplate
            {
                Id = "circle_area",
                Name = "圆面积",
                Description = "圆的面积",
                Expression = "PI() * POWER(r, 2)",
                Category = "geometry",
                Unit = "m2",
                Complexity = FormulaComplexity.Simple,
                IsBuiltIn = true,
                Parameters = new List<FormulaParameter> { new FormulaParameter { Name = "r", DisplayName = "半径", DataType = FieldDataType.Number, IsRequired = true, MinValue = 0, Unit = "m" } },
                Examples = new List<string> { "半径5m: 78.54 m2" },
                Tags = new List<string> { "圆", "面积" }
            };
            _templates.Add(circle);
            _templateCache[circle.Id] = circle;
            geometry.Templates.Add(circle);
        }
    }

    public class FormulaData
    {
        public List<FormulaCategory> Categories { get; set; } = new();
        public List<FormulaTemplate> Templates { get; set; } = new();
    }
}

