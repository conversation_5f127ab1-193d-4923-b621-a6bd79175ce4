using System;
using System.Threading.Tasks;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Exceptions;

namespace ProjectDigitizer.Application.Services.ErrorRecoveryStrategies;

/// <summary>
/// UI错误恢复策略
/// </summary>
public class UIErrorRecoveryStrategy : IErrorRecoveryStrategy
{
    public int Priority => 15;

    public bool CanRecover(Exception exception)
    {
        return exception is UIException;
    }

    public async Task<RecoveryResult> RecoverAsync(Exception exception, object? context)
    {
        await Task.Delay(50); // 模拟恢复延迟

        return exception switch
        {
            DataBindingException bindingEx => await RecoverDataBindingException(bindingEx, context),
            NavigationException navEx => await RecoverNavigationException(navEx, context),
            CommandExecutionException cmdEx => await RecoverCommandExecutionException(cmdEx, context),
            RenderingException renderEx => await RecoverRenderingException(renderEx, context),
            _ => RecoveryResult.Failure("不支持的UI异常类型")
        };
    }

    private async Task<RecoveryResult> RecoverDataBindingException(DataBindingException exception, object? context)
    {
        await Task.Delay(25);

        // 数据绑定异常通常需要刷新UI
        return RecoveryResult.Success(
            $"属性 {exception.PropertyName} 数据绑定失败，建议刷新界面",
            new { PropertyName = exception.PropertyName, ViewModelType = exception.ViewModelType, SuggestedAction = "RefreshUI" });
    }

    private async Task<RecoveryResult> RecoverNavigationException(NavigationException exception, object? context)
    {
        await Task.Delay(25);

        // 导航异常可能需要重置导航状态
        return RecoveryResult.Success(
            "页面导航失败，建议返回主页面",
            new { SourceView = exception.SourceView, TargetView = exception.TargetView, SuggestedAction = "NavigateToHome" });
    }

    private async Task<RecoveryResult> RecoverCommandExecutionException(CommandExecutionException exception, object? context)
    {
        await Task.Delay(25);

        // 命令执行异常可以建议重试
        return RecoveryResult.Success(
            $"命令 {exception.CommandName} 执行失败，可以重试",
            new { CommandName = exception.CommandName, CommandParameter = exception.CommandParameter, CanRetry = true });
    }

    private async Task<RecoveryResult> RecoverRenderingException(RenderingException exception, object? context)
    {
        await Task.Delay(25);

        // 渲染异常需要重新渲染
        return RecoveryResult.Success(
            "界面渲染失败，建议刷新界面",
            new { ComponentName = exception.ComponentName, SuggestedAction = "RefreshComponent" });
    }
}
