<UserControl x:Class="ProjectDigitizer.Studio.Controls.Functions.AvalonFormulaEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="160"
             d:DesignWidth="400">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 公式编辑器 -->
        <avalonEdit:TextEditor x:Name="Editor"
                               Grid.Row="0"
                               FontFamily="Consolas"
                               FontSize="12"
                               ShowLineNumbers="True"
                               WordWrap="True"
                               HorizontalScrollBarVisibility="Auto"
                               VerticalScrollBarVisibility="Auto"/>

        <!-- 签名与说明信息条（根据光标上下文动态展示） -->
        <Border Grid.Row="1"
                Background="{StaticResource Brush.SurfaceVariant}"
                BorderBrush="{StaticResource Brush.Border}"
                BorderThickness="1,1,1,0"
                Padding="6"
                Visibility="Collapsed"
                x:Name="InfoPanel">
            <StackPanel>
                <!-- 签名/标题行（原 InfoTextBlock 保留兼容） -->
                <TextBlock x:Name="InfoTextBlock"
                           TextWrapping="Wrap"
                           Foreground="{StaticResource Brush.Text}"
                           FontSize="12"
                           FontWeight="SemiBold"/>
                <!-- 参数说明行：根据当前参数位置加粗对应参数名 -->
                <TextBlock x:Name="InfoParamsTextBlock"
                           Margin="0,2,0,0"
                           TextWrapping="Wrap"
                           Foreground="{StaticResource Brush.TextSecondary}"
                           FontSize="11"/>
                <!-- 示例行 -->
                <TextBlock x:Name="InfoExampleTextBlock"
                           Margin="0,2,0,0"
                           TextWrapping="Wrap"
                           Foreground="{StaticResource Brush.TextSecondary}"
                           FontSize="11"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
