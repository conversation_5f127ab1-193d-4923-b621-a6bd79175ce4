namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 查询处理器接口
/// </summary>
/// <typeparam name="TQuery">查询类型</typeparam>
/// <typeparam name="TResult">结果类型</typeparam>
public interface IQueryHandler<in TQuery, TResult> where TQuery : class
{
    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="query">查询对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>查询结果</returns>
    Task<TResult> HandleAsync(TQuery query, CancellationToken cancellationToken = default);
}
