using System;
using System.Globalization;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 贝塞尔曲线箭头位置转换器
    /// 专门为贝塞尔曲线计算箭头的精确位置，考虑曲线终点的切线方向
    /// </summary>
    public class BezierArrowPositionConverter : IMultiValueConverter
    {
        /// <summary>
        /// 将源点和目标点转换为贝塞尔曲线箭头位置
        /// </summary>
        /// <param name="values">values[0]: Source.X, values[1]: Source.Y, values[2]: Target.X, values[3]: Target.Y</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数（"X" 或 "Y"）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>箭头的X或Y坐标</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 4)
                return 0.0;

            // 尝试解析坐标值
            if (!TryParseDouble(values[0], out double sourceX) ||
                !TryParseDouble(values[1], out double sourceY) ||
                !TryParseDouble(values[2], out double targetX) ||
                !TryParseDouble(values[3], out double targetY))
            {
                return 0.0;
            }

            // 计算贝塞尔曲线的控制点（模拟Nodify的默认行为）
            // Nodify的Connection控件默认使用水平方向的控制点
            double spacing = 15.0; // 默认值

            // 如果提供了spacing参数，使用它
            if (values.Length >= 5 && TryParseDouble(values[4], out double providedSpacing))
            {
                spacing = providedSpacing;
            }

            // 计算控制点
            double controlPoint1X = sourceX + spacing;
            double controlPoint1Y = sourceY;
            double controlPoint2X = targetX - spacing;
            double controlPoint2Y = targetY;

            // 计算贝塞尔曲线在终点处的切线方向
            // 对于三次贝塞尔曲线，终点处的切线方向是从倒数第二个控制点指向终点
            double tangentX = targetX - controlPoint2X;
            double tangentY = targetY - controlPoint2Y;

            // 如果切线长度太小，回退到直线方向
            double tangentLength = Math.Sqrt(tangentX * tangentX + tangentY * tangentY);
            if (tangentLength < 0.001)
            {
                tangentX = targetX - sourceX;
                tangentY = targetY - sourceY;
                tangentLength = Math.Sqrt(tangentX * tangentX + tangentY * tangentY);
            }

            // 避免除零错误
            if (tangentLength < 0.001)
            {
                return parameter?.ToString() == "X" ? targetX : targetY;
            }

            // 标准化切线向量与法向量
            double ux = tangentX / tangentLength;
            double uy = tangentY / tangentLength;
            double nx = -uy;
            double ny = ux;

            // 读取可选参数：同端口连接序号与线宽（若绑定有提供）
            int connectionIndex = 1;
            double strokeThickness = 2.5;
            if (values.Length >= 6)
            {
                if (values[5] is int ci) connectionIndex = Math.Max(1, ci);
                else if (TryParseDouble(values[5], out double cid)) connectionIndex = Math.Max(1, (int)Math.Round(cid));
            }
            if (values.Length >= 7 && TryParseDouble(values[6], out double st)) strokeThickness = st;

            // 前向与侧向偏移（与直线一致的策略）
            double arrowLength = 18.0; // 与箭头宽度一致，确保箭头完全在外
            double forward = arrowLength + strokeThickness * 0.5; // 前探，避免压线
            double separationBase = Math.Max(2.0, strokeThickness * 0.8);
            int idx = Math.Max(0, connectionIndex - 1);
            int side = (idx % 2 == 0) ? 1 : -1; // 左右交替
            int level = (idx / 2) + 0;
            double lateral = (idx == 0 ? 0.0 : separationBase * level) * side;

            // 目标位置：终点 - 前向 * 切线 + 侧向 * 法向
            double arrowX = targetX - ux * forward + nx * lateral;
            double arrowY = targetY - uy * forward + ny * lateral;

            // 返回对应的坐标
            double result = parameter?.ToString() == "X" ? arrowX : arrowY;

            System.Diagnostics.Debug.WriteLine($"BezierArrowPositionConverter: Source=({sourceX},{sourceY}), Target=({targetX},{targetY}), " +
                                             $"ControlPoint2=({controlPoint2X},{controlPoint2Y}), Tangent=({ux},{uy}), " +
                                             $"Arrow=({arrowX},{arrowY}), Parameter={parameter}, Result={result}");

            // 额外的控制台输出，确保我们能看到
            Console.WriteLine($"[BEZIER ARROW] {parameter}: {result}");
            Console.WriteLine($"[BEZIER LINE] Target{parameter}: {(parameter?.ToString() == "X" ? targetX : targetY)}");

            return result;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("BezierArrowPositionConverter does not support ConvertBack");
        }

        /// <summary>
        /// 安全解析double值
        /// </summary>
        private static bool TryParseDouble(object value, out double result)
        {
            result = 0.0;

            if (value == null)
                return false;

            if (value is double d)
            {
                result = d;
                return true;
            }

            if (value is float f)
            {
                result = f;
                return true;
            }

            if (value is int i)
            {
                result = i;
                return true;
            }

            return double.TryParse(value.ToString(), out result);
        }
    }
}
