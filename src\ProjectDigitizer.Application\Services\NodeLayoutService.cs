using System;
using System.Collections.Generic;
using System.Linq;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Application.Services
{
    /// <summary>
    /// 节点自动布局服务
    /// </summary>
    public class NodeLayoutService : INodeLayoutService
    {
        /// <summary>
        /// 执行自动布局
        /// </summary>
        /// <param name="nodes">要布局的节点集合</param>
        /// <param name="connections">连接关系</param>
        /// <param name="algorithm">布局算法</param>
        /// <param name="options">布局选项</param>
        /// <returns>布局结果</returns>
        public LayoutResult CalculateLayout(
            IEnumerable<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutAlgorithm algorithm,
            LayoutOptions? options = null)
        {
            try
            {
                options ??= new LayoutOptions();
                var nodeList = nodes.ToList();

                if (!nodeList.Any())
                    return new LayoutResult { Success = true };

                return algorithm switch
                {
                    LayoutAlgorithm.Hierarchical => CalculateHierarchicalLayout(nodeList, connections, options),
                    LayoutAlgorithm.ForceDirected => CalculateForceDirectedWithComponents(nodeList, connections, options),
                    LayoutAlgorithm.TreeTidy => CalculateTreeTidyWithComponents(nodeList, connections, options, radial: false),
                    LayoutAlgorithm.RadialTree => CalculateTreeTidyWithComponents(nodeList, connections, options, radial: true),
                    _ => new LayoutResult
                    {
                        Success = false,
                        ErrorMessage = $"不支持的布局算法: {algorithm}"
                    }
                };
            }
            catch (Exception ex)
            {
                return new LayoutResult
                {
                    Success = false,
                    ErrorMessage = $"布局计算失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 计算层次布局
        /// </summary>
        private LayoutResult CalculateHierarchicalLayout(
            List<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutOptions options)
        {
            var result = new LayoutResult();
            var connectionList = connections?.ToList() ?? new List<ConnectionInfo>();

            // 分离锁定和未锁定的节点
            var unlockedNodes = nodes.Where(n => !n.IsLocked).ToList();
            var lockedNodes = nodes.Where(n => n.IsLocked).ToList();

            // 将锁定节点的当前位置添加到结果中
            foreach (var lockedNode in lockedNodes)
            {
                result.NodePositions[lockedNode.NodeId] = new NodePosition
                {
                    NodeId = lockedNode.NodeId,
                    X = lockedNode.X,
                    Y = lockedNode.Y,
                    IsLocked = true,
                    Title = lockedNode.Title,
                    NodeType = lockedNode.NodeType
                };
            }

            // 如果没有未锁定的节点，直接返回
            if (!unlockedNodes.Any())
            {
                return result;
            }

            // 构建图结构
            var graph = BuildGraph(unlockedNodes, connectionList);

            // 拓扑排序，确定层级
            var layers = PerformTopologicalSort(graph);

            // 层内排序优化（barycenter + 多轮双向 sweep + 早停）
            layers = OrderLayersByBarycenter(layers, graph, options);

            // 计算位置（带垂直居中）
            CalculatePositions(layers, options, result, lockedNodes);

            return result;
        }

        /// 层内排序：多轮双向重心排序（barycenter sweeps）+ 早停，减少交叉
        private List<List<NodePosition>> OrderLayersByBarycenter(
            List<List<NodePosition>> layers,
            Dictionary<NodePosition, List<NodePosition>> graph,
            LayoutOptions options)
        {
            if (layers == null || layers.Count <= 1) return layers;

            // 基础一次前后双向排序的函数
            static void SingleForwardBackwardSweep(
                List<List<NodePosition>> layers,
                Dictionary<NodePosition, List<NodePosition>> graph)
            {
                // 为快速查找计算每一层中节点到索引的映射
                var indexMaps = layers
                    .Select(layer => layer.Select((n, i) => new { n, i }).ToDictionary(x => x.n, x => x.i))
                    .ToList();

                // 前向一趟：按上一层重心
                for (int layerIdx = 1; layerIdx < layers.Count; layerIdx++)
                {
                    var prevIndex = indexMaps[layerIdx - 1];
                    var thisLayer = layers[layerIdx];

                    double BarycenterFromPrev(NodePosition node)
                    {
                        // 找到所有指向该节点的前驱
                        var predecessors = graph.Where(kvp => kvp.Value.Contains(node)).Select(kvp => kvp.Key).ToList();
                        if (predecessors.Count == 0) return thisLayer.IndexOf(node);
                        var avg = predecessors
                            .Where(p => prevIndex.ContainsKey(p))
                            .Select(p => (double)prevIndex[p])
                            .DefaultIfEmpty(thisLayer.IndexOf(node))
                            .Average();
                        return avg;
                    }

                    var ordered = thisLayer
                        .Select(n => new { n, bc = BarycenterFromPrev(n) })
                        .OrderBy(x => x.bc)
                        .Select(x => x.n)
                        .ToList();

                    layers[layerIdx] = ordered;
                    indexMaps[layerIdx] = ordered.Select((n, i) => new { n, i }).ToDictionary(x => x.n, x => x.i);
                }

                // 反向一趟：按下一层重心
                for (int layerIdx = layers.Count - 2; layerIdx >= 0; layerIdx--)
                {
                    var nextIndex = indexMaps[layerIdx + 1];
                    var thisLayer = layers[layerIdx];

                    double BarycenterFromNext(NodePosition node)
                    {
                        // 该节点的所有后继
                        if (!graph.TryGetValue(node, out var successors) || successors.Count == 0)
                            return thisLayer.IndexOf(node);

                        var avg = successors
                            .Where(s => nextIndex.ContainsKey(s))
                            .Select(s => (double)nextIndex[s])
                            .DefaultIfEmpty(thisLayer.IndexOf(node))
                            .Average();
                        return avg;
                    }

                    var ordered = thisLayer
                        .Select(n => new { n, bc = BarycenterFromNext(n) })
                        .OrderBy(x => x.bc)
                        .Select(x => x.n)
                        .ToList();

                    layers[layerIdx] = ordered;
                    indexMaps[layerIdx] = ordered.Select((n, i) => new { n, i }).ToDictionary(x => x.n, x => x.i);
                }
            }

            // 交叉数估算：只计算相邻层之间的边对是否交叉
            static int CountCrossings(
                List<List<NodePosition>> layers,
                Dictionary<NodePosition, List<NodePosition>> graph)
            {
                int crossings = 0;
                for (int i = 0; i < layers.Count - 1; i++)
                {
                    var upper = layers[i];
                    var lower = layers[i + 1];

                    // 为加速，建立 lower 节点到索引的映射
                    var lowerIndex = lower.Select((n, idx) => new { n, idx }).ToDictionary(x => x.n, x => x.idx);

                    // 取出所有跨 i->i+1 的边，按 upper 中的出现顺序遍历
                    var edges = new List<(int uIdx, int vIdx)>();
                    for (int u = 0; u < upper.Count; u++)
                    {
                        var uNode = upper[u];
                        if (!graph.TryGetValue(uNode, out var succ)) continue;
                        foreach (var vNode in succ)
                        {
                            if (lowerIndex.TryGetValue(vNode, out var v))
                            {
                                edges.Add((u, v));
                            }
                        }
                    }

                    // 统计 edges 序列中的逆序对（inversions）
                    for (int a = 0; a < edges.Count; a++)
                    {
                        for (int b = a + 1; b < edges.Count; b++)
                        {
                            var e1 = edges[a];
                            var e2 = edges[b];
                            if ((e1.uIdx < e2.uIdx && e1.vIdx > e2.vIdx) ||
                                (e1.uIdx > e2.uIdx && e1.vIdx < e2.vIdx))
                            {
                                crossings++;
                            }
                        }
                    }
                }
                return crossings;
            }

            // 开始多轮 sweep，记录最佳结果
            var bestLayers = layers.Select(l => l.ToList()).ToList();
            SingleForwardBackwardSweep(bestLayers, graph);
            var bestCross = CountCrossings(bestLayers, graph);

            var currentLayers = bestLayers.Select(l => l.ToList()).ToList();
            for (int round = 2; round <= Math.Max(1, options.MaxSweepRounds); round++)
            {
                SingleForwardBackwardSweep(currentLayers, graph);
                var cross = CountCrossings(currentLayers, graph);

                // 如果改善明显，则记录最佳
                if (cross < bestCross)
                {
                    var improvement = (bestCross - cross) / (double)Math.Max(1, bestCross);
                    bestCross = cross;
                    bestLayers = currentLayers.Select(l => l.ToList()).ToList();

                    // 提前停止：改善比例过小
                    if (improvement < options.EarlyStopThreshold)
                    {
                        break;
                    }
                }
                else
                {
                    // 未改善，稍作扰动：把奇偶层的两个相邻元素适度交换一次，尝试跳出局部最优
                    var clone = currentLayers.Select(l => l.ToList()).ToList();
                    for (int i = 0; i < clone.Count; i++)
                    {
                        if (clone[i].Count > 3 && i % 2 == 0)
                        {
                            // 交换中间一对
                            var mid = clone[i].Count / 2;
                            (clone[i][mid - 1], clone[i][mid]) = (clone[i][mid], clone[i][mid - 1]);
                        }
                    }
                    currentLayers = clone;
                }
            }

            return bestLayers;
        }

        /// <summary>
        /// 构建图结构
        /// </summary>
        private Dictionary<NodePosition, List<NodePosition>> BuildGraph(
            List<NodePosition> nodes,
            List<ConnectionInfo> connections)
        {
            var graph = new Dictionary<NodePosition, List<NodePosition>>();

            // 初始化图
            foreach (var node in nodes)
            {
                graph[node] = new List<NodePosition>();
            }

            // 添加边
            foreach (var connection in connections)
            {
                var sourceNode = nodes.FirstOrDefault(n => n.NodeId == connection.SourceNodeId);
                var targetNode = nodes.FirstOrDefault(n => n.NodeId == connection.TargetNodeId);

                if (sourceNode != null && targetNode != null &&
                    graph.ContainsKey(sourceNode) && graph.ContainsKey(targetNode))
                {
                    graph[sourceNode].Add(targetNode);
                }
            }

            return graph;
        }

        /// <summary>
        /// 拓扑排序
        /// </summary>
        private List<List<NodePosition>> PerformTopologicalSort(
            Dictionary<NodePosition, List<NodePosition>> graph)
        {
            var layers = new List<List<NodePosition>>();
            var inDegree = new Dictionary<NodePosition, int>();
            var processed = new HashSet<NodePosition>();

            // 计算入度
            foreach (var node in graph.Keys)
            {
                inDegree[node] = 0;
            }

            foreach (var kvp in graph)
            {
                foreach (var target in kvp.Value)
                {
                    if (inDegree.ContainsKey(target))
                    {
                        inDegree[target]++;
                    }
                }
            }

            // 分层处理
            while (processed.Count < graph.Count)
            {
                var currentLayer = new List<NodePosition>();

                // 找到当前层的节点（入度为0的节点）
                foreach (var node in graph.Keys)
                {
                    if (!processed.Contains(node) && inDegree[node] == 0)
                    {
                        currentLayer.Add(node);
                    }
                }

                // 如果没有找到入度为0的节点，选择入度最小的节点打破循环
                if (!currentLayer.Any())
                {
                    var remaining = graph.Keys.Where(n => !processed.Contains(n)).ToList();
                    if (remaining.Any())
                    {
                        var nodeToBreakCycle = remaining.OrderBy(n => inDegree[n]).First();
                        currentLayer.Add(nodeToBreakCycle);
                    }
                }

                if (currentLayer.Any())
                {
                    layers.Add(currentLayer);

                    // 更新入度并标记已处理
                    foreach (var node in currentLayer)
                    {
                        processed.Add(node);

                        if (graph.ContainsKey(node))
                        {
                            foreach (var target in graph[node])
                            {
                                if (!processed.Contains(target) && inDegree.ContainsKey(target))
                                {
                                    inDegree[target]--;
                                }
                            }
                        }
                    }
                }
                else
                {
                    break; // 防止无限循环
                }
            }

            return layers;
        }

        /// <summary>
        /// 计算节点位置
        /// </summary>
        private void CalculatePositions(
            List<List<NodePosition>> layers,
            LayoutOptions options,
            LayoutResult result,
            List<NodePosition> obstacles)
        {
            var currentX = options.StartX;
            var nodeWidthWithSpacing = options.NodeWidth + options.HorizontalSpacing;

            // 计算最大层高度用于居中
            var layerHeights = layers.Select(l => Math.Max(1, l.Count) * options.VerticalSpacing).ToList();
            var maxLayerHeight = layerHeights.Any() ? layerHeights.Max() : options.VerticalSpacing;

            for (int layerIndex = 0; layerIndex < layers.Count; layerIndex++)
            {
                var layer = layers[layerIndex];
                if (!layer.Any())
                {
                    currentX += nodeWidthWithSpacing;
                    continue;
                }

                // 该层的总高度和垂直居中偏移（使该层在最大层高度内居中）
                var thisLayerHeight = layerHeights[layerIndex];
                var verticalOffset = options.StartY + (maxLayerHeight - thisLayerHeight) / 2.0;

                // 分段并行：将超长层拆分为多列
                var leftToRightColumns = Math.Max(1, (int)Math.Ceiling(layer.Count / (double)Math.Max(1, (int)Math.Floor((Math.Max(options.VerticalSpacing, options.CanvasHeight - 200)) / options.VerticalSpacing))));
                var rowsPerColumn = Math.Max(1, (int)Math.Ceiling(layer.Count / (double)leftToRightColumns));

                // 同层分列的水平偏移
                var intraColumnSpacing = options.NodeWidth + Math.Max(60.0, options.HorizontalSpacing / 2.0);

                for (int nodeIndex = 0; nodeIndex < layer.Count; nodeIndex++)
                {
                    var node = layer[nodeIndex];

                    var colIndex = nodeIndex / rowsPerColumn;
                    var rowIndex = nodeIndex % rowsPerColumn;

                    var x = currentX + colIndex * intraColumnSpacing;
                    var y = verticalOffset + rowIndex * options.VerticalSpacing;

                    // 避免与障碍物重叠
                    var position = FindSafePosition(
                        new NodePosition { X = x, Y = y },
                        obstacles,
                        options);

                    result.NodePositions[node.NodeId] = new NodePosition
                    {
                        NodeId = node.NodeId,
                        X = position.X,
                        Y = position.Y,
                        IsLocked = false,
                        Title = node.Title,
                        NodeType = node.NodeType
                    };
                }

                // 下一层起始 X：考虑分列数
                currentX += leftToRightColumns * nodeWidthWithSpacing;
            }
        }

        /// <summary>
        /// 寻找安全位置，避免与障碍物重叠
        /// </summary>
        private NodePosition FindSafePosition(NodePosition proposedPosition, List<NodePosition> obstacles, LayoutOptions options)
        {
            if (!obstacles.Any())
                return proposedPosition;

            var safePosition = new NodePosition
            {
                X = proposedPosition.X,
                Y = proposedPosition.Y
            };

            var attempts = 0;
            const int maxAttempts = 50;

            while (attempts < maxAttempts && IsPositionOccupied(safePosition, obstacles, options))
            {
                if (attempts < 10)
                {
                    // 向右移动
                    safePosition.X += options.HorizontalSpacing / 4;
                }
                else if (attempts < 20)
                {
                    // 向下移动
                    safePosition.Y += options.VerticalSpacing / 4;
                }
                else
                {
                    // 螺旋搜索
                    var angle = attempts * 0.5;
                    var radius = (attempts - 20) * 50;
                    safePosition.X = proposedPosition.X + radius * Math.Cos(angle);
                    safePosition.Y = proposedPosition.Y + radius * Math.Sin(angle);
                }

                attempts++;
            }

            return safePosition;
        }

        /// <summary>
        /// 检查位置是否被障碍物占用
        /// </summary>
        private bool IsPositionOccupied(NodePosition position, List<NodePosition> obstacles, LayoutOptions options)
        {
            foreach (var obstacle in obstacles)
            {
                var dx = Math.Abs(position.X - obstacle.X);
                var dy = Math.Abs(position.Y - obstacle.Y);

                if (dx < options.NodeWidth + options.SafetyMargin &&
                    dy < options.NodeHeight + options.SafetyMargin)
                {
                    return true;
                }
            }

            return false;
        }

        // ---------- 多分量包装与树形/径向树辅助 ----------

        private LayoutResult CalculateForceDirectedWithComponents(
            List<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutOptions options)
        {
            var comps = BuildComponents(nodes, connections);
            var results = new List<(Dictionary<string, NodePosition> positions, (double x, double y, double w, double h) box, HashSet<string> compNodeIds)>();

            foreach (var comp in comps)
            {
                var compNodes = comp.Select(id => nodes.First(n => n.NodeId == id)).ToList();
                var compEdges = connections.Where(c => comp.Contains(c.SourceNodeId) && comp.Contains(c.TargetNodeId)).ToList();

                var local = CalculateForceDirectedLayout(compNodes, compEdges, options);
                var (box, ids) = ComputeBoundingBox(local.NodePositions, options);
                results.Add((local.NodePositions, box, ids));
            }

            PackComponents(results, options);

            var merged = new LayoutResult();
            foreach (var r in results)
            {
                foreach (var kv in r.positions)
                {
                    merged.NodePositions[kv.Key] = kv.Value;
                }
            }
            return merged;
        }

        private LayoutResult CalculateTreeTidyWithComponents(
            List<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutOptions options,
            bool radial)
        {
            var comps = BuildComponents(nodes, connections);
            var results = new List<(Dictionary<string, NodePosition> positions, (double x, double y, double w, double h) box, HashSet<string> compNodeIds)>();

            foreach (var comp in comps)
            {
                var compNodes = comp.Select(id => nodes.First(n => n.NodeId == id)).ToList();
                var compEdges = connections.Where(c => comp.Contains(c.SourceNodeId) && comp.Contains(c.TargetNodeId)).ToList();

                var local = radial
                    ? CalculateRadialTreeLayout(compNodes, compEdges, options)
                    : CalculateTidyTreeLayout(compNodes, compEdges, options);

                var (box, ids) = ComputeBoundingBox(local.NodePositions, options);
                results.Add((local.NodePositions, box, ids));
            }

            PackComponents(results, options);

            var merged = new LayoutResult();
            foreach (var r in results)
            {
                foreach (var kv in r.positions)
                {
                    merged.NodePositions[kv.Key] = kv.Value;
                }
            }
            return merged;
        }

        private ((double x, double y, double w, double h) box, HashSet<string> ids) ComputeBoundingBox(Dictionary<string, NodePosition> positions, LayoutOptions options)
        {
            double minX = double.PositiveInfinity, minY = double.PositiveInfinity;
            double maxX = double.NegativeInfinity, maxY = double.NegativeInfinity;
            var ids = new HashSet<string>();
            foreach (var kv in positions)
            {
                var p = kv.Value;
                double w = p.Width > 0 ? p.Width : options.NodeWidth;
                double h = p.Height > 0 ? p.Height : options.NodeHeight;
                minX = Math.Min(minX, p.X);
                minY = Math.Min(minY, p.Y);
                maxX = Math.Max(maxX, p.X + w);
                maxY = Math.Max(maxY, p.Y + h);
                ids.Add(kv.Key);
            }
            if (!double.IsFinite(minX)) minX = 0;
            if (!double.IsFinite(minY)) minY = 0;
            if (!double.IsFinite(maxX)) maxX = minX + options.NodeWidth;
            if (!double.IsFinite(maxY)) maxY = minY + options.NodeHeight;
            return ((minX, minY, maxX - minX, maxY - minY), ids);
        }

        private void PackComponents(List<(Dictionary<string, NodePosition> positions, (double x, double y, double w, double h) box, HashSet<string> compNodeIds)> comps,
            LayoutOptions options)
        {
            if (comps.Count <= 1) return;

            double shelfX = options.StartX;
            double shelfY = options.StartY;
            double shelfMaxH = 0;
            double maxWidth = Math.Max(options.CanvasWidth, 1200);
            double gapX = Math.Max(40, options.HorizontalSpacing * 0.5);
            double gapY = Math.Max(40, options.VerticalSpacing * 0.5);

            for (int idx = 0; idx < comps.Count; idx++)
            {
                var c = comps[idx];
                var box = c.box;
                if (shelfX > options.StartX && shelfX + box.w > options.StartX + maxWidth)
                {
                    shelfX = options.StartX;
                    shelfY += shelfMaxH + gapY;
                    shelfMaxH = 0;
                }

                double offX = shelfX - box.x;
                double offY = shelfY - box.y;
                shelfX += box.w + gapX;
                shelfMaxH = Math.Max(shelfMaxH, box.h);

                foreach (var k in c.positions.Keys.ToList())
                {
                    var p = c.positions[k];
                    if (!p.IsLocked)
                    {
                        p.X += offX;
                        p.Y += offY;
                        c.positions[k] = p;
                    }
                }

                comps[idx] = (c.positions, (box.x + offX, box.y + offY, box.w, box.h), c.compNodeIds);
            }
        }

        private List<HashSet<string>> BuildComponents(List<NodePosition> nodes, IEnumerable<ConnectionInfo> connections)
        {
            var idSet = new HashSet<string>(nodes.Select(n => n.NodeId));
            var adj = new Dictionary<string, HashSet<string>>();
            foreach (var id in idSet) adj[id] = new HashSet<string>();
            foreach (var c in connections)
            {
                if (idSet.Contains(c.SourceNodeId) && idSet.Contains(c.TargetNodeId))
                {
                    adj[c.SourceNodeId].Add(c.TargetNodeId);
                    adj[c.TargetNodeId].Add(c.SourceNodeId);
                }
            }

            var visited = new HashSet<string>();
            var comps = new List<HashSet<string>>();
            foreach (var id in idSet)
            {
                if (visited.Contains(id)) continue;
                var q = new Queue<string>();
                var set = new HashSet<string>();
                q.Enqueue(id); visited.Add(id); set.Add(id);
                while (q.Count > 0)
                {
                    var u = q.Dequeue();
                    foreach (var v in adj[u])
                    {
                        if (!visited.Contains(v)) { visited.Add(v); set.Add(v); q.Enqueue(v); }
                    }
                }
                comps.Add(set);
            }
            return comps;
        }

        private LayoutResult CalculateTidyTreeLayout(List<NodePosition> nodes, List<ConnectionInfo> connections, LayoutOptions options)
        {
            var result = new LayoutResult();
            if (nodes.Count == 0) return result;

            var id2node = nodes.ToDictionary(n => n.NodeId);
            var children = new Dictionary<string, List<string>>();
            var indeg = nodes.ToDictionary(n => n.NodeId, n => 0);
            foreach (var id in id2node.Keys) children[id] = new List<string>();
            foreach (var e in connections)
            {
                if (!id2node.ContainsKey(e.SourceNodeId) || !id2node.ContainsKey(e.TargetNodeId)) continue;
                children[e.SourceNodeId].Add(e.TargetNodeId);
                indeg[e.TargetNodeId]++;
            }

            string root = indeg.FirstOrDefault(kv => kv.Value == 0).Key ?? nodes[0].NodeId;
            var layers = new List<List<string>>();
            var q = new Queue<string>();
            q.Enqueue(root);
            var seen = new HashSet<string> { root };
            while (q.Count > 0)
            {
                int cnt = q.Count; var layer = new List<string>();
                for (int i = 0; i < cnt; i++)
                {
                    var u = q.Dequeue(); layer.Add(u);
                    foreach (var v in children[u]) if (seen.Add(v)) q.Enqueue(v);
                }
                layers.Add(layer);
            }

            double x0 = options.StartX + Math.Max(400, options.CanvasWidth) / 2.0; // 水平居中
            double y0 = options.StartY;
            double xGap = Math.Max(40, options.HorizontalSpacing);
            double yGap = Math.Max(60, options.VerticalSpacing);

            for (int d = 0; d < layers.Count; d++)
            {
                var layer = layers[d];
                double totalW = 0;
                foreach (var id in layer)
                {
                    var n = id2node[id];
                    totalW += (n.Width > 0 ? n.Width : options.NodeWidth) + xGap;
                }
                if (totalW > 0) totalW -= xGap;
                double curX = x0 - totalW / 2.0;
                double y = y0 + d * ((options.NodeHeight > 0 ? options.NodeHeight : 120) + yGap);
                foreach (var id in layer)
                {
                    var n = id2node[id];
                    double w = n.Width > 0 ? n.Width : options.NodeWidth;
                    result.NodePositions[id] = new NodePosition
                    {
                        NodeId = id,
                        X = curX,
                        Y = y,
                        Width = n.Width,
                        Height = n.Height,
                        IsLocked = n.IsLocked,
                        Title = n.Title,
                        NodeType = n.NodeType
                    };
                    curX += w + xGap;
                }
            }

            return result;
        }

        private LayoutResult CalculateRadialTreeLayout(List<NodePosition> nodes, List<ConnectionInfo> connections, LayoutOptions options)
        {
            var result = new LayoutResult(); if (nodes.Count == 0) return result;
            var id2node = nodes.ToDictionary(n => n.NodeId);
            var children = new Dictionary<string, List<string>>(); var indeg = nodes.ToDictionary(n => n.NodeId, n => 0);
            foreach (var id in id2node.Keys) children[id] = new List<string>();
            foreach (var e in connections)
            {
                if (!id2node.ContainsKey(e.SourceNodeId) || !id2node.ContainsKey(e.TargetNodeId)) continue;
                children[e.SourceNodeId].Add(e.TargetNodeId); indeg[e.TargetNodeId]++;
            }
            string root = indeg.FirstOrDefault(kv => kv.Value == 0).Key ?? nodes[0].NodeId;

            var leafCount = id2node.Keys.ToDictionary(id => id, id => 1);
            void Dp(string u)
            {
                if (children[u].Count == 0) { leafCount[u] = 1; return; }
                int s = 0; foreach (var v in children[u]) { Dp(v); s += leafCount[v]; }
                leafCount[u] = Math.Max(1, s);
            }
            Dp(root);

            double startAngle = -Math.PI / 2; double full = 2 * Math.PI;
            double centerX = options.StartX + Math.Max(400, options.CanvasWidth) / 2.0;
            double centerY = options.StartY + Math.Max(300, options.CanvasHeight) / 2.0;
            double radiusStep = Math.Max(100, options.VerticalSpacing + (options.NodeHeight > 0 ? options.NodeHeight : 120));

            void Place(string u, int depth, double a0, double a1)
            {
                double ang = (a0 + a1) / 2.0; double r = (depth + 1) * radiusStep;
                var n = id2node[u]; double w = n.Width > 0 ? n.Width : options.NodeWidth; double h = n.Height > 0 ? n.Height : options.NodeHeight;
                double cx = centerX + r * Math.Cos(ang); double cy = centerY + r * Math.Sin(ang);
                result.NodePositions[u] = new NodePosition { NodeId = u, X = cx - w / 2.0, Y = cy - h / 2.0, Width = n.Width, Height = n.Height, IsLocked = n.IsLocked, Title = n.Title, NodeType = n.NodeType };
                if (children[u].Count == 0) return;
                double span = a1 - a0; double acc = a0;
                foreach (var v in children[u])
                {
                    double frac = (double)leafCount[v] / Math.Max(1, leafCount[u]);
                    double next = acc + span * frac; Place(v, depth + 1, acc, next); acc = next;
                }
            }

            Place(root, 0, startAngle, startAngle + full);
            return result;
        }

        /// <summary>
        /// 力导向布局（Fruchterman-Reingold 简化版）
        /// 固定节点参与受力但位置保持不变
        /// </summary>
        private LayoutResult CalculateForceDirectedLayout(
            List<NodePosition> nodes,
            IEnumerable<ConnectionInfo> connections,
            LayoutOptions options)
        {
            var result = new LayoutResult();

            if (nodes == null || nodes.Count == 0)
                return result;

            var totalNodes = nodes.Count;
            var unlocked = nodes.Where(n => !n.IsLocked).ToList();
            var locked = nodes.Where(n => n.IsLocked).ToList();

            // 先写入锁定节点原位置
            foreach (var ln in locked)
            {
                result.NodePositions[ln.NodeId] = new NodePosition
                {
                    NodeId = ln.NodeId,
                    X = ln.X,
                    Y = ln.Y,
                    IsLocked = true,
                    Title = ln.Title,
                    NodeType = ln.NodeType
                };
            }

            if (unlocked.Count == 0)
                return result;

            var width = Math.Max(100, options.CanvasWidth);
            var height = Math.Max(100, options.CanvasHeight);
            var startX = options.StartX;
            var startY = options.StartY;

            double area = width * height;
            double k = Math.Sqrt(area / Math.Max(1.0, totalNodes));
            k = Math.Max(10.0, k * 0.9);
            // 节点几何参数（用于尺寸感知力与碰撞阈值）— 支持每个节点不同尺寸
            var nodeSize = nodes.ToDictionary(n => n.NodeId, n => (
                w: (n.Width > 0 ? n.Width : options.NodeWidth),
                h: (n.Height > 0 ? n.Height : options.NodeHeight)));
            var nodeHalf = nodes.ToDictionary(n => n.NodeId, n => (
                hw: Math.Max(1.0, (n.Width > 0 ? n.Width : options.NodeWidth) * 0.5),
                hh: Math.Max(1.0, (n.Height > 0 ? n.Height : options.NodeHeight) * 0.5)));
            var nodeRadius = nodes.ToDictionary(n => n.NodeId, n =>
            {
                var hw = Math.Max(1.0, (n.Width > 0 ? n.Width : options.NodeWidth) * 0.5);
                var hh = Math.Max(1.0, (n.Height > 0 ? n.Height : options.NodeHeight) * 0.5);
                return Math.Sqrt(hw * hw + hh * hh);
            });
            double padding = Math.Max(4.0, options.SafetyMargin);

            var rand = new Random(1337);

            // 初始位置（若为近零则随机）
            var pos = nodes.ToDictionary(n => n.NodeId, n => (
                x: ((!double.IsFinite(n.X) || Math.Abs(n.X) < 0.0001) ? startX + rand.NextDouble() * width : n.X),
                y: ((!double.IsFinite(n.Y) || Math.Abs(n.Y) < 0.0001) ? startY + rand.NextDouble() * height : n.Y)));

            // 构造无向边（去重）
            var edges = new HashSet<(string a, string b)>();
            if (connections != null)
            {
                foreach (var c in connections)
                {
                    if (string.IsNullOrEmpty(c.SourceNodeId) || string.IsNullOrEmpty(c.TargetNodeId))
                        continue;
                    var a = c.SourceNodeId;
                    var b = c.TargetNodeId;
                    if (a == b) continue;
                    var e = string.CompareOrdinal(a, b) < 0 ? (a, b) : (b, a);
                    edges.Add(e);
                }
            }

            double t = options.InitialTemperature > 0 ? options.InitialTemperature : Math.Max(width, height) / 2.0;
            int iterations = Math.Max(1, options.Iterations);
            // 向心重力与软边界参数
            double cx = startX + width / 2.0;
            double cy = startY + height / 2.0;
            double gravity = Math.Max(0.005, 0.02 * (150.0 / (50.0 + nodes.Count)));
            double wallK = 0.05;

            var isLocked = nodes.ToDictionary(n => n.NodeId, n => n.IsLocked);

            for (int iter = 0; iter < iterations; iter++)
            {
                var disp = nodes.ToDictionary(n => n.NodeId, n => (dx: 0.0, dy: 0.0));

                // 斥力：任意两点（用中心点距离，考虑节点尺寸）
                for (int i = 0; i < nodes.Count; i++)
                {
                    var v = nodes[i];
                    var pv = pos[v.NodeId];
                    var sv = nodeSize[v.NodeId];
                    double cvx = pv.x + sv.w * 0.5;
                    double cvy = pv.y + sv.h * 0.5;
                    for (int j = i + 1; j < nodes.Count; j++)
                    {
                        var u = nodes[j];
                        var pu = pos[u.NodeId];
                        var su = nodeSize[u.NodeId];
                        double cux = pu.x + su.w * 0.5;
                        double cuy = pu.y + su.h * 0.5;
                        double dx = cvx - cux;
                        double dy = cvy - cuy;
                        double dist = Math.Sqrt(dx * dx + dy * dy);
                        if (dist < 0.001)
                        {
                            dx = (rand.NextDouble() - 0.5) * 0.01;
                            dy = (rand.NextDouble() - 0.5) * 0.01;
                            dist = Math.Sqrt(dx * dx + dy * dy);
                        }
                        // 尺寸感知斥力（两节点半径之和 + padding）
                        double eff = Math.Max(1e-3, dist - (nodeRadius[v.NodeId] + nodeRadius[u.NodeId] + padding));
                        double force = (k * k) / eff;
                        double rx = (dx / dist) * force;
                        double ry = (dy / dist) * force;
                        if (!isLocked[v.NodeId]) { var dv = disp[v.NodeId]; dv.dx += rx; dv.dy += ry; disp[v.NodeId] = dv; }
                        if (!isLocked[u.NodeId]) { var du = disp[u.NodeId]; du.dx -= rx; du.dy -= ry; disp[u.NodeId] = du; }
                    }
                }

                // 引力：沿边（用中心点距离）
                foreach (var (a, b) in edges)
                {
                    if (!pos.ContainsKey(a) || !pos.ContainsKey(b))
                        continue;
                    var pa = pos[a]; var pb = pos[b];
                    var sa = nodeSize[a]; var sb = nodeSize[b];
                    double axc = pa.x + sa.w * 0.5; double ayc = pa.y + sa.h * 0.5;
                    double bxc = pb.x + sb.w * 0.5; double byc = pb.y + sb.h * 0.5;
                    double dx = axc - bxc; double dy = ayc - byc;
                    double dist = Math.Sqrt(dx * dx + dy * dy);
                    if (dist < 0.001) continue;
                    double force = (dist * dist) / k;
                    double ax = (dx / dist) * force;
                    double ay = (dy / dist) * force;
                    if (!isLocked[a]) { var da = disp[a]; da.dx -= ax; da.dy -= ay; disp[a] = da; }
                    if (!isLocked[b]) { var db = disp[b]; db.dx += ax; db.dy += ay; disp[b] = db; }
                }

                // 向心重力：将节点轻推向中心，打破围墙与中空（按中心点）
                foreach (var n in nodes)
                {
                    if (isLocked[n.NodeId]) continue;
                    var p = pos[n.NodeId];
                    var dv = disp[n.NodeId];
                    var s = nodeSize[n.NodeId];
                    double ncx = p.x + s.w * 0.5;
                    double ncy = p.y + s.h * 0.5;
                    dv.dx += (cx - ncx) * gravity;
                    dv.dy += (cy - ncy) * gravity;
                    disp[n.NodeId] = dv;
                }

                // 软边界力：靠近四周时线性推回
                foreach (var n in nodes)
                {
                    if (isLocked[n.NodeId]) continue;
                    var p = pos[n.NodeId];
                    var dv = disp[n.NodeId];
                    var half = nodeHalf[n.NodeId];
                    double wallPadLocal = Math.Max(half.hw, half.hh) + 12.0;
                    double leftBound = startX + wallPadLocal;
                    double rightBound = startX + width - wallPadLocal;
                    double topBound = startY + wallPadLocal;
                    double bottomBound = startY + height - wallPadLocal;
                    if (p.x < leftBound) dv.dx += (leftBound - p.x) * wallK;
                    if (p.x > rightBound) dv.dx -= (p.x - rightBound) * wallK;
                    if (p.y < topBound) dv.dy += (topBound - p.y) * wallK;
                    if (p.y > bottomBound) dv.dy -= (p.y - bottomBound) * wallK;
                    disp[n.NodeId] = dv;
                }

                // 迭代期轻量碰撞分离（圆形半径 + 轴对齐矩形两种方式结合）
                double minSepBase = Math.Max(options.MinDistance, 8.0);
                double sepK = 0.05;
                for (int i = 0; i < nodes.Count; i++)
                {
                    var ni = nodes[i];
                    var pi = pos[ni.NodeId];
                    var si = nodeSize[ni.NodeId];
                    double cix = pi.x + si.w * 0.5;
                    double ciy = pi.y + si.h * 0.5;
                    for (int j = i + 1; j < nodes.Count; j++)
                    {
                        var nj = nodes[j];
                        var pj = pos[nj.NodeId];
                        var sj = nodeSize[nj.NodeId];
                        double cjx = pj.x + sj.w * 0.5;
                        double cjy = pj.y + sj.h * 0.5;
                        double dx = cix - cjx;
                        double dy = ciy - cjy;
                        double adx = Math.Abs(dx);
                        double ady = Math.Abs(dy);
                        double dist = Math.Sqrt(dx * dx + dy * dy);
                        if (dist < 1e-6)
                        {
                            dx = 1e-3; dy = 0; dist = 1e-3; adx = Math.Abs(dx); ady = Math.Abs(dy);
                        }

                        double sepX = (si.w + sj.w) * 0.5 + padding;
                        double sepY = (si.h + sj.h) * 0.5 + padding;
                        bool rectOverlap = (adx < sepX) && (ady < sepY);
                        double minSep = nodeRadius[ni.NodeId] + nodeRadius[nj.NodeId] + minSepBase;
                        bool radialOverlap = dist < minSep;
                        if (!rectOverlap && !radialOverlap)
                            continue;

                        // 先按轴向把矩形碰撞拉开，再用径向微调
                        if (rectOverlap)
                        {
                            double pushX = (sepX - adx);
                            double pushY = (sepY - ady);
                            // 优先沿重叠更大的轴分离
                            if (pushX >= pushY)
                            {
                                double sx = Math.Sign(dx);
                                if (!isLocked[ni.NodeId]) { var dvi = disp[ni.NodeId]; dvi.dx += sepK * pushX * sx; disp[ni.NodeId] = dvi; }
                                if (!isLocked[nj.NodeId]) { var dvj = disp[nj.NodeId]; dvj.dx -= sepK * pushX * sx; disp[nj.NodeId] = dvj; }
                            }
                            else
                            {
                                double sy = Math.Sign(dy);
                                if (!isLocked[ni.NodeId]) { var dvi = disp[ni.NodeId]; dvi.dy += sepK * pushY * sy; disp[ni.NodeId] = dvi; }
                                if (!isLocked[nj.NodeId]) { var dvj = disp[nj.NodeId]; dvj.dy -= sepK * pushY * sy; disp[nj.NodeId] = dvj; }
                            }
                        }

                        if (radialOverlap)
                        {
                            double overlap = (minSep - dist);
                            double ux = dx / dist;
                            double uy = dy / dist;
                            if (!isLocked[ni.NodeId]) { var dvi = disp[ni.NodeId]; dvi.dx += ux * sepK * overlap; dvi.dy += uy * sepK * overlap; disp[ni.NodeId] = dvi; }
                            if (!isLocked[nj.NodeId]) { var dvj = disp[nj.NodeId]; dvj.dx -= ux * sepK * overlap; dvj.dy -= uy * sepK * overlap; disp[nj.NodeId] = dvj; }
                        }
                    }
                }

                // 更新位置并裁剪到边界（按各自尺寸）
                foreach (var n in nodes)
                {
                    if (isLocked[n.NodeId]) continue;
                    var p = pos[n.NodeId];
                    var d = disp[n.NodeId];
                    double len = Math.Sqrt(d.dx * d.dx + d.dy * d.dy);
                    if (len > 0)
                    {
                        var step = Math.Min(len, t);
                        p.x += d.dx / len * step;
                        p.y += d.dy / len * step;
                    }
                    var sizeClamp = nodeSize[n.NodeId];
                    p.x = Math.Max(startX, Math.Min(startX + width - sizeClamp.w, p.x));
                    p.y = Math.Max(startY, Math.Min(startY + height - sizeClamp.h, p.y));
                    pos[n.NodeId] = p;
                }

                t *= 1.0 - (1.0 / iterations);
                if (t < 1.0) t = 1.0;
            }

            // 简易防重叠：拉开到最小中心距
            if (options.PreventOverlap && options.MinDistance > 0)
            {
                double minDist = options.MinDistance;
                for (int i = 0; i < nodes.Count; i++)
                {
                    for (int j = i + 1; j < nodes.Count; j++)
                    {
                        var a = nodes[i];
                        var b = nodes[j];
                        var pa = pos[a.NodeId];
                        var pb = pos[b.NodeId];
                        double dx = pa.x - pb.x;
                        double dy = pa.y - pb.y;
                        double dist = Math.Sqrt(dx * dx + dy * dy);
                        if (dist < 0.001)
                        {
                            pa.x += (rand.NextDouble() - 0.5) * 2.0;
                            pa.y += (rand.NextDouble() - 0.5) * 2.0;
                            pb.x -= (rand.NextDouble() - 0.5) * 2.0;
                            pb.y -= (rand.NextDouble() - 0.5) * 2.0;
                            pos[a.NodeId] = pa;
                            pos[b.NodeId] = pb;
                            continue;
                        }
                        if (dist < minDist)
                        {
                            var overlap = (minDist - dist) / 2.0;
                            var ux = dx / dist;
                            var uy = dy / dist;
                            if (!isLocked[a.NodeId]) { pa.x += ux * overlap; pa.y += uy * overlap; }
                            if (!isLocked[b.NodeId]) { pb.x -= ux * overlap; pb.y -= uy * overlap; }
                            // 边界（按各自尺寸）
                            var sizeA = nodeSize[a.NodeId];
                            var sizeB = nodeSize[b.NodeId];
                            pa.x = Math.Max(startX, Math.Min(startX + width - sizeA.w, pa.x));
                            pa.y = Math.Max(startY, Math.Min(startY + height - sizeA.h, pa.y));
                            pb.x = Math.Max(startX, Math.Min(startX + width - sizeB.w, pb.x));
                            pb.y = Math.Max(startY, Math.Min(startY + height - sizeB.h, pb.y));
                            pos[a.NodeId] = pa;
                            pos[b.NodeId] = pb;
                        }
                    }
                }
            }

            // 全局去重叠（多轮中心点矩形分离），保证最终无矩形交叠
            if (options.PreventOverlap)
            {
                int maxRounds = 20;
                double stepFactor = 0.6;
                for (int round = 0; round < maxRounds; round++)
                {
                    bool any = false;
                    var disp2 = nodes.ToDictionary(n => n.NodeId, n => (dx: 0.0, dy: 0.0));
                    for (int i = 0; i < nodes.Count; i++)
                    {
                        var ni = nodes[i]; var pi = pos[ni.NodeId]; var si = nodeSize[ni.NodeId];
                        double cix = pi.x + si.w * 0.5; double ciy = pi.y + si.h * 0.5;
                        for (int j = i + 1; j < nodes.Count; j++)
                        {
                            var nj = nodes[j]; var pj = pos[nj.NodeId]; var sj = nodeSize[nj.NodeId];
                            double cjx = pj.x + sj.w * 0.5; double cjy = pj.y + sj.h * 0.5;
                            double reqX = (si.w + sj.w) * 0.5 + padding;
                            double reqY = (si.h + sj.h) * 0.5 + padding;
                            double dx = cix - cjx; double adx = Math.Abs(dx);
                            double dy = ciy - cjy; double ady = Math.Abs(dy);
                            double ox = reqX - adx; double oy = reqY - ady;
                            if (ox <= 0 || oy <= 0) continue;
                            any = true;
                            if (ox >= oy)
                            {
                                double push = oy * stepFactor; double sy = Math.Sign(dy);
                                if (!isLocked[ni.NodeId]) { var d = disp2[ni.NodeId]; d.dy += push * sy; disp2[ni.NodeId] = d; }
                                if (!isLocked[nj.NodeId]) { var d = disp2[nj.NodeId]; d.dy -= push * sy; disp2[nj.NodeId] = d; }
                                else if (!isLocked[ni.NodeId]) { var d = disp2[ni.NodeId]; d.dy += push * sy; disp2[ni.NodeId] = d; }
                            }
                            else
                            {
                                double push = ox * stepFactor; double sx = Math.Sign(dx);
                                if (!isLocked[ni.NodeId]) { var d = disp2[ni.NodeId]; d.dx += push * sx; disp2[ni.NodeId] = d; }
                                if (!isLocked[nj.NodeId]) { var d = disp2[nj.NodeId]; d.dx -= push * sx; disp2[nj.NodeId] = d; }
                                else if (!isLocked[ni.NodeId]) { var d = disp2[ni.NodeId]; d.dx += push * sx; disp2[ni.NodeId] = d; }
                            }
                        }
                    }

                    if (!any) break;
                    foreach (var n in nodes)
                    {
                        if (isLocked[n.NodeId]) continue;
                        var p = pos[n.NodeId]; var s = nodeSize[n.NodeId]; var d = disp2[n.NodeId];
                        p.x += d.dx; p.y += d.dy;
                        p.x = Math.Max(startX, Math.Min(startX + width - s.w, p.x));
                        p.y = Math.Max(startY, Math.Min(startY + height - s.h, p.y));
                        pos[n.NodeId] = p;
                    }
                }
            }

            // 输出
            foreach (var n in nodes)
            {
                var p = pos[n.NodeId];
                result.NodePositions[n.NodeId] = new NodePosition
                {
                    NodeId = n.NodeId,
                    X = n.IsLocked ? n.X : p.x,
                    Y = n.IsLocked ? n.Y : p.y,
                    Width = nodeSize[n.NodeId].w,
                    Height = nodeSize[n.NodeId].h,
                    IsLocked = n.IsLocked,
                    Title = n.Title,
                    NodeType = n.NodeType
                };
            }

            return result;
        }
    }
}


