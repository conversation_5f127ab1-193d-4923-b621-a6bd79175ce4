using System;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Controls.Functions;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Inspector.Components
{
    /// <summary>
    /// 函数编辑器组件 - 包装原有的FunctionPropertyPanel到Inspector系统中
    /// </summary>
    [InspectorComponent(
        Title = "函数编辑器",
        Description = "配置数据计算函数和表达式",
        Priority = 1,
        IsExpandedByDefault = true,
        CanBeRemoved = false)]
    public partial class FunctionEditorComponent : InspectorComponent
    {
        public override string ComponentTitle => "函数编辑器";
        public override string ComponentDescription => "配置数据计算函数和表达式";

        private FunctionPropertyPanel? _functionPanel;

        public FunctionEditorComponent()
        {
            InitializeComponent();
        }

        public override bool IsApplicableToNode(ModuleNodeViewModel node)
        {
            // FunctionEditorComponent已被DataCalculationComponent替代，不再适用于任何节点
            return false;
        }

        protected override void InitializeFromNode(ModuleNodeViewModel node)
        {
            // 创建FunctionPropertyPanel实例
            _functionPanel = new FunctionPropertyPanel
            {
                CurrentNode = node,
                Margin = new System.Windows.Thickness(0)
            };

            // 将FunctionPropertyPanel添加到容器中
            FunctionPanelContainer.Children.Clear();
            FunctionPanelContainer.Children.Add(_functionPanel);

            System.Diagnostics.Debug.WriteLine($"[FunctionEditorComponent] Initialized with node: {node.Title}");
        }

        protected override void ClearComponent()
        {
            if (_functionPanel != null)
            {
                FunctionPanelContainer.Children.Clear();
                _functionPanel = null;
            }
        }

        public override void ApplyToNode()
        {
            // FunctionPropertyPanel会自动处理数据应用
            // 这里不需要额外的逻辑
            System.Diagnostics.Debug.WriteLine($"[FunctionEditorComponent] ApplyToNode called");
        }

        public override void ResetToDefault()
        {
            // 重新初始化FunctionPropertyPanel
            if (CurrentNode != null)
            {
                InitializeFromNode(CurrentNode);
            }
        }
    }
}
