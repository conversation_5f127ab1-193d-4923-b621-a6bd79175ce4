using ProjectDigitizer.Application.Interfaces.UI;
using ProjectDigitizer.Core.Entities;

namespace Plugins.DataCalculation
{
    /// <summary>
    /// 数据计算节点的属性面板 Provider：提供资源键，便于 Studio 通过适配器解析。
    /// </summary>
    public sealed class DataCalculationPropertyPanelProvider : IPropertyPanelProvider
    {
        public ModuleType TargetModuleType => ModuleType.DataCalculation;
        public string ResourceKey => "PropertyPanel.ModuleType.DataCalculation";
    }
}

