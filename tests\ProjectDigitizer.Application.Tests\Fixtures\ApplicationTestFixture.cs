using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application;
using ProjectDigitizer.Core;

namespace ProjectDigitizer.Application.Tests.Fixtures;

/// <summary>
/// Application层测试固件
/// 提供Application层测试所需的依赖注入容器和服务
/// </summary>
public class ApplicationTestFixture : IDisposable
{
    public IServiceProvider ServiceProvider { get; private set; }
    public IServiceCollection Services { get; private set; }

    public ApplicationTestFixture()
    {
        Services = new ServiceCollection();
        ConfigureServices();
        ServiceProvider = Services.BuildServiceProvider();
    }

    /// <summary>
    /// 配置测试服务
    /// </summary>
    private void ConfigureServices()
    {
        // 添加日志记录
        Services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加Core层服务
        Services.AddCore();

        // 添加Application层服务
        Services.AddApplication();

        // 添加测试专用的服务配置
        ConfigureTestServices();
    }

    /// <summary>
    /// 配置测试专用服务
    /// </summary>
    protected virtual void ConfigureTestServices()
    {
        // 可以在这里添加Mock服务或测试专用配置
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// 获取可选服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或null</returns>
    public T? GetOptionalService<T>() where T : class
    {
        return ServiceProvider.GetService<T>();
    }

    /// <summary>
    /// 创建服务作用域
    /// </summary>
    /// <returns>服务作用域</returns>
    public IServiceScope CreateScope()
    {
        return ServiceProvider.CreateScope();
    }

    public void Dispose()
    {
        if (ServiceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}

/// <summary>
/// Application层测试集合定义
/// </summary>
[CollectionDefinition("Application Tests")]
public class ApplicationTestCollection : ICollectionFixture<ApplicationTestFixture>
{
    // 此类仅用于定义测试集合，不需要实现
}
