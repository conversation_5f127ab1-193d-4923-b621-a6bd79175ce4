using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Services;

namespace ProjectDigitizer.Infrastructure.Extensions;

/// <summary>
/// 代码质量验证扩展方法
/// </summary>
public static class CodeQualityExtensions
{
    /// <summary>
    /// 添加代码质量验证服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCodeQualityValidation(this IServiceCollection services)
    {
        // 注册命名约定服务
        services.AddScoped<INamingConventionService, NamingConventionService>();

        // 注册代码结构验证器
        services.AddScoped<ICodeStructureValidator, CodeStructureValidator>();

        return services;
    }

    /// <summary>
    /// 添加代码质量验证服务（带配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCodeQualityValidation(
        this IServiceCollection services,
        Action<CodeQualityOptions> configureOptions)
    {
        var options = new CodeQualityOptions();
        configureOptions(options);

        services.AddSingleton(options);
        services.AddCodeQualityValidation();

        return services;
    }

    /// <summary>
    /// 验证项目代码质量
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="projectPath">项目路径</param>
    /// <returns>验证结果</returns>
    public static async Task<CodeQualityValidationResult> ValidateProjectCodeQualityAsync(
        this IServiceProvider serviceProvider,
        string projectPath)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<CodeQualityValidator>>();
        var namingService = serviceProvider.GetRequiredService<INamingConventionService>();
        var structureValidator = serviceProvider.GetRequiredService<ICodeStructureValidator>();

        var validator = new CodeQualityValidator(logger, namingService, structureValidator);
        return await validator.ValidateProjectAsync(projectPath);
    }
}

/// <summary>
/// 代码质量配置选项
/// </summary>
public class CodeQualityOptions
{
    /// <summary>
    /// 是否启用命名约定验证
    /// </summary>
    public bool EnableNamingConventionValidation { get; set; } = true;

    /// <summary>
    /// 是否启用代码结构验证
    /// </summary>
    public bool EnableCodeStructureValidation { get; set; } = true;

    /// <summary>
    /// 是否启用架构层次验证
    /// </summary>
    public bool EnableArchitectureValidation { get; set; } = true;

    /// <summary>
    /// 是否将警告视为错误
    /// </summary>
    public bool TreatWarningsAsErrors { get; set; } = false;

    /// <summary>
    /// 验证详细程度
    /// </summary>
    public ValidationVerbosity Verbosity { get; set; } = ValidationVerbosity.Normal;

    /// <summary>
    /// 排除的文件模式
    /// </summary>
    public List<string> ExcludePatterns { get; set; } = new()
    {
        "*.Designer.cs",
        "*.g.cs",
        "*.g.i.cs",
        "**/bin/**",
        "**/obj/**"
    };
}

/// <summary>
/// 验证详细程度
/// </summary>
public enum ValidationVerbosity
{
    /// <summary>静默</summary>
    Quiet,
    /// <summary>正常</summary>
    Normal,
    /// <summary>详细</summary>
    Verbose,
    /// <summary>诊断</summary>
    Diagnostic
}

/// <summary>
/// 代码质量验证结果
/// </summary>
public class CodeQualityValidationResult
{
    /// <summary>
    /// 是否通过验证
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// 警告数量
    /// </summary>
    public int WarningCount { get; set; }

    /// <summary>
    /// 验证的文件数量
    /// </summary>
    public int ValidatedFileCount { get; set; }

    /// <summary>
    /// 验证耗时
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// 详细结果
    /// </summary>
    public List<CodeQualityIssue> Issues { get; set; } = new();

    /// <summary>
    /// 摘要信息
    /// </summary>
    public string Summary => $"验证完成: {ValidatedFileCount}个文件, {ErrorCount}个错误, {WarningCount}个警告";
}

/// <summary>
/// 代码质量问题
/// </summary>
public class CodeQualityIssue
{
    /// <summary>
    /// 问题类型
    /// </summary>
    public IssueType Type { get; set; }

    /// <summary>
    /// 严重程度
    /// </summary>
    public IssueSeverity Severity { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int LineNumber { get; set; }

    /// <summary>
    /// 列号
    /// </summary>
    public int ColumnNumber { get; set; }

    /// <summary>
    /// 问题描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 建议修复方案
    /// </summary>
    public string? SuggestedFix { get; set; }

    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;
}

/// <summary>
/// 问题类型
/// </summary>
public enum IssueType
{
    /// <summary>命名约定</summary>
    NamingConvention,
    /// <summary>代码结构</summary>
    CodeStructure,
    /// <summary>架构违规</summary>
    ArchitectureViolation,
    /// <summary>依赖关系</summary>
    Dependency,
    /// <summary>其他</summary>
    Other
}

/// <summary>
/// 问题严重程度
/// </summary>
public enum IssueSeverity
{
    /// <summary>信息</summary>
    Info,
    /// <summary>警告</summary>
    Warning,
    /// <summary>错误</summary>
    Error,
    /// <summary>严重错误</summary>
    Critical
}

/// <summary>
/// 代码质量验证器
/// </summary>
internal class CodeQualityValidator
{
    private readonly ILogger<CodeQualityValidator> _logger;
    private readonly INamingConventionService _namingService;
    private readonly ICodeStructureValidator _structureValidator;

    public CodeQualityValidator(
        ILogger<CodeQualityValidator> logger,
        INamingConventionService namingService,
        ICodeStructureValidator structureValidator)
    {
        _logger = logger;
        _namingService = namingService;
        _structureValidator = structureValidator;
    }

    public Task<CodeQualityValidationResult> ValidateProjectAsync(string projectPath)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = new CodeQualityValidationResult();

        try
        {
            _logger.LogInformation("开始验证项目代码质量: {ProjectPath}", projectPath);

            // 验证项目结构
            var structureResult = _namingService.ValidateProjectStructure(projectPath);
            ProcessValidationResult(structureResult, result, "项目结构");

            // 验证架构
            var architectureResult = _structureValidator.ValidateProjectArchitecture(projectPath);
            ProcessValidationResult(architectureResult, result, "项目架构");

            // TODO: 添加更多具体的文件验证逻辑

            result.IsValid = result.ErrorCount == 0;
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("代码质量验证完成: {Summary}", result.Summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "代码质量验证失败");
            result.Issues.Add(new CodeQualityIssue
            {
                Type = IssueType.Other,
                Severity = IssueSeverity.Critical,
                Description = $"验证过程发生异常: {ex.Message}"
            });
            result.ErrorCount++;
        }

        return Task.FromResult(result);
    }

    private void ProcessValidationResult(
        ProjectDigitizer.Core.ValueObjects.ValidationResult validationResult,
        CodeQualityValidationResult result,
        string context)
    {
        foreach (var error in validationResult.Errors)
        {
            result.Issues.Add(new CodeQualityIssue
            {
                Type = IssueType.CodeStructure,
                Severity = IssueSeverity.Error,
                Description = $"{context}: {error}",
                RuleId = "CQ001"
            });
            result.ErrorCount++;
        }

        foreach (var warning in validationResult.Warnings)
        {
            result.Issues.Add(new CodeQualityIssue
            {
                Type = IssueType.CodeStructure,
                Severity = IssueSeverity.Warning,
                Description = $"{context}: {warning}",
                RuleId = "CQ002"
            });
            result.WarningCount++;
        }
    }
}
