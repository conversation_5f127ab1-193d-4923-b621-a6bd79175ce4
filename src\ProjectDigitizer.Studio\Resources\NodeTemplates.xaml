<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
    xmlns:entities="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 新的分层资源字典架构 ==========  -->
    <!--  重构后的NodeTemplates.xaml - 采用精细化分层架构  -->

    <ResourceDictionary.MergedDictionaries>

        <!--  Foundation 层 - 基础资源  -->
        <ResourceDictionary Source="Foundation/Converters.xaml" />
        <ResourceDictionary Source="Foundation/Colors.xaml" />
        <ResourceDictionary Source="Foundation/Effects.xaml" />
        <ResourceDictionary Source="Foundation/CommonStyles.xaml" />

        <!--  Components 层 - 可复用组件  -->
        <ResourceDictionary Source="Components/Connectors/InputConnectorStyles.xaml" />
        <ResourceDictionary Source="Components/Connectors/OutputConnectorStyles.xaml" />
        <ResourceDictionary Source="Components/Connections/LineConnections.xaml" />
        <ResourceDictionary Source="Components/Connections/BezierConnections.xaml" />
        <ResourceDictionary Source="Components/Connections/StepConnections.xaml" />
        <ResourceDictionary Source="Components/Connections/CircuitConnections.xaml" />

        <!--  NodeTemplates 层 - 基础模板  -->
        <ResourceDictionary Source="NodeTemplates/Base/BaseNodeTemplate.xaml" />

        <!--  输入节点模板 - 已完成迁移  -->
        <ResourceDictionary Source="NodeTemplates/InputNodes/ManualDataInputNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/InputNodes/FileInputNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/InputNodes/DatabaseInputNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/InputNodes/APIInputNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/InputNodes/CADInputNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/InputNodes/DataFormatInputNodes.xaml" />

        <!--  转换节点模板 - 已完成迁移  -->
        <!-- DataCalculationNode moved to plugin UI; loaded via PluginResources.xaml -->
        <ResourceDictionary Source="NodeTemplates/TransformNodes/AIAgentNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/TransformNodes/ArrayExpansionNode.xaml" />
        <ResourceDictionary Source="NodeTemplates/TransformNodes/PipelineNodes.xaml" />
        <ResourceDictionary Source="NodeTemplates/TransformNodes/ConstructionNodes.xaml" />
        <ResourceDictionary Source="NodeTemplates/TransformNodes/DataProcessingNodes.xaml" />
        <ResourceDictionary Source="NodeTemplates/TransformNodes/AuxiliaryNodes.xaml" />

        <!--  输出节点模板 - 已完成迁移  -->
        <ResourceDictionary Source="NodeTemplates/OutputNodes/FileOutputNodes.xaml" />
        <ResourceDictionary Source="NodeTemplates/OutputNodes/ServiceOutputNodes.xaml" />

        <!--  控制节点模板 - 已完成迁移  -->
        <ResourceDictionary Source="NodeTemplates/ControlNodes/TriggerNodes.xaml" />
        <ResourceDictionary Source="NodeTemplates/ControlNodes/FlowControlNodes.xaml" />

    </ResourceDictionary.MergedDictionaries>

    <!--  ========== 临时保留的原始模板 ==========  -->
    <!--  在完成所有节点迁移之前，保留原始的主要模板  -->

    <!--  现代化节点模板 - 基于新的基础架构  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="NodeTemplate">
        <nodify:Node
            Height="120"
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}"
            Width="280">

            <!--  使用高级连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  节点内容 - 使用基础样式  -->
            <Border Style="{StaticResource BaseNodeBorderStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="56" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  标题栏  -->
                    <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                        <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource BaseNodeHeaderContentTemplate}" />
                    </Border>

                    <!--  内容区域  -->
                    <Border
                        Background="{StaticResource Brush.SurfaceVariant}"
                        CornerRadius="0,0,12,12"
                        Grid.Row="1"
                        Padding="12,8">
                        <StackPanel>
                            <TextBlock
                                FontSize="10"
                                Foreground="{StaticResource Brush.TextSecondary}"
                                HorizontalAlignment="Center"
                                Text="节点内容区域" />
                            <TextBlock
                                FontSize="9"
                                Foreground="{StaticResource Brush.Placeholder}"
                                HorizontalAlignment="Center"
                                Margin="0,2,0,0"
                                Text="{Binding Module.Type}" />
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </nodify:Node>
    </DataTemplate>

    <!--  手动输入节点模板 - 临时保留  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ManualDataInputNodeTemplate">
        <nodify:Node
            Height="120"
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}"
            Width="200">

            <!--  使用简化的连接器样式  -->
            <nodify:Node.InputConnectorTemplate>
                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                    <nodify:NodeInput
                        Anchor="{Binding Anchor, Mode=OneWayToSource}"
                        Header=""
                        IsConnected="{Binding IsConnected}"
                        Style="{StaticResource HtmlPrototypeInputConnectorStyle}"
                        ToolTip="{Binding Title}" />
                </DataTemplate>
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                    <nodify:NodeOutput
                        Anchor="{Binding Anchor, Mode=OneWayToSource}"
                        Header=""
                        IsConnected="{Binding IsConnected}"
                        Style="{StaticResource HtmlPrototypeOutputConnectorStyle}"
                        ToolTip="{Binding Title}" />
                </DataTemplate>
            </nodify:Node.OutputConnectorTemplate>

            <!--  手动输入节点专用内容  -->
            <Border
                Background="{StaticResource Brush.Surface}"
                BorderBrush="{StaticResource Brush.Secondary}"
                BorderThickness="2"
                CornerRadius="8"
                Effect="{StaticResource CardShadow}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="40" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  Header  -->
                    <Border
                        Background="{StaticResource ModernHeaderGradientCyan}"
                        CornerRadius="6,6,0,0"
                        Grid.Row="0"
                        Height="40"
                        Padding="12,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  键盘图标  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="0"
                                Height="16"
                                Kind="Keyboard"
                                Margin="0,0,8,0"
                                VerticalAlignment="Center"
                                Width="16" />

                            <TextBlock
                                FontSize="12"
                                FontWeight="Medium"
                                Foreground="White"
                                Grid.Column="1"
                                Text="手动输入"
                                VerticalAlignment="Center" />

                            <Ellipse
                                Fill="{Binding StatusBrush}"
                                Grid.Column="2"
                                Height="6"
                                VerticalAlignment="Center"
                                Width="6" />
                        </Grid>
                    </Border>

                    <!--  Body  -->
                    <Border
                        Background="{StaticResource Brush.SurfaceVariant}"
                        CornerRadius="0,0,6,6"
                        Grid.Row="1"
                        Padding="12,8">
                        <StackPanel>
                            <TextBlock
                                FontSize="10"
                                Foreground="{StaticResource Brush.TextSecondary}"
                                Margin="0,0,0,4"
                                Text="数据输入节点" />
                            <TextBlock
                                FontSize="9"
                                Foreground="{StaticResource Brush.Placeholder}"
                                Text="支持手动输入各种数据类型"
                                TextWrapping="Wrap" />
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
