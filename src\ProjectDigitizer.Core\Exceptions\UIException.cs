namespace ProjectDigitizer.Core.Exceptions;

/// <summary>
/// UI层异常基类
/// </summary>
public class UIException : ProjectDigitizerException
{
    public UIException(string message) : base(message)
    {
    }

    public UIException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public UIException(string message, string? errorCode) : base(message, errorCode)
    {
    }

    public UIException(string message, string? errorCode, Exception innerException) : base(message, errorCode, innerException)
    {
    }
}

/// <summary>
/// 数据绑定异常
/// </summary>
public class DataBindingException : UIException
{
    public string PropertyName { get; }
    public string? ViewModelType { get; }

    public DataBindingException(string propertyName, string message) : base(message, "DATA_BINDING_ERROR")
    {
        PropertyName = propertyName;
        WithDetail("PropertyName", propertyName);
    }

    public DataBindingException(string propertyName, string? viewModelType, string message) : base(message, "DATA_BINDING_ERROR")
    {
        PropertyName = propertyName;
        ViewModelType = viewModelType;
        WithDetail("PropertyName", propertyName);
        if (viewModelType != null) WithDetail("ViewModelType", viewModelType);
    }

    public DataBindingException(string propertyName, string message, Exception innerException) : base(message, "DATA_BINDING_ERROR", innerException)
    {
        PropertyName = propertyName;
        WithDetail("PropertyName", propertyName);
    }
}

/// <summary>
/// 导航异常
/// </summary>
public class NavigationException : UIException
{
    public string? SourceView { get; }
    public string? TargetView { get; }

    public NavigationException(string message) : base(message, "NAVIGATION_ERROR")
    {
    }

    public NavigationException(string? sourceView, string? targetView, string message) : base(message, "NAVIGATION_ERROR")
    {
        SourceView = sourceView;
        TargetView = targetView;
        if (sourceView != null) WithDetail("SourceView", sourceView);
        if (targetView != null) WithDetail("TargetView", targetView);
    }

    public NavigationException(string message, Exception innerException) : base(message, "NAVIGATION_ERROR", innerException)
    {
    }
}

/// <summary>
/// 命令执行异常
/// </summary>
public class CommandExecutionException : UIException
{
    public string CommandName { get; }
    public object? CommandParameter { get; }

    public CommandExecutionException(string commandName, string message) : base(message, "COMMAND_EXECUTION_ERROR")
    {
        CommandName = commandName;
        WithDetail("CommandName", commandName);
    }

    public CommandExecutionException(string commandName, object? commandParameter, string message) : base(message, "COMMAND_EXECUTION_ERROR")
    {
        CommandName = commandName;
        CommandParameter = commandParameter;
        WithDetail("CommandName", commandName);
        if (commandParameter != null) WithDetail("CommandParameter", commandParameter);
    }

    public CommandExecutionException(string commandName, string message, Exception innerException) : base(message, "COMMAND_EXECUTION_ERROR", innerException)
    {
        CommandName = commandName;
        WithDetail("CommandName", commandName);
    }
}

/// <summary>
/// UI渲染异常
/// </summary>
public class RenderingException : UIException
{
    public string? ComponentName { get; }

    public RenderingException(string message) : base(message, "RENDERING_ERROR")
    {
    }

    public RenderingException(string? componentName, string message) : base(message, "RENDERING_ERROR")
    {
        ComponentName = componentName;
        if (componentName != null) WithDetail("ComponentName", componentName);
    }

    public RenderingException(string message, Exception innerException) : base(message, "RENDERING_ERROR", innerException)
    {
    }
}
