using System.Collections.Generic;

using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Application.Interfaces.UI
{
    public class GraphSourceNode
    {
        public required string Id { get; init; }
        public required string Title { get; init; }
        public required ModuleType ModuleType { get; init; }
        public int OutputCount { get; init; }
        public bool IsConnected { get; init; }
    }

    public class GraphNodeProperty
    {
        public required string Name { get; init; }
        public string Type { get; init; } = "Any";
        public string Description { get; init; } = "";
    }

    /// <summary>
    /// 图模型查询契约：插件属性面板通过该接口查询上下游节点和运行期值。
    /// 不依赖前端（WPF）类型。
    /// </summary>
    public interface INodeGraphQuery
    {
        IReadOnlyList<GraphSourceNode> GetUpstreamNodes(string currentNodeId);
        IReadOnlyList<GraphNodeProperty> GetNodeProperties(string nodeId);
        (bool isAvailable, object? value) TryGetRuntimeValue(string nodeId, string propertyName);
    }
}

