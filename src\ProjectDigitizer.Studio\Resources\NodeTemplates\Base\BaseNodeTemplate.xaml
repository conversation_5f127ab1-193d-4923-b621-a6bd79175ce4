<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 基础节点模板 ==========  -->
    <!--  所有节点的基础模板，提供通用的结构和样式  -->

    <!--  基础节点样式  -->
    <Style TargetType="nodify:Node" x:Key="BaseNodeStyle">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="nodify:Node">
                    <Grid Background="Transparent" x:Name="NodeRoot">
                        <!--  节点内容  -->
                        <ContentPresenter Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" />

                        <!--  输入连接器 - 调整到分层标题栏的中心位置  -->
                        <ItemsControl
                            Focusable="False"
                            HorizontalAlignment="Left"
                            ItemTemplate="{TemplateBinding InputConnectorTemplate}"
                            ItemsSource="{TemplateBinding Input}"
                            Margin="-9,28,0,0"
                            VerticalAlignment="Top"
                            x:Name="PART_Input" />

                        <!--  输出连接器 - 调整到分层标题栏的中心位置  -->
                        <ItemsControl
                            Focusable="False"
                            HorizontalAlignment="Right"
                            ItemTemplate="{TemplateBinding OutputConnectorTemplate}"
                            ItemsSource="{TemplateBinding Output}"
                            Margin="0,28,-9,0"
                            VerticalAlignment="Top"
                            x:Name="PART_Output" />
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!--  选中状态 - 添加优雅的发光效果，就像数据计算节点一样  -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" TargetName="NodeRoot" Value="{StaticResource SelectedNodeGlow}" />
                        </DataTrigger>

                        <!--  未选中状态 - 移除发光效果  -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="False">
                            <Setter Property="Effect" TargetName="NodeRoot" Value="{x:Null}" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  基础节点内容边框样式  -->
    <Style TargetType="Border" x:Key="BaseNodeBorderStyle">
        <Setter Property="Background" Value="White" />
        <Setter Property="CornerRadius" Value="12" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Effect" Value="{StaticResource StandardNodeShadow}" />
        <Style.Triggers>
            <!--  基于节点类型的边框颜色  -->
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Input}">
                <Setter Property="BorderBrush" Value="#4CAF50" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Transform}">
                <Setter Property="BorderBrush" Value="#2196F3" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Output}">
                <Setter Property="BorderBrush" Value="#FF9800" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Control}">
                <Setter Property="BorderBrush" Value="#9C27B0" />
            </DataTrigger>

            <!--  未选中状态 - 确保重置边框  -->
            <DataTrigger Binding="{Binding IsSelected}" Value="False">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Effect" Value="{StaticResource StandardNodeShadow}" />
            </DataTrigger>

            <!--  选中状态 - 只使用发光效果，不显示边框  -->
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Effect" Value="{StaticResource SelectedNodeGlow}" />
            </DataTrigger>

            <!--  锁定状态  -->
            <DataTrigger Binding="{Binding IsLocked}" Value="True">
                <Setter Property="Opacity" Value="0.8" />
                <Setter Property="Effect" Value="{StaticResource LockedNodeShadow}" />
            </DataTrigger>

            <!--  禁用状态  -->
            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                <Setter Property="Opacity" Value="0.5" />
                <Setter Property="BorderBrush" Value="#BDBDBD" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--  基础节点标题栏样式  -->
    <Style TargetType="Border" x:Key="BaseNodeHeaderStyle">
        <Setter Property="CornerRadius" Value="12,12,0,0" />
        <Setter Property="Padding" Value="8,4" />
        <Setter Property="Height" Value="56" />
        <Setter Property="Background" Value="{StaticResource ModernHeaderGradientBlue}" />
        <Style.Triggers>
            <!--  基于节点类型的背景颜色  -->
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Input}">
                <Setter Property="Background" Value="{StaticResource ModernHeaderGradientGreen}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Transform}">
                <Setter Property="Background" Value="{StaticResource ModernHeaderGradientBlue}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Output}">
                <Setter Property="Background" Value="{StaticResource ModernHeaderGradientOrange}" />
            </DataTrigger>
            <DataTrigger Binding="{Binding Module.NodeType}" Value="{x:Static models:NodeType.Control}">
                <Setter Property="Background" Value="{StaticResource ModernHeaderGradientPurple}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--  基础节点标题栏内容网格  -->
    <DataTemplate x:Key="BaseNodeHeaderContentTemplate">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="26" />
                <RowDefinition Height="26" />
            </Grid.RowDefinitions>

            <!--  第一行：主要信息  -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  1. 折叠/展开按钮  -->
                <Button
                    Grid.Column="0"
                    Style="{StaticResource ExpandCollapseButtonStyle}"
                    ToolTip="折叠/展开节点">
                    <ipack:PackIconMaterial
                        Foreground="White"
                        Height="14"
                        Kind="ChevronDown"
                        Opacity="0.9"
                        Width="14" />
                </Button>

                <!--  2. 节点logo (模块类型图标)  -->
                <ipack:PackIconMaterial
                    Foreground="White"
                    Grid.Column="1"
                    Height="18"
                    Kind="{Binding Module.Type, Converter={StaticResource ModuleTypeToIconConverter}}"
                    Margin="0,0,4,0"
                    VerticalAlignment="Center"
                    Width="18" />

                <!--  3. 节点名称 (可编辑文本框)  -->
                <TextBox
                    Grid.Column="2"
                    Margin="0,0,4,0"
                    Style="{StaticResource NodeTitleTextBoxStyle}"
                    Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                    ToolTip="双击编辑节点名称" />
            </Grid>

            <!--  第二行：功能按钮组  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  功能按钮组 - 右对齐  -->
                <StackPanel
                    Grid.Column="1"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal">

                    <!--  节点信息按钮  -->
                    <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="节点信息">
                        <ipack:PackIconMaterial
                            Foreground="White"
                            Height="14"
                            Kind="Information"
                            Opacity="0.9"
                            Width="14" />
                    </Button>

                    <!--  锁定位置按钮  -->
                    <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="锁定/解锁位置">
                        <ipack:PackIconMaterial
                            Foreground="White"
                            Height="14"
                            Opacity="0.9"
                            Width="14">
                            <ipack:PackIconMaterial.Style>
                                <Style TargetType="ipack:PackIconMaterial">
                                    <Setter Property="Kind" Value="LockOpen" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                            <Setter Property="Kind" Value="Lock" />
                                            <Setter Property="Opacity" Value="1.0" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ipack:PackIconMaterial.Style>
                        </ipack:PackIconMaterial>
                    </Button>

                    <!--  启用/禁用节点按钮  -->
                    <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="启用/禁用节点">
                        <ipack:PackIconMaterial
                            Foreground="White"
                            Height="14"
                            Opacity="0.9"
                            Width="14">
                            <ipack:PackIconMaterial.Style>
                                <Style TargetType="ipack:PackIconMaterial">
                                    <Setter Property="Kind" Value="Play" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                            <Setter Property="Kind" Value="Pause" />
                                            <Setter Property="Foreground" Value="#FF6B6B" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ipack:PackIconMaterial.Style>
                        </ipack:PackIconMaterial>
                    </Button>

                    <!--  执行状态信息  -->
                    <Ellipse
                        Height="12"
                        Margin="3,0,3,0"
                        ToolTip="执行状态"
                        VerticalAlignment="Center"
                        Width="12">
                        <Ellipse.Style>
                            <Style TargetType="Ellipse">
                                <Setter Property="Fill" Value="#4CAF50" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding ExecutionStatus}" Value="Running">
                                        <Setter Property="Fill" Value="#2196F3" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                        <Setter Property="Fill" Value="#F44336" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding ExecutionStatus}" Value="Warning">
                                        <Setter Property="Fill" Value="#FF9800" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding ExecutionStatus}" Value="Completed">
                                        <Setter Property="Fill" Value="#4CAF50" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Ellipse.Style>
                    </Ellipse>
                </StackPanel>
            </Grid>
        </Grid>
    </DataTemplate>

</ResourceDictionary>
