<UserControl x:Class="ProjectDigitizer.Studio.Controls.Common.ProgressIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="100"
        d:DesignWidth="400">

    <UserControl.Resources>
        <!-- 进度条样式 -->
        <Style x:Key="ModernProgressBarStyle"
                TargetType="ProgressBar">
            <Setter Property="Height"
                    Value="6"/>
            <Setter Property="Background" Value="{StaticResource Brush.Border}"/>
            <Setter Property="Foreground" Value="{StaticResource Brush.Primary}"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3">
                            <Grid>
                                <Rectangle Name="PART_Track"
                                           Fill="{TemplateBinding Background}"
                                           RadiusX="3"
                                        RadiusY="3"/>
                                <Rectangle Name="PART_Indicator"
                                           Fill="{TemplateBinding Foreground}"
                                           RadiusX="3"
                                        RadiusY="3"
                                           HorizontalAlignment="Left"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 操作项样式 -->
        <Style x:Key="OperationItemStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource Brush.Surface}"/>
            <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="CornerRadius"
                    Value="4"/>
            <Setter Property="Margin"
                    Value="0,2"/>
            <Setter Property="Padding"
                    Value="12,8"/>
        </Style>

        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButtonStyle" TargetType="Button">
            <Setter Property="Background"
                    Value="Transparent"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Foreground" Value="{StaticResource Brush.TextSecondary}"/>
            <Setter Property="FontSize"
                    Value="12"/>
            <Setter Property="Padding"
                    Value="8,4"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="2"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource Brush.SurfaceVariant}"/>
                                <Setter Property="Foreground" Value="{StaticResource Brush.Text}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Border Background="{StaticResource Brush.Surface}"
            BorderBrush="{StaticResource Brush.Border}"
            BorderThickness="1"
            CornerRadius="6"
            Visibility="{Binding IsVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
        <Border.Effect>
            <DropShadowEffect Color="Black"
                              Opacity="0.1"
                              ShadowDepth="2"
                              BlurRadius="8"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0"
                    Background="{StaticResource Brush.SurfaceVariant}"
                    CornerRadius="6,6,0,0"
                    Padding="12,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="操作进度"
                               FontWeight="SemiBold"
                               FontSize="14"
                               Foreground="{StaticResource Brush.Text}"/>

                    <Button Grid.Column="1"
                            Content="全部取消"
                            Style="{StaticResource CancelButtonStyle}"
                            Command="{Binding CancelAllCommand}"
                            Visibility="{Binding HasActiveOperations, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </Grid>
            </Border>

            <!-- 操作列表 -->
            <ScrollViewer Grid.Row="1"
                          MaxHeight="200"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled">
                <ItemsControl ItemsSource="{Binding ActiveOperations}"
                              Margin="8">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Style="{StaticResource OperationItemStyle}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 操作名称 -->
                                    <TextBlock Grid.Row="0"
                                            Grid.Column="0"
                                               Text="{Binding OperationName}"
                                               FontWeight="Medium"
                                               FontSize="13"
                                               Foreground="{StaticResource Brush.Text}"/>

                                    <!-- 取消按钮 -->
                                    <Button Grid.Row="0"
                                            Grid.Column="1"
                                            Content="✕"
                                            Style="{StaticResource CancelButtonStyle}"
                                            Command="{Binding CancelCommand}"
                                            Visibility="{Binding IsRunning, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                    <!-- 进度条 -->
                                    <ProgressBar Grid.Row="1"
                                            Grid.ColumnSpan="2"
                                                 Value="{Binding Progress}"
                                                 Maximum="100"
                                                 Style="{StaticResource ModernProgressBarStyle}"
                                                 Margin="0,4,0,2"/>

                                    <!-- 状态信息 -->
                                    <Grid Grid.Row="2"
                                            Grid.ColumnSpan="2">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="{Binding Message}" FontSize="11" Foreground="{StaticResource Brush.TextSecondary}" TextTrimming="CharacterEllipsis"/>

                                        <TextBlock Grid.Column="1" FontSize="11" Foreground="{StaticResource Brush.TextSecondary}">
                                            <TextBlock.Text>
                                                <MultiBinding StringFormat="{}{0:F0}%">
                                                    <Binding Path="Progress"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                        </TextBlock>
                                    </Grid>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>

            <!-- 总体进度 -->
            <Border Grid.Row="2" Background="{StaticResource Brush.SurfaceVariant}"
                    CornerRadius="0,0,6,6"
                    Padding="12,8"
                    Visibility="{Binding HasActiveOperations, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding CurrentMessage}" FontSize="12" Foreground="{StaticResource Brush.TextSecondary}" TextTrimming="CharacterEllipsis"/>

                    <TextBlock Grid.Column="1" FontSize="12" FontWeight="Medium" Foreground="{StaticResource Brush.Text}">
                        <TextBlock.Text>
                            <MultiBinding StringFormat="总体进度: {0:F0}%">
                                <Binding Path="OverallProgress"/>
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
