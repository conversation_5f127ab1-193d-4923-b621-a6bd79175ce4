<UserControl
    d:DesignHeight="450"
    d:DesignWidth="300"
    mc:Ignorable="d"
    x:Class="ProjectDigitizer.Studio.Controls.Properties.DynamicPropertyPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:selectors="clr-namespace:ProjectDigitizer.Studio.Selectors"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="NodePropertyPanels.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  节点属性数据模板选择器  -->
            <selectors:NodePropertyDataTemplateSelector
                ControlNodeTemplate="{StaticResource ControlNodePropertyPanel}"
                DefaultTemplate="{StaticResource TransformNodePropertyPanel}"
                InputNodeTemplate="{StaticResource InputNodePropertyPanel}"
                OutputNodeTemplate="{StaticResource OutputNodePropertyPanel}"
                TransformNodeTemplate="{StaticResource TransformNodePropertyPanel}"
                x:Key="NodePropertyTemplateSelector" />
        </ResourceDictionary>
    </UserControl.Resources>

    <!--  替换 Card 为普通 Border，保留边距与阴影  -->
    <Border
        Background="{DynamicResource Brush.Surface}"
        CornerRadius="6"
        Effect="{StaticResource Shadow.M}"
        Margin="8">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题栏：ColorZone -> Border  -->
            <Border
                Background="{DynamicResource Brush.Primary}"
                Grid.Row="0"
                Padding="20,16"
                x:Name="TitleBar">
                <StackPanel Orientation="Horizontal">
                    <StackPanel>
                        <TextBlock
                            FontSize="18"
                            FontWeight="Medium"
                            Foreground="{DynamicResource White}"
                            Text="属性配置" />
                        <TextBlock
                            FontSize="12"
                            Foreground="{DynamicResource White}"
                            Margin="0,2,0,0"
                            Opacity="0.7"
                            Text="配置选中模块的参数" />
                    </StackPanel>
                </StackPanel>
            </Border>

            <!--  内容区域  -->
            <ScrollViewer
                Grid.Row="1"
                HorizontalScrollBarVisibility="Disabled"
                Padding="16,20,16,20"
                VerticalScrollBarVisibility="Auto">

                <Grid x:Name="PropertyContainer">
                    <!--  没有选中节点时的提示  -->
                    <StackPanel
                        HorizontalAlignment="Center"
                        Margin="0,40,0,60"
                        VerticalAlignment="Center"
                        x:Name="EmptyStatePanel">
                        <ipack:PackIconMaterial
                            Foreground="{DynamicResource Brush.TextSecondary}"
                            Height="48"
                            HorizontalAlignment="Center"
                            Kind="CursorDefault"
                            Margin="0,0,0,16"
                            Width="48" />
                        <TextBlock
                            FontSize="14"
                            Foreground="{DynamicResource Brush.TextSecondary}"
                            MaxWidth="200"
                            Text="点击画布中的节点查看属性"
                            TextAlignment="Center"
                            TextWrapping="Wrap"
                            x:Name="EmptyStateText" />
                    </StackPanel>

                    <!--  动态属性面板  -->
                    <ContentPresenter
                        ContentTemplateSelector="{StaticResource NodePropertyTemplateSelector}"
                        Margin="0"
                        Visibility="Collapsed"
                        x:Name="PropertyContentPresenter" />
                </Grid>

            </ScrollViewer>

            <!--  操作按钮区域：ColorZone -> Border  -->
            <Border
                Background="{DynamicResource Brush.Primary/16}"
                Grid.Row="2"
                Padding="20,12">

                <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">

                    <Button
                        Background="Transparent"
                        BorderBrush="{DynamicResource Brush.Primary}"
                        BorderThickness="1"
                        Click="ResetButton_Click"
                        Content="重置"
                        Foreground="{DynamicResource Brush.Text}"
                        Margin="0,0,12,0"
                        Padding="16,8"
                        x:Name="ResetButton" />

                    <Button
                        Background="{DynamicResource Brush.Primary}"
                        BorderThickness="0"
                        Click="ApplyButton_Click"
                        Content="应用"
                        Foreground="{DynamicResource Brush.PrimaryForeground}"
                        Padding="16,8"
                        x:Name="ApplyButton" />

                </StackPanel>

            </Border>

        </Grid>

    </Border>

</UserControl>




