using System.Linq.Expressions;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Infrastructure.Repositories;

/// <summary>
/// 通用存储库基类实现
/// 为WPF桌面应用提供文件、内存和配置数据的统一访问接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public abstract class Repository<T> : IRepository<T> where T : class
{
    protected readonly ILogger<Repository<T>> _logger;
    protected readonly List<T> _entities;

    protected Repository(ILogger<Repository<T>> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _entities = new List<T>();
    }

    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    public virtual async Task<T?> GetByIdAsync(object id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取实体，ID: {Id}", id);

            var entity = await FindByIdInternalAsync(id, cancellationToken);

            if (entity == null)
            {
                _logger.LogWarning("未找到ID为 {Id} 的实体", id);
            }

            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体时发生错误，ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 获取所有实体
    /// </summary>
    public virtual async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取所有实体");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            return _entities.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据条件查找实体
    /// </summary>
    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据条件查找实体");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            var compiledPredicate = predicate.Compile();
            var results = _entities.Where(compiledPredicate).ToList();

            _logger.LogDebug("找到 {Count} 个符合条件的实体", results.Count);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件查找实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据条件查找单个实体
    /// </summary>
    public virtual async Task<T?> FindSingleAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据条件查找单个实体");

            var results = await FindAsync(predicate, cancellationToken);
            var resultList = results.ToList();

            if (resultList.Count > 1)
            {
                _logger.LogWarning("查询返回了多个结果，但期望只有一个");
            }

            return resultList.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据条件查找单个实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 添加实体
    /// </summary>
    public virtual async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            _logger.LogDebug("添加实体");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            _entities.Add(entity);
            await SaveChangesAsync(cancellationToken);

            _logger.LogDebug("实体添加成功");

            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 批量添加实体
    /// </summary>
    public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entities == null)
                throw new ArgumentNullException(nameof(entities));

            var entityList = entities.ToList();
            _logger.LogDebug("批量添加 {Count} 个实体", entityList.Count);

            await LoadEntitiesIfNeededAsync(cancellationToken);

            _entities.AddRange(entityList);
            await SaveChangesAsync(cancellationToken);

            _logger.LogDebug("批量添加实体成功");

            return entityList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量添加实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 更新实体
    /// </summary>
    public virtual async Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            _logger.LogDebug("更新实体");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            var existingEntity = await FindExistingEntityAsync(entity, cancellationToken);
            if (existingEntity != null)
            {
                var index = _entities.IndexOf(existingEntity);
                _entities[index] = entity;
            }
            else
            {
                _entities.Add(entity);
            }

            await SaveChangesAsync(cancellationToken);

            _logger.LogDebug("实体更新成功");

            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 删除实体
    /// </summary>
    public virtual async Task DeleteAsync(T entity, CancellationToken cancellationToken = default)
    {
        try
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            _logger.LogDebug("删除实体");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            var removed = _entities.Remove(entity);
            if (removed)
            {
                await SaveChangesAsync(cancellationToken);
                _logger.LogDebug("实体删除成功");
            }
            else
            {
                _logger.LogWarning("要删除的实体不存在");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除实体时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    public virtual async Task DeleteByIdAsync(object id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据ID删除实体，ID: {Id}", id);

            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity != null)
            {
                await DeleteAsync(entity, cancellationToken);
            }
            else
            {
                _logger.LogWarning("要删除的实体不存在，ID: {Id}", id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID删除实体时发生错误，ID: {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("检查实体是否存在");

            var entity = await FindSingleAsync(predicate, cancellationToken);
            var exists = entity != null;

            _logger.LogDebug("实体存在性检查结果: {Exists}", exists);

            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查实体是否存在时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取实体数量
    /// </summary>
    public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取实体数量");

            await LoadEntitiesIfNeededAsync(cancellationToken);

            int count;
            if (predicate == null)
            {
                count = _entities.Count;
            }
            else
            {
                var compiledPredicate = predicate.Compile();
                count = _entities.Count(compiledPredicate);
            }

            _logger.LogDebug("实体数量: {Count}", count);

            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体数量时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 子类需要实现的抽象方法：根据ID查找实体
    /// </summary>
    protected abstract Task<T?> FindByIdInternalAsync(object id, CancellationToken cancellationToken);

    /// <summary>
    /// 子类需要实现的抽象方法：查找现有实体（用于更新操作）
    /// </summary>
    protected abstract Task<T?> FindExistingEntityAsync(T entity, CancellationToken cancellationToken);

    /// <summary>
    /// 子类需要实现的抽象方法：加载实体数据
    /// </summary>
    protected abstract Task LoadEntitiesIfNeededAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 子类需要实现的抽象方法：保存更改
    /// </summary>
    protected abstract Task SaveChangesAsync(CancellationToken cancellationToken);
}
