using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 轻量的 Excel 风格公式求值器。
    /// 支持：IF, AND, OR, SUM, AVG, MAX, MIN, ABS, ROUND 以及基本算术/比较运算和括号。
    /// 说明：不访问真实数据源，{字段} 占位符会被替换为 0。
    /// </summary>
    public static class ExcelLikeFormulaEvaluator
    {
        public static bool TryEvaluate(string expression, out string result)
        {
            result = string.Empty;
            if (string.IsNullOrWhiteSpace(expression))
                return false;

            try
            {
                var value = EvaluateInternal(expression);
                result = value is IFormattable f
                    ? f.ToString(null, CultureInfo.InvariantCulture)
                    : Convert.ToString(value, CultureInfo.InvariantCulture) ?? string.Empty;
                return true;
            }
            catch
            {
                result = string.Empty;
                return false;
            }
        }

        private static object EvaluateInternal(string expression)
        {
            var expr = ReplacePlaceholdersWithZero(expression).Trim();

            // 去除外层包裹的括号
            expr = TrimOuterParentheses(expr);

            // 字面量数字
            if (double.TryParse(expr, NumberStyles.Float, CultureInfo.InvariantCulture, out var number))
                return number;

            // 字符串字面量 "text"
            if (IsQuotedString(expr))
                return Unquote(expr);

            // 顶层函数调用
            if (TryParseTopLevelFunction(expr, out var name, out var args))
            {
                switch (name.ToUpperInvariant())
                {
                    case "IF":
                        RequireArgCount(name, args, 3);
                        var cond = EvaluateCondition(args[0]);
                        return cond ? EvaluateInternal(args[1]) : EvaluateInternal(args[2]);

                    case "AND":
                        return args.All(a => EvaluateCondition(a));

                    case "OR":
                        return args.Any(a => EvaluateCondition(a));

                    case "SUM":
                        return args.Select(a => ToNumber(EvaluateInternal(a))).Sum();

                    case "AVG":
                    case "AVERAGE":
                        var seq = args.Select(a => ToNumber(EvaluateInternal(a))).ToArray();
                        return seq.Length == 0 ? 0 : seq.Average();

                    case "MAX":
                        return args.Select(a => ToNumber(EvaluateInternal(a))).Max();

                    case "MIN":
                        return args.Select(a => ToNumber(EvaluateInternal(a))).Min();

                    case "ABS":
                        RequireArgCount(name, args, 1);
                        return Math.Abs(ToNumber(EvaluateInternal(args[0])));

                    case "ROUND":
                        if (args.Count == 1)
                            return Math.Round(ToNumber(EvaluateInternal(args[0])));
                        if (args.Count == 2)
                        {
                            var x = ToNumber(EvaluateInternal(args[0]));
                            var d = (int)ToNumber(EvaluateInternal(args[1]));
                            return Math.Round(x, d);
                        }
                        throw new ArgumentException("ROUND 参数个数应为 1 或 2");

                    // 数值取整扩展：ROUNDUP/ROUNDDOWN（中文注释，仅用于预览计算）
                    case "ROUNDUP":
                        // ROUNDUP(number, [digits])，朝远离0方向取整
                        if (args.Count == 1)
                        {
                            var x = ToNumber(EvaluateInternal(args[0]));
                            return x >= 0 ? Math.Ceiling(x) : Math.Floor(x);
                        }
                        if (args.Count == 2)
                        {
                            var x = ToNumber(EvaluateInternal(args[0]));
                            var d = (int)ToNumber(EvaluateInternal(args[1]));
                            double scale = Math.Pow(10, Math.Abs(d));
                            if (d >= 0)
                            {
                                var y = x * scale;
                                y = x >= 0 ? Math.Ceiling(y) : Math.Floor(y);
                                return y / scale;
                            }
                            else
                            {
                                scale = Math.Pow(10, Math.Abs(d));
                                var y = x / scale;
                                y = x >= 0 ? Math.Ceiling(y) : Math.Floor(y);
                                return y * scale;
                            }
                        }
                        throw new ArgumentException("ROUNDUP parameters should be 1 or 2");

                    case "ROUNDDOWN":
                        // ROUNDDOWN(number, [digits])，朝接近0方向取整
                        if (args.Count == 1)
                        {
                            var x = ToNumber(EvaluateInternal(args[0]));
                            return x >= 0 ? Math.Floor(x) : Math.Ceiling(x);
                        }
                        if (args.Count == 2)
                        {
                            var x = ToNumber(EvaluateInternal(args[0]));
                            var d = (int)ToNumber(EvaluateInternal(args[1]));
                            double scale = Math.Pow(10, Math.Abs(d));
                            if (d >= 0)
                            {
                                var y = x * scale;
                                y = x >= 0 ? Math.Floor(y) : Math.Ceiling(y);
                                return y / scale;
                            }
                            else
                            {
                                scale = Math.Pow(10, Math.Abs(d));
                                var y = x / scale;
                                y = x >= 0 ? Math.Floor(y) : Math.Ceiling(y);
                                return y * scale;
                            }
                        }
                        throw new ArgumentException("ROUNDDOWN parameters should be 1 or 2");

                    case "LEN":
                        RequireArgCount(name, args, 1);
                        return EvaluateInternal(args[0]).ToString()?.Length ?? 0;

                    case "CONCAT":
                        return string.Concat(args.Select(a => Convert.ToString(EvaluateInternal(a), CultureInfo.InvariantCulture)));

                    case "TEXT":
                        RequireArgCount(name, args, 2);
                        var num = ToNumber(EvaluateInternal(args[0]));
                        var fmt = Convert.ToString(EvaluateInternal(args[1])) ?? "";
                        return num.ToString(fmt, CultureInfo.InvariantCulture);

                    // 文本处理扩展：LEFT/RIGHT/MID/FIND/SUBSTITUTE/TRIM/UPPER/LOWER（中文注释，仅用于预览计算）
                    case "LEFT":
                        // LEFT(text, [num_chars])
                        if (args.Count < 1 || args.Count > 2) throw new ArgumentException("LEFT parameters should be 1 or 2");
                        {
                            var s = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var n = args.Count == 2 ? (int)ToNumber(EvaluateInternal(args[1])) : 1;
                            if (n <= 0) return string.Empty;
                            return s.Length <= n ? s : s.Substring(0, n);
                        }

                    case "RIGHT":
                        // RIGHT(text, [num_chars])
                        if (args.Count < 1 || args.Count > 2) throw new ArgumentException("RIGHT parameters should be 1 or 2");
                        {
                            var s = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var n = args.Count == 2 ? (int)ToNumber(EvaluateInternal(args[1])) : 1;
                            if (n <= 0) return string.Empty;
                            return s.Length <= n ? s : s.Substring(Math.Max(0, s.Length - n), Math.Min(n, s.Length));
                        }

                    case "MID":
                        // MID(text, start_num, num_chars)，start 从1开始
                        RequireArgCount(name, args, 3);
                        {
                            var s = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var start = (int)ToNumber(EvaluateInternal(args[1]));
                            var count = (int)ToNumber(EvaluateInternal(args[2]));
                            if (count <= 0) return string.Empty;
                            if (start < 1) start = 1;
                            int idx = start - 1;
                            if (idx >= s.Length) return string.Empty;
                            if (idx + count > s.Length) count = s.Length - idx;
                            return s.Substring(idx, count);
                        }

                    case "FIND":
                        // FIND(find_text, within_text, [start_num])，区分大小写，找不到返回 0（Excel 为 #VALUE!，此处简化）
                        if (args.Count < 2 || args.Count > 3) throw new ArgumentException("FIND parameters should be 2 or 3");
                        {
                            var find = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var within = Convert.ToString(EvaluateInternal(args[1])) ?? string.Empty;
                            var start = args.Count == 3 ? (int)ToNumber(EvaluateInternal(args[2])) : 1;
                            if (start < 1) start = 1;
                            int idx = within.IndexOf(find, Math.Min(start - 1, Math.Max(0, within.Length)), StringComparison.Ordinal);
                            return idx >= 0 ? idx + 1 : 0; // 1-based index
                        }

                    case "SUBSTITUTE":
                        // SUBSTITUTE(text, old_text, new_text, [instance_num])
                        if (args.Count < 3 || args.Count > 4) throw new ArgumentException("SUBSTITUTE parameters should be 3 or 4");
                        {
                            var text = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var oldText = Convert.ToString(EvaluateInternal(args[1])) ?? string.Empty;
                            var newText = Convert.ToString(EvaluateInternal(args[2])) ?? string.Empty;
                            if (args.Count == 3)
                                return text.Replace(oldText, newText);
                            var instance = (int)ToNumber(EvaluateInternal(args[3]));
                            if (instance <= 0) return text;
                            int count = 0;
                            int i = 0;
                            var sb = new StringBuilder(text.Length);
                            while (i <= text.Length - oldText.Length && oldText.Length > 0)
                            {
                                if (string.CompareOrdinal(text, i, oldText, 0, oldText.Length) == 0)
                                {
                                    count++;
                                    if (count == instance)
                                    {
                                        sb.Append(newText);
                                        i += oldText.Length;
                                        continue;
                                    }
                                }
                                sb.Append(text[i]);
                                i++;
                            }
                            if (i < text.Length) sb.Append(text.AsSpan(i));
                            return sb.ToString();
                        }

                    case "TRIM":
                        // TRIM(text)：去除首尾空白并将中间连续空白压缩为单个空格
                        RequireArgCount(name, args, 1);
                        {
                            var s = Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty;
                            var trimmed = s.Trim();
                            var sb2 = new StringBuilder(trimmed.Length);
                            bool inSpace = false;
                            foreach (var ch in trimmed)
                            {
                                if (char.IsWhiteSpace(ch))
                                {
                                    if (!inSpace) { sb2.Append(' '); inSpace = true; }
                                }
                                else { inSpace = false; sb2.Append(ch); }
                            }
                            return sb2.ToString();
                        }

                    case "UPPER":
                        RequireArgCount(name, args, 1);
                        return (Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty).ToUpperInvariant();

                    case "LOWER":
                        RequireArgCount(name, args, 1);
                        return (Convert.ToString(EvaluateInternal(args[0])) ?? string.Empty).ToLowerInvariant();

                    case "IFNA":
                        // IFNA(value, value_if_na) —— 简化实现：将空字符串或 NaN 视为 NA
                        RequireArgCount(name, args, 2);
                        try
                        {
                            var v = EvaluateInternal(args[0]);
                            if (v is string sv && string.IsNullOrEmpty(sv))
                                return EvaluateInternal(args[1]);
                            if (v is double dv && double.IsNaN(dv))
                                return EvaluateInternal(args[1]);
                            return v;
                        }
                        catch
                        {
                            return EvaluateInternal(args[1]);
                        }

                    case "IFERROR":
                        RequireArgCount(name, args, 2);
                        try
                        {
                            return EvaluateInternal(args[0]);
                        }
                        catch
                        {
                            return EvaluateInternal(args[1]);
                        }
                }
            }

            // 退回 DataTable.Compute 处理算术和比较表达式
            return EvaluateByDataTable(expr);
        }

        private static bool EvaluateCondition(string expression)
        {
            var val = EvaluateInternal(expression);
            if (val is bool b) return b;
            if (val is string s)
            {
                if (string.Equals(s, "TRUE", StringComparison.OrdinalIgnoreCase)) return true;
                if (string.Equals(s, "FALSE", StringComparison.OrdinalIgnoreCase)) return false;
                // 非空字符串按 true 处理
                return !string.IsNullOrEmpty(s);
            }
            return Math.Abs(ToNumber(val)) > double.Epsilon;
        }

        private static double ToNumber(object value)
        {
            return value switch
            {
                double d => d,
                float f => f,
                int i => i,
                long l => l,
                decimal m => (double)m,
                string s when double.TryParse(s, NumberStyles.Float, CultureInfo.InvariantCulture, out var n) => n,
                _ => throw new ArgumentException($"无法将值转换为数字: {value}")
            };
        }

        private static object EvaluateByDataTable(string expr)
        {
            var table = new DataTable();
            // 将等号比较使用 '='，不做替换；DataTable 表达式支持 >, <, >=, <=, =, <>
            var obj = table.Compute(expr, null);
            return obj ?? string.Empty;
        }

        private static bool TryParseTopLevelFunction(string expr, out string name, out List<string> args)
        {
            name = string.Empty;
            args = new List<string>();

            int idx = expr.IndexOf('(');
            if (idx <= 0) return false;

            name = expr.Substring(0, idx).Trim();
            if (!IsIdentifier(name)) return false;

            // 找到与之匹配的右括号并确保在末尾
            int end = FindMatchingRightParen(expr, idx);
            if (end != expr.Length - 1) return false;

            var inner = expr.Substring(idx + 1, end - idx - 1);
            args = SplitArgs(inner);
            return true;
        }

        private static List<string> SplitArgs(string inner)
        {
            var list = new List<string>();
            int depth = 0;
            bool inStr = false;
            var sb = new StringBuilder();
            for (int i = 0; i < inner.Length; i++)
            {
                char c = inner[i];
                if (c == '"')
                {
                    inStr = !inStr;
                    sb.Append(c);
                    continue;
                }
                if (!inStr)
                {
                    if (c == '(') depth++;
                    else if (c == ')') depth--;
                    else if (c == ',' && depth == 0)
                    {
                        list.Add(sb.ToString().Trim());
                        sb.Clear();
                        continue;
                    }
                }
                sb.Append(c);
            }
            var last = sb.ToString().Trim();
            if (last.Length > 0) list.Add(last);
            return list;
        }

        private static int FindMatchingRightParen(string s, int leftIdx)
        {
            int depth = 0;
            bool inStr = false;
            for (int i = leftIdx; i < s.Length; i++)
            {
                char c = s[i];
                if (c == '"') inStr = !inStr;
                if (inStr) continue;
                if (c == '(') depth++;
                else if (c == ')')
                {
                    depth--;
                    if (depth == 0) return i;
                }
            }
            return -1;
        }

        private static bool IsIdentifier(string name)
        {
            if (string.IsNullOrEmpty(name)) return false;
            if (!(char.IsLetter(name[0]) || name[0] == '_')) return false;
            for (int i = 1; i < name.Length; i++)
            {
                if (!(char.IsLetterOrDigit(name[i]) || name[i] == '_')) return false;
            }
            return true;
        }

        private static bool IsQuotedString(string s)
        {
            return s.Length >= 2 && s[0] == '"' && s[^1] == '"';
        }

        private static string Unquote(string s)
        {
            if (!IsQuotedString(s)) return s;
            return s.Substring(1, s.Length - 2).Replace("\"\"", "\"");
        }

        private static string TrimOuterParentheses(string s)
        {
            s = s.Trim();
            while (s.Length > 0 && s[0] == '(' && FindMatchingRightParen(s, 0) == s.Length - 1)
            {
                s = s.Substring(1, s.Length - 2).Trim();
            }
            return s;
        }

        private static string ReplacePlaceholdersWithZero(string expression)
        {
            if (string.IsNullOrEmpty(expression)) return string.Empty;
            var sb = new StringBuilder(expression.Length);
            bool inBrace = false;
            foreach (var ch in expression)
            {
                if (ch == '{')
                {
                    inBrace = true;
                    sb.Append('0');
                }
                else if (ch == '}')
                {
                    inBrace = false;
                }
                else if (!inBrace)
                {
                    sb.Append(ch);
                }
            }
            return sb.ToString();
        }

        private static void RequireArgCount(string name, List<string> args, int count)
        {
            if (args.Count != count)
                throw new ArgumentException($"函数 {name} 需要 {count} 个参数，实际为 {args.Count}");
        }
    }
}
