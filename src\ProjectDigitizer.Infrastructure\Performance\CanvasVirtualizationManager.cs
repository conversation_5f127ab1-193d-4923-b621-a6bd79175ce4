using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Infrastructure.Performance
{
    /// <summary>
    /// 画布虚拟化管理器 - 优化大量节点的渲染性能
    /// </summary>
    public class CanvasVirtualizationManager : ICanvasVirtualizationManager
    {
        private readonly DispatcherTimer _viewportUpdateTimer;
        private Rect _currentViewport;
        private double _currentZoom = 1.0;
        private readonly HashSet<object> _visibleNodes;
        private readonly HashSet<object> _allNodes;
        private const double VIEWPORT_MARGIN = 100; // 视口边距，提前加载节点
        private bool _isUpdatingViewport = false; // 防止更新循环

        public CanvasVirtualizationManager()
        {
            _visibleNodes = new HashSet<object>();
            _allNodes = new HashSet<object>();

            _viewportUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 10FPS更新频率
            };
            _viewportUpdateTimer.Tick += OnViewportUpdateTimerTick;
        }

        /// <summary>
        /// 当前可见的节点集合
        /// </summary>
        public IEnumerable<T> GetVisibleNodes<T>() where T : class
        {
            return _visibleNodes.OfType<T>();
        }

        /// <summary>
        /// 更新视口信息
        /// </summary>
        public void UpdateViewport(ViewportRect viewport, double zoom)
        {
            // 临时禁用虚拟化功能，防止Stack overflow
            System.Diagnostics.Debug.WriteLine("虚拟化功能已临时禁用，防止Stack overflow问题");
            return;

            // 防止更新循环
            if (_isUpdatingViewport)
            {
                return;
            }

            _currentViewport = new Rect(viewport.X, viewport.Y, viewport.Width, viewport.Height);
            _currentZoom = zoom;

            if (!_viewportUpdateTimer.IsEnabled)
            {
                _viewportUpdateTimer.Start();
            }
        }

        /// <summary>
        /// 添加节点到管理器
        /// </summary>
        public void AddNode<T>(T node) where T : class
        {
            if (node != null)
            {
                _allNodes.Add(node);
                UpdateNodeVisibility();
            }
        }

        /// <summary>
        /// 从管理器移除节点
        /// </summary>
        public void RemoveNode<T>(T node) where T : class
        {
            if (node != null)
            {
                _allNodes.Remove(node);
                _visibleNodes.Remove(node);
            }
        }

        /// <summary>
        /// 清空所有节点
        /// </summary>
        public void Clear()
        {
            _allNodes.Clear();
            _visibleNodes.Clear();
        }

        /// <summary>
        /// 强制更新节点可见性
        /// </summary>
        public void ForceUpdateVisibility()
        {
            UpdateNodeVisibility();
        }

        /// <summary>
        /// 可见性变化事件
        /// </summary>
        public event Action<IEnumerable<object>, IEnumerable<object>, IEnumerable<object>>? OnVisibilityChanged;

        private void OnViewportUpdateTimerTick(object? sender, EventArgs e)
        {
            UpdateNodeVisibility();
            _viewportUpdateTimer.Stop();
        }

        private void UpdateNodeVisibility()
        {
            if (_allNodes.Count == 0 || _isUpdatingViewport) return;

            _isUpdatingViewport = true;
            try
            {

                // 计算扩展的视口区域
                var expandedViewport = new Rect(
                    _currentViewport.X - VIEWPORT_MARGIN,
                    _currentViewport.Y - VIEWPORT_MARGIN,
                    _currentViewport.Width + 2 * VIEWPORT_MARGIN,
                    _currentViewport.Height + 2 * VIEWPORT_MARGIN
                );

                var newVisibleNodes = new HashSet<object>();

                foreach (var node in _allNodes)
                {
                    if (IsNodeInViewport(node, expandedViewport))
                    {
                        newVisibleNodes.Add(node);
                    }
                }

                // 更新可见节点集合
                var nodesToHide = _visibleNodes.Except(newVisibleNodes).ToList();
                var nodesToShow = newVisibleNodes.Except(_visibleNodes).ToList();

                foreach (var node in nodesToHide)
                {
                    _visibleNodes.Remove(node);
                }

                foreach (var node in nodesToShow)
                {
                    _visibleNodes.Add(node);
                }

                // 触发可见性变化事件
                if (nodesToHide.Count > 0 || nodesToShow.Count > 0)
                {
                    OnVisibilityChanged?.Invoke(newVisibleNodes, nodesToHide, nodesToShow);
                }
            }
            finally
            {
                _isUpdatingViewport = false;
            }
        }

        private bool IsNodeInViewport(object node, Rect viewport)
        {
            // 使用反射获取节点位置信息
            var nodeType = node.GetType();
            var locationProperty = nodeType.GetProperty("Location");

            if (locationProperty != null && locationProperty.PropertyType == typeof(Point))
            {
                var locationValue = locationProperty.GetValue(node);
                if (locationValue != null)
                {
                    var location = (Point)locationValue;

                    // 获取节点的边界矩形
                    var nodeRect = new Rect(
                        location.X,
                        location.Y,
                        180, // 默认节点宽度
                        90   // 默认节点高度
                    );

                    return viewport.IntersectsWith(nodeRect);
                }
            }

            // 如果无法获取位置信息，默认认为在视口内
            return true;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _viewportUpdateTimer?.Stop();
            Clear();
        }
    }
}
