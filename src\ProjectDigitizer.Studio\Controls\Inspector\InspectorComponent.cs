using System;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Inspector
{
    /// <summary>
    /// Inspector组件基类 - 类似Unity的Component概念
    /// </summary>
    public abstract class InspectorComponent : UserControl
    {
        public static readonly DependencyProperty CurrentNodeProperty = DependencyProperty.Register(
            nameof(CurrentNode), typeof(ModuleNodeViewModel), typeof(InspectorComponent),
            new PropertyMetadata(null, OnCurrentNodeChanged));

        public ModuleNodeViewModel? CurrentNode
        {
            get => (ModuleNodeViewModel?)GetValue(CurrentNodeProperty);
            set => SetValue(CurrentNodeProperty, value);
        }

        private static void OnCurrentNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is InspectorComponent component)
            {
                component.OnNodeChanged(e.OldValue as ModuleNodeViewModel, e.NewValue as ModuleNodeViewModel);
            }
        }

        /// <summary>
        /// 组件标题
        /// </summary>
        public abstract string ComponentTitle { get; }

        /// <summary>
        /// 组件描述
        /// </summary>
        public virtual string ComponentDescription => "";

        /// <summary>
        /// 是否默认展开
        /// </summary>
        public virtual bool IsExpandedByDefault => true;

        /// <summary>
        /// 是否可以移除
        /// </summary>
        public virtual bool CanBeRemoved => false;

        /// <summary>
        /// 组件优先级（用于排序）
        /// </summary>
        public virtual int Priority => 0;

        /// <summary>
        /// 节点变化时的处理
        /// </summary>
        protected virtual void OnNodeChanged(ModuleNodeViewModel? oldNode, ModuleNodeViewModel? newNode)
        {
            if (newNode != null)
            {
                InitializeFromNode(newNode);
            }
            else
            {
                ClearComponent();
            }
        }

        /// <summary>
        /// 从节点初始化组件
        /// </summary>
        protected abstract void InitializeFromNode(ModuleNodeViewModel node);

        /// <summary>
        /// 清空组件
        /// </summary>
        protected virtual void ClearComponent()
        {
            // 默认实现：清空内容
        }

        /// <summary>
        /// 验证组件数据
        /// </summary>
        public virtual bool ValidateComponent(out string errorMessage)
        {
            errorMessage = "";
            return true;
        }

        /// <summary>
        /// 应用组件数据到节点
        /// </summary>
        public virtual void ApplyToNode()
        {
            // 默认实现：子类重写
        }

        /// <summary>
        /// 重置组件到默认状态
        /// </summary>
        public virtual void ResetToDefault()
        {
            // 默认实现：子类重写
        }

        /// <summary>
        /// 检查组件是否适用于指定节点
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>如果适用返回true，否则返回false</returns>
        public virtual bool IsApplicableToNode(ModuleNodeViewModel node)
        {
            // 默认适用于所有节点
            return true;
        }
    }

    /// <summary>
    /// Inspector组件特性 - 用于标记组件的元数据
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class InspectorComponentAttribute : Attribute
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public int Priority { get; set; } = 0;
        public bool IsExpandedByDefault { get; set; } = true;
        public bool CanBeRemoved { get; set; } = false;
        public Type[] SupportedModuleTypes { get; set; } = Array.Empty<Type>();
    }
}
