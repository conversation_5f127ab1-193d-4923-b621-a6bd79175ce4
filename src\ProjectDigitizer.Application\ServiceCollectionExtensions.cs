using Microsoft.Extensions.DependencyInjection;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;

namespace ProjectDigitizer.Application;

/// <summary>
/// 应用层依赖注入扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加应用层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // 注册应用层核心服务
        RegisterApplicationCoreServices(services);

        // 注册业务逻辑服务
        RegisterBusinessServices(services);

        // 注册命令和查询处理器
        RegisterHandlers(services);

        // 注册错误处理和日志服务
        RegisterErrorHandlingServices(services);

        return services;
    }

    /// <summary>
    /// 注册应用层核心服务
    /// </summary>
    private static void RegisterApplicationCoreServices(IServiceCollection services)
    {
        // 项目文件服务 - Scoped，因为可能包含操作状态
        services.AddScoped<IProjectFileService, ProjectFileService>();
        services.AddScoped<ProjectFileService>();
    }

    /// <summary>
    /// 注册业务逻辑服务
    /// </summary>
    private static void RegisterBusinessServices(IServiceCollection services)
    {
        // 节点布局服务 - Singleton，因为被Singleton服务依赖
        services.AddSingleton<INodeLayoutService, NodeLayoutService>();

        // 连接器兼容性服务 - Singleton，因为兼容性规则是全局的
        services.AddSingleton<IConnectorCompatibilityService, ConnectorCompatibilityService>();

        // 项目文件适配器 - Scoped，因为可能包含转换状态
        services.AddScoped<IProjectFileAdapter, ProjectFileAdapter>();
    }

    /// <summary>
    /// 注册命令和查询处理器
    /// </summary>
    private static void RegisterHandlers(IServiceCollection services)
    {
        // 命令处理器 - Scoped，因为可能包含处理状态
        services.AddScoped(typeof(ICommandHandler<>), typeof(CommandHandler<>));

        // 查询处理器 - Scoped，因为可能包含查询状态
        services.AddScoped(typeof(IQueryHandler<,>), typeof(QueryHandler<,>));
    }

    /// <summary>
    /// 注册错误处理和日志服务
    /// </summary>
    private static void RegisterErrorHandlingServices(IServiceCollection services)
    {
        // 暂时注释掉，等待依赖问题解决
        // services.AddScoped<IExceptionHandlingService, ExceptionHandlingService>();
        // services.AddScoped<IErrorRecoveryManager, ErrorRecoveryManager>();

        // 结构化日志服务 - Singleton，因为被Singleton服务依赖
        services.AddSingleton<IStructuredLogger, StructuredLogger>();

        // 异步操作管理器 - Singleton，全局管理异步操作
        services.AddSingleton<IAsyncOperationManager, AsyncOperationManager>();
    }
}

/// <summary>
/// 默认命令处理器基类
/// </summary>
/// <typeparam name="T">命令类型</typeparam>
public class CommandHandler<T> : ICommandHandler<T> where T : class
{
    public virtual Task HandleAsync(T command, CancellationToken cancellationToken = default)
    {
        // 默认实现为空，具体处理器应继承此类并重写此方法
        return Task.CompletedTask;
    }
}

/// <summary>
/// 默认查询处理器基类
/// </summary>
/// <typeparam name="TQuery">查询类型</typeparam>
/// <typeparam name="TResult">结果类型</typeparam>
public class QueryHandler<TQuery, TResult> : IQueryHandler<TQuery, TResult> where TQuery : class
{
    public virtual Task<TResult> HandleAsync(TQuery query, CancellationToken cancellationToken = default)
    {
        // 默认实现返回默认值，具体处理器应继承此类并重写此方法
        return Task.FromResult(default(TResult)!);
    }
}
