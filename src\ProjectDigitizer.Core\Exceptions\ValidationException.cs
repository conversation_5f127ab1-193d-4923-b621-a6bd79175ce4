using System.Collections.Generic;

namespace ProjectDigitizer.Core.Exceptions;

/// <summary>
/// 验证异常
/// </summary>
public class ValidationException : ApplicationException
{
    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<ValidationError> ValidationErrors { get; }

    /// <summary>
    /// 初始化验证异常
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    public ValidationException(List<ValidationError> validationErrors)
        : base("数据验证失败", "VALIDATION_ERROR")
    {
        ValidationErrors = validationErrors ?? new List<ValidationError>();
    }

    /// <summary>
    /// 初始化验证异常
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="validationErrors">验证错误列表</param>
    public ValidationException(string message, List<ValidationError> validationErrors)
        : base(message, "VALIDATION_ERROR")
    {
        ValidationErrors = validationErrors ?? new List<ValidationError>();
    }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 尝试的值
    /// </summary>
    public object? AttemptedValue { get; set; }
}
