namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 日志记录服务接口
/// 提供结构化日志记录和日志管理功能
/// </summary>
public interface ILoggingService : IApplicationService
{
    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogError(Exception exception, string message, params object[] args);

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="properties">结构化属性</param>
    void LogStructured(LogLevel level, string message, Dictionary<string, object> properties);

    /// <summary>
    /// 配置日志上下文
    /// </summary>
    /// <param name="contextProperties">上下文属性</param>
    /// <returns>可释放的上下文</returns>
    IDisposable BeginScope(Dictionary<string, object> contextProperties);
}

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    Trace,
    Debug,
    Information,
    Warning,
    Error,
    Critical
}
