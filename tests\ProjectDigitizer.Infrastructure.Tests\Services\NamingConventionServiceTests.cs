using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Services;

using Xunit;
using Xunit.Abstractions;

namespace ProjectDigitizer.Infrastructure.Tests.Services;

/// <summary>
/// 命名约定服务测试
/// </summary>
public class NamingConventionServiceTests
{
    private readonly INamingConventionService _namingService;
    private readonly ITestOutputHelper _output;

    public NamingConventionServiceTests(ITestOutputHelper output)
    {
        _output = output;
        var mockLogger = new Mock<ILogger<NamingConventionService>>();
        _namingService = new NamingConventionService(mockLogger.Object);
    }

    [Theory]
    [InlineData("UserService", ClassType.Service, true)]
    [InlineData("DocumentEntity", ClassType.Entity, true)]
    [InlineData("ValidationResult", ClassType.ValueObject, true)]
    [InlineData("UserController", ClassType.Controller, true)]
    [InlineData("MainViewModel", ClassType.ViewModel, true)]
    [InlineData("UserDto", ClassType.Dto, true)]
    [InlineData("AppConfiguration", ClassType.Configuration, true)]
    [InlineData("CustomException", ClassType.Exception, true)]
    [InlineData("StringExtensions", ClassType.Extensions, true)]
    [InlineData("ApiConstants", ClassType.Constants, true)]
    [InlineData("UserStatus", ClassType.Enum, true)]
    [InlineData("BaseEntity", ClassType.Abstract, true)]
    [InlineData("UserServiceTests", ClassType.Test, true)]
    public void ValidateClassName_WithValidNames_ShouldReturnValid(string className, ClassType classType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateClassName(className, classType);

        // Assert
        _output.WriteLine($"类名: {className}, 类型: {classType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
            foreach (var warning in result.Warnings)
            {
                _output.WriteLine($"  警告: {warning}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("userService", ClassType.Service, false)] // 应该是PascalCase
    [InlineData("User", ClassType.Service, false)] // Service类应该以Service结尾
    [InlineData("UserService", ClassType.Interface, false)] // 接口应该以I开头
    [InlineData("IUser", ClassType.Service, false)] // Service类不应该以I开头
    [InlineData("", ClassType.Service, false)] // 空字符串
    [InlineData("user_service", ClassType.Service, false)] // 不应该包含下划线
    [InlineData("123UserService", ClassType.Service, false)] // 不应该以数字开头
    public void ValidateClassName_WithInvalidNames_ShouldReturnInvalid(string className, ClassType classType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateClassName(className, classType);

        // Assert
        _output.WriteLine($"无效类名: {className}, 类型: {classType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("GetUser", MethodType.Public, true)]
    [InlineData("SaveDocument", MethodType.Public, true)]
    [InlineData("ValidateInput", MethodType.Protected, true)]
    [InlineData("processData", MethodType.Private, true)]
    [InlineData("ProcessDataAsync", MethodType.Async, true)]
    [InlineData("OnUserChanged", MethodType.EventHandler, true)]
    [InlineData("UserChangedHandler", MethodType.EventHandler, true)]
    public void ValidateMethodName_WithValidNames_ShouldReturnValid(string methodName, MethodType methodType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateMethodName(methodName, methodType);

        // Assert
        _output.WriteLine($"方法名: {methodName}, 类型: {methodType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("getUser", MethodType.Public, false)] // 公共方法应该是PascalCase
    [InlineData("ProcessData", MethodType.Async, false)] // 异步方法应该以Async结尾
    [InlineData("", MethodType.Public, false)] // 空字符串
    [InlineData("get_user", MethodType.Public, false)] // 不应该包含下划线
    public void ValidateMethodName_WithInvalidNames_ShouldReturnInvalid(string methodName, MethodType methodType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateMethodName(methodName, methodType);

        // Assert
        _output.WriteLine($"无效方法名: {methodName}, 类型: {methodType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("Name", PropertyType.Public, true)]
    [InlineData("UserId", PropertyType.Public, true)]
    [InlineData("IsActive", PropertyType.ReadOnly, true)]
    [InlineData("_userName", PropertyType.PrivateField, true)]
    [InlineData("MAX_LENGTH", PropertyType.Constant, true)]
    [InlineData("UserChanged", PropertyType.Event, true)]
    [InlineData("DataChangedEvent", PropertyType.Event, true)]
    public void ValidatePropertyName_WithValidNames_ShouldReturnValid(string propertyName, PropertyType propertyType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidatePropertyName(propertyName, propertyType);

        // Assert
        _output.WriteLine($"属性名: {propertyName}, 类型: {propertyType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("name", PropertyType.Public, false)] // 公共属性应该是PascalCase
    [InlineData("userName", PropertyType.PrivateField, false)] // 私有字段应该以_开头
    [InlineData("maxLength", PropertyType.Constant, false)] // 常量应该是UPPER_CASE
    [InlineData("", PropertyType.Public, false)] // 空字符串
    public void ValidatePropertyName_WithInvalidNames_ShouldReturnInvalid(string propertyName, PropertyType propertyType, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidatePropertyName(propertyName, propertyType);

        // Assert
        _output.WriteLine($"无效属性名: {propertyName}, 类型: {propertyType}, 结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("ProjectDigitizer.Core", ArchitectureLayer.Core, true)]
    [InlineData("ProjectDigitizer.Core.Entities", ArchitectureLayer.Core, true)]
    [InlineData("ProjectDigitizer.Application.Services", ArchitectureLayer.Application, true)]
    [InlineData("ProjectDigitizer.Infrastructure.Data", ArchitectureLayer.Infrastructure, true)]
    [InlineData("ProjectDigitizer.Studio.ViewModels", ArchitectureLayer.Presentation, true)]
    [InlineData("ProjectDigitizer.Core.Tests", ArchitectureLayer.Tests, true)]
    public void ValidateNamespace_WithValidNamespaces_ShouldReturnValid(string namespaceName, ArchitectureLayer expectedLayer, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateNamespace(namespaceName, expectedLayer);

        // Assert
        _output.WriteLine($"命名空间: {namespaceName}, 期望层: {expectedLayer}, 结果: {(result.IsValid ? "有效" : "无效")}");

        if (!result.IsValid)
        {
            foreach (var error in result.Errors)
            {
                _output.WriteLine($"  错误: {error}");
            }
        }

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("MyProject.Core", ArchitectureLayer.Core, false)] // 应该以ProjectDigitizer开头
    [InlineData("ProjectDigitizer.Application", ArchitectureLayer.Core, false)] // 层不匹配
    [InlineData("", ArchitectureLayer.Core, false)] // 空字符串
    [InlineData("Core", ArchitectureLayer.Core, false)] // 格式不正确
    public void ValidateNamespace_WithInvalidNamespaces_ShouldReturnInvalid(string namespaceName, ArchitectureLayer expectedLayer, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateNamespace(namespaceName, expectedLayer);

        // Assert
        _output.WriteLine($"无效命名空间: {namespaceName}, 期望层: {expectedLayer}, 结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("IUserService", true)]
    [InlineData("IDocumentRepository", true)]
    [InlineData("IValidationService", true)]
    [InlineData("IEntity", true)]
    public void ValidateInterfaceName_WithValidNames_ShouldReturnValid(string interfaceName, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateInterfaceName(interfaceName);

        // Assert
        _output.WriteLine($"接口名: {interfaceName}, 结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("UserService", false)] // 应该以I开头
    [InlineData("iUserService", false)] // I应该大写
    [InlineData("I", false)] // 只有I
    [InlineData("", false)] // 空字符串
    public void ValidateInterfaceName_WithInvalidNames_ShouldReturnInvalid(string interfaceName, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateInterfaceName(interfaceName);

        // Assert
        _output.WriteLine($"无效接口名: {interfaceName}, 结果: {(result.IsValid ? "有效" : "无效")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("UserService.cs", "UserService", true)]
    [InlineData("IUserRepository.cs", "IUserRepository", true)]
    [InlineData("DocumentEntity.cs", "DocumentEntity", true)]
    public void ValidateFileNameMatch_WithMatchingNames_ShouldReturnValid(string fileName, string className, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateFileNameMatch(fileName, className);

        // Assert
        _output.WriteLine($"文件名: {fileName}, 类名: {className}, 结果: {(result.IsValid ? "匹配" : "不匹配")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("UserService.cs", "DocumentService", false)]
    [InlineData("", "UserService", false)]
    [InlineData("UserService.cs", "", false)]
    public void ValidateFileNameMatch_WithNonMatchingNames_ShouldReturnInvalid(string fileName, string className, bool expectedValid)
    {
        // Act
        var result = _namingService.ValidateFileNameMatch(fileName, className);

        // Assert
        _output.WriteLine($"不匹配文件名: {fileName}, 类名: {className}, 结果: {(result.IsValid ? "匹配" : "不匹配")}");

        Assert.Equal(expectedValid, result.IsValid);
    }

    [Theory]
    [InlineData("User", ClassType.Service, "UserService")]
    [InlineData("Document", ClassType.Controller, "DocumentController")]
    [InlineData("Main", ClassType.ViewModel, "MainViewModel")]
    [InlineData("User", ClassType.Dto, "UserDto")]
    [InlineData("Custom", ClassType.Exception, "CustomException")]
    [InlineData("String", ClassType.Extensions, "StringExtensions")]
    [InlineData("UserService", ClassType.Test, "UserServiceTests")]
    [InlineData("UserService", ClassType.Interface, "IUserService")]
    public void GetRecommendedClassName_ShouldReturnCorrectName(string currentName, ClassType classType, string expectedName)
    {
        // Act
        var result = _namingService.GetRecommendedClassName(currentName, classType);

        // Assert
        _output.WriteLine($"当前名称: {currentName}, 类型: {classType}, 推荐名称: {result}");

        Assert.Equal(expectedName, result);
    }

    [Theory]
    [InlineData("/path/to/file", ArchitectureLayer.Core, "ProjectDigitizer.Core")]
    [InlineData("/path/to/file", ArchitectureLayer.Application, "ProjectDigitizer.Application")]
    [InlineData("/path/to/file", ArchitectureLayer.Infrastructure, "ProjectDigitizer.Infrastructure")]
    [InlineData("/path/to/file", ArchitectureLayer.Presentation, "ProjectDigitizer.Studio")]
    [InlineData("/path/to/file", ArchitectureLayer.Tests, "ProjectDigitizer.Tests")]
    public void GetRecommendedNamespace_ShouldReturnCorrectNamespace(string filePath, ArchitectureLayer layer, string expectedNamespace)
    {
        // Act
        var result = _namingService.GetRecommendedNamespace(filePath, layer);

        // Assert
        _output.WriteLine($"文件路径: {filePath}, 架构层: {layer}, 推荐命名空间: {result}");

        Assert.Equal(expectedNamespace, result);
    }
}
