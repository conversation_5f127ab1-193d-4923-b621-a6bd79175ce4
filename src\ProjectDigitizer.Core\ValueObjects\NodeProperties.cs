using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Core.ValueObjects
{
    /// <summary>
    /// 输入节点属性
    /// </summary>
    public class InputNodeProperties : BaseNodeProperties
    {
        private string _dataSource = string.Empty;
        private string _connectionString = string.Empty;
        private string _filePath = string.Empty;
        private string _fileFormat = "Auto";
        private bool _hasHeader = true;
        private string _encoding = "UTF-8";

        /// <summary>
        /// 数据源类型
        /// </summary>
        public string DataSource
        {
            get => _dataSource;
            set => SetProperty(ref _dataSource, value);
        }

        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString
        {
            get => _connectionString;
            set => SetProperty(ref _connectionString, value);
        }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        /// <summary>
        /// 文件格式
        /// </summary>
        public string FileFormat
        {
            get => _fileFormat;
            set => SetProperty(ref _fileFormat, value);
        }

        /// <summary>
        /// 是否包含标题行
        /// </summary>
        public bool HasHeader
        {
            get => _hasHeader;
            set => SetProperty(ref _hasHeader, value);
        }

        /// <summary>
        /// 编码格式
        /// </summary>
        public string Encoding
        {
            get => _encoding;
            set => SetProperty(ref _encoding, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(DataSource))
            {
                result.AddError("数据源类型不能为空");
            }

            if (DataSource == "File" && string.IsNullOrEmpty(FilePath))
            {
                result.AddError("文件路径不能为空");
            }

            if (DataSource == "Database" && string.IsNullOrEmpty(ConnectionString))
            {
                result.AddError("数据库连接字符串不能为空");
            }

            return result;
        }
    }

    /// <summary>
    /// 转换节点属性
    /// </summary>
    public class TransformNodeProperties : BaseNodeProperties
    {
        private string _transformType = string.Empty;
        private string _expression = string.Empty;
        private Dictionary<string, string> _fieldMappings = new();
        private bool _enableValidation = true;
        private string _validationRules = string.Empty;

        /// <summary>
        /// 转换类型
        /// </summary>
        public string TransformType
        {
            get => _transformType;
            set => SetProperty(ref _transformType, value);
        }

        /// <summary>
        /// 转换表达式
        /// </summary>
        public string Expression
        {
            get => _expression;
            set => SetProperty(ref _expression, value);
        }

        /// <summary>
        /// 字段映射
        /// </summary>
        public Dictionary<string, string> FieldMappings
        {
            get => _fieldMappings;
            set => SetProperty(ref _fieldMappings, value);
        }

        /// <summary>
        /// 启用验证
        /// </summary>
        public bool EnableValidation
        {
            get => _enableValidation;
            set => SetProperty(ref _enableValidation, value);
        }

        /// <summary>
        /// 验证规则
        /// </summary>
        public string ValidationRules
        {
            get => _validationRules;
            set => SetProperty(ref _validationRules, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(TransformType))
            {
                result.AddError("转换类型不能为空");
            }

            if (TransformType == "Expression" && string.IsNullOrEmpty(Expression))
            {
                result.AddError("转换表达式不能为空");
            }

            return result;
        }
    }

    /// <summary>
    /// 输出节点属性
    /// </summary>
    public class OutputNodeProperties : BaseNodeProperties
    {
        private string _outputType = string.Empty;
        private string _outputPath = string.Empty;
        private string _outputFormat = "Auto";
        private bool _overwriteExisting = false;
        private string _templatePath = string.Empty;

        /// <summary>
        /// 输出类型
        /// </summary>
        public string OutputType
        {
            get => _outputType;
            set => SetProperty(ref _outputType, value);
        }

        /// <summary>
        /// 输出路径
        /// </summary>
        public string OutputPath
        {
            get => _outputPath;
            set => SetProperty(ref _outputPath, value);
        }

        /// <summary>
        /// 输出格式
        /// </summary>
        public string OutputFormat
        {
            get => _outputFormat;
            set => SetProperty(ref _outputFormat, value);
        }

        /// <summary>
        /// 覆盖已存在文件
        /// </summary>
        public bool OverwriteExisting
        {
            get => _overwriteExisting;
            set => SetProperty(ref _overwriteExisting, value);
        }

        /// <summary>
        /// 模板路径
        /// </summary>
        public string TemplatePath
        {
            get => _templatePath;
            set => SetProperty(ref _templatePath, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(OutputType))
            {
                result.AddError("输出类型不能为空");
            }

            if (string.IsNullOrEmpty(OutputPath))
            {
                result.AddError("输出路径不能为空");
            }

            return result;
        }
    }

    /// <summary>
    /// 控制节点属性
    /// </summary>
    public class ControlNodeProperties : BaseNodeProperties
    {
        private string _controlType = string.Empty;
        private string _condition = string.Empty;
        private int _maxIterations = 100;
        private int _timeoutSeconds = 30;
        private bool _continueOnError = false;

        /// <summary>
        /// 控制类型
        /// </summary>
        public string ControlType
        {
            get => _controlType;
            set => SetProperty(ref _controlType, value);
        }

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string Condition
        {
            get => _condition;
            set => SetProperty(ref _condition, value);
        }

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations
        {
            get => _maxIterations;
            set => SetProperty(ref _maxIterations, value);
        }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds
        {
            get => _timeoutSeconds;
            set => SetProperty(ref _timeoutSeconds, value);
        }

        /// <summary>
        /// 错误时继续执行
        /// </summary>
        public bool ContinueOnError
        {
            get => _continueOnError;
            set => SetProperty(ref _continueOnError, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(ControlType))
            {
                result.AddError("控制类型不能为空");
            }

            if ((ControlType == "Condition" || ControlType == "Loop") && string.IsNullOrEmpty(Condition))
            {
                result.AddError("条件表达式不能为空");
            }

            if (MaxIterations <= 0)
            {
                result.AddError("最大迭代次数必须大于0");
            }

            if (TimeoutSeconds <= 0)
            {
                result.AddError("超时时间必须大于0");
            }

            return result;
        }
    }

    /// <summary>
    /// 基础节点属性类
    /// </summary>
    public abstract class BaseNodeProperties : INodeProperties
    {
        private readonly Dictionary<string, object?> _customProperties = new();

        public virtual object? GetValue(string propertyName)
        {
            // 首先尝试从强类型属性获取
            var property = GetType().GetProperty(propertyName);
            if (property != null)
            {
                return property.GetValue(this);
            }

            // 然后从自定义属性字典获取
            return _customProperties.TryGetValue(propertyName, out var value) ? value : null;
        }

        public virtual T? GetValue<T>(string propertyName)
        {
            var value = GetValue(propertyName);
            if (value is T typedValue)
                return typedValue;
            return default(T);
        }

        public virtual void SetValue(string propertyName, object? value)
        {
            // 首先尝试设置强类型属性
            var property = GetType().GetProperty(propertyName);
            if (property != null && property.CanWrite)
            {
                property.SetValue(this, value);
                return;
            }

            // 然后设置到自定义属性字典
            if (!_customProperties.TryGetValue(propertyName, out var currentValue) ||
                !Equals(currentValue, value))
            {
                _customProperties[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        public virtual Dictionary<string, object?> GetAllValues()
        {
            var result = new Dictionary<string, object?>();

            // 添加强类型属性
            foreach (var property in GetType().GetProperties())
            {
                if (property.CanRead)
                {
                    result[property.Name] = property.GetValue(this);
                }
            }

            // 添加自定义属性
            foreach (var kvp in _customProperties)
            {
                result[kvp.Key] = kvp.Value;
            }

            return result;
        }

        public abstract ValidationResult ValidateProperties();

        public virtual void ResetToDefaults()
        {
            _customProperties.Clear();
            // 子类可以重写此方法来重置强类型属性
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 手动输入数据节点属性
    /// </summary>
    public class ManualDataInputProperties : BaseNodeProperties
    {
        private string _dataType = "Text";
        private string _inputValue = string.Empty;
        private bool _allowMultipleValues = false;
        private string _valuesSeparator = ",";
        private string _description = string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 输入值
        /// </summary>
        public string InputValue
        {
            get => _inputValue;
            set => SetProperty(ref _inputValue, value);
        }

        /// <summary>
        /// 允许多个值
        /// </summary>
        public bool AllowMultipleValues
        {
            get => _allowMultipleValues;
            set => SetProperty(ref _allowMultipleValues, value);
        }

        /// <summary>
        /// 值分隔符
        /// </summary>
        public string ValuesSeparator
        {
            get => _valuesSeparator;
            set => SetProperty(ref _valuesSeparator, value);
        }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(DataType))
            {
                result.AddError("数据类型不能为空");
            }

            if (string.IsNullOrEmpty(InputValue))
            {
                result.AddError("输入值不能为空");
            }

            if (AllowMultipleValues && string.IsNullOrEmpty(ValuesSeparator))
            {
                result.AddError("多值模式下分隔符不能为空");
            }

            return result;
        }
    }

    /// <summary>
    /// 数组展开节点属性
    /// </summary>
    public class ArrayExpansionProperties : BaseNodeProperties
    {
        private string _expansionMode = "Flatten";
        private string _arrayPath = string.Empty;
        private bool _preserveStructure = false;
        private string _outputFormat = "Individual";
        private int _maxDepth = 10;

        /// <summary>
        /// 展开模式
        /// </summary>
        public string ExpansionMode
        {
            get => _expansionMode;
            set => SetProperty(ref _expansionMode, value);
        }

        /// <summary>
        /// 数组路径
        /// </summary>
        public string ArrayPath
        {
            get => _arrayPath;
            set => SetProperty(ref _arrayPath, value);
        }

        /// <summary>
        /// 保持结构
        /// </summary>
        public bool PreserveStructure
        {
            get => _preserveStructure;
            set => SetProperty(ref _preserveStructure, value);
        }

        /// <summary>
        /// 输出格式
        /// </summary>
        public string OutputFormat
        {
            get => _outputFormat;
            set => SetProperty(ref _outputFormat, value);
        }

        /// <summary>
        /// 最大深度
        /// </summary>
        public int MaxDepth
        {
            get => _maxDepth;
            set => SetProperty(ref _maxDepth, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(ExpansionMode))
            {
                result.AddError("展开模式不能为空");
            }

            if (MaxDepth <= 0)
            {
                result.AddError("最大深度必须大于0");
            }

            return result;
        }
    }

    /// <summary>
    /// 智能体节点属性
    /// </summary>
    public class AIAgentProperties : BaseNodeProperties
    {
        private string _agentType = "ChatBot";
        private string _model = "GPT-3.5";
        private string _systemPrompt = string.Empty;
        private string _apiKey = string.Empty;
        private int _maxTokens = 1000;
        private double _temperature = 0.7;
        private int _timeoutSeconds = 30;

        /// <summary>
        /// 智能体类型
        /// </summary>
        public string AgentType
        {
            get => _agentType;
            set => SetProperty(ref _agentType, value);
        }

        /// <summary>
        /// 模型名称
        /// </summary>
        public string Model
        {
            get => _model;
            set => SetProperty(ref _model, value);
        }

        /// <summary>
        /// 系统提示词
        /// </summary>
        public string SystemPrompt
        {
            get => _systemPrompt;
            set => SetProperty(ref _systemPrompt, value);
        }

        /// <summary>
        /// API密钥
        /// </summary>
        public string ApiKey
        {
            get => _apiKey;
            set => SetProperty(ref _apiKey, value);
        }

        /// <summary>
        /// 最大令牌数
        /// </summary>
        public int MaxTokens
        {
            get => _maxTokens;
            set => SetProperty(ref _maxTokens, value);
        }

        /// <summary>
        /// 温度参数
        /// </summary>
        public double Temperature
        {
            get => _temperature;
            set => SetProperty(ref _temperature, value);
        }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds
        {
            get => _timeoutSeconds;
            set => SetProperty(ref _timeoutSeconds, value);
        }

        public override ValidationResult ValidateProperties()
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(AgentType))
            {
                result.AddError("智能体类型不能为空");
            }

            if (string.IsNullOrEmpty(Model))
            {
                result.AddError("模型名称不能为空");
            }

            if (MaxTokens <= 0)
            {
                result.AddError("最大令牌数必须大于0");
            }

            if (Temperature < 0 || Temperature > 2)
            {
                result.AddError("温度参数必须在0-2之间");
            }

            if (TimeoutSeconds <= 0)
            {
                result.AddError("超时时间必须大于0");
            }

            return result;
        }
    }
}
