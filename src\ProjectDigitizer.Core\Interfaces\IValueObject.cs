namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 值对象接口
/// </summary>
public interface IValueObject : IEquatable<IValueObject>
{
    /// <summary>
    /// 获取用于相等性比较的组件
    /// </summary>
    /// <returns>用于相等性比较的组件集合</returns>
    IEnumerable<object?> GetEqualityComponents();
}

/// <summary>
/// 值对象基类
/// </summary>
public abstract class ValueObject : IValueObject
{
    /// <summary>
    /// 获取用于相等性比较的组件
    /// </summary>
    /// <returns>用于相等性比较的组件集合</returns>
    public abstract IEnumerable<object?> GetEqualityComponents();

    /// <summary>
    /// 判断两个值对象是否相等
    /// </summary>
    /// <param name="other">另一个值对象</param>
    /// <returns>如果相等返回true，否则返回false</returns>
    public bool Equals(IValueObject? other)
    {
        if (other is null || other.GetType() != GetType())
            return false;

        return GetEqualityComponents().SequenceEqual(other.GetEqualityComponents());
    }

    /// <summary>
    /// 判断两个对象是否相等
    /// </summary>
    /// <param name="obj">另一个对象</param>
    /// <returns>如果相等返回true，否则返回false</returns>
    public override bool Equals(object? obj)
    {
        return Equals(obj as IValueObject);
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return GetEqualityComponents()
            .Select(x => x?.GetHashCode() ?? 0)
            .Aggregate((x, y) => x ^ y);
    }

    /// <summary>
    /// 相等运算符重载
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>如果相等返回true，否则返回false</returns>
    public static bool operator ==(ValueObject? left, ValueObject? right)
    {
        if (left is null && right is null)
            return true;

        if (left is null || right is null)
            return false;

        return left.Equals(right);
    }

    /// <summary>
    /// 不等运算符重载
    /// </summary>
    /// <param name="left">左操作数</param>
    /// <param name="right">右操作数</param>
    /// <returns>如果不等返回true，否则返回false</returns>
    public static bool operator !=(ValueObject? left, ValueObject? right)
    {
        return !(left == right);
    }
}
