using System;
using System.Windows;

using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Inspector.Components
{
    /// <summary>
    /// 基础属性组件 - 适用于所有节点类型的通用属性
    /// </summary>
    [InspectorComponent(
        Title = "基础属性",
        Description = "节点的基本信息和通用设置",
        Priority = -100, // 最高优先级，总是显示在最上面
        IsExpandedByDefault = true,
        CanBeRemoved = false)] // 不能移除，所有节点都需要
    public partial class BasicPropertiesComponent : InspectorComponent
    {
        public override string ComponentTitle => "基础属性";
        public override string ComponentDescription => "节点的基本信息和通用设置";

        public BasicPropertiesComponent()
        {
            InitializeComponent();
        }

        public override bool IsApplicableToNode(ModuleNodeViewModel node)
        {
            // 基础属性组件适用于所有节点
            return node?.Module != null;
        }

        protected override void InitializeFromNode(ModuleNodeViewModel node)
        {
            // 加载节点的基本属性
            NodeNameTextBox.Text = node.Title ?? "";
            NodeDescriptionTextBox.Text = ""; // ModuleNodeViewModel没有Description属性
            NodeTypeTextBox.Text = node.Module?.Type.ToString() ?? "未知";

            // 加载高级设置
            LoadAdvancedSettings(node);
        }

        protected override void ClearComponent()
        {
            NodeNameTextBox.Text = "";
            NodeDescriptionTextBox.Text = "";
            NodeTypeTextBox.Text = "";
            EnabledCheckBox.IsChecked = true;
            DebugModeCheckBox.IsChecked = false;
            TimeoutSlider.Value = 30;
        }

        public override bool ValidateComponent(out string errorMessage)
        {
            errorMessage = "";

            // 验证节点名称
            if (string.IsNullOrWhiteSpace(NodeNameTextBox.Text))
            {
                errorMessage = "节点名称不能为空";
                return false;
            }

            // 验证超时时间
            if (TimeoutSlider.Value <= 0)
            {
                errorMessage = "超时时间必须大于0";
                return false;
            }

            return true;
        }

        public override void ApplyToNode()
        {
            if (CurrentNode?.Module == null) return;

            try
            {
                // 应用基本属性
                CurrentNode.Title = NodeNameTextBox.Text;
                // CurrentNode.Description = NodeDescriptionTextBox.Text; // ModuleNodeViewModel没有Description属性

                // 应用高级设置到模块参数
                CurrentNode.Module.Parameters["enabled"] = EnabledCheckBox.IsChecked?.ToString() ?? "true";
                CurrentNode.Module.Parameters["debugMode"] = DebugModeCheckBox.IsChecked?.ToString() ?? "false";
                CurrentNode.Module.Parameters["timeout"] = TimeoutSlider.Value.ToString();

                System.Diagnostics.Debug.WriteLine($"[BasicPropertiesComponent] Applied properties to node: {CurrentNode.Title}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BasicPropertiesComponent] Error applying to node: {ex.Message}");
            }
        }

        public override void ResetToDefault()
        {
            if (CurrentNode != null)
            {
                NodeNameTextBox.Text = CurrentNode.Title ?? "";
                NodeDescriptionTextBox.Text = "";
            }
            EnabledCheckBox.IsChecked = true;
            DebugModeCheckBox.IsChecked = false;
            TimeoutSlider.Value = 30;
        }

        private void LoadAdvancedSettings(ModuleNodeViewModel node)
        {
            if (node?.Module?.Parameters == null) return;

            try
            {
                // 加载启用状态
                if (node.Module.Parameters.TryGetValue("enabled", out var enabled))
                    EnabledCheckBox.IsChecked = bool.Parse(enabled.ToString());

                // 加载调试模式
                if (node.Module.Parameters.TryGetValue("debugMode", out var debugMode))
                    DebugModeCheckBox.IsChecked = bool.Parse(debugMode.ToString());

                // 加载超时时间
                if (node.Module.Parameters.TryGetValue("timeout", out var timeout))
                    TimeoutSlider.Value = double.Parse(timeout.ToString());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BasicPropertiesComponent] Error loading advanced settings: {ex.Message}");
            }
        }
    }
}
