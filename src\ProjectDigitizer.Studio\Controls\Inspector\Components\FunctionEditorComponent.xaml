<local:InspectorComponent x:Class="ProjectDigitizer.Studio.Controls.Inspector.Components.FunctionEditorComponent"
                          xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                          xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                          xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                          xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                          xmlns:local="clr-namespace:ProjectDigitizer.Studio.Controls.Inspector"
                          mc:Ignorable="d"
                          d:DesignHeight="600"
                          d:DesignWidth="350">

    <!-- 函数编辑器容器 -->
    <Border Background="{DynamicResource Brush.SurfaceVariant}"
            CornerRadius="4"
            Padding="0">
        <StackPanel x:Name="FunctionPanelContainer"/>
    </Border>

</local:InspectorComponent>



