using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

using MahApps.Metro.IconPacks;

using Plugins.DataCalculation.Controls.Functions;
using Plugins.DataCalculation.Models;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Controls.Inspector;
using ProjectDigitizer.Studio.ViewModels;

using InfoBarSeverity = Wpf.Ui.Controls.InfoBarSeverity;
namespace Plugins.DataCalculation
{
    /// <summary>
    /// 数据计算组件 - 专用于数据计算节点的 Inspector 组件（迁移自 Studio）。
    /// </summary>
    [InspectorComponent(
        Title = "数据计算",
        Description = "管理数据计算的函数与变量",
        Priority = 0,
        IsExpandedByDefault = true,
        CanBeRemoved = false)]
    public partial class DataCalculationComponent : InspectorComponent
    {
        private ModuleNodeViewModel? _currentNode;
        private bool _isLoadingFromNode;
        private readonly DispatcherTimer _previewTimer = new() { Interval = TimeSpan.FromMilliseconds(400) };
        private FunctionDefinition? _pendingPreviewFunction;
        private readonly DispatcherTimer _infoTimer = new() { Interval = TimeSpan.FromMilliseconds(2500) };

        /// <summary>组件标题。</summary>
        public override string ComponentTitle => "数据计算";
        /// <summary>组件描述。</summary>
        public override string ComponentDescription => "管理数据计算的函数与变量";

        /// <summary>函数列表（名称 + 表达式）。</summary>
        public ObservableCollection<FunctionDefinition> Functions { get; } = [];
        /// <summary>变量列表（变量名 + 上游绑定）。</summary>
        public ObservableCollection<VariableDefinition> Variables { get; } = [];

        /// <summary>
        /// 初始化组件并绑定列表与事件。
        /// </summary>
        public DataCalculationComponent()
        {
            InitializeComponent();
            FunctionsItemsControl.ItemsSource = Functions;
            VariablesItemsControl.ItemsSource = Variables;
            UpdateEmptyState();

            Functions.CollectionChanged += Functions_CollectionChanged;
            Variables.CollectionChanged += Variables_CollectionChanged;

            FunctionsItemsControl.AddHandler(Button.ClickEvent, new RoutedEventHandler(OnFunctionsItemButtonClick), true);

            _previewTimer.Tick += (s, e) =>
            {
                _previewTimer.Stop();
                if (_pendingPreviewFunction != null)
                {
                    try { ExecuteFunction(_pendingPreviewFunction); }
                    catch { /* ignore preview errors */ }
                }
            };

            _infoTimer.Tick += (s, e) =>
            {
                _infoTimer.Stop();
                TryCloseInfoBar();
            };
        }

        private void Functions_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (object? item in e.NewItems)
                {
                    if (item is INotifyPropertyChanged inpc)
                    {
                        inpc.PropertyChanged += Function_PropertyChanged;
                    }
                }
            }
            if (e.OldItems != null)
            {
                foreach (object? item in e.OldItems)
                {
                    if (item is INotifyPropertyChanged inpc)
                    {
                        inpc.PropertyChanged -= Function_PropertyChanged;
                    }
                }
            }
            if (!_isLoadingFromNode)
            {
                ApplyToNode();
            }
        }

        private void Variables_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (object? item in e.NewItems)
                {
                    if (item is VariableDefinition v)
                    {
                        v.PropertyChanged += Variable_PropertyChanged;
                    }
                }
            }
            if (e.OldItems != null)
            {
                foreach (object? item in e.OldItems)
                {
                    if (item is VariableDefinition v)
                    {
                        v.PropertyChanged -= Variable_PropertyChanged;
                    }
                }
            }
            if (!_isLoadingFromNode)
            {
                ApplyToNode();
            }
        }

        private void Function_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (_isLoadingFromNode)
            {
                return;
            }

            if (e.PropertyName == nameof(FunctionDefinition.Name) || e.PropertyName == nameof(FunctionDefinition.Expression))
            {
                ApplyToNode();
                if (e.PropertyName == nameof(FunctionDefinition.Expression) && sender is FunctionDefinition f)
                {
                    _pendingPreviewFunction = f;
                    _previewTimer.Stop();
                    _previewTimer.Start();
                }
            }
        }

        private void Variable_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (_isLoadingFromNode)
            {
                return;
            }

            if (e.PropertyName == nameof(VariableDefinition.VariableName) ||
                e.PropertyName == nameof(VariableDefinition.SelectedSourceNode) ||
                e.PropertyName == nameof(VariableDefinition.SelectedProperty))
            {
                ApplyToNode();
            }
        }

        /// <summary>
        /// 当 Inspector 当前节点变化时触发。
        /// </summary>
        protected override void OnNodeChanged(ModuleNodeViewModel? oldNode, ModuleNodeViewModel? newNode)
        {
            _currentNode = newNode;
            base.OnNodeChanged(oldNode, newNode);
        }

        /// <summary>
        /// 将当前节点的数据加载到组件 UI。
        /// </summary>
        protected override void InitializeFromNode(ModuleNodeViewModel node)
        {
            _currentNode = node;
            LoadComponentFromNode(node);
        }

        private void LoadComponentFromNode(ModuleNodeViewModel node)
        {
            _isLoadingFromNode = true;
            try
            {
                Dictionary<string, object> parameters = node.Module?.Parameters ?? [];
                DataCalculationConfig.TryLoadFromParameters(parameters, out DataCalculationConfig? cfg);

                // options
                CalculationModeComboBox.SelectedIndex = (int)cfg.CalculationMode;
                PrecisionSlider.Value = cfg.Precision;
                ErrorHandlingComboBox.SelectedIndex = (int)cfg.ErrorHandling;
                OutputFormatComboBox.SelectedIndex = (int)cfg.OutputFormat;
                CacheResultsCheckBox.IsChecked = cfg.CacheResults;

                // collections
                Functions.Clear();
                foreach (FunctionDef f in cfg.Functions)
                {
                    FunctionDefinition fd = new() { Name = f.Name, Expression = f.Expression };
                    Functions.Add(fd);
                }

                Variables.Clear();
                foreach (VariableMapping v in cfg.Variables)
                {
                    VariableDefinition vd = new()
                    {
                        VariableName = v.Name,
                        SelectedSourceNode = string.IsNullOrEmpty(v.SourceNodeId) ? null : new SourceNodeInfo { NodeId = v.SourceNodeId, NodeTitle = v.SourceNodeId, NodeType = string.Empty, IsAvailable = true },
                        SelectedProperty = string.IsNullOrEmpty(v.PropertyName) ? null : new NodePropertyInfo { PropertyName = v.PropertyName, PropertyType = string.Empty }
                    };
                    Variables.Add(vd);
                }

                foreach (FunctionDefinition f in Functions)
                {
                    if (f is INotifyPropertyChanged inpc)
                    {
                        inpc.PropertyChanged += Function_PropertyChanged;
                    }
                }
                foreach (VariableDefinition v in Variables)
                {
                    v.PropertyChanged += Variable_PropertyChanged;
                }
            }
            finally
            {
                _isLoadingFromNode = false;
            }
            UpdateEmptyState();
        }

        /// <summary>
        /// 判定该组件是否适用于指定节点类型。
        /// </summary>
        public override bool IsApplicableToNode(ModuleNodeViewModel node)
        {
            bool isApplicable = node?.Module?.Type == ModuleType.DataCalculation;
            System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] IsApplicableToNode: {node?.Module?.Type} -> {isApplicable}");
            return isApplicable;
        }

        /// <summary>
        /// 清空组件内部状态与绑定。
        /// </summary>
        protected override void ClearComponent()
        {
            foreach (FunctionDefinition f in Functions)
            {
                if (f is INotifyPropertyChanged inpc)
                {
                    inpc.PropertyChanged -= Function_PropertyChanged;
                }
            }
            foreach (VariableDefinition v in Variables)
            {
                v.PropertyChanged -= Variable_PropertyChanged;
            }
            Functions.Clear();
            Variables.Clear();
            UpdateEmptyState();
        }

        /// <summary>
        /// 校验组件数据有效性。
        /// </summary>
        public override bool ValidateComponent(out string errorMessage)
        {
            errorMessage = "";
            if (Functions.Count == 0)
            {
                errorMessage = "请至少添加一个计算函数";
                return false;
            }
            foreach (FunctionDefinition function in Functions)
            {
                if (string.IsNullOrWhiteSpace(function.Name))
                {
                    errorMessage = "函数名称不能为空";
                    return false;
                }
                if (string.IsNullOrWhiteSpace(function.Expression))
                {
                    errorMessage = $"函数 '{function.Name}' 的表达式不能为空";
                    return false;
                }
                if (!ValidateExpression(function.Expression))
                {
                    errorMessage = $"函数 '{function.Name}' 的表达式格式不正确";
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 将组件数据写回节点（同时更新节点展开视图）。
        /// </summary>
        public override void ApplyToNode()
        {
            if (CurrentNode?.Module == null)
            {
                return;
            }

            try
            {
                DataCalculationConfig cfg = new()
                {
                    CalculationMode = (CalculationMode)Math.Max(0, CalculationModeComboBox.SelectedIndex),
                    Precision = (int)Math.Round(PrecisionSlider.Value),
                    ErrorHandling = (ErrorHandling)Math.Max(0, ErrorHandlingComboBox.SelectedIndex),
                    OutputFormat = (OutputFormat)Math.Max(0, OutputFormatComboBox.SelectedIndex),
                    CacheResults = CacheResultsCheckBox.IsChecked == true
                };

                foreach (FunctionDefinition f in Functions)
                {
                    cfg.Functions.Add(new FunctionDef { Name = f.Name, Expression = f.Expression });
                }

                foreach (VariableDefinition v in Variables)
                {
                    cfg.Variables.Add(new VariableMapping
                    {
                        Name = v.VariableName,
                        SourceNodeId = v.SelectedSourceNode?.NodeId,
                        PropertyName = v.SelectedProperty?.PropertyName
                    });
                }

                cfg.SaveToParameters(CurrentNode.Module.Parameters, alsoWriteLegacy: true);

                try
                {
                    if (CurrentNode != null)
                    {
                        List<FunctionDisplayItem> toRemove = CurrentNode.ExpandedContent
                            .OfType<FunctionDisplayItem>()
                            .ToList();
                        foreach (FunctionDisplayItem? item in toRemove)
                        {
                            CurrentNode.ExpandedContent.Remove(item);
                        }

                        foreach (FunctionDefinition func in Functions)
                        {
                            CurrentNode.ExpandedContent.Add(new FunctionDisplayItem
                            {
                                Name = func.Name,
                                Expression = func.Expression,
                                IsEnabled = true
                            });
                        }
                    }
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] Update ExpandedContent failed: {ex2.Message}");
                }

                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] Applied {Functions.Count} functions and {Variables.Count} variables to node");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] Error applying to node: {ex.Message}");
            }
        }

        private void LoadFunctionsFromNode(ModuleNodeViewModel node)
        {
            if (node?.Module?.Parameters == null)
            {
                return;
            }

            try
            {
                Functions.Clear();
                if (node.Module.Parameters.TryGetValue("function_count", out object? countStr) &&
                    int.TryParse(countStr.ToString(), out int count))
                {
                    for (int i = 0; i < count; i++)
                    {
                        if (node.Module.Parameters.TryGetValue($"function_{i}_name", out object? name) &&
                            node.Module.Parameters.TryGetValue($"function_{i}_expression", out object? expression))
                        {
                            Functions.Add(new FunctionDefinition
                            {
                                Name = name.ToString(),
                                Expression = expression.ToString()
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] Error loading from node: {ex.Message}");
            }
        }

        private bool ValidateExpression(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
            {
                return false;
            }

            int openParens = 0;
            foreach (char c in expression)
            {
                if (c == '(')
                {
                    openParens++;
                }
                else if (c == ')')
                {
                    openParens--;
                }

                if (openParens < 0)
                {
                    return false;
                }
            }
            return openParens == 0;
        }

        private void ApplyFormulaTemplate(FormulaTemplate template)
        {
            try
            {
                FunctionDefinition newFunction = new()
                {
                    Name = template.Name,
                    Expression = template.Expression
                };

                Functions.Add(newFunction);
                UpdateEmptyState();
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 成功应用模板: {template.Name}");
                ShowStatusInfo($"已引入公式模板：{template.Name}", InfoBarSeverity.Success);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 应用模板失败: {ex.Message}");
                ShowStatusInfo($"应用模板失败：{ex.Message}", InfoBarSeverity.Error);
            }
        }

        private void UpdateEmptyState()
        {
            EmptyFunctionsPanel.Visibility = Functions.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            EmptyVariablesPanel.Visibility = Variables.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        private void AddFunctionButton_Click(object sender, RoutedEventArgs e)
        {
            FunctionDefinition newFunction = new()
            {
                Name = $"函数{Functions.Count + 1}",
                Expression = ""
            };
            Functions.Add(newFunction);
            UpdateEmptyState();
            if (!_isLoadingFromNode)
            {
                ApplyToNode();
            }

            ShowStatusInfo("已添加一个函数", InfoBarSeverity.Success);
        }

        private void DeleteFunctionButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is FunctionDefinition function)
            {
                Functions.Remove(function);
                UpdateEmptyState();
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 删除函数: {function.Name}");
                if (!_isLoadingFromNode)
                {
                    ApplyToNode();
                }

                ShowStatusInfo($"已删除函数：{function.Name}", InfoBarSeverity.Success);
            }
        }

        private void ImportTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                FormulaTemplateSelector templateSelector = new();
                Window window = new()
                {
                    Title = "选择公式模板",
                    Content = templateSelector,
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.CanResize
                };

                templateSelector.FormulaSelected += (s, template) =>
                {
                    ApplyFormulaTemplate(template);
                    window.Close();
                };

                templateSelector.SelectionCancelled += (s, args) =>
                {
                    window.Close();
                };

                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 导入模板失败: {ex.Message}");
                ShowStatusInfo($"导入模板失败：{ex.Message}", InfoBarSeverity.Error);
            }
        }

        private void AddVariableButton_Click(object sender, RoutedEventArgs e)
        {
            VariableDefinition newVariable = new()
            {
                VariableName = $"变量{Variables.Count + 1}",
                AvailableSourceNodes = GetAvailableSourceNodes()
            };
            Variables.Add(newVariable);
            UpdateEmptyState();
            if (!_isLoadingFromNode)
            {
                ApplyToNode();
            }
        }

        private void RefreshVariablesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadAvailableSourceNodes();
            foreach (VariableDefinition variable in Variables)
            {
                variable.AvailableSourceNodes = GetAvailableSourceNodes();
            }
        }

        private void LoadAvailableSourceNodes()
        {
            ObservableCollection<SourceNodeInfo> availableNodes = GetAvailableSourceNodes();
            foreach (VariableDefinition variable in Variables)
            {
                variable.AvailableSourceNodes = availableNodes;
            }
        }

        private void ShowAllFunctionsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AllFunctionsWindow win = new()
                {
                    Owner = Application.Current?.Windows?.OfType<Window>().FirstOrDefault(w => w.IsActive)
                };
                win.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 打开‘所有函数’失败: {ex.Message}");
                MessageBox.Show($"打开‘所有函数’失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private ObservableCollection<SourceNodeInfo> GetAvailableSourceNodes()
        {
            ObservableCollection<SourceNodeInfo> sourceNodes = [];
            if (_currentNode?.Module == null)
            {
                return sourceNodes;
            }

            try
            {
                CanvasViewModel? canvasViewModel = GetCanvasViewModel();
                if (canvasViewModel?.Nodes == null)
                {
                    return sourceNodes;
                }

                IEnumerable<ModuleNodeViewModel> connectedSourceNodes = GetConnectedSourceNodes(canvasViewModel, _currentNode);
                foreach (ModuleNodeViewModel sourceNode in connectedSourceNodes)
                {
                    sourceNodes.Add(new SourceNodeInfo
                    {
                        NodeId = sourceNode.Module?.Id ?? "",
                        NodeTitle = sourceNode.Title,
                        NodeType = GetNodeTypeDisplayName(sourceNode.Module?.Type),
                        OutputDataType = GetNodeOutputDataType(sourceNode),
                        OutputCount = sourceNode.Outputs?.Count ?? 0,
                        IsAvailable = true,
                        IsConnected = true
                    });
                }
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 找到 {sourceNodes.Count} 个可用源节点");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] 获取源节点失败: {ex.Message}");
            }
            return sourceNodes;
        }

        private string GetNodeTypeDisplayName(ModuleType? type) => type switch
        {
            ModuleType.PiConstant => "π常量",
            ModuleType.EConstant => "e常量",
            ModuleType.DataCalculation => "数据计算",
            ModuleType.FileInput => "文件输入",
            _ => "未知"
        };

        private string GetNodeOutputDataType(ModuleNodeViewModel node)
        {
            return node.Module?.Type switch
            {
                ModuleType.PiConstant or ModuleType.EConstant => "Number",
                ModuleType.DataCalculation => "Any",
                ModuleType.FileInput => "Object",
                _ => "Any"
            };
        }

        private void LoadVariablesFromNode(ModuleNodeViewModel node)
        {
            if (node?.Module?.Parameters == null)
            {
                return;
            }

            try
            {
                Variables.Clear();
                if (node.Module.Parameters.TryGetValue("variable_count", out object? countStr) &&
                    int.TryParse(countStr.ToString(), out int count))
                {
                    for (int i = 0; i < count; i++)
                    {
                        if (node.Module.Parameters.TryGetValue($"variable_{i}_name", out object? name) &&
                            node.Module.Parameters.TryGetValue($"variable_{i}_sourceNodeId", out object? sourceNodeId) &&
                            node.Module.Parameters.TryGetValue($"variable_{i}_propertyName", out object? propertyName))
                        {
                            VariableDefinition variable = new()
                            {
                                VariableName = name.ToString(),
                                AvailableSourceNodes = GetAvailableSourceNodes()
                            };
                            SourceNodeInfo? selectedNode = variable.AvailableSourceNodes.FirstOrDefault(n => n.NodeId == sourceNodeId.ToString());
                            if (selectedNode != null)
                            {
                                variable.SelectedSourceNode = selectedNode;
                                variable.LoadPropertiesForNode(selectedNode);
                                NodePropertyInfo? selectedProperty = variable.AvailableProperties.FirstOrDefault(p => p.PropertyName == propertyName.ToString());
                                if (selectedProperty != null)
                                {
                                    variable.SelectedProperty = selectedProperty;
                                }
                            }
                            Variables.Add(variable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[DataCalculationComponent] Error loading variables from node: {ex.Message}");
            }
        }
    }

    // ==== Inline helpers for UX enhancements (shortcuts, InfoBar, variable insertion) ====
    public partial class DataCalculationComponent
    {
        private void OnComponentPreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            try
            {
                // Ctrl+Enter: execute all functions
                if (e.Key == System.Windows.Input.Key.Enter && (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
                {
                    int count = 0;
                    foreach (FunctionDefinition f in Functions)
                    {
                        try { ExecuteFunction(f); count++; }
                        catch { }
                    }
                    ShowStatusInfo($"已执行 {count} 个函数", InfoBarSeverity.Success);
                    e.Handled = true;
                    return;
                }

                // Ctrl+D: duplicate current function (based on last focused editor)
                if (e.Key == System.Windows.Input.Key.D && (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
                {
                    FunctionDefinition? current = GetCurrentFunctionFromEditor();
                    if (current != null)
                    {
                        FunctionDefinition clone = new() { Name = BuildCopyName(current.Name), Expression = current.Expression };
                        Functions.Add(clone);
                        UpdateEmptyState();
                        if (!_isLoadingFromNode)
                        {
                            ApplyToNode();
                        }

                        ShowStatusInfo($"已复制函数：{current.Name}", InfoBarSeverity.Success);
                    }
                    else
                    {
                        ShowStatusInfo("请先激活一个表达式编辑器", InfoBarSeverity.Warning);
                    }
                    e.Handled = true;
                    return;
                }

                // Delete: remove current function (no modifiers)
                if (e.Key == System.Windows.Input.Key.Delete && (System.Windows.Input.Keyboard.Modifiers & (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift | System.Windows.Input.ModifierKeys.Alt)) == 0)
                {
                    FunctionDefinition? current = GetCurrentFunctionFromEditor();
                    if (current != null)
                    {
                        Functions.Remove(current);
                        UpdateEmptyState();
                        if (!_isLoadingFromNode)
                        {
                            ApplyToNode();
                        }

                        ShowStatusInfo($"已删除函数：{current.Name}", InfoBarSeverity.Success);
                    }
                    else
                    {
                        ShowStatusInfo("未找到当前函数", InfoBarSeverity.Informational);
                    }
                    e.Handled = true;
                    return;
                }

                // Ctrl+N: add new function
                if (e.Key == System.Windows.Input.Key.N && (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
                {
                    AddFunctionButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    return;
                }

                // Ctrl+Shift+F: open All Functions window
                if (e.Key == System.Windows.Input.Key.F && (System.Windows.Input.Keyboard.Modifiers & (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift)) == (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift))
                {
                    ShowAllFunctionsButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    return;
                }

                // Ctrl+Shift+L: refresh variables
                if (e.Key == System.Windows.Input.Key.L && (System.Windows.Input.Keyboard.Modifiers & (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift)) == (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift))
                {
                    RefreshVariablesButton_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    return;
                }
            }
            catch (Exception ex)
            {
                ShowStatusInfo($"快捷键操作失败：{ex.Message}", InfoBarSeverity.Error);
            }
        }

        private FunctionDefinition? GetCurrentFunctionFromEditor()
        {
            AvalonFormulaEditor? editor = Plugins.DataCalculation.Controls.Functions.AvalonFormulaEditor.TryGetLastFocused();
            if (editor is FrameworkElement fe)
            {
                return fe.DataContext as FunctionDefinition;
            }

            return null;
        }

        private static string BuildCopyName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                return "函数副本";
            }

            string baseName = name.Trim();
            string suffix = " 副本";
            return baseName.EndsWith(suffix, StringComparison.Ordinal) ? baseName : baseName + suffix;
        }

        private void InsertVariableToken_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button btn)
                {
                    string varName = Convert.ToString(btn.Tag) ?? string.Empty;
                    if (string.IsNullOrWhiteSpace(varName))
                    {
                        return;
                    }

                    AvalonFormulaEditor? editor = Plugins.DataCalculation.Controls.Functions.AvalonFormulaEditor.TryGetLastFocused();
                    if (editor == null)
                    {
                        ShowStatusInfo("请先点击某个表达式编辑器", InfoBarSeverity.Warning);
                        return;
                    }
                    editor.InsertAtCaret("{" + varName + "}");
                    ShowStatusInfo($"已插入变量：{{{varName}}}", InfoBarSeverity.Success);
                }
            }
            catch (Exception ex)
            {
                ShowStatusInfo($"插入变量失败：{ex.Message}", InfoBarSeverity.Error);
            }
        }

        private void ShowStatusInfo(string message, InfoBarSeverity severity, string? title = null)
        {
            try
            {
                if (StatusInfoBar == null)
                {
                    return;
                }

                StatusInfoBar.Severity = severity;
                StatusInfoBar.Title = string.IsNullOrWhiteSpace(title) ? (severity switch
                {
                    InfoBarSeverity.Success => "成功",
                    InfoBarSeverity.Error => "错误",
                    InfoBarSeverity.Warning => "提示",
                    _ => "提示"
                }) : title;
                StatusInfoBar.Message = message;
                StatusInfoBar.IsOpen = true;
                _infoTimer.Stop();
                _infoTimer.Start();
            }
            catch { }
        }

        private void TryCloseInfoBar()
        {
            try
            {
                if (StatusInfoBar != null)
                {
                    StatusInfoBar.IsOpen = false;
                }
            }
            catch { }
        }
    }
    public class FunctionDefinition : INotifyPropertyChanged
    {
        private string _name = "";
        private string _expression = "";
        private string _validationError = "";
        private string _result = "";

        public string Name { get => _name; set => SetProperty(ref _name, value); }
        public string Expression { get => _expression; set => SetProperty(ref _expression, value); }
        public string ValidationError
        {
            get => _validationError;
            set
            {
                if (SetProperty(ref _validationError, value))
                {
                    OnPropertyChanged(nameof(HasValidationError));
                }
            }
        }
        public bool HasValidationError => !string.IsNullOrEmpty(ValidationError);
        public string Result
        {
            get => _result;
            set
            {
                if (SetProperty(ref _result, value))
                {
                    OnPropertyChanged(nameof(HasResult));
                }
            }
        }
        public bool HasResult => !string.IsNullOrEmpty(Result);

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
            {
                return false;
            }

            field = value; OnPropertyChanged(propertyName); return true;
        }
    }

    public class VariableDefinition : INotifyPropertyChanged
    {
        private string _variableName = "";
        private SourceNodeInfo? _selectedSourceNode;
        private NodePropertyInfo? _selectedProperty;
        private ObservableCollection<SourceNodeInfo> _availableSourceNodes = [];
        private ObservableCollection<NodePropertyInfo> _availableProperties = [];

        public string VariableName { get => _variableName; set => SetProperty(ref _variableName, value); }
        public SourceNodeInfo? SelectedSourceNode
        {
            get => _selectedSourceNode;
            set
            {
                if (SetProperty(ref _selectedSourceNode, value))
                {
                    LoadPropertiesForNode(value);
                    OnPropertyChanged(nameof(HasSelectedSourceNode));
                }
            }
        }
        public NodePropertyInfo? SelectedProperty { get => _selectedProperty; set => SetProperty(ref _selectedProperty, value); }
        public ObservableCollection<SourceNodeInfo> AvailableSourceNodes { get => _availableSourceNodes; set => SetProperty(ref _availableSourceNodes, value); }
        public ObservableCollection<NodePropertyInfo> AvailableProperties { get => _availableProperties; set => SetProperty(ref _availableProperties, value); }
        public bool HasSelectedSourceNode => SelectedSourceNode != null;

        public void LoadPropertiesForNode(SourceNodeInfo? sourceNode)
        {
            AvailableProperties.Clear();
            if (sourceNode == null)
            {
                return;
            }

            switch (sourceNode.NodeType)
            {
                case "π常量":
                case "e常量":
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "Value", PropertyType = "Number", Description = "常量值" });
                    break;
                case "数据计算":
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "Result", PropertyType = "Any", Description = "计算结果" });
                    break;
                case "文件输入":
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "Data", PropertyType = "Object", Description = "文件数据" });
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "FileName", PropertyType = "Text", Description = "文件名" });
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "FileSize", PropertyType = "Number", Description = "文件大小" });
                    break;
                case "手动输入":
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "InputValue", PropertyType = "Any", Description = "输入值" });
                    break;
                default:
                    AvailableProperties.Add(new NodePropertyInfo { PropertyName = "Output", PropertyType = "Any", Description = "默认输出" });
                    break;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
            {
                return false;
            }

            field = value; OnPropertyChanged(propertyName); return true;
        }
    }

    public class NodePropertyInfo
    {
        public string PropertyName { get; set; } = "";
        public string PropertyType { get; set; } = "";
        public string Description { get; set; } = "";
        public string DisplayText => $"{PropertyName} ({PropertyType})";
    }

    public class SourceNodeInfo
    {
        public string? NodeId { get; set; }
        public string NodeTitle { get; set; } = string.Empty;
        public string NodeType { get; set; } = string.Empty;
        public string OutputDataType { get; set; } = string.Empty;
        public int OutputCount { get; set; }
        public bool IsAvailable { get; set; }
        public bool IsConnected { get; set; }
        public string DisplayText => $"{NodeTitle} [{NodeType}]";
    }
}


