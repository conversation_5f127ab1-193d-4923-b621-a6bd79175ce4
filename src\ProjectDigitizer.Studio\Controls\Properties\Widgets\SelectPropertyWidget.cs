using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Studio.Controls.Properties.Widgets
{
    /// <summary>
    /// 选择属性编辑器
    /// </summary>
    public class SelectPropertyWidget : IPropertyWidget
    {
        private readonly ComboBox _comboBox;
        private PropertyDefinition _propertyDefinition = new();
        private object? _value;

        public SelectPropertyWidget()
        {
            _comboBox = new ComboBox
            {
                Margin = new Thickness(0, 8, 0, 16),
                FontSize = 14,
                Padding = new Thickness(8),
                BorderThickness = new Thickness(1),
                BorderBrush = System.Windows.Media.Brushes.LightGray
            };

            // 使用项目通用样式（在全局 ResourceDictionary 中已定义）

            _comboBox.SelectionChanged += OnSelectionChanged;
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    UpdateSelection();
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _comboBox.IsEnabled;
            set => _comboBox.IsEnabled = value;
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Core.ValueObjects.ValidationResult Validate()
        {
            var result = new Core.ValueObjects.ValidationResult();

            // 必填验证
            if (PropertyDefinition.Required && _comboBox.SelectedItem == null)
            {
                result.AddError($"{PropertyDefinition.Title} 是必选项");
            }

            return result;
        }

        public FrameworkElement GetElement()
        {
            return _comboBox;
        }

        private void UpdateUI()
        {
            // 清空现有选项
            _comboBox.Items.Clear();

            // 添加选项
            foreach (var option in PropertyDefinition.Options)
            {
                var item = new ComboBoxItem
                {
                    Content = option.Label,
                    Tag = option.Value,
                    ToolTip = option.Description
                };
                _comboBox.Items.Add(item);
            }

            // 设置默认值
            if (PropertyDefinition.DefaultValue != null)
            {
                Value = PropertyDefinition.DefaultValue;
            }

            // 设置 ToolTip 作为帮助文本
            if (!string.IsNullOrEmpty(PropertyDefinition.Description))
            {
                _comboBox.ToolTip = PropertyDefinition.Description;
            }
        }

        private void UpdateSelection()
        {
            if (_value == null)
            {
                _comboBox.SelectedItem = null;
                return;
            }

            // 查找匹配的选项
            var valueString = _value.ToString();
            foreach (ComboBoxItem item in _comboBox.Items)
            {
                if (item.Tag?.ToString() == valueString)
                {
                    _comboBox.SelectedItem = item;
                    return;
                }
            }

            // 如果没找到匹配项，清空选择
            _comboBox.SelectedItem = null;
        }

        private void OnSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var oldValue = _value;

            if (_comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _value = selectedItem.Tag;
            }
            else
            {
                _value = null;
            }

            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, oldValue, _value));
        }
    }
}
