<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 手动数据输入节点模板 ==========  -->
    <!--  专用于 ModuleType.ManualDataInput 的节点模板  -->

    <!--  手动输入节点专用样式  -->
    <Style
        BasedOn="{StaticResource BaseNodeStyle}"
        TargetType="nodify:Node"
        x:Key="ManualDataInputNodeStyle">
        <Setter Property="Width" Value="200" />
        <Setter Property="Height" Value="120" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="nodify:Node">
                    <Grid Background="Transparent">
                        <ContentPresenter Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" />
                        <!--  输入连接器位置调整  -->
                        <ItemsControl
                            Focusable="False"
                            HorizontalAlignment="Left"
                            ItemTemplate="{TemplateBinding InputConnectorTemplate}"
                            ItemsSource="{TemplateBinding Input}"
                            Margin="-9,59,0,0"
                            VerticalAlignment="Top"
                            x:Name="PART_Input" />
                        <!--  输出连接器位置调整  -->
                        <ItemsControl
                            Focusable="False"
                            HorizontalAlignment="Right"
                            ItemTemplate="{TemplateBinding OutputConnectorTemplate}"
                            ItemsSource="{TemplateBinding Output}"
                            Margin="0,59,-9,0"
                            VerticalAlignment="Top"
                            x:Name="PART_Output" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  手动输入节点连接器模板  -->
    <DataTemplate x:Key="ManualDataInputConnectorTemplate">
        <nodify:NodeInput
            Anchor="{Binding Anchor, Mode=OneWayToSource}"
            Header=""
            IsConnected="{Binding IsConnected}"
            Style="{StaticResource HtmlPrototypeInputConnectorStyle}"
            ToolTip="{Binding Title}" />
    </DataTemplate>

    <DataTemplate x:Key="ManualDataOutputConnectorTemplate">
        <nodify:NodeOutput
            Anchor="{Binding Anchor, Mode=OneWayToSource}"
            Header=""
            IsConnected="{Binding IsConnected}"
            Style="{StaticResource HtmlPrototypeOutputConnectorStyle}"
            ToolTip="{Binding Title}" />
    </DataTemplate>

    <!--  手动输入节点内容模板  -->
    <DataTemplate x:Key="ManualDataInputContentTemplate">
        <Border
            Background="White"
            BorderBrush="#00BCD4"
            BorderThickness="2"
            CornerRadius="8"
            Effect="{StaticResource CardShadow}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border
                    Background="{StaticResource ModernHeaderGradientCyan}"
                    CornerRadius="6,6,0,0"
                    Grid.Row="0"
                    Height="40"
                    Padding="12,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  键盘图标  -->
                        <ipack:PackIconMaterial
                            Foreground="White"
                            Grid.Column="0"
                            Height="16"
                            Kind="Keyboard"
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            Width="16" />

                        <!--  节点标题  -->
                        <TextBlock
                            FontSize="12"
                            FontWeight="Medium"
                            Foreground="White"
                            Grid.Column="1"
                            Text="手动输入"
                            VerticalAlignment="Center" />

                        <!--  状态指示器  -->
                        <Ellipse
                            Fill="{Binding StatusBrush}"
                            Grid.Column="2"
                            Height="6"
                            VerticalAlignment="Center"
                            Width="6" />
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#F0F8FF"
                    CornerRadius="0,0,6,6"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  节点描述  -->
                        <TextBlock
                            FontSize="10"
                            Foreground="#555"
                            Margin="0,0,0,4"
                            Text="数据输入节点" />

                        <!--  功能说明  -->
                        <TextBlock
                            FontSize="9"
                            Foreground="#777"
                            Margin="0,0,0,4"
                            Text="支持手动输入各种数据类型"
                            TextWrapping="Wrap" />

                        <!--  数据类型指示  -->
                        <Border
                            Background="#E3F2FD"
                            CornerRadius="3"
                            HorizontalAlignment="Left"
                            Margin="0,2,0,0"
                            Padding="4,2">
                            <TextBlock
                                FontSize="8"
                                FontWeight="Medium"
                                Foreground="#1976D2"
                                Text="{Binding NodeProperties.DataType, FallbackValue='Text'}" />
                        </Border>

                        <!--  输入值预览  -->
                        <TextBlock
                            FontSize="8"
                            Foreground="#999"
                            Margin="0,2,0,0"
                            MaxWidth="150"
                            Text="{Binding NodeProperties.InputValue, FallbackValue='未设置'}"
                            TextTrimming="CharacterEllipsis"
                            ToolTip="{Binding NodeProperties.InputValue}" />
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  手动数据输入节点主模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ManualDataInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource ManualDataInputNodeStyle}">

            <!--  使用专用连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="ManualDataInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="ManualDataOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  使用专用内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ManualDataInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  为特定模块类型注册模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}">
        <DataTemplate.Resources>
            <Style TargetType="ContentPresenter">
                <Style.Triggers>
                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ManualDataInput}">
                        <Setter Property="ContentTemplate" Value="{StaticResource ManualDataInputNodeTemplate}" />
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </DataTemplate.Resources>

        <!--  默认使用通用模板  -->
        <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource NodeTemplate}" />
    </DataTemplate>

</ResourceDictionary>
