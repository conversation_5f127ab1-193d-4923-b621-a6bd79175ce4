<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== CAD输入节点模板 ==========  -->
    <!--  专用于 ModuleType.CADInput 的节点模板  -->

    <!--  CAD输入节点内容模板  -->
    <DataTemplate x:Key="CADInputContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource BaseNodeHeaderContentTemplate}" />
                </Border>

                <!--  内容区域 - CAD专用  -->
                <Border
                    Background="#E8F5E8"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  CAD文件类型  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="类型:"
                                VerticalAlignment="Center" />

                            <Border
                                Background="#4CAF50"
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <StackPanel Orientation="Horizontal">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="10"
                                        Kind="FileCAD"
                                        Margin="0,0,4,0"
                                        VerticalAlignment="Center"
                                        Width="10" />
                                    <TextBlock
                                        FontSize="9"
                                        FontWeight="Medium"
                                        Foreground="White"
                                        Text="{Binding NodeProperties.CADFormat, FallbackValue='DWG'}" />
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!--  图层信息  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="图层:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Foreground="#4CAF50"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.LayerFilter, FallbackValue='全部图层'}"
                                TextTrimming="CharacterEllipsis"
                                VerticalAlignment="Center" />
                        </Grid>

                        <!--  坐标系统  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="坐标:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Foreground="#4CAF50"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.CoordinateSystem, FallbackValue='WGS84'}"
                                VerticalAlignment="Center" />
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  实体数量  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="实体" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#4CAF50"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.EntityCount, FallbackValue=0}" />
                            </StackPanel>

                            <!--  单位  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="单位" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#4CAF50"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.DrawingUnits, FallbackValue='mm'}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  CAD输入节点主模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="CADInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">

            <!--  使用高级连接器模板  -->
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>

            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>

            <!--  使用专用内容模板  -->
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource CADInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
