using Microsoft.Extensions.DependencyInjection;

using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Core;

/// <summary>
/// 核心层依赖注入扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加核心层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCore(this IServiceCollection services)
    {
        // 注册领域事件发布器 - Scoped生命周期，因为需要在请求范围内保持状态
        services.AddScoped<IDomainEventPublisher, DomainEventPublisher>();

        // 注册领域事件处理器（泛型注册）- Scoped生命周期
        services.AddScoped(typeof(IDomainEventHandler<>), typeof(DomainEventHandler<>));

        // 注册规约模式接口（泛型注册）- Transient生命周期，因为规约通常是无状态的
        services.AddTransient(typeof(ISpecification<>), typeof(Specification<>));

        return services;
    }
}

/// <summary>
/// 领域事件发布器实现
/// </summary>
public class DomainEventPublisher : IDomainEventPublisher
{
    private readonly IServiceProvider _serviceProvider;

    public DomainEventPublisher(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task PublishAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default)
    {
        var eventType = domainEvent.GetType();
        var handlerType = typeof(IDomainEventHandler<>).MakeGenericType(eventType);
        var handlers = _serviceProvider.GetServices(handlerType);

        foreach (var handler in handlers)
        {
            var method = handlerType.GetMethod("HandleAsync");
            if (method != null)
            {
                var task = (Task)method.Invoke(handler, new object[] { domainEvent, cancellationToken })!;
                await task;
            }
        }
    }

    public async Task PublishAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default)
    {
        foreach (var domainEvent in domainEvents)
        {
            await PublishAsync(domainEvent, cancellationToken);
        }
    }
}

/// <summary>
/// 默认领域事件处理器实现
/// </summary>
/// <typeparam name="T">领域事件类型</typeparam>
public class DomainEventHandler<T> : IDomainEventHandler<T> where T : IDomainEvent
{
    public virtual Task HandleAsync(T domainEvent, CancellationToken cancellationToken = default)
    {
        // 默认实现为空，具体处理器应继承此类并重写此方法
        return Task.CompletedTask;
    }
}

/// <summary>
/// 默认规约实现
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class Specification<T> : BaseSpecification<T>
{
    public Specification() : base()
    {
    }

    public Specification(System.Linq.Expressions.Expression<Func<T, bool>> criteria) : base(criteria)
    {
    }
}
