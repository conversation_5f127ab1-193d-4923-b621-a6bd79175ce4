using System.Collections.Generic;

using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.ValueObjects
{
    /// <summary>
    /// 连接器信息
    /// </summary>
    public class ConnectorInfo
    {
        public string Id { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public ConnectorDataType DataType { get; set; }
        public bool IsInput { get; set; }
        public bool IsConnected { get; set; }
        public bool IsCompatible { get; set; } = true;
        public bool IsHighlighted { get; set; } = false;
    }

    /// <summary>
    /// 兼容性检查结果
    /// </summary>
    public class CompatibilityResult
    {
        public bool IsCompatible { get; set; }
        public int Score { get; set; } // 0-100的兼容性评分
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 连接器兼容性规则
    /// </summary>
    public class CompatibilityRule
    {
        public ConnectorDataType SourceType { get; set; }
        public ConnectorDataType TargetType { get; set; }
        public bool IsCompatible { get; set; }
        public int Score { get; set; } = 100;
        public string Description { get; set; } = string.Empty;
    }
}
