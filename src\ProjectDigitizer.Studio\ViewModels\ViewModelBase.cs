using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 优化的ViewModel基类，包含性能优化功能
    /// </summary>
    public abstract class ViewModelBase : INotifyPropertyChanged
    {
        private readonly object _lockObject = new object();
        private volatile bool _isUpdating = false;

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性更改通知
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            // 避免在更新过程中重复触发通知
            if (_isUpdating || string.IsNullOrEmpty(propertyName))
                return;

            try
            {
                if (PropertyChanged != null)
                {
                    System.Diagnostics.Debug.WriteLine($"触发PropertyChanged事件: {GetType().Name}.{propertyName}");
                    PropertyChanged.Invoke(this, new PropertyChangedEventArgs(propertyName));
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"PropertyChanged事件为null: {GetType().Name}.{propertyName}");
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不阻止程序运行
                System.Diagnostics.Debug.WriteLine($"PropertyChanged事件异常: {propertyName}, 错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置属性值，仅在值实际改变时触发通知
        /// </summary>
        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(storage, value))
            {
                return false;
            }

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 批量更新属性，减少通知频率
        /// </summary>
        protected void BeginUpdate()
        {
            lock (_lockObject)
            {
                _isUpdating = true;
            }
        }

        /// <summary>
        /// 结束批量更新，触发所有待定的通知
        /// </summary>
        protected void EndUpdate()
        {
            lock (_lockObject)
            {
                _isUpdating = false;
            }
        }

        /// <summary>
        /// 在批量更新模式下执行操作
        /// </summary>
        protected void BatchUpdate(System.Action updateAction)
        {
            BeginUpdate();
            try
            {
                updateAction?.Invoke();
            }
            finally
            {
                EndUpdate();
            }
        }
    }
}
