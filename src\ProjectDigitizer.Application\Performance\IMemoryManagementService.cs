using System;
using System.Collections.Generic;

namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 内存管理服务接口
    /// </summary>
    public interface IMemoryManagementService : IDisposable
    {
        MemoryUsageInfo CurrentMemoryUsage { get; }
        IEnumerable<MemoryUsageInfo> MemoryUsageHistory { get; }
        event Action<MemoryWarningEventArgs>? MemoryWarning;
        event Action<GarbageCollectionEventArgs>? GarbageCollectionCompleted;
        void StartMonitoring();
        void StopMonitoring();
        void ForceGarbageCollection(int generation = -1);
        IEnumerable<MemoryCleanupSuggestion> GetCleanupSuggestions();
        void ExecuteCleanup(IEnumerable<MemoryCleanupSuggestion> suggestions);
        MemoryLeakDetectionResult DetectMemoryLeaks();
        void SetMemoryThresholds(long warningThresholdMB, long criticalThresholdMB);
        LargeObjectHeapInfo GetLargeObjectHeapInfo();
    }

    public class MemoryUsageInfo
    {
        public DateTime Timestamp { get; set; }
        public long WorkingSetMB { get; set; }
        public long PrivateMemoryMB { get; set; }
        public long ManagedMemoryMB { get; set; }
        public long GCHeapSizeMB { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
        public long LargeObjectHeapMB { get; set; }
    }

    public class MemoryWarningEventArgs : EventArgs
    {
        public MemoryWarningLevel Level { get; set; }
        public MemoryUsageInfo CurrentUsage { get; set; } = new();
        public string Message { get; set; } = string.Empty;
        public IEnumerable<MemoryCleanupSuggestion> SuggestedActions { get; set; } = new List<MemoryCleanupSuggestion>();
    }

    public class GarbageCollectionEventArgs : EventArgs
    {
        public int Generation { get; set; }
        public long MemoryBeforeMB { get; set; }
        public long MemoryAfterMB { get; set; }
        public long MemoryReleasedMB => MemoryBeforeMB - MemoryAfterMB;
        public long DurationMs { get; set; }
    }

    public enum MemoryWarningLevel
    {
        Info,
        Warning,
        Critical
    }

    public class MemoryCleanupSuggestion
    {
        public MemoryCleanupType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public long ExpectedMemoryReleaseMB { get; set; }
        public CleanupPriority Priority { get; set; }
        public Action? ExecuteAction { get; set; }
    }

    public enum MemoryCleanupType
    {
        ForceGarbageCollection,
        ClearCaches,
        ReleaseUnusedResources,
        CompactLargeObjectHeap,
        ClearTemporaryData
    }

    public enum CleanupPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    public class MemoryLeakDetectionResult
    {
        public bool HasMemoryLeaks { get; set; }
        public IEnumerable<MemoryLeakSuspect> Suspects { get; set; } = new List<MemoryLeakSuspect>();
        public string Report { get; set; } = string.Empty;
    }

    public class MemoryLeakSuspect
    {
        public string ObjectType { get; set; } = string.Empty;
        public int InstanceCount { get; set; }
        public long MemorySizeMB { get; set; }
        public MemoryGrowthTrend GrowthTrend { get; set; }
    }

    public enum MemoryGrowthTrend
    {
        Stable,
        SlowGrowth,
        RapidGrowth,
        AbnormalGrowth
    }

    public class LargeObjectHeapInfo
    {
        public long SizeMB { get; set; }
        public int ObjectCount { get; set; }
        public double FragmentationRatio { get; set; }
        public bool NeedsCompaction { get; set; }
    }
}

