using System;
using System.Collections.Generic;
using System.Linq;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.DTOs;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Extensions
{
    /// <summary>
    /// ProjectFileAdapter扩展方法
    /// 提供ViewModel与DTO之间的转换功能
    /// </summary>
    public static class ProjectFileAdapterExtensions
    {
        /// <summary>
        /// 从画布ViewModel创建项目文件数据
        /// </summary>
        /// <param name="adapter">项目文件适配器</param>
        /// <param name="canvasViewModel">画布视图模型</param>
        /// <param name="projectInfo">项目信息</param>
        /// <returns>项目文件数据</returns>
        public static ProjectFile CreateProjectFileFromCanvas(
            this IProjectFileAdapter adapter,
            CanvasViewModel canvasViewModel,
            ProjectInfo? projectInfo = null)
        {
            var projectFile = new ProjectFile
            {
                ProjectInfo = projectInfo ?? new ProjectInfo(),
                CanvasData = new CanvasData()
            };

            // 转换节点数据
            if (canvasViewModel.Nodes != null)
            {
                projectFile.CanvasData.Nodes = canvasViewModel.Nodes
                    .Select(ConvertNodeViewModelToData)
                    .ToList();
            }

            // 转换连接数据
            if (canvasViewModel.Connections != null)
            {
                projectFile.CanvasData.Connections = canvasViewModel.Connections
                    .Select(ConvertConnectionViewModelToData)
                    .ToList();
            }

            // 保存画布视图状态
            projectFile.CanvasData.Settings.OffsetX = canvasViewModel.ViewportCenter.X;
            projectFile.CanvasData.Settings.OffsetY = canvasViewModel.ViewportCenter.Y;
            projectFile.CanvasData.Settings.ZoomLevel = canvasViewModel.ViewportZoom;

            return projectFile;
        }

        /// <summary>
        /// 从项目文件恢复画布状态
        /// </summary>
        /// <param name="adapter">项目文件适配器</param>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="canvasViewModel">画布视图模型</param>
        /// <param name="logger">日志记录器（可选）</param>
        public static void RestoreCanvasFromProjectFile(
            this IProjectFileAdapter adapter,
            ProjectFile projectFile,
            CanvasViewModel canvasViewModel,
            ILogger? logger = null)
        {
            if (projectFile.CanvasData == null)
            {
                logger?.LogWarning("项目文件中没有画布数据");
                return;
            }

            // 清空现有数据
            canvasViewModel.Nodes.Clear();
            canvasViewModel.Connections.Clear();

            // 恢复节点
            if (projectFile.CanvasData.Nodes != null)
            {
                foreach (var nodeData in projectFile.CanvasData.Nodes)
                {
                    var nodeViewModel = ConvertNodeDataToViewModel(nodeData);
                    canvasViewModel.Nodes.Add(nodeViewModel);
                }
            }

            // 恢复连接
            if (projectFile.CanvasData.Connections != null)
            {
                foreach (var connectionData in projectFile.CanvasData.Connections)
                {
                    var connectionViewModel = ConvertConnectionDataToViewModel(connectionData, canvasViewModel.Nodes);
                    if (connectionViewModel != null)
                    {
                        canvasViewModel.Connections.Add(connectionViewModel);
                    }
                }
            }

            // 恢复视图状态
            canvasViewModel.ViewportCenter = new System.Windows.Point(
                projectFile.CanvasData.Settings.OffsetX,
                projectFile.CanvasData.Settings.OffsetY);
            canvasViewModel.ViewportZoom = projectFile.CanvasData.Settings.ZoomLevel;
        }

        /// <summary>
        /// 将节点ViewModel转换为数据对象
        /// </summary>
        private static NodeData ConvertNodeViewModelToData(ModuleNodeViewModel nodeViewModel)
        {
            return new NodeData
            {
                Id = nodeViewModel.Module?.Id ?? Guid.NewGuid().ToString(),
                Title = nodeViewModel.Title ?? string.Empty,
                Type = nodeViewModel.Module?.Type ?? ModuleType.ManualDataInput,
                X = nodeViewModel.Location.X,
                Y = nodeViewModel.Location.Y,
                IsExpanded = nodeViewModel.IsExpanded,
                IsEnabled = nodeViewModel.IsEnabled,
                PropertyValues = nodeViewModel.Module?.Parameters?.ToDictionary(p => p.Key, p => (object?)p.Value) ?? new Dictionary<string, object?>()
            };
        }

        /// <summary>
        /// 将连接ViewModel转换为数据对象
        /// </summary>
        private static ConnectionData ConvertConnectionViewModelToData(ConnectionViewModel connectionViewModel)
        {
            return new ConnectionData
            {
                Id = connectionViewModel.Id.ToString(),
                SourceNodeId = (connectionViewModel.Source?.Node as ModuleNodeViewModel)?.Module?.Id ?? string.Empty,
                SourceConnectorId = connectionViewModel.Source?.Id.ToString() ?? string.Empty,
                TargetNodeId = (connectionViewModel.Target?.Node as ModuleNodeViewModel)?.Module?.Id ?? string.Empty,
                TargetConnectorId = connectionViewModel.Target?.Id.ToString() ?? string.Empty
            };
        }

        /// <summary>
        /// 将节点数据转换为ViewModel
        /// </summary>
        private static ModuleNodeViewModel ConvertNodeDataToViewModel(NodeData nodeData)
        {
            var nodeViewModel = new ModuleNodeViewModel
            {
                Location = new System.Windows.Point(nodeData.X, nodeData.Y),
                IsExpanded = nodeData.IsExpanded,
                IsEnabled = nodeData.IsEnabled
            };

            // 创建模块模型
            var module = new ModuleModel
            {
                Id = nodeData.Id,
                Name = nodeData.Title, // 设置模块名称为节点标题
                Type = nodeData.Type,
                Parameters = nodeData.PropertyValues?.Where(p => p.Value != null).ToDictionary(p => p.Key, p => p.Value!) ?? new Dictionary<string, object>()
            };

            // 先设置模块，再设置标题（这样Title不会被Module.Name覆盖）
            nodeViewModel.Module = module;
            nodeViewModel.Title = nodeData.Title;

            return nodeViewModel;
        }

        /// <summary>
        /// 将连接数据转换为ViewModel
        /// </summary>
        private static ConnectionViewModel? ConvertConnectionDataToViewModel(
            ConnectionData connectionData,
            IEnumerable<ModuleNodeViewModel> nodes)
        {
            var sourceNode = nodes.FirstOrDefault(n => n.Module?.Id == connectionData.SourceNodeId);
            var targetNode = nodes.FirstOrDefault(n => n.Module?.Id == connectionData.TargetNodeId);

            if (sourceNode == null || targetNode == null)
                return null;

            var sourceConnector = sourceNode.Outputs.FirstOrDefault(c => c.Id == connectionData.SourceConnectorId);
            var targetConnector = targetNode.Inputs.FirstOrDefault(c => c.Id == connectionData.TargetConnectorId);

            if (sourceConnector == null || targetConnector == null)
                return null;

            return new ConnectionViewModel
            {
                Id = connectionData.Id,
                Source = sourceConnector,
                Target = targetConnector
            };
        }
    }
}
