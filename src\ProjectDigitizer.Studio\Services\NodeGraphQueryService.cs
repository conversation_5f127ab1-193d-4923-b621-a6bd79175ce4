using System;
using System.Collections.Generic;
using System.Linq;

using ProjectDigitizer.Application.Interfaces.UI;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Services
{
    internal sealed class NodeGraphQueryService : INodeGraphQuery
    {
        public IReadOnlyList<GraphSourceNode> GetUpstreamNodes(string currentNodeId)
        {
            var list = new List<GraphSourceNode>();
            var canvas = TryGetCanvas();
            if (canvas == null) return list;

            var current = canvas.Nodes?.FirstOrDefault(n => n.Module?.Id == currentNodeId);
            if (current == null) return list;

            var sources = canvas.Connections
                .Where(c => (c.Target?.Node as ModuleNodeViewModel) == current)
                .Select(c => c.Source?.Node as ModuleNodeViewModel)
                .Where(n => n != null)!
                .Distinct()!
                .ToList()!;

            foreach (var n in sources!)
            {
                if (n?.Module == null) continue;
                list.Add(new GraphSourceNode
                {
                    Id = n.Module.Id,
                    Title = n.Title,
                    ModuleType = n.Module.Type,
                    OutputCount = n.Outputs?.Count ?? 0,
                    IsConnected = true
                });
            }

            return list;
        }

        public IReadOnlyList<GraphNodeProperty> GetNodeProperties(string nodeId)
        {
            var list = new List<GraphNodeProperty>();
            var canvas = TryGetCanvas();
            var node = canvas?.Nodes?.FirstOrDefault(n => n.Module?.Id == nodeId);
            var mt = node?.Module?.Type ?? ModuleType.Other;

            // 简单映射 + 回退
            switch (mt)
            {
                case ModuleType.PiConstant:
                case ModuleType.EConstant:
                case ModuleType.GoldenRatioConstant:
                    list.Add(new GraphNodeProperty { Name = "Value", Type = "Number", Description = "常数值" });
                    break;
                case ModuleType.DataCalculation:
                    list.Add(new GraphNodeProperty { Name = "Result", Type = "Any", Description = "计算结果" });
                    break;
                default:
                    list.Add(new GraphNodeProperty { Name = "Output", Type = "Any", Description = "默认输出" });
                    break;
            }

            return list;
        }

        public (bool isAvailable, object? value) TryGetRuntimeValue(string nodeId, string propertyName)
        {
            try
            {
                var canvas = TryGetCanvas();
                var node = canvas?.Nodes?.FirstOrDefault(n => n.Module?.Id == nodeId);
                if (node != null)
                {
                    var upstream = App.GetOptionalService<IUpstreamResultProvider>();
                    if (upstream != null)
                    {
                        var (ok, val) = upstream.TryGetResult(node, propertyName);
                        if (ok) return (true, val);
                    }

                    // 回退：NodeProperties 和 Parameters
                    var preferred = new[] { propertyName, "Result", "Value", "InputValue", "Output" };
                    foreach (var p in preferred.Where(p => !string.IsNullOrEmpty(p)))
                    {
                        var fromProps = node.NodeProperties?.GetValue(p);
                        if (fromProps != null) return (true, fromProps);
                        if (node.Module?.Parameters != null && node.Module.Parameters.TryGetValue(p, out var pv))
                            return (true, pv);
                    }
                }
            }
            catch { }
            return (false, null);
        }

        private static CanvasViewModel? TryGetCanvas()
        {
            try
            {
                foreach (System.Windows.Window w in System.Windows.Application.Current.Windows)
                {
                    if (w is ProjectDigitizer.Studio.Views.MainWindow mw)
                        return mw.DataContext as CanvasViewModel;
                }
            }
            catch { }
            return null;
        }
    }
}

