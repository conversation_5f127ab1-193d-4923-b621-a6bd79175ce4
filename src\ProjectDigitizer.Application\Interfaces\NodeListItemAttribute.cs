using System;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// Optional attribute a template class can use to describe how it appears in the palette.
/// If both interface and attribute are present, interface values take precedence.
/// </summary>
[AttributeUsage(AttributeTargets.Class, Inherited = false, AllowMultiple = false)]
public sealed class NodeListItemAttribute : Attribute
{
    public string Group { get; init; } = string.Empty;
    public int Order { get; init; } = 0;
    public string? IconPath { get; init; }
    public string? DisplayName { get; init; }
    public string? Description { get; init; }
    public string[] Tags { get; init; } = Array.Empty<string>();
}

