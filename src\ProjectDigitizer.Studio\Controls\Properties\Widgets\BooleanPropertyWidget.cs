using System;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Studio.Controls.Properties.Widgets
{
    /// <summary>
    /// 布尔值属性编辑器
    /// </summary>
    public class BooleanPropertyWidget : IPropertyWidget
    {
        private readonly CheckBox _checkBox;
        private PropertyDefinition _propertyDefinition = new();
        private object? _value;

        public BooleanPropertyWidget()
        {
            _checkBox = new CheckBox
            {
                Margin = new Thickness(0, 8, 0, 16),
                FontSize = 14,
                VerticalAlignment = VerticalAlignment.Center
            };

            try
            {
                {
                }
            }
            catch (Exception ex)
            {
            }

            _checkBox.Checked += OnCheckedChanged;
            _checkBox.Unchecked += OnCheckedChanged;
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    UpdateCheckState();
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _checkBox.IsEnabled;
            set => _checkBox.IsEnabled = value;
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Core.ValueObjects.ValidationResult Validate()
        {
            // 布尔值通常不需要特殊验证
            return new Core.ValueObjects.ValidationResult();
        }

        public FrameworkElement GetElement()
        {
            return _checkBox;
        }

        private void UpdateUI()
        {
            // 设置复选框内容
            _checkBox.Content = PropertyDefinition.Title;

            // 设置默认值
            if (PropertyDefinition.DefaultValue != null)
            {
                Value = PropertyDefinition.DefaultValue;
            }

            // 设置工具提示
            if (!string.IsNullOrEmpty(PropertyDefinition.Description))
            {
                _checkBox.ToolTip = PropertyDefinition.Description;
            }
        }

        private void UpdateCheckState()
        {
            if (_value is bool boolValue)
            {
                _checkBox.IsChecked = boolValue;
            }
            else
            {
                _checkBox.IsChecked = false;
            }
        }

        private void OnCheckedChanged(object sender, RoutedEventArgs e)
        {
            var oldValue = _value;
            _value = _checkBox.IsChecked == true;

            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, oldValue, _value));
        }
    }
}



