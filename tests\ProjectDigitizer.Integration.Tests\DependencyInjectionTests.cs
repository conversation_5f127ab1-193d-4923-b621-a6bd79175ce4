using System;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Core;
using ProjectDigitizer.Infrastructure;
using ProjectDigitizer.Infrastructure.Services;

namespace ProjectDigitizer.Integration.Tests;

/// <summary>
/// 依赖注入配置集成测试
/// </summary>
public class DependencyInjectionTests : IDisposable
{
    private readonly IHost _host;
    private readonly IServiceProvider _serviceProvider;

    public DependencyInjectionTests()
    {
        // 构建测试配置
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Logging:LogLevel:Default"] = "Information",
                ["Application:Name"] = "Test Application",
                ["Application:Version"] = "1.0.0-test",
                ["Application:Environment"] = "Test"
            })
            .Build();

        // 构建测试主机
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册配置
                services.AddSingleton<IConfiguration>(configuration);

                // 注册各层服务
                services.AddCore();
                services.AddApplication();
                services.AddInfrastructure(configuration);

                // 添加测试专用的日志记录
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
                });
            })
            .Build();

        _serviceProvider = _host.Services;
    }

    [Fact]
    public void ServiceProvider_Should_BeConfigured()
    {
        // Assert
        _serviceProvider.Should().NotBeNull();
    }

    [Fact]
    public void Configuration_Should_BeRegistered()
    {
        // Act
        var configuration = _serviceProvider.GetService<IConfiguration>();

        // Assert
        configuration.Should().NotBeNull();
        configuration!["Application:Name"].Should().Be("Test Application");
        configuration["Application:Version"].Should().Be("1.0.0-test");
    }

    [Fact]
    public void Logger_Should_BeRegistered()
    {
        // Act
        var logger = _serviceProvider.GetService<ILogger<DependencyInjectionTests>>();

        // Assert
        logger.Should().NotBeNull();
    }

    [Fact]
    public void ApplicationServices_Should_BeRegistered()
    {
        // Act
        var projectFileService = _serviceProvider.GetService<ProjectFileService>();

        // Assert
        projectFileService.Should().NotBeNull();
    }

    [Fact]
    public void InfrastructureServices_Should_BeRegistered()
    {
        // Act
        var projectService = _serviceProvider.GetService<IProjectService>();
        var canvasService = _serviceProvider.GetService<ICanvasService>();
        var fileService = _serviceProvider.GetService<FileService>();

        // Assert
        projectService.Should().NotBeNull();
        canvasService.Should().NotBeNull();
        fileService.Should().NotBeNull();
    }

    [Fact]
    public void ProjectFileService_Should_BeCreatedWithDependencies()
    {
        // Act
        var service = _serviceProvider.GetRequiredService<ProjectFileService>();

        // Assert
        service.Should().NotBeNull();

        // 测试服务功能
        var projectFile = service.CreateNewProject("集成测试项目", "这是一个集成测试项目");
        projectFile.Should().NotBeNull();
        projectFile.ProjectInfo.Name.Should().Be("集成测试项目");
        projectFile.ProjectInfo.Description.Should().Be("这是一个集成测试项目");
    }

    // FunctionRegistry测试暂时注释掉，因为依赖还没有完全实现
    // [Fact]
    // public void FunctionRegistry_Should_BeCreatedWithDependencies()
    // {
    //     // Act
    //     var registry = _serviceProvider.GetRequiredService<FunctionRegistry>();

    //     // Assert
    //     registry.Should().NotBeNull();

    //     // 测试服务功能
    //     var categories = registry.GetCategories();
    //     categories.Should().NotBeNull();
    // }

    [Fact]
    public void ProjectService_Should_BeCreatedWithDependencies()
    {
        // Act
        var service = _serviceProvider.GetRequiredService<IProjectService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<ProjectService>();
    }

    [Fact]
    public void CanvasService_Should_BeCreatedWithDependencies()
    {
        // Act
        var service = _serviceProvider.GetRequiredService<ICanvasService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<CanvasService>();
    }

    [Fact]
    public void FileService_Should_BeCreatedWithDependencies()
    {
        // Act
        var service = _serviceProvider.GetRequiredService<FileService>();

        // Assert
        service.Should().NotBeNull();
    }

    [Fact]
    public void Services_Should_HaveCorrectLifetime()
    {
        // Act - 获取两次相同的服务
        var projectFileService1 = _serviceProvider.GetRequiredService<ProjectFileService>();
        var projectFileService2 = _serviceProvider.GetRequiredService<ProjectFileService>();

        var projectService1 = _serviceProvider.GetRequiredService<IProjectService>();
        var projectService2 = _serviceProvider.GetRequiredService<IProjectService>();

        // Assert - 应用层服务应该是Scoped，每次获取应该是不同的实例（在同一作用域内是相同的）
        using (var scope1 = _serviceProvider.CreateScope())
        using (var scope2 = _serviceProvider.CreateScope())
        {
            var scopedService1 = scope1.ServiceProvider.GetRequiredService<ProjectFileService>();
            var scopedService2 = scope1.ServiceProvider.GetRequiredService<ProjectFileService>();
            var scopedService3 = scope2.ServiceProvider.GetRequiredService<ProjectFileService>();

            // 同一作用域内应该是相同实例
            scopedService1.Should().BeSameAs(scopedService2);
            // 不同作用域应该是不同实例
            scopedService1.Should().NotBeSameAs(scopedService3);
        }
    }

    [Fact]
    public void AllRegisteredServices_Should_BeResolvable()
    {
        // Arrange
        var serviceTypes = new[]
        {
            typeof(IConfiguration),
            typeof(ILogger<DependencyInjectionTests>),
            typeof(ProjectFileService),
            // typeof(FunctionRegistry), // 暂时注释掉
            typeof(IProjectService),
            typeof(ICanvasService),
            typeof(FileService)
        };

        // Act & Assert
        foreach (var serviceType in serviceTypes)
        {
            var service = _serviceProvider.GetService(serviceType);
            service.Should().NotBeNull($"Service {serviceType.Name} should be resolvable");
        }
    }

    [Fact]
    public void ServiceResolution_Should_NotThrowExceptions()
    {
        // Act & Assert - 尝试解析所有注册的服务，不应该抛出异常
        var action = () =>
        {
            using var scope = _serviceProvider.CreateScope();
            var projectFileService = scope.ServiceProvider.GetRequiredService<ProjectFileService>();
            // var functionRegistry = scope.ServiceProvider.GetRequiredService<FunctionRegistry>(); // 暂时注释掉
            var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
            var canvasService = scope.ServiceProvider.GetRequiredService<ICanvasService>();
            var fileService = scope.ServiceProvider.GetRequiredService<FileService>();
        };

        action.Should().NotThrow();
    }

    public void Dispose()
    {
        _host?.Dispose();
    }
}
