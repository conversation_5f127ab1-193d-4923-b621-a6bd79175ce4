namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 外部服务客户端接口
/// 提供与外部API和服务集成的统一接口
/// </summary>
public interface IExternalServiceClient : IApplicationService
{
    /// <summary>
    /// 发送HTTP GET请求
    /// </summary>
    /// <typeparam name="T">响应类型</typeparam>
    /// <param name="endpoint">端点URL</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 发送HTTP POST请求
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="endpoint">端点URL</param>
    /// <param name="request">请求数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class;

    /// <summary>
    /// 发送HTTP PUT请求
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="endpoint">端点URL</param>
    /// <param name="request">请求数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    Task<TResponse?> PutAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class;

    /// <summary>
    /// 发送HTTP DELETE请求
    /// </summary>
    /// <param name="endpoint">端点URL</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> DeleteAsync(string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    Task<ServiceHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 服务健康状态
/// </summary>
public class ServiceHealthStatus
{
    public bool IsHealthy { get; set; }
    public string? Message { get; set; }
    public DateTime CheckTime { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}
