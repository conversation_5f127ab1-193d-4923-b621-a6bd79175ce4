using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

using MahApps.Metro.IconPacks;

namespace Plugins.DataCalculation
{
    /// <summary>
    /// 将执行与预览逻辑拆分到局部类，保持易维护。
    /// </summary>
    public partial class DataCalculationComponent
    {
        /// <summary>
        /// 处理函数项内按钮点击（目前支持“播放”图标触发预览）。
        /// </summary>
        private void OnFunctionsItemButtonClick(object? sender, RoutedEventArgs e)
        {
            if (e.OriginalSource is not DependencyObject source)
                return;

            var button = FindAncestor<Button>(source);
            if (button == null)
                return;

            if (button.Tag != null)
                return;

            if (button.Content is PackIconMaterial icon && icon.Kind == PackIconMaterialKind.Play)
            {
                if (button.DataContext is FunctionDefinition func)
                {
                    ExecuteFunction(func);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 执行单个函数的预览求值。
        /// </summary>
        private void ExecuteFunction(FunctionDefinition func)
        {
            if (string.IsNullOrWhiteSpace(func.Expression))
            {
                func.ValidationError = "表达式不能为空";
                func.Result = string.Empty;
                return;
            }

            if (!ValidateExpression(func.Expression))
            {
                func.ValidationError = "表达式格式不正确（括号不匹配）";
                func.Result = string.Empty;
                return;
            }

            func.ValidationError = string.Empty;

            var exprWithVars = ReplaceVariablesWithValues(func.Expression);
            if (ProjectDigitizer.Studio.Services.ExcelLikeFormulaEvaluator.TryEvaluate(exprWithVars, out var excelResult))
            {
                func.Result = excelResult;
                return;
            }

            // Fallback to CalcEngine for complex expressions (e.g., PI, POWER, EXP, LN, nested functions)
            try
            {
                var value = Services.CalcEngineFacade.Evaluate(exprWithVars, new System.Collections.Generic.Dictionary<string, object>());
                func.Result = value is IFormattable f
                    ? f.ToString(null, CultureInfo.InvariantCulture)
                    : Convert.ToString(value, CultureInfo.InvariantCulture) ?? string.Empty;
                return;
            }
            catch { }

            if (TryEvaluateBasic(func.Expression, out var preview))
            {
                func.Result = preview;
            }
            else
            {
                func.Result = "校验通过（无法预览复杂表达式）";
            }
        }

        /// <summary>
        /// 将表达式中的 {变量} 替换为具体值（字符串加引号，数字按 InvariantCulture）。
        /// </summary>
        private string ReplaceVariablesWithValues(string expression)
        {
            if (string.IsNullOrEmpty(expression)) return string.Empty;
            if (Variables.Count == 0) return expression;

            var dict = new Dictionary<string, object?>(StringComparer.OrdinalIgnoreCase);
            foreach (var v in Variables)
            {
                if (string.IsNullOrWhiteSpace(v.VariableName)) continue;
                var value = GetValueFromSourceNode(v);
                dict[v.VariableName] = value;
            }

            var sb = new System.Text.StringBuilder(expression.Length);
            bool inBrace = false;
            var nameSb = new System.Text.StringBuilder();
            foreach (var ch in expression)
            {
                if (ch == '{')
                {
                    inBrace = true; nameSb.Clear(); continue;
                }
                if (ch == '}')
                {
                    inBrace = false;
                    var varName = nameSb.ToString();
                    if (dict.TryGetValue(varName, out var val) && val != null)
                    {
                        if (val is string s)
                            sb.Append('"').Append(s.Replace("\"", "\"\"")).Append('"');
                        else
                            sb.Append(Convert.ToString(val, System.Globalization.CultureInfo.InvariantCulture));
                    }
                    else
                    {
                        sb.Append('0');
                    }
                    continue;
                }
                if (inBrace) nameSb.Append(ch); else sb.Append(ch);
            }
            if (inBrace) sb.Append('{').Append(nameSb.ToString());
            return sb.ToString();
        }

        /// <summary>
        /// 从绑定的上游源节点获取变量的值（优先运行时结果，其次节点属性，再次参数字典）。
        /// </summary>
        private object? GetValueFromSourceNode(VariableDefinition v)
        {
            try
            {
                var propName = v.SelectedProperty?.PropertyName ?? string.Empty;

                var upstream = ProjectDigitizer.Studio.App.GetOptionalService<ProjectDigitizer.Studio.Services.IUpstreamResultProvider>();
                if (upstream != null && v.SelectedSourceNode != null)
                {
                    var srcNode = FindSourceNodeById(v.SelectedSourceNode.NodeId ?? "");
                    if (srcNode != null)
                    {
                        var runtime = upstream.TryGetResult(srcNode, propName);
                        if (runtime.isAvailable)
                            return runtime.value;
                    }
                }

                var node = FindSourceNodeById(v.SelectedSourceNode?.NodeId ?? "");
                if (node?.NodeProperties != null)
                {
                    var preferred = new[] { propName, "Result", "Value", "InputValue", "Output" };
                    foreach (var p in preferred.Where(p => !string.IsNullOrEmpty(p)))
                    {
                        var candidate = node.NodeProperties.GetValue(p);
                        if (candidate != null)
                            return candidate;
                    }
                }

                var valFromParams = node?.Module?.Parameters?.GetValueOrDefault(propName ?? "");
                return valFromParams;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 通过节点 Id 在画布中查找源节点。
        /// </summary>
        private ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel? FindSourceNodeById(string nodeId)
        {
            if (string.IsNullOrEmpty(nodeId)) return null;
            var canvas = GetCanvasViewModel();
            return canvas?.Nodes?.FirstOrDefault(n => n.Module?.Id == nodeId);
        }

        /// <summary>
        /// 尝试使用 DataTable.Compute 对简单算术表达式进行估值（含数字与算符）。
        /// </summary>
        private static bool TryEvaluateBasic(string expression, out string result)
        {
            string expr = ReplacePlaceholdersWithZero(expression);
            if (expr.Any(char.IsLetter))
            {
                result = string.Empty; return false;
            }
            try
            {
                var table = new DataTable();
                var value = table.Compute(expr, null);
                result = Convert.ToString(value) ?? string.Empty; return true;
            }
            catch { result = string.Empty; return false; }
        }

        /// <summary>
        /// 将 {...} 占位替换为 0，便于基础算术预览。
        /// </summary>
        private static string ReplacePlaceholdersWithZero(string expression)
        {
            if (string.IsNullOrEmpty(expression)) return string.Empty;
            var sb = new System.Text.StringBuilder(expression.Length);
            bool inBrace = false;
            foreach (var ch in expression)
            {
                if (ch == '{') { inBrace = true; sb.Append('0'); }
                else if (ch == '}') { inBrace = false; }
                else if (!inBrace) { sb.Append(ch); }
            }
            return sb.ToString();
        }

        /// <summary>
        /// 获取当前主窗口中的画布 ViewModel。
        /// </summary>
        private ProjectDigitizer.Studio.ViewModels.CanvasViewModel? GetCanvasViewModel()
        {
            try
            {
                foreach (Window w in Application.Current.Windows)
                {
                    if (w is ProjectDigitizer.Studio.Views.MainWindow mw)
                        return mw.DataContext as ProjectDigitizer.Studio.ViewModels.CanvasViewModel;
                }
            }
            catch { }
            return null;
        }

        /// <summary>
        /// 获取所有连到当前节点的上游源节点。
        /// </summary>
        private IEnumerable<ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel> GetConnectedSourceNodes(
            ProjectDigitizer.Studio.ViewModels.CanvasViewModel canvas,
            ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel current)
        {
            try
            {
                return canvas.Connections
                    .Where(c => (c.Target?.Node as ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel) == current)
                    .Select(c => c.Source?.Node as ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel)
                    .Where(n => n != null)!
                    .ToList()!;
            }
            catch { return Enumerable.Empty<ProjectDigitizer.Studio.ViewModels.ModuleNodeViewModel>(); }
        }

        /// <summary>
        /// 在可视树上向上查找最近的指定类型元素。
        /// </summary>
        private static T? FindAncestor<T>(DependencyObject? current) where T : DependencyObject
        {
            while (current != null)
            {
                if (current is T t) return t;
                current = VisualTreeHelper.GetParent(current);
            }
            return null;
        }
    }
}


