<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========== 颜色资源和渐变画刷 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的所有颜色相关资源 -->

    <!-- 连接点颜色资源 -->
    <!-- 输入连接点颜色 -->
    <SolidColorBrush x:Key="InputConnectionBrush" Color="{StaticResource Color.Success}"/>
    <SolidColorBrush x:Key="InputConnectionAccentBrush" Color="{StaticResource Color.Success}"/>

    <!-- 输出连接点颜色 -->
    <SolidColorBrush x:Key="OutputConnectionBrush" Color="{StaticResource Color.Primary}"/>
    <SolidColorBrush x:Key="OutputConnectionAccentBrush" Color="{StaticResource Color.PrimaryVariant}"/>

    <!-- 连接点边框颜色 -->
    <SolidColorBrush x:Key="ConnectionPointBorderBrush" Color="{StaticResource Color.Surface}"/>

    <!-- 数据节点连接点颜色 -->
    <SolidColorBrush x:Key="DataNodeInputConnectionBrush" Color="{StaticResource Color.Success}"/>
    <SolidColorBrush x:Key="DataNodeOutputConnectionBrush" Color="{StaticResource Color.Primary}"/>

    <!-- 连接器类型的颜色映射到统一主题画刷 -->
    <SolidColorBrush x:Key="Connector.Number.Brush" Color="{StaticResource Color.Warning}"/>
    <SolidColorBrush x:Key="Connector.Text.Brush" Color="{StaticResource Color.Secondary}"/>
    <SolidColorBrush x:Key="Connector.Boolean.Brush" Color="{StaticResource Color.Danger}"/>
    <SolidColorBrush x:Key="Connector.File.Brush" Color="{StaticResource Color.Success}"/>
    <SolidColorBrush x:Key="Connector.Geometry.Brush" Color="{StaticResource Color.Secondary}"/>
    <SolidColorBrush x:Key="Connector.Control.Brush" Color="{StaticResource Color.PrimaryVariant}"/>

    <!-- 现代化渐变画刷 -->
    <LinearGradientBrush x:Key="ModernHeaderGradientBlue" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#2196F3" Offset="0"/>
        <GradientStop Color="#1976D2" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientGreen" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#4CAF50" Offset="0"/>
        <GradientStop Color="#388E3C" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientOrange" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FF9800" Offset="0"/>
        <GradientStop Color="#F57C00" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientPurple" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#9C27B0" Offset="0"/>
        <GradientStop Color="#7B1FA2" Offset="1"/>
    </LinearGradientBrush>

    <!-- 示例图片样式的深色渐变 -->
    <LinearGradientBrush x:Key="ModernHeaderGradientGray" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#616161" Offset="0"/>
        <GradientStop Color="#424242" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientDarkPurple" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#7B1FA2" Offset="0"/>
        <GradientStop Color="#7B1FA2" Offset="1"/>
    </LinearGradientBrush>

    <!-- 特殊节点类型渐变 - 基于草图设计 -->
    <LinearGradientBrush x:Key="ModernHeaderGradientCyan" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#00BCD4" Offset="0"/>
        <GradientStop Color="#0097A7" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientIndigo" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#3F51B5" Offset="0"/>
        <GradientStop Color="#303F9F" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ModernHeaderGradientPink" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E91E63" Offset="0"/>
        <GradientStop Color="#C2185B" Offset="1"/>
    </LinearGradientBrush>

</ResourceDictionary>
