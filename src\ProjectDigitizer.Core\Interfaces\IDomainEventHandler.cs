namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 领域事件处理器接口
/// </summary>
/// <typeparam name="T">领域事件类型</typeparam>
public interface IDomainEventHandler<in T> where T : IDomainEvent
{
    /// <summary>
    /// 处理领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task HandleAsync(T domainEvent, CancellationToken cancellationToken = default);
}

/// <summary>
/// 领域事件基接口
/// </summary>
public interface IDomainEvent
{
    /// <summary>
    /// 事件发生时间
    /// </summary>
    DateTime OccurredOn { get; }

    /// <summary>
    /// 事件ID
    /// </summary>
    Guid EventId { get; }
}

/// <summary>
/// 领域事件发布器接口
/// </summary>
public interface IDomainEventPublisher
{
    /// <summary>
    /// 发布领域事件
    /// </summary>
    /// <param name="domainEvent">领域事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task PublishAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量发布领域事件
    /// </summary>
    /// <param name="domainEvents">领域事件集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task PublishAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default);
}
