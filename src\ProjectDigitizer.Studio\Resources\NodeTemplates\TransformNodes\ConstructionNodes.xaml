<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 施工相关转换节点模板集合 ==========  -->
    <!--  用于 Excavation、Demolition、AntiCorrosion、LightningProtection 等施工相关节点  -->

    <!--  通用施工节点内容模板  -->
    <DataTemplate x:Key="ConstructionNodeContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  施工图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="Shovel" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                <Setter Property="Kind" Value="Shovel" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Demolition}">
                                                <Setter Property="Kind" Value="Hammer" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                <Setter Property="Kind" Value="ShieldCheck" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                <Setter Property="Kind" Value="Flash" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：施工类型信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  施工类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="#FF5722" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                    <Setter Property="Background" Value="#8D6E63" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Demolition}">
                                                    <Setter Property="Background" Value="#FF5722" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                    <Setter Property="Background" Value="#607D8B" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                    <Setter Property="Background" Value="#FFC107" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="施工" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                        <Setter Property="Text" Value="开挖" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Demolition}">
                                                        <Setter Property="Text" Value="破除" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                        <Setter Property="Text" Value="防腐" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                        <Setter Property="Text" Value="防雷" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    MaxWidth="100"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.WorkType, FallbackValue='标准作业'}"
                                    TextTrimming="CharacterEllipsis"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  工艺配置按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="工艺配置">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Cog"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="施工状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="InProgress">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Background" Value="#FFF3E0" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                    <Setter Property="Background" Value="#EFEBE9" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Demolition}">
                                    <Setter Property="Background" Value="#FFF3E0" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                    <Setter Property="Background" Value="#ECEFF1" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                    <Setter Property="Background" Value="#FFFDE7" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <StackPanel>
                        <!--  施工方法  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="方法:"
                                VerticalAlignment="Center" />

                            <Border
                                CornerRadius="3"
                                Grid.Column="1"
                                HorizontalAlignment="Left"
                                Padding="6,2">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="Background" Value="#FF5722" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                <Setter Property="Background" Value="#8D6E63" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                <Setter Property="Background" Value="#607D8B" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                <Setter Property="Background" Value="#FFC107" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock
                                    FontSize="9"
                                    FontWeight="Medium"
                                    Foreground="White">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.Method, FallbackValue='机械施工'}" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.CoatingType, FallbackValue='环氧涂层'}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                    <Setter Property="Text" Value="{Binding NodeProperties.ProtectionType, FallbackValue='接地保护'}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </Border>
                        </Grid>

                        <!--  规格/标准  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="标准:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.Standard, FallbackValue='GB/T 50028'}"
                                TextTrimming="CharacterEllipsis"
                                VerticalAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#FF5722" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                <Setter Property="Foreground" Value="#8D6E63" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                <Setter Property="Foreground" Value="#607D8B" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                <Setter Property="Foreground" Value="#FFC107" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  工程量  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="工程量" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    HorizontalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="{Binding NodeProperties.Quantity, FallbackValue=0, StringFormat={}{0}m³}" />
                                            <Setter Property="Foreground" Value="#FF5722" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                    <Setter Property="Foreground" Value="#8D6E63" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                    <Setter Property="Foreground" Value="#607D8B" />
                                                    <Setter Property="Text" Value="{Binding NodeProperties.Area, FallbackValue=0, StringFormat={}{0}m²}" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                    <Setter Property="Foreground" Value="#FFC107" />
                                                    <Setter Property="Text" Value="{Binding NodeProperties.Points, FallbackValue=0, StringFormat={}{0}点}" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  工期  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="工期" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.Duration, FallbackValue=1, StringFormat={}{0}天}">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="#FF5722" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.Excavation}">
                                                    <Setter Property="Foreground" Value="#8D6E63" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.AntiCorrosion}">
                                                    <Setter Property="Foreground" Value="#607D8B" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.LightningProtection}">
                                                    <Setter Property="Foreground" Value="#FFC107" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  开挖回填节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ExcavationNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ConstructionNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  破除恢复节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="DemolitionNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ConstructionNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  防腐节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="AntiCorrosionNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ConstructionNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  防雷防静电节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="LightningProtectionNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource ConstructionNodeContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
