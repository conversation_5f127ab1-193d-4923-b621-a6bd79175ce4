<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 应用 Shell 的主窗体样式（命名样式，避免影响第三方窗体） -->
    <Style x:Key="Shell.Window" TargetType="Window">
        <Setter Property="FontFamily" Value="{StaticResource Font.App}"/>
        <Setter Property="Background" Value="{StaticResource Brush.SurfaceVariant}"/>
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <!-- 注意：WindowStartupLocation、ResizeMode 不是依赖属性，不能通过 Setter 设置 -->
    </Style>

    <!-- 对话框 Window 样式（圆角、内边距、阴影） -->
    <Style x:Key="Dialog.Window" TargetType="Window" BasedOn="{StaticResource {x:Type Window}}">
        <Setter Property="Width" Value="640"/>
        <Setter Property="Height" Value="520"/>
        <!-- 注意：WindowStartupLocation、ResizeMode 不是依赖属性，不能通过 Setter 设置 -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Window">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{StaticResource Brush.Border}"
                            BorderThickness="{StaticResource Border.Thin}"
                            CornerRadius="{StaticResource Radius.L}">
                        <AdornerDecorator>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <!-- 标题栏 -->
                                <DockPanel Grid.Row="0" Background="{StaticResource Brush.Surface}" Height="44">
                                    <TextBlock Text="{TemplateBinding Title}" Margin="16,0" VerticalAlignment="Center" FontWeight="SemiBold"/>
                                </DockPanel>
                                <!-- 内容区 -->
                                <ContentPresenter Grid.Row="1" Margin="16"/>
                            </Grid>
                        </AdornerDecorator>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>

