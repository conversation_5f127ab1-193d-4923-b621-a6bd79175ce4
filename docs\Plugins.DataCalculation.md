% Plugins.DataCalculation 节点完善方案（含现状与规划）

本文档面向 ProjectDigitizer 的“数据计算”插件（Plugins.DataCalculation），综合描述当前实现与规划路线，覆盖 UI/UX、处理器能力、数据模型、集成与测试。实现遵循本仓库的分层架构与风格规范，尽量在不破坏既有行为的基础上迭代。

## 现状与总体
- 节点注册：模板/模块/处理器已在 `INodeRegistry` 注册，节点可创建与执行。
- UI 能力：提供变量管理、函数列表、表达式编辑与预览（AvalonEdit），带基础校验与高亮；可将函数摘要写入节点展开区以便浏览。
- 执行能力：处理器当前按“单一表达式”执行（来源于 `TransformNodeProperties.Expression`）。若表达式为空，则对输入数据进行递归求和作为保底模式。
- 属性面板与资源：通过 `IPropertyPanelProvider` 返回资源键 `PropertyPanel.ModuleType.DataCalculation`，在 `Resources/PluginResources.xaml` 映射为 `DataCalculationPanel`；同时提供新 Inspector 组件 `DataCalculationComponent`（逐步迁移中）。
- 端口定义：输入端口 `in`（number，可多连）；输出端口 `result`（number）。
- 参数持久化：UI 侧暂以扁平键（`function_*`、`variable_*` 等）写入 `Module.Parameters` 用于展示与预览；处理器当前仅消费节点属性 `Expression`，未消费这些参数。
- 关键代码位置：
  - 模块与注册：`src/Plugins.DataCalculation/DataCalculationModule.cs`
  - 模板：`src/Plugins.DataCalculation/DataCalculationTemplate.cs`
  - 处理器（当前实现）：`src/Plugins.DataCalculation/DataCalculationProcessor.cs`
  - 属性面板 Provider：`src/Plugins.DataCalculation/DataCalculationPropertyPanelProvider.cs`
  - 插件资源：`src/Plugins.DataCalculation/Resources/PluginResources.xaml`
  - 属性面板/Inspector：`src/Plugins.DataCalculation/Controls/Inspector/DataCalculationPanel.xaml(.cs)`、`src/Plugins.DataCalculation/Controls/Inspector/Components/DataCalculationComponent.*`
  - 公式编辑器：`src/Plugins.DataCalculation/Controls/Functions/AvalonFormulaEditor.*`
  - Excel 风格求值器（预览）：`src/ProjectDigitizer.Studio/Services/ExcelLikeFormulaEvaluator.cs`
  - 计算引擎门面：`src/Plugins.DataCalculation/Services/CalcEngineFacade.cs`

### 执行细节（当前实现）
- 表达式模式：当 `TransformNodeProperties.Expression` 非空时，使用 `CalcEngineFacade.Evaluate(expr, inputData)` 计算；`inputData` 字典作为变量表参与求值。输出 `OutputData["Result"] = value`，并写入元数据 `processor=DataCalculationProcessor`、`mode=CalcEngine`、`timestamp`、`executionTime`。
- 回退求和：当表达式为空时，遍历 `inputData` 对各种数值类型/可解析字符串/嵌套集合进行递归求和，输出至 `Result`，并写入 `mode=SumFallback`。
- 异常处理：捕获异常并返回 `IsSuccess=false`、`ErrorMessage`、`ExecutionTime`。

注意：属性面板中的 `{Var}` 变量占位仅用于预览与输入便捷；实际执行由处理器按 `Expression + inputData` 求值，变量名需与输入数据字典键一致。

## 目标（保持不变）
— 提供“变量映射 + 多函数表达式”的可靠计算节点：
- 支持从上游/外部数据源取值，支持变量占位 `{VarName}`。
- 支持实时/手动/定时 预览与执行，模式可配置。
- 支持精度、小数位、舍入模式、输出格式（数值/文本/JSON/表格）、缓存等。
- 输出结构包括：默认结果 + 各函数结果 + 元数据；用于节点展开内容可视化。
- 与现有架构一致：持久化/注册/Inspector 交互一致，必要时抽象接口。
- 强可用：含单测、集成测试、UI 自测与文档。

## 架构概览
- 插件位于 Studio ← Application ← Core 之上实现，Studio 侧通过 DI 加载与持久化。
- 注册：通过 `INodeRegistry` 注册模板与处理器。
- 参数持久化路线：短期沿用扁平键（`function_0_name` 等）；中期迁移为统一 JSON（`Module.Parameters["dataCalculation"]`）以便扩展与版本化。

## UI/UX 现状（问题点）
- 版式密度不均：GroupBox 层级深、留白不一致，标签与输入控件对齐不统一，阅读负担大。
- 视觉层次弱：缺少卡片化样式与统一的阴影/圆角/分隔线，信息块不易分辨。
- 操作入口分散：变量/函数的常用操作尺寸与位置不统一，图标大小不一致（16/14 混用）。
- 反馈不明确：缺少统一的 InfoBar/状态提示（加载/成功/失败/只读等）。
- 预览阻塞与频繁：未做去抖与后台执行，编辑中容易卡顿或误触发。
- 列表缺少虚拟化：函数/变量多时渲染卡顿。
- 可访问性弱：焦点可视、Tab 顺序、快捷键映射与提示文案不统一。

## UI/UX 优化方案
- 布局与信息架构
  - 分区顺序：基础设置 → 变量管理 → 函数列表（编辑/预览）→ 运行与结果（规划）。
  - 可折叠分区：采用 `Expander` 或统一样式的分组卡片，降低拥挤度。
  - 两列布局：左列标签固定宽度（建议 96px），右列自适应；统一行间距 8px、组间距 16px、面板边距 16px。
- 视觉样式
  - 卡片化：统一使用 `MaterialDesignCardBackground`、`MaterialDesignDivider`，圆角 6、内边距 12，弱阴影（或沿用节点卡片阴影）。
  - 图标与按钮：图标统一 16px；主按钮 `Button.Primary`，次按钮 `Button.Secondary`，纯图标 `Button.Text`；禁用/悬停态一致。
  - 颜色与对比：沿用动态资源 `Brush.*`；正文 12pt、行高 1.4；强调文本半粗。
- 控件与组件
  - 变量卡片：标题 + 来源节点 + 属性下拉 + “插入到编辑器”按钮（将 `{变量名}` 插入当前 `AvalonFormulaEditor` 光标处，通过 `AvalonFormulaEditor.TryGetLastFocused()?.InsertAtCaret("{"+name+"}")`）。
  - 函数卡片：名称输入 + `AvalonFormulaEditor` + 校验提示 + 预览结果；“执行”按钮置于卡片右上角。
  - 模板选择：保留“导入模板”，对话框内支持搜索、预览与插入。
- 交互与反馈
  - 预览去抖：编辑表达式 300–500ms 去抖后后台线程预览；点击执行按钮立即预览并在按钮上显示进度。
  - 统一 InfoBar：成功/错误/警告/信息四类，位于面板顶部或卡片顶部，可关闭。
  - 快捷键：保留 Ctrl+J 补全；补充 Enter（执行当前）、Ctrl+Enter（执行全部）、Ctrl+D（复制当前）、Delete（删除当前）提示与开关。
- 性能与可访问
  - 虚拟化：变量/函数列表启用 `VirtualizingStackPanel.IsVirtualizing=True`、`VirtualizationMode=Recycling`。
  - 可访问性：启用 FocusVisual；为关键控件补充 `AutomationProperties.Name` 与 Tooltip；Tab 顺序自上而下、左到右。
- 国际化与文案
  - 统一中文术语与标点；空状态、占位符、错误提示采用一致的句式与语气。

## UI 任务清单（现状标记）
- [ ] 统一面板边距/行间距/标签列宽（16/8/96）
- [ ] GroupBox → 卡片样式（圆角 6、内边距 12、分隔线/阴影）
- [x] 公式编辑器：Ctrl+J 补全、语法高亮、函数签名信息面板（基础已具备）
- [ ] 变量卡片：一键“插入到编辑器”按钮（插入 `{变量名}`）
- [x] 函数卡片：名称/表达式/执行按钮/校验与预览（基础已具备）
- [ ] 统一图标尺寸 16px 与按钮风格（Primary/Secondary/Text）
- [ ] 统一 InfoBar（成功/错误/警告/信息）与错误收敛展示
- [ ] 预览去抖（300–500ms）与后台执行
- [ ] 列表虚拟化（变量/函数 ItemsControl）
- [ ] 可访问性：FocusVisual、Tab 顺序、AutomationProperties
- [ ] 空状态与占位文案统一

## 快速改造建议（优先级 1，可 1–2 天落地）
- `src/Plugins.DataCalculation/Controls/Inspector/Components/DataCalculationComponent.xaml`：将 GroupBox 改为卡片式容器（Border + 统一样式），整理间距与标题层级；图标统一 16px。
- `src/Plugins.DataCalculation/Controls/Inspector/DataCalculationPanel.xaml`：在变量项模板中新增“插入到编辑器”按钮，调用 `AvalonFormulaEditor.TryGetLastFocused()?.InsertAtCaret("{" + varName + "}")`。
- `src/Plugins.DataCalculation/Controls/Functions/AvalonFormulaEditor.xaml(.cs)`：保持 Ctrl+J 补全；使用 `DispatcherTimer` 实现 300–500ms 去抖预览；InfoPanel 展示函数签名/示例。
- 变量/函数 `ItemsControl`：启用 `VirtualizingStackPanel` 与回收，显著降低大列表卡顿。
- 统一 InfoBar：封装简单 `InfoBar`（或使用 WPFUI 组件）在预览/执行/保存失败时统一展示。
- 空状态：为变量/函数列表添加空状态说明与快捷入口（添加变量/添加函数）。

## 实施清单（现状标记）

核心与注册
- [x] `INodeRegistry` 注册模板与处理器（`DataCalculationModule`）
- [x] 属性面板 Provider 与资源键（`PropertyPanel.ModuleType.DataCalculation`）
- [x] 插件资源映射到 `DataCalculationPanel`（`Resources/PluginResources.xaml`）

模板与 Schema
- [x] NodeList 元信息（名称/分组/标签/IconGlyph）
- [x] `PropertySchema` 包含 `Expression` 字段（textarea）
- [x] `CreateInstance` 使用 `TransformNodeProperties`

处理器（现状）
- [x] 读取 `TransformNodeProperties.Expression`
- [x] 调用 `CalcEngineFacade.Evaluate` 求值（含变量字典）
- [x] 输出 `OutputData["Result"]` + `Metadata.processor/mode/timestamp`
- [x] 空表达式回退：对 `inputData` 递归求和
- [x] 输入/输出端口定义（`in`/`result`）
- [ ] 消费 `Module.Parameters` 中的函数/变量映射
- [ ] 支持精度/舍入/输出格式/缓存/错误处理模式

UI 与 Inspector
- [x] `DataCalculationPanel` 作为属性面板加载
- [x] `DataCalculationComponent`（新 Inspector 组件）可用
- [x] 变量管理：来源节点/属性选择，读写到 `Module.Parameters`
- [x] 函数列表：新增/删除/模板导入/单条执行按钮
- [x] 公式编辑器：Ctrl+J 补全、语法高亮、插入到编辑器（通过模板/窗口，变量插入待加）
- [x] 预览：`ExcelLikeFormulaEvaluator` + 基础回退
- [x] 节点展开区写入函数摘要（`ExpandedContent`）
- [ ] 快捷键 Enter/Ctrl+Enter/Ctrl+D/Delete 的全链路行为
- [ ] 预览去抖/后台线程执行

持久化与迁移
- [x] 扁平键 `function_*`/`variable_*` 写入 `Module.Parameters`
- [ ] 统一 JSON 配置（`dataCalculation`）与强类型 `DataCalculationConfig`
- [ ] JSON 读写与从扁平键迁移逻辑

测试与文档
- [ ] 单元/集成/UI 测试覆盖插件
- [x] 本文档按现状更新，并与 `docs/tasks/*` 对齐

## 配置模型（建议）
为简化扩展与版本化，新增强类型配置 `DataCalculationConfig`，以 JSON 形式持久化到 `Module.Parameters["dataCalculation"]`。

```json
{
  "calculationMode": "Realtime|Manual|Scheduled",
  "precision": 2,
  "errorHandling": "ReturnError|UseDefault|LogOnly|Stop",
  "outputFormat": "Number|Text|Json|Table",
  "cacheResults": true,
  "variables": [
    { "name": "a", "sourceNodeId": "node-1", "propertyName": "Result" },
    { "name": "b", "sourceNodeId": "node-2", "propertyName": "Value" }
  ],
  "functions": [
    { "name": "Total", "expression": "SUM({a}, {b}*2)" },
    { "name": "Remark", "expression": "IF({a} > 10, \"OK\", \"LOW\")" }
  ]
}
```

## 执行摘要（建议实现）
1) 从变量映射收集输入字典；对 `{name}` 做值替换（字符串加引号，数值 InvariantCulture）。
2) 表达式求值：
   - 首选 `ExcelLikeFormulaEvaluator.TryEvaluate`（支持 IF/AND/OR/SUM/AVG/MAX/MIN/ABS/ROUND/LEFT/RIGHT/MID/FIND/...）。
   - 失败回退 `DataTable.Compute`（仅数值 + 基础运算符）。
3) 输出：
   - `Result`：默认主结果；`Functions`：函数结果字典；
   - `Metadata`：processor、timestamp、durationMs、usedVariables、usedFunctions、cacheHit 等。

## 里程碑与 DoD（摘）
- M1：处理器与迁移、预览与参数落地。
- M2：Inspector 组件完整交互；节点展开内容展示。
- M3：中间模型（DataTable/Enumerable）与 Table 输出；可选仅预览模式。
- DoD：表达式/变量映射/预览/执行/节点展开可用；覆盖率目标 80%+（核心 90%+）。

## 使用与构建
- 还原/构建：`dotnet restore && dotnet build -c Debug`
- 运行 Studio：`dotnet run --project src/ProjectDigitizer.Studio/ProjectDigitizer.Studio.csproj`
- 测试：`dotnet test -c Debug`

插件项目（`net8.0-windows`，WPF）在构建后会将 DLL 复制到 Studio 输出目录 `Plugins/`（见 `src/Plugins.DataCalculation/Plugins.DataCalculation.csproj` 构建目标）。

## 已知限制（与规划差异）
- 未实现统一 JSON 配置（`DataCalculationConfig`）；仍使用扁平键保存变量与函数。
- 处理器仅消费单一 `Expression`，未消费 `function_*`/`variable_*` 项；无多函数执行聚合。
- 未实现精度/舍入/输出格式/缓存等运行时控制；面板中的相关设置仅保存参数，未参与执行。
- 输出仅包含 `Result` 与基本元数据；不含表格/多结果聚合与缓存命中信息。

## 建议推进顺序
- 先落地 `DataCalculationConfig` 与处理器完整执行流；
- 再补 UI 与节点展开（结果/耗时/缓存状态）；
- 最后做迁移/缓存/输出格式与精度控制等增强。

## 里程碑与状态记录
- 详见 `docs/tasks/Plugins.DataCalculation.Task.md` 与 `docs/tasks/Plugins.DataCalculation.Backlog.md`。

