<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 将色板转换为画刷，便于控件样式复用 -->
    <SolidColorBrush x:Key="Brush.Primary" Color="{DynamicResource Color.Primary}"/>
    <SolidColorBrush x:Key="Brush.PrimaryVariant" Color="{DynamicResource Color.PrimaryVariant}"/>
    <SolidColorBrush x:Key="Brush.Secondary" Color="{DynamicResource Color.Secondary}"/>
    <SolidColorBrush x:Key="Brush.SecondaryVariant" Color="{DynamicResource Color.SecondaryVariant}"/>
    
    <!-- 顶部 AppBar 背景刷（更商务的深蓝） -->
    <SolidColorBrush x:Key="Brush.AppBar" Color="{DynamicResource Color.AppBar}"/>

    <SolidColorBrush x:Key="Brush.Surface" Color="{DynamicResource Color.Surface}"/>
    <SolidColorBrush x:Key="Brush.SurfaceVariant" Color="{DynamicResource Color.SurfaceVariant}"/>
    <SolidColorBrush x:Key="Brush.Outline" Color="{DynamicResource Color.Outline}"/>
    <SolidColorBrush x:Key="Brush.Border" Color="{DynamicResource Color.Border}"/>
    <SolidColorBrush x:Key="Brush.Text" Color="{DynamicResource Color.Text}"/>
    <SolidColorBrush x:Key="Brush.TextSecondary" Color="{DynamicResource Color.TextSecondary}"/>
    <SolidColorBrush x:Key="Brush.Placeholder" Color="{DynamicResource Color.Placeholder}"/>

    <SolidColorBrush x:Key="Brush.Success" Color="{DynamicResource Color.Success}"/>
    <SolidColorBrush x:Key="Brush.Warning" Color="{DynamicResource Color.Warning}"/>
    <SolidColorBrush x:Key="Brush.Danger" Color="{DynamicResource Color.Danger}"/>
    <SolidColorBrush x:Key="Brush.Info" Color="{DynamicResource Color.Info}"/>

    <SolidColorBrush x:Key="Brush.Shadow" Color="{DynamicResource Color.Shadow}"/>
    <SolidColorBrush x:Key="Brush.Divider" Color="{DynamicResource Color.Divider}"/>

    <!-- 半透明画刷 -->
    <SolidColorBrush x:Key="Brush.Primary/08" Color="{DynamicResource Color.Primary/08}"/>
    <SolidColorBrush x:Key="Brush.Primary/16" Color="{DynamicResource Color.Primary/16}"/>
    <SolidColorBrush x:Key="Brush.Text/60" Color="{DynamicResource Color.Text/60}"/>
    <SolidColorBrush x:Key="Brush.Text/40" Color="{DynamicResource Color.Text/40}"/>
    <SolidColorBrush x:Key="Brush.Text/24" Color="{DynamicResource Color.Text/24}"/>
</ResourceDictionary>
