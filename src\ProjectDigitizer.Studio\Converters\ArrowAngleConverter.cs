using System;
using System.Globalization;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 箭头角度转换器
    /// 根据连接线的源点和目标点计算箭头的旋转角度
    /// </summary>
    public class ArrowAngleConverter : IMultiValueConverter
    {
        /// <summary>
        /// 将源点和目标点转换为箭头角度
        /// </summary>
        /// <param name="values">values[0]: Source.X, values[1]: Source.Y, values[2]: Target.X, values[3]: Target.Y</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数（可选）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>箭头的旋转角度（度）</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 4)
                return 0.0;

            // 尝试解析坐标值
            if (!TryParseDouble(values[0], out double sourceX) ||
                !TryParseDouble(values[1], out double sourceY) ||
                !TryParseDouble(values[2], out double targetX) ||
                !TryParseDouble(values[3], out double targetY))
            {
                return 0.0;
            }

            // 计算连接线的方向向量
            double deltaX = targetX - sourceX;
            double deltaY = targetY - sourceY;

            // 避免除零错误
            if (Math.Abs(deltaX) < 0.001 && Math.Abs(deltaY) < 0.001)
                return 0.0;

            // 计算角度（弧度）
            double angleRadians = Math.Atan2(deltaY, deltaX);

            // 转换为度数
            double angleDegrees = angleRadians * 180.0 / Math.PI;

            // 确保角度在0-360度范围内
            if (angleDegrees < 0)
                angleDegrees += 360;

            return angleDegrees;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("ArrowAngleConverter does not support ConvertBack");
        }

        /// <summary>
        /// 安全解析double值
        /// </summary>
        private static bool TryParseDouble(object value, out double result)
        {
            result = 0.0;

            if (value == null)
                return false;

            if (value is double d)
            {
                result = d;
                return true;
            }

            if (value is float f)
            {
                result = f;
                return true;
            }

            if (value is int i)
            {
                result = i;
                return true;
            }

            return double.TryParse(value.ToString(), out result);
        }
    }
}
