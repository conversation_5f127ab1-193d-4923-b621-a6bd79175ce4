using System;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;

using Xunit;

namespace ProjectDigitizer.Application.Tests.Services
{
    public class AsyncOperationManagerTests : IDisposable
    {
        private readonly Mock<ILogger<AsyncOperationManager>> _mockLogger;
        private readonly Mock<IStructuredLogger> _mockStructuredLogger;
        private readonly Mock<IOperationTracker> _mockOperationTracker;
        private readonly AsyncOperationManager _operationManager;

        public AsyncOperationManagerTests()
        {
            _mockLogger = new Mock<ILogger<AsyncOperationManager>>();
            _mockStructuredLogger = new Mock<IStructuredLogger>();
            _mockOperationTracker = new Mock<IOperationTracker>();

            // 设置 StructuredLogger 返回模拟的 OperationTracker
            _mockStructuredLogger
                .Setup(x => x.StartOperation(It.IsAny<string>(), It.IsAny<Dictionary<string, object>>()))
                .Returns(_mockOperationTracker.Object);

            _operationManager = new AsyncOperationManager(_mockLogger.Object, _mockStructuredLogger.Object);
        }

        [Fact]
        public async Task StartOperationAsync_WithValidOperation_ShouldReturnHandle()
        {
            // Arrange
            var operationName = "Test Operation";
            var expectedResult = "Test Result";

            // Act
            var handle = await _operationManager.StartOperationAsync(
                operationName,
                async (progress, cancellationToken) =>
                {
                    progress.Report(new OperationProgress(50, "Processing..."));
                    await Task.Delay(10, cancellationToken);
                    return expectedResult;
                });

            // Assert
            Assert.NotNull(handle);
            Assert.Equal(operationName, handle.OperationName);
            Assert.NotEmpty(handle.OperationId);

            // 等待操作完成
            var result = await handle.WaitAsync();
            Assert.Equal(expectedResult, result);
            Assert.Equal(OperationStatus.Completed, handle.Status);
        }

        [Fact]
        public async Task StartOperationAsync_WithProgressReporting_ShouldReportProgress()
        {
            // Arrange
            var operationName = "Progress Test";
            var progressReports = new List<OperationProgress>();
            var progressReporter = new Progress<OperationProgress>(progress => progressReports.Add(progress));

            // Act
            var handle = await _operationManager.StartOperationAsync(
                operationName,
                async (progress, cancellationToken) =>
                {
                    progress.Report(new OperationProgress(25, "Step 1"));
                    await Task.Delay(10, cancellationToken);
                    progress.Report(new OperationProgress(75, "Step 2"));
                    await Task.Delay(10, cancellationToken);
                    progress.Report(new OperationProgress(100, "Complete"));
                    return "Done";
                },
                progressReporter);

            await handle.WaitAsync();

            // Assert
            Assert.True(progressReports.Count >= 3);
            Assert.Contains(progressReports, p => p.Percentage == 25 && p.Message == "Step 1");
            Assert.Contains(progressReports, p => p.Percentage == 75 && p.Message == "Step 2");
            Assert.Contains(progressReports, p => p.Percentage == 100 && p.Message == "Complete");
        }

        [Fact]
        public async Task StartOperationAsync_WithCancellation_ShouldCancelOperation()
        {
            // Arrange
            var operationName = "Cancellation Test";
            var cts = new CancellationTokenSource();

            // Act
            var handle = await _operationManager.StartOperationAsync(
                operationName,
                async (progress, cancellationToken) =>
                {
                    await Task.Delay(1000, cancellationToken); // 长时间操作
                    return "Should not complete";
                },
                cancellationToken: cts.Token);

            // 等待一小段时间确保操作开始运行
            await Task.Delay(50);

            // 取消操作
            cts.Cancel();

            // Assert
            await Assert.ThrowsAnyAsync<OperationCanceledException>(() => handle.WaitAsync());
            Assert.Equal(OperationStatus.Cancelled, handle.Status);
        }

        [Fact]
        public async Task StartOperationAsync_WithException_ShouldHandleException()
        {
            // Arrange
            var operationName = "Exception Test";
            var expectedException = new InvalidOperationException("Test exception");

            // Act
            var handle = await _operationManager.StartOperationAsync<string>(
                operationName,
                (progress, cancellationToken) =>
                {
                    throw expectedException;
                });

            // Assert
            var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() => handle.WaitAsync());
            Assert.Equal(expectedException.Message, actualException.Message);
            Assert.Equal(OperationStatus.Failed, handle.Status);
            Assert.Equal(expectedException, handle.Exception);
        }

        [Fact]
        public void GetActiveOperations_WithNoOperations_ShouldReturnEmpty()
        {
            // Act
            var activeOperations = _operationManager.GetActiveOperations();

            // Assert
            Assert.Empty(activeOperations);
        }

        [Fact]
        public async Task GetActiveOperations_WithRunningOperation_ShouldReturnOperation()
        {
            // Arrange
            var operationName = "Long Running Test";
            var tcs = new TaskCompletionSource<bool>();

            // Act
            var handle = await _operationManager.StartOperationAsync(
                operationName,
                async (progress, cancellationToken) =>
                {
                    await tcs.Task; // 等待外部信号
                    return "Done";
                });

            var activeOperations = _operationManager.GetActiveOperations().ToList();

            // Assert
            Assert.Single(activeOperations);
            Assert.Equal(handle.OperationId, activeOperations[0].OperationId);

            // 清理：完成操作
            tcs.SetResult(true);
            await handle.WaitAsync();
        }

        [Fact]
        public async Task CancelAllOperationsAsync_WithActiveOperations_ShouldCancelAll()
        {
            // Arrange
            var handle1 = await _operationManager.StartOperationAsync(
                "Operation 1",
                async (progress, cancellationToken) =>
                {
                    // 模拟长时间运行的操作，但响应取消令牌
                    await Task.Delay(10000, cancellationToken);
                    return "Done 1";
                });

            var handle2 = await _operationManager.StartOperationAsync(
                "Operation 2",
                async (progress, cancellationToken) =>
                {
                    // 模拟长时间运行的操作，但响应取消令牌
                    await Task.Delay(10000, cancellationToken);
                    return "Done 2";
                });

            // 等待一小段时间确保操作开始运行
            await Task.Delay(100);

            // Act
            await _operationManager.CancelAllOperationsAsync();

            // Assert
            Assert.Equal(OperationStatus.Cancelled, handle1.Status);
            Assert.Equal(OperationStatus.Cancelled, handle2.Status);
        }

        public void Dispose()
        {
            _operationManager?.Dispose();
        }
    }
}
