using System.Collections.Generic;

using ProjectDigitizer.Core.Enums;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Application.Interfaces
{
    /// <summary>
    /// 连接器兼容性服务接口
    /// 负责管理连接器之间的兼容性检查和高亮显示
    /// </summary>
    public interface IConnectorCompatibilityService
    {
        /// <summary>
        /// 检查两个连接器是否兼容
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        /// <returns>兼容性检查结果</returns>
        CompatibilityResult CheckCompatibility(ConnectorInfo source, ConnectorInfo target);

        /// <summary>
        /// 检查数据类型是否兼容
        /// </summary>
        /// <param name="sourceType">源数据类型</param>
        /// <param name="targetType">目标数据类型</param>
        /// <returns>是否兼容</returns>
        bool IsDataTypeCompatible(ConnectorDataType sourceType, ConnectorDataType targetType);

        /// <summary>
        /// 获取与指定连接器兼容的所有连接器
        /// </summary>
        /// <param name="connector">指定连接器</param>
        /// <param name="allConnectors">所有连接器列表</param>
        /// <returns>兼容的连接器列表</returns>
        IEnumerable<ConnectorInfo> GetCompatibleConnectors(
            ConnectorInfo connector,
            IEnumerable<ConnectorInfo> allConnectors);

        /// <summary>
        /// 获取连接器的兼容性评分
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        /// <returns>兼容性评分 (0-100)</returns>
        int GetCompatibilityScore(ConnectorInfo source, ConnectorInfo target);

        /// <summary>
        /// 获取数据类型的描述信息
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>描述信息</returns>
        string GetDataTypeDescription(ConnectorDataType dataType);

        /// <summary>
        /// 获取兼容的数据类型列表
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>兼容的数据类型列表</returns>
        IEnumerable<ConnectorDataType> GetCompatibleDataTypes(ConnectorDataType dataType);
    }
}
