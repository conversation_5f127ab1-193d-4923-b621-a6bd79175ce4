using System.Collections.Generic;

using ProjectDigitizer.Application.Interfaces.UI;
using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Adapters
{
    /// <summary>
    /// 将 ModuleNodeViewModel 适配为 Application 层的 INodeContext。
    /// </summary>
    public sealed class NodeContextAdapter : INodeContext
    {
        private readonly ModuleNodeViewModel _node;

        public NodeContextAdapter(ModuleNodeViewModel node)
        {
            _node = node;
        }

        public string NodeId => _node.Module?.Id ?? string.Empty;
        public ModuleType ModuleType => _node.Module?.Type ?? default;

        public IReadOnlyDictionary<string, object> GetParameters()
        {
            return _node.Module?.Parameters ?? new Dictionary<string, object>();
        }

        public void SetParameters(IDictionary<string, object> parameters)
        {
            if (_node.Module == null) return;
            foreach (var kv in parameters)
            {
                _node.Module.Parameters[kv.Key] = kv.Value ?? string.Empty;
            }
        }

        public void SetParameter(string key, object value)
        {
            if (_node.Module == null) return;
            _node.Module.Parameters[key] = value ?? string.Empty;
        }
    }
}
