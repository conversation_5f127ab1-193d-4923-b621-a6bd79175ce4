{"categories": [{"id": "geometry", "name": "几何计算", "description": "常见平面/立体图形的面积、体积、周长等", "iconGlyph": "", "color": "#4CAF50", "order": 1}, {"id": "math", "name": "数学基础", "description": "算术/代数/三角/指数与对数", "iconGlyph": "", "color": "#607D8B", "order": 2}, {"id": "statistics", "name": "统计分析", "description": "求和/均值/中位数/方差/标准差等", "iconGlyph": "", "color": "#9C27B0", "order": 3}, {"id": "physics", "name": "物理量计算", "description": "速度/加速度/力/功/能量/电学等", "iconGlyph": "", "color": "#2196F3", "order": 4}, {"id": "engineering", "name": "工程计算", "description": "材料/结构/荷载/截面等", "iconGlyph": "", "color": "#FF9800", "order": 5}, {"id": "finance", "name": "财务金融", "description": "单利/复利/折现/回报率", "iconGlyph": "", "color": "#795548", "order": 6}, {"id": "conversion", "name": "单位换算", "description": "常见温度/速度/压力等单位换算", "iconGlyph": "", "color": "#009688", "order": 7}], "templates": [{"id": "circle_area", "name": "圆面积", "description": "πr²", "expression": "PI()*POWER(r,2)", "category": "geometry", "unit": "m2", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "r", "displayName": "半径", "description": "圆的半径", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "m"}], "examples": ["r=5 -> 78.54 m2"], "tags": ["圆", "面积"]}, {"id": "circle_circumference", "name": "圆周长", "description": "2πr", "expression": "2*PI()*r", "category": "geometry", "unit": "m", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "r", "displayName": "半径", "description": "圆的半径", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "m"}], "examples": ["r=10 -> 62.83 m"], "tags": ["圆", "周长"]}, {"id": "rectangle_area", "name": "矩形面积", "description": "长×宽", "expression": "length*width", "category": "geometry", "unit": "m2", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "length", "displayName": "长度", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "m"}, {"name": "width", "displayName": "宽度", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "m"}], "examples": ["10×5 -> 50 m2"], "tags": ["矩形", "面积"]}, {"id": "rectangle_perimeter", "name": "矩形周长", "description": "2(长+宽)", "expression": "2*(length+width)", "category": "geometry", "unit": "m", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "length", "displayName": "长度", "dataType": "Number", "isRequired": true}, {"name": "width", "displayName": "宽度", "dataType": "Number", "isRequired": true}], "examples": ["10×5 -> 30 m"], "tags": ["矩形", "周长"]}, {"id": "triangle_area", "name": "三角形面积", "description": "底×高÷2", "expression": "base*height/2", "category": "geometry", "unit": "m2", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "base", "displayName": "底", "dataType": "Number", "isRequired": true, "minValue": 0}, {"name": "height", "displayName": "高", "dataType": "Number", "isRequired": true, "minValue": 0}], "examples": ["底8 高6 -> 24 m2"], "tags": ["三角形", "面积"]}, {"id": "right_triangle_hypotenuse", "name": "直角三角斜边", "description": "勾股定理", "expression": "SQRT(POWER(a,2)+POWER(b,2))", "category": "geometry", "unit": "m", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "直角边a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "直角边b", "dataType": "Number", "isRequired": true}], "examples": ["3,4 -> 5"], "tags": ["三角形", "勾股"]}, {"id": "trapezoid_area", "name": "梯形面积", "description": "(上底+下底)×高÷2", "expression": "(a+b)*h/2", "category": "geometry", "unit": "m2", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "上底", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "下底", "dataType": "Number", "isRequired": true}, {"name": "h", "displayName": "高", "dataType": "Number", "isRequired": true}], "examples": ["(5+7)×4/2 -> 24 m2"], "tags": ["梯形", "面积"]}, {"id": "sphere_volume", "name": "球体积", "description": "4/3·πr³", "expression": "(4/3)*PI()*POWER(r,3)", "category": "geometry", "unit": "m3", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "r", "displayName": "半径", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "m"}], "examples": ["r=3 -> 113.10 m3"], "tags": ["球", "体积"]}, {"id": "cylinder_volume", "name": "圆柱体积", "description": "πr²h", "expression": "PI()*POWER(r,2)*h", "category": "geometry", "unit": "m3", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "r", "displayName": "半径", "dataType": "Number", "isRequired": true}, {"name": "h", "displayName": "高", "dataType": "Number", "isRequired": true}], "examples": ["r=2,h=5 -> 62.83 m3"], "tags": ["圆柱", "体积"]}, {"id": "ellipse_area", "name": "椭圆面积", "description": "πab", "expression": "PI()*a*b", "category": "geometry", "unit": "m2", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "长半轴", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "短半轴", "dataType": "Number", "isRequired": true}], "examples": ["a=3,b=2 -> 18.85 m2"], "tags": ["椭圆", "面积"]}, {"id": "percentage", "name": "百分比", "description": "值/总计×100%", "expression": "value/total*100", "category": "math", "unit": "%", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "value", "displayName": "值", "dataType": "Number", "isRequired": true}, {"name": "total", "displayName": "总计", "dataType": "Number", "isRequired": true}], "examples": ["30/120 -> 25%"], "tags": ["百分比", "比例"]}, {"id": "percentage_change", "name": "百分比变化", "description": "(新-旧)/旧×100%", "expression": "(new-old)/old*100", "category": "math", "unit": "%", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "new", "displayName": "新值", "dataType": "Number", "isRequired": true}, {"name": "old", "displayName": "旧值", "dataType": "Number", "isRequired": true}], "examples": ["(120-100)/100 -> 20%"], "tags": ["增长率", "变动"]}, {"id": "weighted_average3", "name": "加权平均(3项)", "description": "Σxw/Σw", "expression": "(x1*w1+x2*w2+x3*w3)/(w1+w2+w3)", "category": "math", "unit": "", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "x1", "displayName": "值1", "dataType": "Number", "isRequired": true}, {"name": "w1", "displayName": "权重1", "dataType": "Number", "isRequired": true}, {"name": "x2", "displayName": "值2", "dataType": "Number", "isRequired": true}, {"name": "w2", "displayName": "权重2", "dataType": "Number", "isRequired": true}, {"name": "x3", "displayName": "值3", "dataType": "Number", "isRequired": true}, {"name": "w3", "displayName": "权重3", "dataType": "Number", "isRequired": true}], "examples": ["(80*0.2+90*0.3+70*0.5)/1 -> 78"], "tags": ["加权", "平均"]}, {"id": "deg_to_rad", "name": "角度→弧度", "description": "deg·π/180", "expression": "deg*PI()/180", "category": "math", "unit": "rad", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "deg", "displayName": "角度(°)", "dataType": "Number", "isRequired": true}], "examples": ["180 -> 3.1416"], "tags": ["角度", "弧度"]}, {"id": "rad_to_deg", "name": "弧度→角度", "description": "rad·180/π", "expression": "rad*180/PI()", "category": "math", "unit": "deg", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "rad", "displayName": "弧度", "dataType": "Number", "isRequired": true}], "examples": ["3.1416 -> 180"], "tags": ["角度", "弧度"]}, {"id": "sum3", "name": "求和(3项)", "description": "SUM(a,b,c)", "expression": "SUM(a,b,c)", "category": "statistics", "unit": "", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "b", "dataType": "Number", "isRequired": true}, {"name": "c", "displayName": "c", "dataType": "Number", "isRequired": true}], "examples": ["1+2+3 -> 6"], "tags": ["求和"]}, {"id": "mean3", "name": "平均值(3项)", "description": "AVERAGE(a,b,c)", "expression": "AVERAGE(a,b,c)", "category": "statistics", "unit": "", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "b", "dataType": "Number", "isRequired": true}, {"name": "c", "displayName": "c", "dataType": "Number", "isRequired": true}], "examples": ["1,2,3 -> 2"], "tags": ["平均"]}, {"id": "median3", "name": "中位数(3项)", "description": "MEDIAN(a,b,c)", "expression": "MEDIAN(a,b,c)", "category": "statistics", "unit": "", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "b", "dataType": "Number", "isRequired": true}, {"name": "c", "displayName": "c", "dataType": "Number", "isRequired": true}], "examples": ["1,100,2 -> 2"], "tags": ["中位数"]}, {"id": "variance3", "name": "样本方差(3项)", "description": "VAR(a,b,c)", "expression": "VAR(a,b,c)", "category": "statistics", "unit": "", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "b", "dataType": "Number", "isRequired": true}, {"name": "c", "displayName": "c", "dataType": "Number", "isRequired": true}], "examples": ["2,4,6 -> 4"], "tags": ["方差"]}, {"id": "stdev3", "name": "样本标准差(3项)", "description": "STDEV(a,b,c)", "expression": "STDEV(a,b,c)", "category": "statistics", "unit": "", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "a", "displayName": "a", "dataType": "Number", "isRequired": true}, {"name": "b", "displayName": "b", "dataType": "Number", "isRequired": true}, {"name": "c", "displayName": "c", "dataType": "Number", "isRequired": true}], "examples": ["2,4,6 -> 2"], "tags": ["标准差"]}, {"id": "velocity", "name": "速度", "description": "位移/时间", "expression": "s/t", "category": "physics", "unit": "m/s", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "s", "displayName": "位移", "dataType": "Number", "isRequired": true}, {"name": "t", "displayName": "时间", "dataType": "Number", "isRequired": true}], "examples": ["100/9.58 -> 10.44"], "tags": ["速度"]}, {"id": "acceleration", "name": "加速度", "description": "(v2-v1)/t", "expression": "(v2-v1)/t", "category": "physics", "unit": "m/s2", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "v2", "displayName": "末速度", "dataType": "Number", "isRequired": true}, {"name": "v1", "displayName": "初速度", "dataType": "Number", "isRequired": true}, {"name": "t", "displayName": "时间", "dataType": "Number", "isRequired": true}], "examples": ["(30-0)/3 -> 10"], "tags": ["加速度"]}, {"id": "kinetic_energy", "name": "动能", "description": "1/2·mv²", "expression": "0.5*m*POWER(v,2)", "category": "physics", "unit": "J", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "m", "displayName": "质量m", "dataType": "Number", "isRequired": true, "unit": "kg"}, {"name": "v", "displayName": "速度v", "dataType": "Number", "isRequired": true, "unit": "m/s"}], "examples": ["m=1,v=10 -> 50"], "tags": ["能量", "动能"]}, {"id": "potential_energy", "name": "重力势能", "description": "mgh", "expression": "m*g*h", "category": "physics", "unit": "J", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "m", "displayName": "质量m", "dataType": "Number", "isRequired": true}, {"name": "g", "displayName": "重力加速度g", "dataType": "Number", "isRequired": true}, {"name": "h", "displayName": "高度h", "dataType": "Number", "isRequired": true}], "examples": ["m=2,g=9.81,h=10 -> 196.2"], "tags": ["能量", "势能"]}, {"id": "pressure", "name": "压强", "description": "P=F/A", "expression": "F/A", "category": "physics", "unit": "Pa", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "F", "displayName": "力F", "dataType": "Number", "isRequired": true}, {"name": "A", "displayName": "面积A", "dataType": "Number", "isRequired": true}], "examples": ["F=100,A=0.5 -> 200"], "tags": ["压强"]}, {"id": "density", "name": "密度", "description": "ρ=m/V", "expression": "m/V", "category": "physics", "unit": "kg/m3", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "m", "displayName": "质量m", "dataType": "Number", "isRequired": true}, {"name": "V", "displayName": "体积V", "dataType": "Number", "isRequired": true}], "examples": ["m=1000,V=1 -> 1000"], "tags": ["密度"]}, {"id": "ohms_law_v", "name": "欧姆定律-电压", "description": "V=IR", "expression": "I*R", "category": "physics", "unit": "V", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "I", "displayName": "电流I", "dataType": "Number", "isRequired": true}, {"name": "R", "displayName": "电阻R", "dataType": "Number", "isRequired": true}], "examples": ["I=2,R=10 -> 20"], "tags": ["电学", "欧姆"]}, {"id": "steel_rebar_area", "name": "圆钢截面积", "description": "πd²/4", "expression": "PI()*POWER(d,2)/4", "category": "engineering", "unit": "mm2", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "d", "displayName": "直径d", "dataType": "Number", "isRequired": true, "minValue": 0, "unit": "mm"}], "examples": ["d=16 -> 201"], "tags": ["钢筋", "截面"]}, {"id": "stress_axial", "name": "轴向应力", "description": "σ=F/A", "expression": "F/A", "category": "engineering", "unit": "MPa", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "F", "displayName": "内力F", "dataType": "Number", "isRequired": true, "unit": "N"}, {"name": "A", "displayName": "截面A", "dataType": "Number", "isRequired": true, "unit": "mm2"}], "examples": ["F=30000,A=600 -> 50"], "tags": ["应力", "拉压"]}, {"id": "bending_moment_udl", "name": "简支梁均布荷载最大弯矩", "description": "qL²/8", "expression": "q*POWER(L,2)/8", "category": "engineering", "unit": "kN·m", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "q", "displayName": "均布荷载q", "dataType": "Number", "isRequired": true}, {"name": "L", "displayName": "跨度L", "dataType": "Number", "isRequired": true}], "examples": ["q=10,L=6 -> 45"], "tags": ["梁", "弯矩"]}, {"id": "simple_interest", "name": "单利", "description": "I=Prt", "expression": "P*r*t", "category": "finance", "unit": "", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "P", "displayName": "本金P", "dataType": "Number", "isRequired": true}, {"name": "r", "displayName": "年利率r", "dataType": "Number", "isRequired": true}, {"name": "t", "displayName": "年限t", "dataType": "Number", "isRequired": true}], "examples": ["P=1000,r=0.05,t=2 -> 100"], "tags": ["利息", "单利"]}, {"id": "compound_interest", "name": "复利终值", "description": "A=P(1+r/n)^{nt}", "expression": "P*POWER(1+r/n,n*t)", "category": "finance", "unit": "", "complexity": "Medium", "isBuiltIn": true, "parameters": [{"name": "P", "displayName": "本金P", "dataType": "Number", "isRequired": true}, {"name": "r", "displayName": "年利率r", "dataType": "Number", "isRequired": true}, {"name": "n", "displayName": "年复利次数n", "dataType": "Number", "isRequired": true}, {"name": "t", "displayName": "年限t", "dataType": "Number", "isRequired": true}], "examples": ["P=1000,r=0.05,n=12,t=1 -> 1051.16"], "tags": ["复利"]}, {"id": "c_to_f", "name": "摄氏->华氏", "description": "°F=°C×9/5+32", "expression": "C*9/5+32", "category": "conversion", "unit": "°F", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "C", "displayName": "摄氏°C", "dataType": "Number", "isRequired": true}], "examples": ["0 -> 32"], "tags": ["温度", "换算"]}, {"id": "kmh_to_ms", "name": "km/h->m/s", "description": "÷3.6", "expression": "kmh/3.6", "category": "conversion", "unit": "m/s", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "kmh", "displayName": "速度(km/h)", "dataType": "Number", "isRequired": true}], "examples": ["72 -> 20"], "tags": ["速度", "换算"]}, {"id": "psi_to_mpa", "name": "psi->MPa", "description": "*0.006894757", "expression": "psi*0.006894757", "category": "conversion", "unit": "MPa", "complexity": "Simple", "isBuiltIn": true, "parameters": [{"name": "psi", "displayName": "压力(psi)", "dataType": "Number", "isRequired": true}], "examples": ["1000 -> 6.895"], "tags": ["压力", "换算"]}]}