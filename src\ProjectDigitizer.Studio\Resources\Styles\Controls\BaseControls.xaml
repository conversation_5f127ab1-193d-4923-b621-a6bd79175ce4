<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  基础文本设置（全局）  -->
    <Style TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
        <Setter Property="TextWrapping" Value="Wrap" />
    </Style>

    <!--  按钮：主按钮/次级按钮/文本按钮  -->
    <Style TargetType="Button" x:Key="Button.Primary">
        <Setter Property="Foreground" Value="White" />
        <Setter Property="Background" Value="{StaticResource Brush.Primary}" />
        <Setter Property="BorderBrush" Value="{StaticResource Brush.Primary}" />
        <Setter Property="BorderThickness" Value="{StaticResource Border.Thin}" />
        <Setter Property="Padding" Value="10,8" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Radius.M}"
                        x:Name="Root">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Effect" TargetName="Root" Value="{StaticResource Shadow.S}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="Button.Secondary">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
        <Setter Property="Background" Value="{StaticResource Brush.Surface}" />
        <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}" />
        <Setter Property="BorderThickness" Value="{StaticResource Border.Thin}" />
        <Setter Property="Padding" Value="10,8" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="Root"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource Radius.M}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="Root" Value="{StaticResource Brush.Primary}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Button" x:Key="Button.Text">
        <Setter Property="Foreground" Value="{StaticResource Brush.Primary}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Padding" Value="8,6" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Background="{TemplateBinding Background}"
                        CornerRadius="{StaticResource Radius.S}"
                        x:Name="Root">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="Root" Value="{StaticResource Brush.Primary/08}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Button 默认样式：未显式指定 Style 时，采用次级按钮外观  -->
    <Style BasedOn="{StaticResource Button.Secondary}" TargetType="Button" />

    <!--  文本框  -->
    <Style TargetType="TextBox">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
        <Setter Property="Background" Value="{StaticResource Brush.Surface}" />
        <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}" />
        <Setter Property="BorderThickness" Value="{StaticResource Border.Thin}" />
        <Setter Property="Padding" Value="10,6" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{StaticResource Radius.S}"
                        x:Name="Root">
                        <ScrollViewer x:Name="PART_ContentHost" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter Property="BorderBrush" TargetName="Root" Value="{StaticResource Brush.Primary}" />
                            <Setter Property="BorderThickness" TargetName="Root" Value="2" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  下拉选择  -->
    <!-- <Style TargetType="ComboBox"> -->
    <!--     <Setter Property="Foreground" Value="{StaticResource Brush.Text}"/> -->
    <!--     <Setter Property="Background" Value="{StaticResource Brush.Surface}"/> -->
    <!--     <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}"/> -->
    <!--     <Setter Property="Padding" Value="8,4"/> -->
    <!-- </Style> -->

    <!--  复选/单选  -->
    <Style TargetType="CheckBox">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
    </Style>
    <Style TargetType="RadioButton">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
    </Style>

    <!--  列表与滚动条  -->
    <Style TargetType="ListView">
        <Setter Property="Background" Value="{StaticResource Brush.Surface}" />
        <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}" />
        <Setter Property="BorderThickness" Value="{StaticResource Border.Thin}" />
    </Style>

    <Style TargetType="ListViewItem">
        <Setter Property="Padding" Value="10,6" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{StaticResource Radius.S}"
                        x:Name="Root">
                        <ContentPresenter Margin="4,0" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="Root" Value="{StaticResource Brush.Primary/08}" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" TargetName="Root" Value="{StaticResource Brush.Primary/16}" />
                            <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="ScrollBar">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Width" Value="10" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid Background="{TemplateBinding Background}" x:Name="Root">
                        <Track
                            Maximum="{TemplateBinding Maximum}"
                            Minimum="{TemplateBinding Minimum}"
                            Orientation="{TemplateBinding Orientation}"
                            Value="{TemplateBinding Value}"
                            ViewportSize="{TemplateBinding ViewportSize}"
                            x:Name="PART_Track">
                            <Track.Thumb>
                                <Thumb
                                    Background="{StaticResource Brush.Shadow}"
                                    BorderBrush="{StaticResource Brush.Shadow}"
                                    BorderThickness="0">
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="5" />
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter Property="IsDirectionReversed" TargetName="PART_Track" Value="True" />
                        </Trigger>
                        <Trigger Property="Orientation" Value="Horizontal">
                            <Setter Property="IsDirectionReversed" TargetName="PART_Track" Value="False" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  工具提示  -->
    <Style TargetType="ToolTip">
        <Setter Property="Foreground" Value="{StaticResource Brush.Text}" />
        <Setter Property="Background" Value="{StaticResource Brush.Surface}" />
        <Setter Property="BorderBrush" Value="{StaticResource Brush.Border}" />
        <Setter Property="BorderThickness" Value="{StaticResource Border.Thin}" />
        <Setter Property="Padding" Value="8,6" />
        <Setter Property="Effect" Value="{StaticResource Shadow.S}" />
    </Style>

</ResourceDictionary>
