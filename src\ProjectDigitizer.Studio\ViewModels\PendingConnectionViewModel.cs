using System.Windows;
using System.Windows.Input;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 挂起连接的视图模型
    /// </summary>
    public class PendingConnectionViewModel : ViewModelBase
    {
        private ConnectorViewModel? _source;
        private ConnectorViewModel? _target;
        private bool _isVisible;
        private Point _targetLocation;

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            set => SetProperty(ref _isVisible, value);
        }

        /// <summary>
        /// 连接源
        /// </summary>
        public ConnectorViewModel? Source
        {
            get => _source;
            set => SetProperty(ref _source, value);
        }

        /// <summary>
        /// 连接目标
        /// </summary>
        public ConnectorViewModel? Target
        {
            get => _target;
            set => SetProperty(ref _target, value);
        }

        /// <summary>
        /// 目标位置
        /// </summary>
        public Point TargetLocation
        {
            get => _targetLocation;
            set => SetProperty(ref _targetLocation, value);
        }

        /// <summary>
        /// 开始连接命令
        /// </summary>
        public ICommand? StartCommand { get; set; }

        /// <summary>
        /// 完成连接命令
        /// </summary>
        public ICommand? FinishCommand { get; set; }

        public PendingConnectionViewModel()
        {
        }
    }
}
