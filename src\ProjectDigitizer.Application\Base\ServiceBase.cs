using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Application.Base;

/// <summary>
/// 应用服务基类
/// </summary>
public abstract class ServiceBase
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    protected readonly ILogger Logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    protected ServiceBase(ILogger logger)
    {
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行带异常处理的操作
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作结果</returns>
    protected async Task<ServiceResult<T>> ExecuteAsync<T>(
        Func<Task<T>> operation,
        string operationName)
    {
        try
        {
            Logger.LogDebug("开始执行操作: {OperationName}", operationName);

            var result = await operation();

            Logger.LogDebug("操作执行成功: {OperationName}", operationName);
            return ServiceResult<T>.Success(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "操作执行失败: {OperationName}", operationName);
            return ServiceResult<T>.Failure($"操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行带异常处理的操作（无返回值）
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作结果</returns>
    protected async Task<ServiceResult> ExecuteAsync(
        Func<Task> operation,
        string operationName)
    {
        try
        {
            Logger.LogDebug("开始执行操作: {OperationName}", operationName);

            await operation();

            Logger.LogDebug("操作执行成功: {OperationName}", operationName);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "操作执行失败: {OperationName}", operationName);
            return ServiceResult.Failure($"操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证输入参数
    /// </summary>
    /// <param name="validatable">可验证对象</param>
    /// <param name="parameterName">参数名称</param>
    /// <returns>验证结果</returns>
    protected ValidationResult ValidateInput(IValidatable validatable, string parameterName)
    {
        if (validatable == null)
        {
            var result = new ValidationResult();
            result.AddError($"参数 {parameterName} 不能为空");
            return result;
        }

        return validatable.Validate();
    }

    /// <summary>
    /// 验证输入参数（多个）
    /// </summary>
    /// <param name="validatables">可验证对象列表</param>
    /// <returns>验证结果</returns>
    protected ValidationResult ValidateInputs(params (IValidatable validatable, string parameterName)[] validatables)
    {
        var result = new ValidationResult();

        foreach (var (validatable, parameterName) in validatables)
        {
            var validation = ValidateInput(validatable, parameterName);
            result.Merge(validation);
        }

        return result;
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <param name="duration">执行时长</param>
    /// <param name="additionalData">附加数据</param>
    protected virtual void LogPerformanceMetrics(string operationName, TimeSpan duration, object? additionalData = null)
    {
        Logger.LogInformation("性能指标 - 操作: {OperationName}, 耗时: {Duration}ms, 数据: {@AdditionalData}",
            operationName, duration.TotalMilliseconds, additionalData);
    }

    /// <summary>
    /// 带性能监控的操作执行
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <returns>操作结果</returns>
    protected async Task<ServiceResult<T>> ExecuteWithMetricsAsync<T>(
        Func<Task<T>> operation,
        string operationName)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            var result = await ExecuteAsync(operation, operationName);
            stopwatch.Stop();

            LogPerformanceMetrics(operationName, stopwatch.Elapsed, new { Success = result.IsSuccess });
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            LogPerformanceMetrics(operationName, stopwatch.Elapsed, new { Success = false, Error = ex.Message });
            throw;
        }
    }
}

/// <summary>
/// 服务结果
/// </summary>
public class ServiceResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; protected set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; protected set; }

    /// <summary>
    /// 验证结果
    /// </summary>
    public ValidationResult? ValidationResult { get; protected set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <returns>成功结果</returns>
    public static ServiceResult Success()
    {
        return new ServiceResult { IsSuccess = true };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败结果</returns>
    public static ServiceResult Failure(string errorMessage)
    {
        return new ServiceResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// 创建验证失败结果
    /// </summary>
    /// <param name="validationResult">验证结果</param>
    /// <returns>验证失败结果</returns>
    public static ServiceResult ValidationFailure(ValidationResult validationResult)
    {
        return new ServiceResult
        {
            IsSuccess = false,
            ValidationResult = validationResult,
            ErrorMessage = "验证失败"
        };
    }
}

/// <summary>
/// 带数据的服务结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ServiceResult<T> : ServiceResult
{
    /// <summary>
    /// 结果数据
    /// </summary>
    public T? Data { get; private set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="data">结果数据</param>
    /// <returns>成功结果</returns>
    public static ServiceResult<T> Success(T data)
    {
        return new ServiceResult<T>
        {
            IsSuccess = true,
            Data = data
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败结果</returns>
    public new static ServiceResult<T> Failure(string errorMessage)
    {
        return new ServiceResult<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// 创建验证失败结果
    /// </summary>
    /// <param name="validationResult">验证结果</param>
    /// <returns>验证失败结果</returns>
    public new static ServiceResult<T> ValidationFailure(ValidationResult validationResult)
    {
        return new ServiceResult<T>
        {
            IsSuccess = false,
            ValidationResult = validationResult,
            ErrorMessage = "验证失败"
        };
    }
}
