using System.Reflection;

namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 节点模块元信息。
/// </summary>
public sealed class NodeModuleInfo
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string? Description { get; init; }
    public string? Version { get; init; }
}

/// <summary>
/// 节点模块（插件）约定：用于对外暴露注册点。
/// </summary>
public interface INodeModule
{
    /// <summary>
    /// 模块元信息。
    /// </summary>
    NodeModuleInfo Info { get; }

    /// <summary>
    /// 调用方通过该方法进行节点注册（避免 Core 依赖 DI）。
    /// </summary>
    void RegisterNodes(INodeRegistry registry);
}

