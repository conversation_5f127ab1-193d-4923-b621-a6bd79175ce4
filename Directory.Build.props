<Project>
  <PropertyGroup>
    <!-- 目标框架 -->
    <TargetFramework>net8.0</TargetFramework>
    
    <!-- 语言特性 -->
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>latest</LangVersion>
    
    <!-- 编译设置 -->
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701</WarningsNotAsErrors>

    <!-- 代码分析规则 - 将严格的规则降级为警告 -->
    <WarningsNotAsErrors>$(WarningsNotAsErrors);CA1720;CA1711;CA1805;CA1860;CA1311;CA1304;CA1305;CA1822;CA1854;CA1707;CA1816</WarningsNotAsErrors>
    
    <!-- 程序集信息 -->
    <Company>ProjectDigitizer</Company>
    <Product>ProjectDigitizer</Product>
    <Copyright>Copyright © ProjectDigitizer 2025</Copyright>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
    <Version>1.0.0</Version>
    
    <!-- 输出设置 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    
    <!-- 调试设置 -->
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
    
    <!-- 性能设置 -->
    <Optimize Condition="'$(Configuration)' == 'Release'">true</Optimize>
    
    <!-- 代码分析 -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <AnalysisMode>Recommended</AnalysisMode>

    <!-- 依赖注入编译时检查 -->
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <Deterministic>true</Deterministic>
  </PropertyGroup>

  <!-- 条件属性组 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <!-- 项目类型特定设置 -->
  <PropertyGroup Condition="'$(MSBuildProjectExtension)' == '.csproj' AND '$(OutputType)' == 'WinExe'">
    <UseWPF>true</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
  </PropertyGroup>

  <!-- 测试项目设置 -->
  <PropertyGroup Condition="$(MSBuildProjectName.EndsWith('.Tests'))">
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <!-- 全局包引用 - 代码分析工具 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" PrivateAssets="all" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" PrivateAssets="all" />
  </ItemGroup>

  <!-- 依赖注入分析器包不存在，使用现有的代码分析器已足够 -->

</Project>