<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:models="clr-namespace:ProjectDigitizer.Core.Entities;assembly=ProjectDigitizer.Core"
    xmlns:nodify="https://miroiu.github.io/nodify"
    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  ========== 数据格式输入节点模板集合 ==========  -->
    <!--  用于 Excel、CSV、XML、JSON 等格式的输入节点  -->

    <!--  通用数据格式输入节点内容模板  -->
    <DataTemplate x:Key="DataFormatInputContentTemplate">
        <Border Style="{StaticResource BaseNodeBorderStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="56" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  标题栏  -->
                <Border Grid.Row="0" Style="{StaticResource BaseNodeHeaderStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="26" />
                            <RowDefinition Height="26" />
                        </Grid.RowDefinitions>

                        <!--  第一行：主要信息  -->
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  折叠/展开按钮  -->
                            <Button
                                Grid.Column="0"
                                Style="{StaticResource ExpandCollapseButtonStyle}"
                                ToolTip="折叠/展开节点">
                                <ipack:PackIconMaterial
                                    Foreground="White"
                                    Height="14"
                                    Kind="ChevronDown"
                                    Opacity="0.9"
                                    Width="14" />
                            </Button>

                            <!--  格式图标 - 根据模块类型动态显示  -->
                            <ipack:PackIconMaterial
                                Foreground="White"
                                Grid.Column="1"
                                Height="18"
                                Margin="0,0,4,0"
                                VerticalAlignment="Center"
                                Width="18">
                                <ipack:PackIconMaterial.Style>
                                    <Style TargetType="ipack:PackIconMaterial">
                                        <Setter Property="Kind" Value="FileDocument" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                <Setter Property="Kind" Value="FileExcel" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CSVInput}">
                                                <Setter Property="Kind" Value="FileDelimited" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.XMLInput}">
                                                <Setter Property="Kind" Value="FileCode" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.JSONInput}">
                                                <Setter Property="Kind" Value="CodeJson" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </ipack:PackIconMaterial.Style>
                            </ipack:PackIconMaterial>

                            <!--  节点名称  -->
                            <TextBox
                                Grid.Column="2"
                                Margin="0,0,4,0"
                                Style="{StaticResource NodeTitleTextBoxStyle}"
                                Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                ToolTip="双击编辑节点名称" />
                        </Grid>

                        <!--  第二行：格式信息  -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  格式类型信息  -->
                            <StackPanel
                                Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center">
                                <Border
                                    CornerRadius="3"
                                    Margin="0,0,4,0"
                                    Padding="4,1">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                    <Setter Property="Background" Value="#1B5E20" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CSVInput}">
                                                    <Setter Property="Background" Value="#2E7D32" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.XMLInput}">
                                                    <Setter Property="Background" Value="#388E3C" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.JSONInput}">
                                                    <Setter Property="Background" Value="#43A047" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock
                                        FontSize="8"
                                        FontWeight="Bold"
                                        Foreground="White">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="DATA" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                        <Setter Property="Text" Value="XLSX" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.CSVInput}">
                                                        <Setter Property="Text" Value="CSV" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.XMLInput}">
                                                        <Setter Property="Text" Value="XML" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.JSONInput}">
                                                        <Setter Property="Text" Value="JSON" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                                <TextBlock
                                    FontSize="9"
                                    Foreground="White"
                                    MaxWidth="100"
                                    Opacity="0.8"
                                    Text="{Binding NodeProperties.FilePath, FallbackValue='未选择文件'}"
                                    TextTrimming="CharacterEllipsis"
                                    VerticalAlignment="Center" />
                            </StackPanel>

                            <!--  功能按钮组  -->
                            <StackPanel
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal">

                                <!--  预览数据按钮  -->
                                <Button Style="{StaticResource NodeFunctionButtonStyle}" ToolTip="预览数据">
                                    <ipack:PackIconMaterial
                                        Foreground="White"
                                        Height="14"
                                        Kind="Eye"
                                        Opacity="0.9"
                                        Width="14" />
                                </Button>

                                <!--  执行状态  -->
                                <Ellipse
                                    Height="12"
                                    Margin="3,0,3,0"
                                    ToolTip="数据状态"
                                    VerticalAlignment="Center"
                                    Width="12">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Error">
                                                    <Setter Property="Fill" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ExecutionStatus}" Value="Loading">
                                                    <Setter Property="Fill" Value="#FF9800" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>

                <!--  内容区域  -->
                <Border
                    Background="#E8F5E8"
                    CornerRadius="0,0,12,12"
                    Grid.Row="1"
                    Padding="12,8">
                    <StackPanel>
                        <!--  数据源信息  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                Text="源:"
                                VerticalAlignment="Center" />

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Foreground="#4CAF50"
                                Grid.Column="1"
                                Text="{Binding NodeProperties.DataSource, FallbackValue='本地文件'}"
                                TextTrimming="CharacterEllipsis"
                                VerticalAlignment="Center" />
                        </Grid>

                        <!--  编码/工作表信息  -->
                        <Grid Margin="0,0,0,6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                FontSize="10"
                                FontWeight="Medium"
                                Foreground="#666"
                                Grid.Column="0"
                                Margin="0,0,6,0"
                                VerticalAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="编码:" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                <Setter Property="Text" Value="工作表:" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>

                            <TextBlock
                                FontSize="9"
                                FontWeight="Medium"
                                Foreground="#4CAF50"
                                Grid.Column="1"
                                VerticalAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Text" Value="{Binding NodeProperties.Encoding, FallbackValue='UTF-8'}" />
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                <Setter Property="Text" Value="{Binding NodeProperties.WorksheetName, FallbackValue='Sheet1'}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </Grid>

                        <!--  配置参数  -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  标题行/起始行  -->
                            <StackPanel Grid.Column="0" Margin="0,0,4,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="标题行" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                    <Setter Property="Text" Value="起始行" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <TextBlock HorizontalAlignment="Center">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <!--  默认显示复选框  -->
                                            <Setter Property="FontFamily" Value="Segoe MDL2 Assets" />
                                            <Setter Property="Text" Value="&#xE739;" />
                                            <Setter Property="FontSize" Value="12" />
                                            <Setter Property="Foreground" Value="#4CAF50" />
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding NodeProperties.HasHeader}" Value="False">
                                                    <Setter Property="Text" Value="&#xE738;" />
                                                    <Setter Property="Foreground" Value="#999" />
                                                </DataTrigger>
                                                <!--  Excel显示起始行号  -->
                                                <DataTrigger Binding="{Binding Module.Type}" Value="{x:Static models:ModuleType.ExcelInput}">
                                                    <Setter Property="FontFamily" Value="Segoe UI" />
                                                    <Setter Property="Text" Value="{Binding NodeProperties.StartRow, FallbackValue=1}" />
                                                    <Setter Property="FontSize" Value="10" />
                                                    <Setter Property="FontWeight" Value="Bold" />
                                                    <Setter Property="Foreground" Value="#4CAF50" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <!--  记录数  -->
                            <StackPanel Grid.Column="1" Margin="2,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="记录数" />
                                <TextBlock
                                    FontSize="10"
                                    FontWeight="Bold"
                                    Foreground="#4CAF50"
                                    HorizontalAlignment="Center"
                                    Text="{Binding NodeProperties.RecordCount, FallbackValue=0}" />
                            </StackPanel>

                            <!--  状态指示  -->
                            <StackPanel Grid.Column="2" Margin="4,0,0,0">
                                <TextBlock
                                    FontSize="8"
                                    Foreground="#999"
                                    HorizontalAlignment="Center"
                                    Text="状态" />
                                <ipack:PackIconMaterial
                                    Foreground="#4CAF50"
                                    Height="12"
                                    HorizontalAlignment="Center"
                                    Kind="CheckCircle"
                                    Width="12" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </DataTemplate>

    <!--  Excel输入节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="ExcelInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataFormatInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  CSV输入节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="CSVInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataFormatInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  XML输入节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="XMLInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataFormatInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

    <!--  JSON输入节点模板  -->
    <DataTemplate DataType="{x:Type viewmodels:ModuleNodeViewModel}" x:Key="JSONInputNodeTemplate">
        <nodify:Node
            Input="{Binding Inputs}"
            Output="{Binding Outputs}"
            Style="{StaticResource BaseNodeStyle}">
            <nodify:Node.InputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedInputConnectorTemplate" />
            </nodify:Node.InputConnectorTemplate>
            <nodify:Node.OutputConnectorTemplate>
                <StaticResource ResourceKey="AdvancedOutputConnectorTemplate" />
            </nodify:Node.OutputConnectorTemplate>
            <ContentPresenter Content="{Binding}" ContentTemplate="{StaticResource DataFormatInputContentTemplate}" />
        </nodify:Node>
    </DataTemplate>

</ResourceDictionary>
