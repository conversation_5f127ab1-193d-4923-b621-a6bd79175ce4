using Microsoft.Extensions.DependencyInjection;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Application.Services.ErrorRecoveryStrategies;

namespace ProjectDigitizer.Application.Extensions;

/// <summary>
/// 错误处理服务扩展方法
/// </summary>
public static class ErrorHandlingServiceExtensions
{
    /// <summary>
    /// 添加错误处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddErrorHandlingServices(this IServiceCollection services)
    {
        // 注册结构化日志记录器
        services.AddScoped<IStructuredLogger, StructuredLogger>();

        // 注册异常处理服务
        services.AddScoped<ExceptionHandlingService>();

        // 注册错误恢复策略
        services.AddScoped<IErrorRecoveryStrategy, ValidationErrorRecoveryStrategy>();
        services.AddScoped<IErrorRecoveryStrategy, ServiceErrorRecoveryStrategy>();
        services.AddScoped<IErrorRecoveryStrategy, UIErrorRecoveryStrategy>();

        // 注册错误恢复管理器
        services.AddScoped<ErrorRecoveryManager>();

        return services;
    }

    /// <summary>
    /// 添加自定义错误恢复策略
    /// </summary>
    /// <typeparam name="T">策略类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddErrorRecoveryStrategy<T>(this IServiceCollection services)
        where T : class, IErrorRecoveryStrategy
    {
        services.AddScoped<IErrorRecoveryStrategy, T>();
        return services;
    }
}
