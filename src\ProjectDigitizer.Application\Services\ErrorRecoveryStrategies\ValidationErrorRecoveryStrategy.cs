using System;
using System.Linq;
using System.Threading.Tasks;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Exceptions;

namespace ProjectDigitizer.Application.Services.ErrorRecoveryStrategies;

/// <summary>
/// 验证错误恢复策略
/// </summary>
public class ValidationErrorRecoveryStrategy : IErrorRecoveryStrategy
{
    public int Priority => 10;

    public bool CanRecover(Exception exception)
    {
        return exception is ValidationException;
    }

    public async Task<RecoveryResult> RecoverAsync(Exception exception, object? context)
    {
        if (exception is not ValidationException validationException)
        {
            return RecoveryResult.Failure("不是验证异常");
        }

        await Task.Delay(1); // 模拟异步操作

        // 对于验证错误，通常需要用户修正输入
        var errorMessages = string.Join("; ", validationException.ValidationErrors.Select(e => e.ErrorMessage));

        return RecoveryResult.Failure(
            $"数据验证失败: {errorMessages}",
            requiresUserIntervention: true);
    }
}
