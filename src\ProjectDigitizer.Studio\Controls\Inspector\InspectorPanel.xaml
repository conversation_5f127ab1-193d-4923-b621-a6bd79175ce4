<UserControl x:Class="ProjectDigitizer.Studio.Controls.Inspector.InspectorPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:ipack="http://metro.mahapps.com/winfx/xaml/iconpacks"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="350"
             Background="{DynamicResource Brush.Surface}">

    <UserControl.Resources>
        <!-- 基础转换器（本地提供，避免外部资源未合并导致模板解析失败） -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Inspector组件容器样式 -->
        <Style x:Key="InspectorComponentStyle"
               TargetType="Expander">
            <Setter Property="Background"
                    Value="{DynamicResource Brush.SurfaceVariant}"/>
            <Setter Property="BorderBrush"
                    Value="{DynamicResource Brush.Divider}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Margin"
                    Value="8,6,8,6"/>
            <Setter Property="Padding"
                    Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Expander">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <DockPanel>
                                <!-- 组件头部 -->
                                <Border DockPanel.Dock="Top"
                                        Background="{DynamicResource Brush.AppBar}"
                                        CornerRadius="6,6,0,0"
                                        Padding="12,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 展开/折叠按钮 -->
                                        <ToggleButton Grid.Column="0"
                                                      IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                      
                                                      Margin="0,0,8,0"/>

                                        <!-- 组件标题 -->
                                        <TextBlock Grid.Column="1"
                                                   Text="{Binding Header, RelativeSource={RelativeSource TemplatedParent}}"
                                                   FontWeight="Medium"
                                                   FontSize="14"
                                                   VerticalAlignment="Center"/>

                                        <!-- 组件状态指示器 -->
                                        <Ellipse Grid.Column="2"
                                                 Width="8"
                                                 Height="8"
                                                 Fill="{DynamicResource Brush.Primary}"
                                                 Margin="8,0"
                                                 VerticalAlignment="Center"/>

                                        <!-- 组件菜单 -->
                                        <Button Grid.Column="3"
                                                Style="{StaticResource Button.Text}"
                                                Width="24"
                                                Height="24"
                                                Padding="0"
                                                ToolTip="组件选项">
                                            <Button.Content>
                                                <ipack:PackIconMaterial Kind="DotsVertical" Width="16" Height="16" />
                                            </Button.Content>
                                            <Button.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="重置到默认值">
                                                        <MenuItem.Icon>
                                                            <ipack:PackIconMaterial Kind="Refresh" />
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                    <MenuItem Header="复制组件">
                                                        <MenuItem.Icon>
                                                            <ipack:PackIconMaterial Kind="ContentCopy" />
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                    <Separator/>
                                                    <MenuItem Header="移除组件">
                                                        <MenuItem.Icon>
                                                            <ipack:PackIconMaterial Kind="Delete" />
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                </ContextMenu>
                                            </Button.ContextMenu>
                                        </Button>
                                    </Grid>
                                </Border>

                                <!-- 组件内容 -->
                                <ContentPresenter DockPanel.Dock="Top"
                                                  Margin="12"
                                                  Visibility="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent},
                                                              Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </DockPanel>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- GroupBox 卡片化样式：替换默认厚重外观 -->
        <Style x:Key="InspectorGroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Border CornerRadius="6"
                                Background="{DynamicResource Brush.SurfaceVariant}"
                                BorderBrush="{DynamicResource Brush.Divider}"
                                BorderThickness="1"
                                Padding="12"
                                Margin="0,0,0,12"
                                >
                            <DockPanel>
                                <TextBlock DockPanel.Dock="Top"
                                           Margin="0,0,0,8"
                                           Text="{TemplateBinding Header}"
                                           FontSize="13"
                                           FontWeight="SemiBold"
                                           Foreground="{DynamicResource Brush.Text}"/>
                                <ContentPresenter/>
                            </DockPanel>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 作用域内的 GroupBox 默认应用卡片样式 -->
        <Style TargetType="GroupBox" BasedOn="{StaticResource InspectorGroupBoxStyle}"/>

        <!-- 统一标签与编辑器样式（供各组件共用） -->
        <Style x:Key="InspectorLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{DynamicResource Brush.Text}"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,12,8"/>
        </Style>

        <Style x:Key="InspectorEditorStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <!-- 通用卡片边框（用于 ItemsControl 内部条目） -->
        <Style x:Key="InspectorCardBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource Brush.SurfaceVariant}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource Brush.Divider}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="0,6,0,6"/>
            
        </Style>

        <!-- ItemsControl 默认内边距与条目间距优化 -->
        <Style x:Key="InspectorItemsControlStyle" TargetType="ItemsControl">
            <Setter Property="Margin" Value="0,4,0,0"/>
            <Setter Property="ItemsPanel">
                <Setter.Value>
                    <ItemsPanelTemplate>
                        <StackPanel/>
                    </ItemsPanelTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="ItemContainerStyle">
                <Setter.Value>
                    <Style TargetType="ContentPresenter">
                        <Setter Property="Margin" Value="0,4,0,4"/>
                    </Style>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 添加组件按钮样式 -->
        <Style x:Key="AddComponentButtonStyle"
               TargetType="Button">
            <Setter Property="Background"
                    Value="{DynamicResource Brush.Surface}"/>
            <Setter Property="BorderBrush"
                    Value="{DynamicResource Brush.Divider}"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Padding"
                    Value="16,12"/>
            <Setter Property="Margin"
                    Value="0,8,0,0"/>
            <Setter Property="HorizontalAlignment"
                    Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment"
                    Value="Left"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Inspector标题栏（由 ColorZone 改为 Border） -->
        <Border Grid.Row="0"
                Background="{DynamicResource Brush.AppBar}"
                BorderBrush="{DynamicResource Brush.Divider}"
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <ipack:PackIconMaterial Grid.Column="0"
                                         Kind="Cog"
                                         Width="20"
                                         Height="20"
                                         Margin="0,0,12,0"
                                         Foreground="{DynamicResource Brush.TextSecondary}"/>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="属性"
                               FontSize="16"
                               FontWeight="Medium"
                               Foreground="{DynamicResource Brush.Text}"/>
                    <TextBlock x:Name="NodeTitleText"
                               Text="未选择节点"
                               FontSize="12"
                               Opacity="0.8"
                               Foreground="{DynamicResource Brush.TextSecondary}"/>
                </StackPanel>

                <Button Grid.Column="2"
                        Style="{StaticResource Button.Text}"
                        Width="32"
                        Height="32"
                        Foreground="{DynamicResource Brush.Text}"
                        ToolTip="属性设置">
                    <Button.Content>
                        <ipack:PackIconMaterial Kind="Cog" Width="18" Height="18" />
                    </Button.Content>
                </Button>
            </Grid>
        </Border>

        <!-- 组件列表 -->
        <StackPanel Grid.Row="1"
                    x:Name="ComponentsContainer"
                    Margin="0"/>

        <!-- 添加组件区域 -->
        <Border Grid.Row="2"
                Background="{DynamicResource Brush.SurfaceVariant}"
                BorderBrush="{DynamicResource Brush.Divider}"
                BorderThickness="0,1,0,0"
                Padding="12">
            <Button x:Name="AddComponentButton"
                    Style="{StaticResource AddComponentButtonStyle}"
                    Content="添加组件..."
                    Click="AddComponentButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <ipack:PackIconMaterial Kind="Plus"
                                                     Width="16"
                                                     Height="16"
                                                     Margin="0,0,8,0"
                                                     VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </Border>
    </Grid>
</UserControl>


