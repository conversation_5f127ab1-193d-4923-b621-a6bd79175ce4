using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;

using ProjectDigitizer.Infrastructure.UI.Animation;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Common
{
    /// <summary>
    /// 统一的连接点控件
    /// </summary>
    public partial class ConnectionPoint : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 连接点类型
        /// </summary>
        public static readonly DependencyProperty ConnectionTypeProperty =
            DependencyProperty.Register("ConnectionType", typeof(ConnectionType),
            typeof(ConnectionPoint), new PropertyMetadata(ConnectionType.Input));

        public ConnectionType ConnectionType
        {
            get { return (ConnectionType)GetValue(ConnectionTypeProperty); }
            set { SetValue(ConnectionTypeProperty, value); }
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public static readonly DependencyProperty IsConnectedProperty =
            DependencyProperty.Register("IsConnected", typeof(bool),
            typeof(ConnectionPoint), new PropertyMetadata(false, OnIsConnectedChanged));

        public bool IsConnected
        {
            get { return (bool)GetValue(IsConnectedProperty); }
            set { SetValue(IsConnectedProperty, value); }
        }

        /// <summary>
        /// 强调色（用于外圈和发光效果）
        /// </summary>
        public static readonly DependencyProperty AccentColorProperty =
            DependencyProperty.Register("AccentColor", typeof(Brush),
            typeof(ConnectionPoint), new PropertyMetadata(new SolidColorBrush(Colors.DodgerBlue)));

        public Brush AccentColor
        {
            get { return (Brush)GetValue(AccentColorProperty); }
            set { SetValue(AccentColorProperty, value); }
        }

        /// <summary>
        /// 是否处于连接模式（拖拽连接时）
        /// </summary>
        public static readonly DependencyProperty IsConnectingProperty =
            DependencyProperty.Register("IsConnecting", typeof(bool),
            typeof(ConnectionPoint), new PropertyMetadata(false, OnIsConnectingChanged));

        public bool IsConnecting
        {
            get { return (bool)GetValue(IsConnectingProperty); }
            set { SetValue(IsConnectingProperty, value); }
        }

        /// <summary>
        /// 连接器数据类型
        /// </summary>
        public static readonly DependencyProperty DataTypeProperty =
            DependencyProperty.Register("DataType", typeof(ConnectorDataType),
            typeof(ConnectionPoint), new PropertyMetadata(ConnectorDataType.Any, OnDataTypeChanged));

        public ConnectorDataType DataType
        {
            get { return (ConnectorDataType)GetValue(DataTypeProperty); }
            set { SetValue(DataTypeProperty, value); }
        }

        #endregion

        #region 私有字段

        private Storyboard? _pulseAnimation;
        private Storyboard? _connectionSuccessAnimation;
        private Storyboard? _connectionDisconnectAnimation;

        #endregion

        #region 构造函数

        public ConnectionPoint()
        {
            InitializeComponent();

            // 获取动画资源
            _pulseAnimation = FindResource("ConnectionPulseAnimation") as Storyboard;
            _connectionSuccessAnimation = FindResource("ConnectionSuccessAnimation") as Storyboard;
            _connectionDisconnectAnimation = FindResource("ConnectionDisconnectAnimation") as Storyboard;

            // 初始化数据类型样式
            Loaded += (s, e) => UpdateDataTypeStyle(DataType);
        }

        #endregion

        #region 属性变化处理

        private static void OnIsConnectedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as ConnectionPoint;
            if (control == null) return;

            var isConnected = (bool)e.NewValue;
            var wasConnected = (bool)e.OldValue;
            control.UpdateConnectionState(isConnected, wasConnected);
        }

        private static void OnIsConnectingChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as ConnectionPoint;
            if (control == null) return;

            var isConnecting = (bool)e.NewValue;
            control.UpdateConnectingState(isConnecting);
        }

        private static void OnDataTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as ConnectionPoint;
            if (control == null) return;

            var dataType = (ConnectorDataType)e.NewValue;
            control.UpdateDataTypeStyle(dataType);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新连接状态的视觉反馈
        /// </summary>
        private void UpdateConnectionState(bool isConnected, bool wasConnected)
        {
            var elementId = $"ConnectionPoint_{GetHashCode()}";

            if (isConnected && !wasConnected)
            {
                // 连接成功 - 播放连接成功动画
                if (_connectionSuccessAnimation != null)
                {
                    AnimationManager.Instance.StartAnimation(
                        _connectionSuccessAnimation,
                        AnimationType.Connection,
                        elementId);
                }

                // 更新颜色为连接状态
                var connectedColor = GetConnectedColor();
                AccentColor = new SolidColorBrush(connectedColor);
            }
            else if (!isConnected && wasConnected)
            {
                // 连接断开 - 播放断开动画
                if (_connectionDisconnectAnimation != null)
                {
                    AnimationManager.Instance.StartAnimation(
                        _connectionDisconnectAnimation,
                        AnimationType.Connection,
                        elementId);
                }

                // 恢复默认颜色
                AccentColor = Foreground;
            }
            else if (isConnected)
            {
                // 已连接状态 - 直接设置样式
                var connectionIndicator = FindName("ConnectionIndicator") as FrameworkElement;
                if (connectionIndicator != null)
                {
                    connectionIndicator.Opacity = 0.7;
                }

                var connectedColor = GetConnectedColor();
                AccentColor = new SolidColorBrush(connectedColor);
            }
            else
            {
                // 未连接状态 - 直接设置样式
                var connectionIndicator = FindName("ConnectionIndicator") as FrameworkElement;
                if (connectionIndicator != null)
                {
                    connectionIndicator.Opacity = 0.0;
                }

                AccentColor = Foreground;
            }
        }

        /// <summary>
        /// 更新连接状态的视觉反馈（向后兼容性重载）
        /// </summary>
        private void UpdateConnectionState(bool isConnected)
        {
            UpdateConnectionState(isConnected, !isConnected);
        }

        /// <summary>
        /// 更新连接模式的视觉反馈
        /// </summary>
        private void UpdateConnectingState(bool isConnecting)
        {
            var elementId = $"ConnectionPoint_Pulse_{GetHashCode()}";

            if (isConnecting)
            {
                // 开始脉冲动画
                if (_pulseAnimation != null)
                {
                    AnimationManager.Instance.StartAnimation(
                        _pulseAnimation,
                        AnimationType.Pulse,
                        elementId);
                }
            }
            else
            {
                // 停止脉冲动画
                AnimationManager.Instance.StopAnimation(elementId);
            }
        }

        /// <summary>
        /// 获取连接状态的颜色
        /// </summary>
        private Color GetConnectedColor()
        {
            return ConnectionType switch
            {
                ConnectionType.Input => Colors.LimeGreen,
                ConnectionType.Output => Colors.DodgerBlue,
                _ => Colors.Gray
            };
        }

        /// <summary>
        /// 更新数据类型样式
        /// </summary>
        private void UpdateDataTypeStyle(ConnectorDataType dataType)
        {
            var innerDotBrush = FindName("InnerDotBrush") as RadialGradientBrush;
            if (innerDotBrush == null) return;

            // 获取数据类型对应的颜色
            var dataTypeColor = GetDataTypeColor(dataType);
            var shape = GetDataTypeShape(dataType);

            // 更新渐变画刷的颜色
            if (innerDotBrush.GradientStops.Count >= 2)
            {
                innerDotBrush.GradientStops[0].Color = dataTypeColor;
                innerDotBrush.GradientStops[1].Color = Color.FromRgb(
                    (byte)(dataTypeColor.R * 0.7),
                    (byte)(dataTypeColor.G * 0.7),
                    (byte)(dataTypeColor.B * 0.7));
            }

            // 更新强调色
            AccentColor = new SolidColorBrush(dataTypeColor);

            // 根据数据类型调整形状（如果需要）
            UpdateConnectionPointShape(shape);
        }

        /// <summary>
        /// 获取数据类型对应的颜色
        /// </summary>
        private Color GetDataTypeColor(ConnectorDataType dataType)
        {
            return dataType switch
            {
                ConnectorDataType.Any => Color.FromRgb(74, 144, 226),      // #4A90E2 - 蓝色
                ConnectorDataType.Number => Color.FromRgb(255, 152, 0),    // #FF9800 - 橙色
                ConnectorDataType.Text => Color.FromRgb(156, 39, 176),     // #9C27B0 - 紫色
                ConnectorDataType.Boolean => Color.FromRgb(244, 67, 54),   // #F44336 - 红色
                ConnectorDataType.File => Color.FromRgb(56, 142, 60),      // #388E3C - 绿色
                ConnectorDataType.Geometry => Color.FromRgb(0, 188, 212),  // #00BCD4 - 青色
                ConnectorDataType.Control => Color.FromRgb(158, 158, 158), // #9E9E9E - 灰色
                _ => Color.FromRgb(74, 144, 226)                           // 默认蓝色
            };
        }

        /// <summary>
        /// 获取数据类型对应的形状
        /// </summary>
        private string GetDataTypeShape(ConnectorDataType dataType)
        {
            return dataType switch
            {
                ConnectorDataType.Control => "Square",
                ConnectorDataType.Boolean => "Diamond",
                _ => "Circle"
            };
        }

        /// <summary>
        /// 更新连接点形状
        /// </summary>
        private void UpdateConnectionPointShape(string shape)
        {
            // 目前保持圆形，未来可以扩展为不同形状
            // 这里可以根据需要实现形状变换逻辑
        }

        #endregion
    }

    /// <summary>
    /// 连接点类型枚举
    /// </summary>
    public enum ConnectionType
    {
        Input,
        Output
    }
}
