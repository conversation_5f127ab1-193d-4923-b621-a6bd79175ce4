using System;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace ProjectDigitizer.Studio.Controls.Canvas
{
    /// <summary>
    /// 画布缩放控制器
    /// </summary>
    public partial class CanvasZoomControl : UserControl, INotifyPropertyChanged
    {
        private double _zoomPercentage = 100.0;
        private double _minZoom = 10.0;
        private double _maxZoom = 500.0;
        private double _zoomStep = 10.0;

        /// <summary>
        /// 缩放百分比
        /// </summary>
        public double ZoomPercentage
        {
            get => _zoomPercentage;
            set
            {
                if (Math.Abs(_zoomPercentage - value) > 0.01)
                {
                    _zoomPercentage = Math.Max(_minZoom, Math.Min(_maxZoom, value));
                    OnPropertyChanged();

                    // 控件内部更新：选择最近预设项
                    if (!_isInternalUpdate && FindName("ZoomPresetCombo") is ComboBox combo)
                    {
                        try
                        {
                            _isInternalUpdate = true;
                            var nearest = FindNearestPreset(_zoomPercentage);
                            foreach (var obj in combo.Items)
                            {
                                if (obj is ComboBoxItem cbi && double.TryParse(cbi.Tag?.ToString(), out var pct))
                                {
                                    if (Math.Abs(pct - nearest) < 0.5)
                                    {
                                        combo.SelectedItem = cbi;
                                        break;
                                    }
                                }
                            }
                        }
                        finally { _isInternalUpdate = false; }
                    }

                    ZoomChanged?.Invoke(this, new ZoomChangedEventArgs(_zoomPercentage / 100.0));
                }
            }
        }

        /// <summary>
        /// 最小缩放百分比
        /// </summary>
        public double MinZoom
        {
            get => _minZoom;
            set
            {
                _minZoom = Math.Max(1.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 最大缩放百分比
        /// </summary>
        public double MaxZoom
        {
            get => _maxZoom;
            set
            {
                _maxZoom = Math.Min(1000.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 缩放步长
        /// </summary>
        public double ZoomStep
        {
            get => _zoomStep;
            set
            {
                _zoomStep = Math.Max(1.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 缩放变化事件
        /// </summary>
        public event EventHandler<ZoomChangedEventArgs>? ZoomChanged;

        /// <summary>
        /// 重置缩放事件
        /// </summary>
        public event EventHandler? ResetZoomRequested;

        /// <summary>
        /// 适应画布事件
        /// </summary>
        public event EventHandler? FitToCanvasRequested;

        public CanvasZoomControl()
        {
            InitializeComponent();
            DataContext = this;
            Loaded += CanvasZoomControl_Loaded;

        }

        private void CanvasZoomControl_Loaded(object sender, RoutedEventArgs e)
        {
            // 初始化：根据默认缩放选择最近的预设，避免下拉为空
            if (FindName("ZoomPresetCombo") is ComboBox initCombo)
            {
                try
                {
                    _isInternalUpdate = true;
                    var nearest = FindNearestPreset(_zoomPercentage);
                    foreach (var obj in initCombo.Items)
                    {
                        if (obj is ComboBoxItem cbi && double.TryParse(cbi.Tag?.ToString(), out var pct))
                        {
                            if (Math.Abs(pct - nearest) < 0.5)
                            {
                                initCombo.SelectedItem = cbi;
                                break;
                            }
                        }
                    }
                }
                finally { _isInternalUpdate = false; }
            }
        }

        /*

                    // 初始化：根据默认缩放选择最近的预设，避免下拉为空
                    if (FindName("ZoomPresetCombo") is ComboBox initCombo)
                    {
                        try
                        {
                            _isInternalUpdate = true;
                            var nearest = FindNearestPreset(_zoomPercentage);
                            foreach (var obj in initCombo.Items)
                            {
                                if (obj is ComboBoxItem cbi && double.TryParse(cbi.Tag?.ToString(), out var pct))
                                {
                                    if (Math.Abs(pct - nearest) < 0.5)
                                    {
                                        initCombo.SelectedItem = cbi;
                                        break;
                                    }
        }
                            }
                        }
                        finally { _isInternalUpdate = false; }
                    }


                }
        */


        /// <summary>
        /// 设置缩放值（不触发事件）
        /// </summary>
        /// <param name="zoomLevel">缩放级别（0.1 = 10%, 1.0 = 100%）</param>
        public void SetZoomLevel(double zoomLevel)
        {
            var percentage = zoomLevel * 100.0;
            if (Math.Abs(_zoomPercentage - percentage) > 0.01)
            {
                _zoomPercentage = Math.Max(_minZoom, Math.Min(_maxZoom, percentage));
                OnPropertyChanged(nameof(ZoomPercentage));

                // 同步下拉选中项（内部更新，避免循环）
                if (FindName("ZoomPresetCombo") is ComboBox combo)
                {
                    try
                    {
                        _isInternalUpdate = true;
                        var nearest = FindNearestPreset(_zoomPercentage);
                        foreach (var obj in combo.Items)
                        {
                            if (obj is ComboBoxItem cbi && double.TryParse(cbi.Tag?.ToString(), out var pct))
                            {
                                if (Math.Abs(pct - nearest) < 0.5)
                                {
                                    combo.SelectedItem = cbi;
                                    break;
                                }
                            }
                        }
                    }
                    finally { _isInternalUpdate = false; }
                }
            }
        }

        private bool _isInternalUpdate = false;

        private static readonly double[] Presets = new double[] { 25, 50, 75, 100, 125, 150, 200, 400 };

        private static double FindNearestPreset(double pct)
        {
            if (pct <= Presets[0]) return Presets[0];
            if (pct >= Presets[^1]) return Presets[^1];
            double best = Presets[0];
            double bestDiff = Math.Abs(pct - best);
            foreach (var v in Presets)
            {
                var d = Math.Abs(pct - v);
                if (d < bestDiff)
                {
                    best = v;
                    bestDiff = d;
                }
            }
            return best;
        }

        private void ZoomPresetCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox combo && combo.SelectedItem is ComboBoxItem item)
            {
                if (double.TryParse(item.Tag?.ToString(), out var pct))
                {
                    if (_isInternalUpdate) return; // 来自内部选中，避免循环
                    ZoomPercentage = pct; // 触发 ZoomChanged (pct/100)
                }
            }
        }


        /// <summary>
        /// 放大
        /// </summary>
        public void ZoomIn()
        {
            ZoomPercentage += ZoomStep;
        }

        /// <summary>
        /// 缩小
        /// </summary>
        public void ZoomOut()
        {
            ZoomPercentage -= ZoomStep;
        }

        /// <summary>
        /// 重置缩放到100%
        /// </summary>
        public void ResetZoom()
        {
            ZoomPercentage = 100.0;
            ResetZoomRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 适应画布
        /// </summary>
        public void FitToCanvas()
        {
            FitToCanvasRequested?.Invoke(this, EventArgs.Empty);
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomIn();
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomOut();
        }

        private void ResetZoomButton_Click(object sender, RoutedEventArgs e)
        {
            ResetZoom();
        }

        private void FitToCanvasButton_Click(object sender, RoutedEventArgs e)
        {
            FitToCanvas();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 缩放变化事件参数
    /// </summary>
    public class ZoomChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 缩放级别（0.1 = 10%, 1.0 = 100%）
        /// </summary>
        public double ZoomLevel { get; }

        /// <summary>
        /// 缩放百分比（10 = 10%, 100 = 100%）
        /// </summary>
        public double ZoomPercentage => ZoomLevel * 100.0;

        public ZoomChangedEventArgs(double zoomLevel)
        {
            ZoomLevel = zoomLevel;
        }
    }
}
