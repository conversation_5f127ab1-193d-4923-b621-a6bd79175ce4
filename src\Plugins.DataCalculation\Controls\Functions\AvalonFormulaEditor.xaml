<UserControl
    d:DesignHeight="160"
    d:DesignWidth="400"
    mc:Ignorable="d"
    x:Class="Plugins.DataCalculation.Controls.Functions.AvalonFormulaEditor"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  公式编辑器  -->
        <avalonEdit:TextEditor
            FontFamily="Consolas"
            FontSize="12"
            Grid.Row="0"
            HorizontalScrollBarVisibility="Auto"
            ShowLineNumbers="True"
            VerticalScrollBarVisibility="Auto"
            WordWrap="True"
            x:Name="Editor" />

        <!--  签名与说明信息条（根据光标上下文动态展示）  -->
        <Border
            Background="{DynamicResource Brush.SurfaceVariant}"
            BorderBrush="{DynamicResource Brush.Border}"
            BorderThickness="1,1,1,0"
            Grid.Row="1"
            Padding="6"
            Visibility="Collapsed"
            x:Name="InfoPanel">
            <StackPanel>
                <!--  签名/标题行（原 InfoTextBlock 保留兼容）  -->
                <TextBlock
                    FontSize="12"
                    FontWeight="SemiBold"
                    Foreground="{DynamicResource Brush.Text}"
                    TextWrapping="Wrap"
                    x:Name="InfoTextBlock" />
                <!--  参数说明行：根据当前参数位置加粗对应参数名  -->
                <TextBlock
                    FontSize="11"
                    Foreground="{DynamicResource Brush.TextSecondary}"
                    Margin="0,2,0,0"
                    TextWrapping="Wrap"
                    x:Name="InfoParamsTextBlock" />
                <!--  示例行  -->
                <TextBlock
                    FontSize="11"
                    Foreground="{DynamicResource Brush.TextSecondary}"
                    Margin="0,2,0,0"
                    TextWrapping="Wrap"
                    x:Name="InfoExampleTextBlock" />
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
