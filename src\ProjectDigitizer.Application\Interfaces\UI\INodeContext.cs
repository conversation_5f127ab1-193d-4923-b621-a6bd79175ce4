using System.Collections.Generic;

using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Application.Interfaces.UI
{
    /// <summary>
    /// 节点上下文的最小抽象，供插件属性面板读取/写入节点配置。
    /// 注意：不暴露前端（WPF）类型，保持 Application 层纯净。
    /// </summary>
    public interface INodeContext
    {
        /// <summary>
        /// 节点唯一 Id（对应 Module.Id）。
        /// </summary>
        string NodeId { get; }

        ModuleType ModuleType { get; }

        /// <summary>
        /// 读取节点参数（序列化存放于 Module.Parameters）。
        /// </summary>
        IReadOnlyDictionary<string, object> GetParameters();

        /// <summary>
        /// 批量设置/覆盖节点参数。
        /// </summary>
        void SetParameters(IDictionary<string, object> parameters);

        /// <summary>
        /// 设置单个参数。
        /// </summary>
        void SetParameter(string key, object value);
    }
}
