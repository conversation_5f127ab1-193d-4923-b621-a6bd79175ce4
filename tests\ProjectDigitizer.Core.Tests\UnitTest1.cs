using ProjectDigitizer.Core.Exceptions;
using ProjectDigitizer.Core.Tests.Fixtures;

namespace ProjectDigitizer.Core.Tests;

/// <summary>
/// Core层基础功能测试
/// </summary>
[Collection("Core Tests")]
public class CoreBasicTests
{
    private readonly CoreTestFixture _fixture;

    public CoreBasicTests(CoreTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public void ServiceProvider_Should_BeConfigured()
    {
        // Arrange & Act
        var serviceProvider = _fixture.ServiceProvider;

        // Assert
        serviceProvider.Should().NotBeNull();
    }

    [Fact]
    public void DomainException_Should_HaveCorrectProperties()
    {
        // Arrange
        var message = "Test error message";
        var errorCode = "TEST_ERROR";

        // Act
        var exception = new DomainException(message, errorCode);

        // Assert
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be(errorCode);
        exception.Details.Should().NotBeNull();
        exception.Details.Should().BeEmpty();
    }

    [Fact]
    public void DomainException_WithDetail_Should_AddDetail()
    {
        // Arrange
        var exception = new DomainException("Test message");
        var key = "TestKey";
        var value = "TestValue";

        // Act
        var result = exception.WithDetail(key, value);

        // Assert
        result.Should().BeSameAs(exception);
        exception.Details.Should().ContainKey(key);
        exception.Details[key].Should().Be(value);
    }
}
