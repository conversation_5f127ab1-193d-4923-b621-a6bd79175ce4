using System;
using System.Collections.Generic;

namespace ProjectDigitizer.Application.Performance
{
    /// <summary>
    /// 性能优化的连接器状态批更新接口
    /// </summary>
    public interface IPerformanceOptimizedConnectorManager : IDisposable
    {
        /// <summary>
        /// 请求单个连接器状态更新（聚合后批处理）
        /// </summary>
        void RequestConnectorUpdate<T>(T connector) where T : class;

        /// <summary>
        /// 批量请求连接器状态更新
        /// </summary>
        void BatchUpdateConnectors<T>(IEnumerable<T> connectors) where T : class;

        /// <summary>
        /// 立即冲刷待处理更新
        /// </summary>
        void FlushPendingUpdates();
    }
}

