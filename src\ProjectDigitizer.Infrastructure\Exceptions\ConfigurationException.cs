namespace ProjectDigitizer.Infrastructure.Exceptions;

/// <summary>
/// 配置相关异常
/// </summary>
public class ConfigurationException : InfrastructureException
{
    public string? ConfigurationKey { get; }

    public ConfigurationException(string message) : base(message)
    {
    }

    public ConfigurationException(string message, string configurationKey) : base(message)
    {
        ConfigurationKey = configurationKey;
    }

    public ConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ConfigurationException(string message, string configurationKey, Exception innerException) : base(message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}
