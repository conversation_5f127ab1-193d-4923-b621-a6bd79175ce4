<Application
    x:Class="ProjectDigitizer.Studio.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:local="clr-namespace:ProjectDigitizer.Studio"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  Telerik Fluent主题资源 - 使用正确的路径  -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/System.Windows.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.Input.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.Navigation.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.RibbonView.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.RichTextBox.xaml"/> -->

                <!--  MaterialDesign 主题  -->


                <!--  MaterialDesign 控件样式  -->


                <!--  WPF UI 主题与控件资源  -->
                <ui:ThemesDictionary Theme="Light" />
                <ui:ControlsDictionary />

                <!--  统一主题与全局控件样式  -->
                <ResourceDictionary Source="Resources/Styles/Theme/Palette.xaml" />
                <ResourceDictionary Source="Resources/Styles/Theme/Brushes.xaml" />
                <ResourceDictionary Source="Resources/Styles/Theme/Shapes.xaml" />
                <ResourceDictionary Source="Resources/Styles/Theme/Typography.xaml" />
                
                <ResourceDictionary Source="Resources/Styles/Controls/BaseControls.xaml" />

                <!--  Nodify 主题（使用浅色主题配合Fluent Design）  -->
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/Light.xaml" />

                <!--  Fluent Design 节点模板  -->
                <ResourceDictionary Source="Resources/NodeTemplates.xaml" />

                <!--  自定义样式  -->
                <ResourceDictionary>
                    <!--  全局禁用焦点视觉样式  -->
                    <Style x:Key="{x:Static SystemParameters.FocusVisualStyleKey}">
                        <Setter Property="Control.Template" Value="{x:Null}" />
                    </Style>

                    <!--  连接点颜色资源  -->
                    <!--  输入连接点颜色  -->
                    <SolidColorBrush Color="#4CAF50" x:Key="InputConnectionBrush" />
                    <SolidColorBrush Color="#66BB6A" x:Key="InputConnectionAccentBrush" />

                    <!--  输出连接点颜色  -->
                    <SolidColorBrush Color="#2196F3" x:Key="OutputConnectionBrush" />
                    <SolidColorBrush Color="#42A5F5" x:Key="OutputConnectionAccentBrush" />

                    <!--  数据计算节点连接点颜色  -->
                    <SolidColorBrush Color="#9C27B0" x:Key="DataNodeInputConnectionBrush" />
                    <SolidColorBrush Color="#BA68C8" x:Key="DataNodeInputConnectionAccentBrush" />
                    <SolidColorBrush Color="#673AB7" x:Key="DataNodeOutputConnectionBrush" />
                    <SolidColorBrush Color="#9575CD" x:Key="DataNodeOutputConnectionAccentBrush" />

                    <!--  连接点通用颜色  -->
                    <SolidColorBrush Color="White" x:Key="ConnectionPointBorderBrush" />
                    <SolidColorBrush Color="#40000000" x:Key="ConnectionPointShadowBrush" />

                    <!--  连接点动画资源  -->
                    <Storyboard x:Key="ConnectionPointHoverAnimation">
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                            To="1.2" />
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                            To="1.2" />
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="Opacity"
                            To="1.0" />
                    </Storyboard>

                    <Storyboard x:Key="ConnectionPointLeaveAnimation">
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                            To="1.0" />
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                            To="1.0" />
                        <DoubleAnimation
                            Duration="0:0:0.2"
                            Storyboard.TargetProperty="Opacity"
                            To="0.8" />
                    </Storyboard>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>

