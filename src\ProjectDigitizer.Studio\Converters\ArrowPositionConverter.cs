using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 箭头位置转换器
    /// 根据连接线的源点和目标点计算箭头在目标点的精确位置
    /// </summary>
    public class ArrowPositionConverter : IMultiValueConverter
    {
        // 前移距离：设为“箭头从底边到尖端”的长度（像素）
        public double ForwardOffset { get; set; } = 12;
        /// <summary>
        /// 箭头距离目标点的偏移距离（像素）
        /// 箭头长度为8像素，偏移8像素让箭头底边与线条终点连接
        /// </summary>
        public double ArrowOffset { get; set; } = 20.0;

        /// <summary>
        /// 将源点和目标点转换为箭头位置
        /// </summary>
        /// <param name="values">values[0]: Source.X, values[1]: Source.Y, values[2]: Target.X, values[3]: Target.Y</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数（"X" 或 "Y"）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>箭头的X或Y坐标</returns>
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 4)
                return 0.0;

            // 解析必要坐标
            if (!TryParseDouble(values[0], out double sourceX) ||
                !TryParseDouble(values[1], out double sourceY) ||
                !TryParseDouble(values[2], out double targetX) ||
                !TryParseDouble(values[3], out double targetY))
            {
                return 0.0;
            }

            // 可选：同端口连接序号，用于侧向避让；没有则为1
            int connectionIndex = 1;
            if (values.Length >= 5)
            {
                if (values[4] is int ci)
                    connectionIndex = Math.Max(1, ci);
                else if (TryParseDouble(values[4], out double cid))
                    connectionIndex = Math.Max(1, (int)Math.Round(cid));
            }

            // 可选：线条粗细，用于决定避让距离；没有则为2.5
            double strokeThickness = 2.5;
            if (values.Length >= 6 && TryParseDouble(values[5], out double st))
            {
                strokeThickness = st;
            }

            // 方向与长度
            double dx = targetX - sourceX;
            double dy = targetY - sourceY;
            double length = Math.Sqrt(dx * dx + dy * dy);
            if (length < 0.001)
                return parameter?.ToString() == "X" ? targetX : targetY;

            double ux = dx / length;
            double uy = dy / length;

            // 垂直单位法向量（右手法则）
            double nx = -uy;
            double ny = ux;

            // 前向偏移：让箭头完全在连接线外部，避免重叠
            double arrowLength = 24.0; // 与箭头宽度一致
            double forward = arrowLength + strokeThickness * 0.5;

            // 侧向避让：同一端口的多条线分层左右交替偏移
            double separationBase = Math.Max(2.0, strokeThickness * 0.8);
            int idx = Math.Max(0, connectionIndex - 1);
            int side = (idx % 2 == 0) ? 1 : -1; // 奇偶交替：+/-
            int level = (idx / 2) + 0;          // 第0层开始，第一条不偏移
            double lateral = (idx == 0 ? 0.0 : separationBase * level) * side;

            // 目标位置 = 目标点 - 前向偏移 * 切线方向 + 侧向偏移 * 法向量
            double px = targetX - ux * forward + nx * lateral;
            double py = targetY - uy * forward + ny * lateral;

            return parameter?.ToString() == "X" ? px : py;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("ArrowPositionConverter does not support ConvertBack");
        }

        /// <summary>
        /// 安全解析double值
        /// </summary>
        private static bool TryParseDouble(object value, out double result)
        {
            result = 0.0;

            if (value == null)
                return false;

            if (value is double d)
            {
                result = d;
                return true;
            }

            if (value is float f)
            {
                result = f;
                return true;
            }

            if (value is int i)
            {
                result = i;
                return true;
            }

            return double.TryParse(value.ToString(), out result);
        }
    }
}
