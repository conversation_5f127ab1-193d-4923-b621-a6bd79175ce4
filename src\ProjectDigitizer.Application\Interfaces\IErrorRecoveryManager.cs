namespace ProjectDigitizer.Application.Interfaces;

/// <summary>
/// 错误恢复管理器接口
/// </summary>
public interface IErrorRecoveryManager
{
    /// <summary>
    /// 尝试恢复错误
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文</param>
    /// <returns>恢复结果</returns>
    Task<RecoveryResult> TryRecoverAsync(Exception exception, object context);

    /// <summary>
    /// 注册恢复策略
    /// </summary>
    /// <param name="strategy">恢复策略</param>
    void RegisterStrategy(IErrorRecoveryStrategy strategy);

    /// <summary>
    /// 获取可用的恢复策略
    /// </summary>
    /// <param name="exceptionType">异常类型</param>
    /// <returns>恢复策略列表</returns>
    IEnumerable<IErrorRecoveryStrategy> GetAvailableStrategies(Type exceptionType);
}

