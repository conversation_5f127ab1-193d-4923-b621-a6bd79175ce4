using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Performance;
using ProjectDigitizer.Core;
using ProjectDigitizer.Core.Configuration;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure;
using ProjectDigitizer.Infrastructure.Configuration;
using ProjectDigitizer.Infrastructure.Services;
using ProjectDigitizer.Studio.ErrorHandling;
using ProjectDigitizer.Studio.ViewModels;
using ProjectDigitizer.Studio.Views;

using Serilog;

using Wpf.Ui.Appearance;


namespace ProjectDigitizer.Studio
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : System.Windows.Application
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool AllocConsole();

        private IHost? _host;
        private IServiceProvider? _serviceProvider;
        private GlobalExceptionHandler? _globalExceptionHandler;



        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 分配控制台窗口以便查看输出
                AllocConsole();

                // 设置关闭模式为显式关闭，防止主窗口创建前程序退出
                ShutdownMode = ShutdownMode.OnExplicitShutdown;

                System.Diagnostics.Debug.WriteLine("=== 应用程序启动开始 ===");
                Console.WriteLine("=== 应用程序启动开始 ===");

                // 配置依赖注入容器
                ConfigureServices();

                // 设置全局异常处理
                SetupGlobalExceptionHandling();

                base.OnStartup(e);

                System.Diagnostics.Debug.WriteLine("=== 应用程序启动完成 ===");
                Console.WriteLine("=== 应用程序启动完成 ===");
            }
            catch (Exception ex)
            {
                var errorMessage = $"应用程序启动失败: {ex.Message}\n\n详细信息:\n{ex.StackTrace}";
                System.Diagnostics.Debug.WriteLine($"启动异常: {errorMessage}");
                Console.WriteLine($"启动异常: {errorMessage}");
                MessageBox.Show(errorMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // 释放资源管理器中的所有资源
                var resourceManager = _serviceProvider?.GetService<IResourceManager>();
                resourceManager?.DisposeAllResources();

                // 释放服务生命周期管理器
                var lifecycleManager = _serviceProvider?.GetService<IServiceLifecycleManager>();
                lifecycleManager?.DisposeAllServices();

                // 释放主机
                _host?.Dispose();

                // 关闭日志
                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                // 记录退出时的错误，但不阻止应用程序退出
                System.Diagnostics.Debug.WriteLine($"应用程序退出时发生错误: {ex.Message}");
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void ConfigureServices()
        {
            System.Diagnostics.Debug.WriteLine("=== 开始配置服务 ===");
            Console.WriteLine("=== 开始配置服务 ===");

            // 构建配置
            var configuration = BuildConfiguration();
            System.Diagnostics.Debug.WriteLine("配置构建完成");
            Console.WriteLine("配置构建完成");

            // 预先创建必要的目录以避免配置验证警告
            EnsureRequiredDirectoriesExist(configuration);
            System.Diagnostics.Debug.WriteLine("目录创建完成");
            Console.WriteLine("目录创建完成");

            // 验证配置
            ValidateConfiguration(configuration);
            System.Diagnostics.Debug.WriteLine("配置验证完成");
            Console.WriteLine("配置验证完成");

            // 配置Serilog
            System.Diagnostics.Debug.WriteLine("开始配置Serilog...");
            Console.WriteLine("开始配置Serilog...");
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
            System.Diagnostics.Debug.WriteLine("Serilog配置完成");
            Console.WriteLine("Serilog配置完成");

            // 构建主机
            System.Diagnostics.Debug.WriteLine("开始构建主机...");
            Console.WriteLine("开始构建主机...");
            _host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    System.Diagnostics.Debug.WriteLine("开始注册服务...");
                    Console.WriteLine("开始注册服务...");

                    // 注册配置
                    services.AddSingleton<IConfiguration>(configuration);
                    System.Diagnostics.Debug.WriteLine("配置服务注册完成");
                    Console.WriteLine("配置服务注册完成");

                    // 注册各层服务
                    System.Diagnostics.Debug.WriteLine("注册Core服务...");
                    Console.WriteLine("注册Core服务...");
                    services.AddCore();

                    System.Diagnostics.Debug.WriteLine("注册Application服务...");
                    Console.WriteLine("注册Application服务...");
                    services.AddApplication();

                    System.Diagnostics.Debug.WriteLine("注册Infrastructure服务...");
                    Console.WriteLine("注册Infrastructure服务...");
                    services.AddInfrastructure(configuration);

                    // 服务验证已移除，使用编译时检测替代

                    // 注册UI服务
                    System.Diagnostics.Debug.WriteLine("注册UI服务...");
                    Console.WriteLine("注册UI服务...");
                    services.AddSingleton<MainWindow>();
                    services.AddTransient<PluginManagerWindow>();

                    // UI 查询服务（供插件属性面板使用）
                    services.AddSingleton<ProjectDigitizer.Application.Interfaces.UI.INodeGraphQuery, ProjectDigitizer.Studio.Services.NodeGraphQueryService>();

                    // 注册ViewModels
                    System.Diagnostics.Debug.WriteLine("注册ViewModels...");
                    Console.WriteLine("注册ViewModels...");
                    RegisterViewModels(services);

                    System.Diagnostics.Debug.WriteLine("服务注册完成");
                    Console.WriteLine("服务注册完成");
                })
                .Build();

            System.Diagnostics.Debug.WriteLine("主机构建完成");
            Console.WriteLine("主机构建完成");

            _serviceProvider = _host.Services;
            // 在创建主窗口前合并插件的资源字典，确保 XAML StaticResource 可解析
            TryMergePluginResourceDictionaries();
            System.Diagnostics.Debug.WriteLine("服务提供程序设置完成");
            Console.WriteLine("服务提供程序设置完成");

            // 启动主机
            System.Diagnostics.Debug.WriteLine("启动主机...");
            Console.WriteLine("启动主机...");
            _host.StartAsync();
            System.Diagnostics.Debug.WriteLine("主机启动完成");
            Console.WriteLine("主机启动完成");

            // 服务验证已移除，直接进入主窗口创建

            // 设置主窗口
            try
            {
                System.Diagnostics.Debug.WriteLine("开始创建主窗口...");

                // 验证关键依赖项是否可用
                ValidateKeyDependencies();

                var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                System.Diagnostics.Debug.WriteLine("主窗口实例创建成功");

                MainWindow = mainWindow;

                // 确保主窗口正确显示
                mainWindow.Show();
                mainWindow.Activate();
                mainWindow.WindowState = WindowState.Normal;

                // 设置关闭模式为主窗口关闭时退出
                ShutdownMode = ShutdownMode.OnMainWindowClose;

                System.Diagnostics.Debug.WriteLine("主窗口已成功显示");
                Console.WriteLine("主窗口已成功显示");

                // 验证主窗口是否真的可见
                System.Diagnostics.Debug.WriteLine($"主窗口可见性: {mainWindow.IsVisible}");
                System.Diagnostics.Debug.WriteLine($"主窗口状态: {mainWindow.WindowState}");
                System.Diagnostics.Debug.WriteLine($"主窗口位置: {mainWindow.Left}, {mainWindow.Top}");
                System.Diagnostics.Debug.WriteLine($"主窗口大小: {mainWindow.Width} x {mainWindow.Height}");
                Console.WriteLine($"主窗口可见性: {mainWindow.IsVisible}, 状态: {mainWindow.WindowState}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示主窗口时发生错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"错误堆栈: {ex.StackTrace}");
                MessageBox.Show($"显示主窗口失败: {ex.Message}\n\n详细信息:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        /// <summary>
        /// 尝试将插件中的 Resources/PluginResources.xaml 合并到全局资源中。
        /// 这样 MainWindow XAML 中使用的 StaticResource 能够解析到插件提供的样式/模板。
        /// </summary>
        private void TryMergePluginResourceDictionaries()
        {
            try
            {
                var catalog = _serviceProvider?.GetService<ProjectDigitizer.Application.Interfaces.IPluginCatalog>();
                if (catalog?.Assemblies is not { Count: > 0 })
                    return;

                foreach (var asm in catalog.Assemblies)
                {
                    try
                    {
                        var asmName = asm.GetName().Name;
                        if (string.IsNullOrWhiteSpace(asmName))
                            continue;

                        var uri = new Uri($"pack://application:,,,/{asmName};component/Resources/PluginResources.xaml", UriKind.Absolute);
                        var dict = new ResourceDictionary { Source = uri };
                        Current.Resources.MergedDictionaries.Add(dict);
                        System.Diagnostics.Debug.WriteLine($"[Plugins] Merged ResourceDictionary from {asmName}");
                    }
                    catch (Exception ex)
                    {
                        // 插件未提供资源字典或加载失败时忽略
                        System.Diagnostics.Debug.WriteLine($"[Plugins] No PluginResources for {asm.FullName}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[Plugins] Merge resources failed: {ex.Message}");
            }
        }

        private static IConfiguration BuildConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            return builder.Build();
        }

        /// <summary>
        /// 验证关键依赖项是否可用
        /// </summary>
        private void ValidateKeyDependencies()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始验证关键依赖项...");

                if (_serviceProvider == null)
                {
                    throw new InvalidOperationException("服务提供程序未初始化");
                }

                // 验证 CanvasViewModel 的依赖项
                var virtualizationManager = _serviceProvider.GetService<ICanvasVirtualizationManager>();
                System.Diagnostics.Debug.WriteLine($"ICanvasVirtualizationManager: {(virtualizationManager != null ? "已注册" : "未注册")}");

                var connectorManager = _serviceProvider.GetService<IPerformanceOptimizedConnectorManager>();
                System.Diagnostics.Debug.WriteLine($"IPerformanceOptimizedConnectorManager: {(connectorManager != null ? "已注册" : "未注册")}");

                var layoutService = _serviceProvider.GetService<INodeLayoutService>();
                System.Diagnostics.Debug.WriteLine($"INodeLayoutService: {(layoutService != null ? "已注册" : "未注册")}");

                // 验证 MainWindow 的依赖项
                var canvasViewModel = _serviceProvider.GetService<CanvasViewModel>();
                System.Diagnostics.Debug.WriteLine($"CanvasViewModel: {(canvasViewModel != null ? "已注册" : "未注册")}");

                var projectFileAdapter = _serviceProvider.GetService<IProjectFileAdapter>();
                System.Diagnostics.Debug.WriteLine($"IProjectFileAdapter: {(projectFileAdapter != null ? "已注册" : "未注册")}");

                System.Diagnostics.Debug.WriteLine("关键依赖项验证完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证依赖项时发生错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 预先创建必要的目录以避免配置验证警告
        /// </summary>
        /// <param name="configuration">配置对象</param>
        private static void EnsureRequiredDirectoriesExist(IConfiguration configuration)
        {
            try
            {
                // 创建数据目录
                var dataDirectory = configuration.GetValue<string>("DataDirectory", "Data");
                if (!string.IsNullOrWhiteSpace(dataDirectory))
                {
                    var fullDataPath = Path.GetFullPath(dataDirectory);
                    if (!Directory.Exists(fullDataPath))
                    {
                        Directory.CreateDirectory(fullDataPath);
                        System.Diagnostics.Debug.WriteLine($"预创建数据目录: {fullDataPath}");
                    }
                }

                // 创建日志目录
                var logPath = configuration.GetValue<string>("Serilog:WriteTo:1:Args:path", "logs/app-.log");
                if (!string.IsNullOrWhiteSpace(logPath))
                {
                    var logDirectory = Path.GetDirectoryName(logPath);
                    if (!string.IsNullOrEmpty(logDirectory))
                    {
                        var fullLogPath = Path.GetFullPath(logDirectory);
                        if (!Directory.Exists(fullLogPath))
                        {
                            Directory.CreateDirectory(fullLogPath);
                            System.Diagnostics.Debug.WriteLine($"预创建日志目录: {fullLogPath}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果预创建目录失败，记录错误但不阻止程序启动
                System.Diagnostics.Debug.WriteLine($"预创建目录时发生错误: {ex.Message}");
            }
        }

        private static void RegisterViewModels(IServiceCollection services)
        {
            // 注册实际存在的ViewModels
            services.AddTransient<CanvasViewModel>();
            services.AddTransient<PluginManagerViewModel>();
            services.AddTransient<ConnectionViewModel>();
            services.AddTransient<ConnectorViewModel>();
            services.AddTransient<ModuleNodeViewModel>();
            services.AddTransient<PendingConnectionViewModel>();

            // 性能管理组件现在通过Infrastructure层注册
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public static T GetService<T>() where T : class
        {
            var app = Current as App;
            return app?._serviceProvider?.GetRequiredService<T>()
                ?? throw new InvalidOperationException($"无法获取服务 {typeof(T).Name}");
        }

        /// <summary>
        /// 获取可选服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例或null</returns>
        public static T? GetOptionalService<T>() where T : class
        {
            var app = Current as App;
            return app?._serviceProvider?.GetService<T>();
        }

        /// <summary>
        /// 验证应用程序配置
        /// </summary>
        /// <param name="configuration">配置对象</param>
        private static void ValidateConfiguration(IConfiguration configuration)
        {
            try
            {
                // 创建配置验证器
                var validator = new ConfigurationValidator();

                // 绑定应用程序设置
                var appSettings = new ApplicationSettings();
                configuration.Bind(appSettings);

                // 验证配置
                var validationResult = validator.ValidateApplicationSettings(appSettings);

                if (!validationResult.IsValid)
                {
                    var errors = string.Join("\n", validationResult.Errors.Select(e => $"- {e.PropertyName}: {e.ErrorMessage}"));
                    var warnings = string.Join("\n", validationResult.Warnings.Select(w => $"- {w.PropertyName}: {w.WarningMessage}"));

                    var message = "配置验证失败：\n\n错误：\n" + errors;
                    if (!string.IsNullOrEmpty(warnings))
                    {
                        message += "\n\n警告：\n" + warnings;
                    }

                    MessageBox.Show(message, "配置错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Environment.Exit(1);
                }
                else if (validationResult.Warnings.Count > 0)
                {
                    var warnings = string.Join("\n", validationResult.Warnings.Select(w => $"- {w.PropertyName}: {w.WarningMessage}"));
                    var message = "配置验证警告：\n\n" + warnings + "\n\n是否继续启动应用程序？";

                    var result = MessageBox.Show(message, "配置警告", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                    if (result == MessageBoxResult.No)
                    {
                        Environment.Exit(0);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置验证时发生错误：{ex.Message}", "配置错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupGlobalExceptionHandling()
        {
            try
            {
                if (_serviceProvider == null)
                    return;

                // 获取日志记录器
                var logger = _serviceProvider.GetService<ILogger<GlobalExceptionHandler>>();
                if (logger == null)
                    return;

                // 检查是否为开发环境
                var configuration = _serviceProvider.GetService<IConfiguration>();
                var isDevelopment = configuration?.GetValue<string>("Application:Environment") == "Development";

                // 创建全局异常处理器
                _globalExceptionHandler = new GlobalExceptionHandler(logger, isDevelopment);

                // 注册应用程序域未处理异常事件
                AppDomain.CurrentDomain.UnhandledException += _globalExceptionHandler.HandleUnhandledException;

                // 注册WPF调度器未处理异常事件
                DispatcherUnhandledException += _globalExceptionHandler.HandleDispatcherUnhandledException;

                // 注册任务未处理异常事件
                TaskScheduler.UnobservedTaskException += _globalExceptionHandler.HandleTaskException;
            }
            catch (Exception ex)
            {
                // 如果全局异常处理器设置失败，至少记录到调试输出
                System.Diagnostics.Debug.WriteLine($"设置全局异常处理失败: {ex.Message}");
            }
        }
    }
}
