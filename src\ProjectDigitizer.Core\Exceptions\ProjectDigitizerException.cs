namespace ProjectDigitizer.Core.Exceptions;

/// <summary>
/// ProjectDigitizer应用程序异常基类
/// </summary>
public abstract class ProjectDigitizerException : Exception
{
    /// <summary>
    /// 异常代码
    /// </summary>
    public string? ErrorCode { get; protected set; }

    /// <summary>
    /// 异常详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; protected set; } = new();

    protected ProjectDigitizerException(string message) : base(message)
    {
    }

    protected ProjectDigitizerException(string message, Exception innerException) : base(message, innerException)
    {
    }

    protected ProjectDigitizerException(string message, string? errorCode) : base(message)
    {
        ErrorCode = errorCode;
    }

    protected ProjectDigitizerException(string message, string? errorCode, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 添加异常详细信息
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <returns>当前异常实例</returns>
    public ProjectDigitizerException WithDetail(string key, object value)
    {
        Details[key] = value;
        return this;
    }

    /// <summary>
    /// 添加多个异常详细信息
    /// </summary>
    /// <param name="details">详细信息字典</param>
    /// <returns>当前异常实例</returns>
    public ProjectDigitizerException WithDetails(Dictionary<string, object> details)
    {
        foreach (var detail in details)
        {
            Details[detail.Key] = detail.Value;
        }
        return this;
    }
}
