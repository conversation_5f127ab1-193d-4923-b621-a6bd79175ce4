using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Threading;

using ProjectDigitizer.Application.Performance;

namespace ProjectDigitizer.Infrastructure.Performance
{
    /// <summary>
    /// 性能优化的连接器管理器
    /// </summary>
    public class PerformanceOptimizedConnectorManager : IPerformanceOptimizedConnectorManager
    {
        private readonly DispatcherTimer _updateTimer;
        private readonly HashSet<object> _pendingUpdates = new();
        private readonly object _lockObject = new();
        private const int UPDATE_INTERVAL_MS = 16; // 约60FPS

        public PerformanceOptimizedConnectorManager()
        {
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(UPDATE_INTERVAL_MS)
            };
            _updateTimer.Tick += OnUpdateTimerTick;
        }

        /// <summary>
        /// 请求更新连接器状态（节流处理）
        /// </summary>
        public void RequestConnectorUpdate<T>(T connector) where T : class
        {
            if (connector == null) return;

            lock (_lockObject)
            {
                _pendingUpdates.Add(connector);

                if (!_updateTimer.IsEnabled)
                {
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 批量更新连接器状态
        /// </summary>
        public void BatchUpdateConnectors<T>(IEnumerable<T> connectors) where T : class
        {
            if (connectors == null) return;

            lock (_lockObject)
            {
                foreach (var connector in connectors)
                {
                    _pendingUpdates.Add(connector);
                }

                if (!_updateTimer.IsEnabled && _pendingUpdates.Count > 0)
                {
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 立即处理所有待定更新
        /// </summary>
        public void FlushPendingUpdates()
        {
            lock (_lockObject)
            {
                if (_pendingUpdates.Count > 0)
                {
                    ProcessPendingUpdates();
                }
                _updateTimer.Stop();
            }
        }

        private void OnUpdateTimerTick(object? sender, EventArgs e)
        {
            lock (_lockObject)
            {
                if (_pendingUpdates.Count > 0)
                {
                    ProcessPendingUpdates();
                }
                else
                {
                    _updateTimer.Stop();
                }
            }
        }

        private void ProcessPendingUpdates()
        {
            var connectorsToUpdate = _pendingUpdates.ToList();
            _pendingUpdates.Clear();

            // 在UI线程上批量处理更新
            foreach (var connector in connectorsToUpdate)
            {
                try
                {
                    // 使用反射来触发UI更新，保持通用性
                    var connectorType = connector.GetType();
                    var isConnectedProperty = connectorType.GetProperty("IsConnected");

                    if (isConnectedProperty != null && isConnectedProperty.CanRead && isConnectedProperty.CanWrite)
                    {
                        var currentValue = isConnectedProperty.GetValue(connector);
                        isConnectedProperty.SetValue(connector, currentValue);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"连接器更新错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _updateTimer?.Stop();
            lock (_lockObject)
            {
                _pendingUpdates.Clear();
            }
        }
    }
}
