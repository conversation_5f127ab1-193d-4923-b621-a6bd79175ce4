<Project>
  <PropertyGroup>
    <!-- 启用中央包管理 -->
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <!-- 启用包版本覆盖 -->
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
  </PropertyGroup>
  <ItemGroup Label="Core Framework Packages">
    <!-- Microsoft Extensions -->
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup Label="WPF and UI Packages">
    <!-- WPF相关包 -->
    <PackageVersion Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    
    <PackageVersion Include="wpf-ui" Version="4.0.3" />
    <PackageVersion Include="Nodify" Version="7.0.3" />
    <PackageVersion Include="MahApps.Metro.IconPacks.Material" Version="6.1.0" />
    <PackageVersion Include="AvalonEdit" Version="4.3.0-alpha-00000000" />
  </ItemGroup>
  <ItemGroup Label="Data and Serialization">
    <!-- 数据处理 -->
    <PackageVersion Include="System.Text.Json" Version="8.0.5" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>
  <ItemGroup Label="Validation and Mapping">
    <!-- 验证和映射 -->
    <PackageVersion Include="FluentValidation" Version="11.9.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
    <PackageVersion Include="AutoMapper" Version="12.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
  </ItemGroup>
  <ItemGroup Label="Testing Packages">
    <!-- 测试框架 -->
    <PackageVersion Include="xunit" Version="2.6.6" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.6" />
    <PackageVersion Include="xunit.analyzers" Version="1.10.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Moq" Version="4.20.70" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.0" />
  </ItemGroup>
  <ItemGroup Label="Code Analysis">
    <!-- 代码分析 -->
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" />
    <PackageVersion Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="8.0.0" />
    <PackageVersion Include="StyleCop.Analyzers" Version="1.1.118" />
  </ItemGroup>
  <ItemGroup Label="Utilities">
    <!-- 实用工具 -->
    <PackageVersion Include="Serilog" Version="3.1.1" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageVersion Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="8.0.0" />
  </ItemGroup>
</Project>
