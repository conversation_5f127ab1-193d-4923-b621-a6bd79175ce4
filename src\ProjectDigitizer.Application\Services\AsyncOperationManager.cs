using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;

namespace ProjectDigitizer.Application.Services
{
    /// <summary>
    /// 异步操作管理器实现
    /// 提供统一的异步操作管理、进度报告和取消机制
    /// </summary>
    public class AsyncOperationManager : IAsyncOperationManager, IDisposable
    {
        private readonly ILogger<AsyncOperationManager> _logger;
        private readonly IStructuredLogger _structuredLogger;
        private readonly ConcurrentDictionary<string, IAsyncOperationHandle> _activeOperations;
        private readonly SemaphoreSlim _operationSemaphore;
        private bool _disposed;

        public event EventHandler<OperationStatusChangedEventArgs>? OperationStatusChanged;
        public event EventHandler<OperationProgressEventArgs>? OperationProgressUpdated;

        public AsyncOperationManager(ILogger<AsyncOperationManager> logger, IStructuredLogger structuredLogger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _structuredLogger = structuredLogger ?? throw new ArgumentNullException(nameof(structuredLogger));
            _activeOperations = new ConcurrentDictionary<string, IAsyncOperationHandle>();
            _operationSemaphore = new SemaphoreSlim(1, 1);
        }

        public async Task<IAsyncOperationHandle<T>> StartOperationAsync<T>(
            string operationName,
            Func<IProgress<OperationProgress>, CancellationToken, Task<T>> operation,
            IProgress<OperationProgress>? progressReporter = null,
            CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            var operationId = Guid.NewGuid().ToString();
            var cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            var handle = new AsyncOperationHandle<T>(
                operationId,
                operationName,
                cancellationTokenSource,
                _structuredLogger.StartOperation(operationName));

            // 设置进度报告器
            var internalProgressReporter = new Progress<OperationProgress>(progress =>
            {
                handle.UpdateProgress(progress);
                progressReporter?.Report(progress);
                OnOperationProgressUpdated(operationId, operationName, progress);
            });

            // 注册状态变更事件
            handle.StatusChanged += (sender, args) => OnOperationStatusChanged(args);

            // 添加到活动操作集合
            _activeOperations.TryAdd(operationId, handle);

            try
            {
                // 启动操作
                var task = Task.Run(async () =>
                {
                    try
                    {
                        handle.SetStatus(OperationStatus.Running);
                        var result = await operation(internalProgressReporter, cancellationTokenSource.Token);
                        handle.SetResult(result);
                        handle.SetStatus(OperationStatus.Completed);
                        return result;
                    }
                    catch (OperationCanceledException)
                    {
                        handle.SetStatus(OperationStatus.Cancelled);
                        throw;
                    }
                    catch (Exception ex)
                    {
                        handle.SetException(ex);
                        handle.SetStatus(OperationStatus.Failed);
                        throw;
                    }
                    finally
                    {
                        // 操作完成后从活动集合中移除
                        _activeOperations.TryRemove(operationId, out _);
                    }
                }, cancellationTokenSource.Token);

                handle.SetTask(task);

                _logger.LogInformation("异步操作已启动: {OperationName} (ID: {OperationId})", operationName, operationId);

                return handle;
            }
            catch (Exception ex)
            {
                _activeOperations.TryRemove(operationId, out _);
                handle.SetException(ex);
                handle.SetStatus(OperationStatus.Failed);
                _logger.LogError(ex, "启动异步操作失败: {OperationName}", operationName);
                throw;
            }
        }

        public async Task<IAsyncOperationHandle> StartOperationAsync(
            string operationName,
            Func<IProgress<OperationProgress>, CancellationToken, Task> operation,
            IProgress<OperationProgress>? progressReporter = null,
            CancellationToken cancellationToken = default)
        {
            // 包装无返回值的操作为有返回值的操作
            return await StartOperationAsync<object?>(
                operationName,
                async (progress, token) =>
                {
                    await operation(progress, token);
                    return null;
                },
                progressReporter,
                cancellationToken);
        }

        public IEnumerable<IAsyncOperationHandle> GetActiveOperations()
        {
            ThrowIfDisposed();
            return _activeOperations.Values.ToList();
        }

        public IAsyncOperationHandle? GetOperation(string operationId)
        {
            ThrowIfDisposed();
            _activeOperations.TryGetValue(operationId, out var handle);
            return handle;
        }

        public async Task CancelAllOperationsAsync()
        {
            ThrowIfDisposed();

            var operations = _activeOperations.Values.ToList();

            _logger.LogInformation("取消所有活动操作，共 {Count} 个操作", operations.Count);

            // 发送取消信号
            foreach (var operation in operations)
            {
                try
                {
                    operation.Cancel();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "取消操作失败: {OperationName}", operation.OperationName);
                }
            }

            // 等待所有操作完成或取消
            var waitTasks = operations.Select(op => op.WaitAsync()).ToArray();

            try
            {
                await Task.WhenAll(waitTasks);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "等待操作取消完成时发生异常");
            }
        }

        public async Task WaitForAllOperationsAsync(TimeSpan? timeout = null)
        {
            ThrowIfDisposed();

            var operations = _activeOperations.Values.ToList();
            if (!operations.Any())
            {
                return;
            }

            var waitTasks = operations.Select(op => op.WaitAsync()).ToArray();

            if (timeout.HasValue)
            {
                using var cts = new CancellationTokenSource(timeout.Value);
                try
                {
                    await Task.WhenAll(waitTasks).WaitAsync(cts.Token);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("等待所有操作完成超时: {Timeout}", timeout.Value);
                    throw new TimeoutException($"等待所有操作完成超时: {timeout.Value}");
                }
            }
            else
            {
                await Task.WhenAll(waitTasks);
            }
        }

        private void OnOperationStatusChanged(OperationStatusChangedEventArgs args)
        {
            try
            {
                OperationStatusChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "操作状态变更事件处理失败: {OperationName}", args.OperationName);
            }
        }

        private void OnOperationProgressUpdated(string operationId, string operationName, OperationProgress progress)
        {
            try
            {
                var args = new OperationProgressEventArgs(operationId, operationName, progress);
                OperationProgressUpdated?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "操作进度更新事件处理失败: {OperationName}", operationName);
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(AsyncOperationManager));
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    // 取消所有活动操作
                    CancelAllOperationsAsync().Wait(TimeSpan.FromSeconds(5));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "释放异步操作管理器时取消操作失败");
                }

                _operationSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 异步操作句柄实现
    /// </summary>
    internal class AsyncOperationHandle<T> : IAsyncOperationHandle<T>
    {
        private readonly IOperationTracker _operationTracker;
        private volatile OperationStatus _status;
        private OperationProgress? _currentProgress;
        private Exception? _exception;
        private T? _result;
        private Task<T>? _task;
        private bool _disposed;

        public string OperationId { get; }
        public string OperationName { get; }
        public OperationStatus Status => _status;
        public OperationProgress? CurrentProgress => _currentProgress;
        public DateTime StartTime { get; }
        public DateTime? EndTime { get; private set; }
        public Exception? Exception => _exception;
        public T? Result => _result;
        public CancellationTokenSource CancellationTokenSource { get; }

        public Task Task => _task ?? Task.CompletedTask;
        Task<T> IAsyncOperationHandle<T>.WaitAsync() => WaitAsync();

        public event EventHandler<OperationStatusChangedEventArgs>? StatusChanged;

        public AsyncOperationHandle(string operationId, string operationName,
            CancellationTokenSource cancellationTokenSource, IOperationTracker operationTracker)
        {
            OperationId = operationId;
            OperationName = operationName;
            CancellationTokenSource = cancellationTokenSource;
            _operationTracker = operationTracker;
            StartTime = DateTime.UtcNow;
            _status = OperationStatus.Pending;
        }

        public void Cancel()
        {
            if (!_disposed && _status == OperationStatus.Running || _status == OperationStatus.Pending)
            {
                CancellationTokenSource.Cancel();
            }
        }

        async Task IAsyncOperationHandle.WaitAsync()
        {
            if (_task != null)
            {
                try
                {
                    await _task;
                }
                catch (OperationCanceledException)
                {
                    // 取消操作是正常的，不需要重新抛出
                }
                catch
                {
                    // 其他异常会在Task中处理
                    throw;
                }
            }
        }

        public async Task<T> WaitAsync()
        {
            if (_task != null)
            {
                return await _task;
            }

            if (_status == OperationStatus.Completed && _result != null)
            {
                return _result;
            }

            if (_status == OperationStatus.Failed && _exception != null)
            {
                throw _exception;
            }

            if (_status == OperationStatus.Cancelled)
            {
                throw new OperationCanceledException();
            }

            throw new InvalidOperationException("操作尚未完成");
        }

        internal void SetTask(Task<T> task)
        {
            _task = task;
        }

        internal void SetStatus(OperationStatus newStatus)
        {
            var oldStatus = _status;
            _status = newStatus;

            if (newStatus == OperationStatus.Completed || newStatus == OperationStatus.Failed || newStatus == OperationStatus.Cancelled)
            {
                EndTime = DateTime.UtcNow;

                if (newStatus == OperationStatus.Completed)
                {
                    _operationTracker.Complete(_result);
                }
                else if (newStatus == OperationStatus.Failed && _exception != null)
                {
                    _operationTracker.Fail(_exception);
                }
            }

            OnStatusChanged(oldStatus, newStatus);
        }

        internal void SetResult(T result)
        {
            _result = result;
        }

        internal void SetException(Exception exception)
        {
            _exception = exception;
        }

        internal void UpdateProgress(OperationProgress progress)
        {
            _currentProgress = progress;
            _operationTracker.LogProgress(progress.Percentage, progress.Message);
        }

        private void OnStatusChanged(OperationStatus oldStatus, OperationStatus newStatus)
        {
            try
            {
                var args = new OperationStatusChangedEventArgs(OperationId, OperationName, oldStatus, newStatus, _exception);
                StatusChanged?.Invoke(this, args);
            }
            catch
            {
                // 忽略事件处理异常
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                CancellationTokenSource?.Dispose();
                _operationTracker?.Dispose();
                _disposed = true;
            }
        }
    }
}
