using System;

namespace ProjectDigitizer.Studio.Helpers
{
    /// <summary>
    /// 布局暂停辅助类，用于减少布局更新频率
    /// </summary>
    public class LayoutSuspension : IDisposable
    {
        private static int _suspensionCount = 0;
        private static readonly object _lockObject = new object();

        public LayoutSuspension()
        {
            lock (_lockObject)
            {
                _suspensionCount++;
            }
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                _suspensionCount--;
                if (_suspensionCount < 0)
                {
                    _suspensionCount = 0;
                }
            }
        }

        /// <summary>
        /// 检查布局是否被暂停
        /// </summary>
        public static bool IsLayoutSuspended
        {
            get
            {
                lock (_lockObject)
                {
                    return _suspensionCount > 0;
                }
            }
        }
    }
}
