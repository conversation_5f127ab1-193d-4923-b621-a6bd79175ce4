<ui:FluentWindow
    x:Class="ProjectDigitizer.Studio.Views.PluginManagerWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    mc:Ignorable="d"
    Title="插件管理"
    Width="700"
    Height="480"
    WindowStartupLocation="CenterOwner">
    <Grid Margin="12">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock Text="已加载插件" FontSize="18" FontWeight="SemiBold" Margin="0,0,0,8" />

        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding Plugins}"
                  HeadersVisibility="Column"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  IsReadOnly="True">
            <DataGrid.Columns>
                <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="2*" />
                <DataGridTextColumn Header="版本" Binding="{Binding Version}" Width="1*" />
                <DataGridTextColumn Header="位置" Binding="{Binding Location}" Width="3*" />
                <DataGridTextColumn Header="节点模块" Binding="{Binding Modules}" Width="2*">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="Text" Value="{Binding Modules, StringFormat={}{0}}" />
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</ui:FluentWindow>

