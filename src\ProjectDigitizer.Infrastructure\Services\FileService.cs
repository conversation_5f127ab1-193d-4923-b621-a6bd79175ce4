using System.IO;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 文件操作服务
/// 提供通用的文件系统操作功能
/// </summary>
public class FileService
{
    private readonly ILogger<FileService> _logger;

    public FileService(ILogger<FileService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    public void EnsureDirectoryExists(string directoryPath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
                _logger.LogDebug("创建目录: {DirectoryPath}", directoryPath);
            }
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            _logger.LogError(ex, "创建目录时发生错误: {DirectoryPath}", directoryPath);
            throw new FileOperationException($"创建目录失败: {ex.Message}", directoryPath, ex);
        }
    }

    /// <summary>
    /// 安全删除文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否成功删除</returns>
    public bool SafeDeleteFile(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            if (!File.Exists(filePath))
            {
                _logger.LogDebug("文件不存在，无需删除: {FilePath}", filePath);
                return true;
            }

            File.Delete(filePath);
            _logger.LogDebug("文件删除成功: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "删除文件时发生错误: {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// 获取文件大小
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件大小（字节）</returns>
    public long GetFileSize(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            var fileInfo = new FileInfo(filePath);
            return fileInfo.Length;
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is FileNotFoundException))
        {
            _logger.LogError(ex, "获取文件大小时发生错误: {FilePath}", filePath);
            throw new FileOperationException($"获取文件大小失败: {ex.Message}", filePath, ex);
        }
    }

    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourcePath">源文件路径</param>
    /// <param name="destinationPath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖现有文件</param>
    public async Task CopyFileAsync(string sourcePath, string destinationPath, bool overwrite = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(sourcePath))
                throw new ArgumentException("源文件路径不能为空", nameof(sourcePath));

            if (string.IsNullOrWhiteSpace(destinationPath))
                throw new ArgumentException("目标文件路径不能为空", nameof(destinationPath));

            if (!File.Exists(sourcePath))
                throw new FileNotFoundException($"源文件不存在: {sourcePath}");

            // 确保目标目录存在
            var destinationDirectory = Path.GetDirectoryName(destinationPath);
            if (!string.IsNullOrEmpty(destinationDirectory))
            {
                EnsureDirectoryExists(destinationDirectory);
            }

            _logger.LogDebug("开始复制文件: {SourcePath} -> {DestinationPath}", sourcePath, destinationPath);

            using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read);
            using var destinationStream = new FileStream(destinationPath,
                overwrite ? FileMode.Create : FileMode.CreateNew, FileAccess.Write);

            await sourceStream.CopyToAsync(destinationStream);

            _logger.LogDebug("文件复制完成: {DestinationPath}", destinationPath);
        }
        catch (Exception ex) when (!(ex is ArgumentException || ex is FileNotFoundException))
        {
            _logger.LogError(ex, "复制文件时发生错误: {SourcePath} -> {DestinationPath}", sourcePath, destinationPath);
            throw new FileOperationException($"复制文件失败: {ex.Message}", sourcePath, ex);
        }
    }
}
