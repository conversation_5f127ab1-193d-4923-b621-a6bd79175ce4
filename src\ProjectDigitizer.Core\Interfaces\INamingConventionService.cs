using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 命名约定服务接口
/// </summary>
public interface INamingConventionService
{
    /// <summary>
    /// 验证类名是否符合命名约定
    /// </summary>
    /// <param name="className">类名</param>
    /// <param name="classType">类类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateClassName(string className, ClassType classType);

    /// <summary>
    /// 验证方法名是否符合命名约定
    /// </summary>
    /// <param name="methodName">方法名</param>
    /// <param name="methodType">方法类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateMethodName(string methodName, MethodType methodType);

    /// <summary>
    /// 验证属性名是否符合命名约定
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <param name="propertyType">属性类型</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidatePropertyName(string propertyName, PropertyType propertyType);

    /// <summary>
    /// 验证命名空间是否符合架构层次
    /// </summary>
    /// <param name="namespaceName">命名空间</param>
    /// <param name="expectedLayer">期望的架构层</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateNamespace(string namespaceName, ArchitectureLayer expectedLayer);

    /// <summary>
    /// 验证接口名是否符合命名约定
    /// </summary>
    /// <param name="interfaceName">接口名</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateInterfaceName(string interfaceName);

    /// <summary>
    /// 验证文件名是否与类名匹配
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="className">类名</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateFileNameMatch(string fileName, string className);

    /// <summary>
    /// 验证项目结构是否符合约定
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateProjectStructure(string projectPath);

    /// <summary>
    /// 获取推荐的类名
    /// </summary>
    /// <param name="currentName">当前名称</param>
    /// <param name="classType">类类型</param>
    /// <returns>推荐的类名</returns>
    string GetRecommendedClassName(string currentName, ClassType classType);

    /// <summary>
    /// 获取推荐的命名空间
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="layer">架构层</param>
    /// <returns>推荐的命名空间</returns>
    string GetRecommendedNamespace(string filePath, ArchitectureLayer layer);
}

/// <summary>
/// 类类型枚举
/// </summary>
public enum ClassType
{
    /// <summary>实体类</summary>
    Entity,
    /// <summary>值对象</summary>
    ValueObject,
    /// <summary>服务类</summary>
    Service,
    /// <summary>控制器</summary>
    Controller,
    /// <summary>视图模型</summary>
    ViewModel,
    /// <summary>数据传输对象</summary>
    Dto,
    /// <summary>配置类</summary>
    Configuration,
    /// <summary>异常类</summary>
    Exception,
    /// <summary>扩展方法类</summary>
    Extensions,
    /// <summary>常量类</summary>
    Constants,
    /// <summary>枚举</summary>
    Enum,
    /// <summary>接口</summary>
    Interface,
    /// <summary>抽象类</summary>
    Abstract,
    /// <summary>测试类</summary>
    Test
}

/// <summary>
/// 方法类型枚举
/// </summary>
public enum MethodType
{
    /// <summary>公共方法</summary>
    Public,
    /// <summary>私有方法</summary>
    Private,
    /// <summary>受保护方法</summary>
    Protected,
    /// <summary>异步方法</summary>
    Async,
    /// <summary>事件处理方法</summary>
    EventHandler,
    /// <summary>属性访问器</summary>
    PropertyAccessor,
    /// <summary>构造函数</summary>
    Constructor,
    /// <summary>析构函数</summary>
    Destructor,
    /// <summary>操作符重载</summary>
    Operator,
    /// <summary>扩展方法</summary>
    Extension
}

/// <summary>
/// 属性类型枚举
/// </summary>
public enum PropertyType
{
    /// <summary>公共属性</summary>
    Public,
    /// <summary>私有字段</summary>
    PrivateField,
    /// <summary>常量</summary>
    Constant,
    /// <summary>只读属性</summary>
    ReadOnly,
    /// <summary>静态属性</summary>
    Static,
    /// <summary>依赖注入属性</summary>
    Dependency,
    /// <summary>配置属性</summary>
    Configuration,
    /// <summary>事件</summary>
    Event
}

/// <summary>
/// 架构层枚举
/// </summary>
public enum ArchitectureLayer
{
    /// <summary>核心层</summary>
    Core,
    /// <summary>应用层</summary>
    Application,
    /// <summary>基础设施层</summary>
    Infrastructure,
    /// <summary>表示层</summary>
    Presentation,
    /// <summary>测试层</summary>
    Tests
}
