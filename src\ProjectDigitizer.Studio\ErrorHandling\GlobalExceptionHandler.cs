using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Core.Exceptions;
using ProjectDigitizer.Infrastructure.Exceptions;

namespace ProjectDigitizer.Studio.ErrorHandling
{
    /// <summary>
    /// 全局异常处理器
    /// 负责捕获和处理应用程序中的未处理异常
    /// </summary>
    public class GlobalExceptionHandler : IGlobalExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;
        private readonly bool _isDevelopment;
        private readonly Dictionary<Type, Func<Exception, bool>> _exceptionHandlers;

        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, bool isDevelopment = false)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _isDevelopment = isDevelopment;
            _exceptionHandlers = new Dictionary<Type, Func<Exception, bool>>();
        }

        #region IGlobalExceptionHandler Implementation

        public void HandleException(Exception exception, string context)
        {
            LogException(exception, context);
        }

        public async Task HandleExceptionAsync(Exception exception, string context)
        {
            await Task.Run(() => HandleException(exception, context));
        }

        public void RegisterExceptionHandler<T>(Func<T, bool> handler) where T : Exception
        {
            _exceptionHandlers[typeof(T)] = ex => handler((T)ex);
        }

        public bool CanRecover(Exception exception)
        {
            return exception switch
            {
                ValidationException => true,
                BusinessRuleViolationException => true,
                ServiceException => true,
                DataBindingException => true,
                CommandExecutionException => true,
                OutOfMemoryException => false,
                StackOverflowException => false,
                AccessViolationException => false,
                _ => true
            };
        }

        public async Task<bool> TryRecoverAsync(Exception exception, object? context = null)
        {
            try
            {
                // 检查是否有注册的处理器
                if (_exceptionHandlers.TryGetValue(exception.GetType(), out var handler))
                {
                    return await Task.FromResult(handler(exception));
                }

                // 默认恢复策略
                return await Task.FromResult(CanRecover(exception));
            }
            catch (Exception recoveryException)
            {
                _logger.LogError(recoveryException, "异常恢复过程中发生错误");
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 处理应用程序域未处理异常
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        public void HandleUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                if (exception != null)
                {
                    LogException(exception, "应用程序域未处理异常");
                    ShowErrorDialog(exception, "应用程序遇到未处理的错误");
                }
            }
            catch (Exception ex)
            {
                // 防止异常处理器本身出错
                System.Diagnostics.Debug.WriteLine($"全局异常处理器错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理WPF应用程序未处理异常
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        public void HandleDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                LogException(e.Exception, "WPF调度器未处理异常");

                var userChoice = ShowErrorDialog(e.Exception, "应用程序遇到错误");

                // 根据用户选择决定是否继续运行
                if (userChoice == MessageBoxResult.Yes)
                {
                    e.Handled = true; // 标记异常已处理，继续运行
                }
                else
                {
                    e.Handled = false; // 让应用程序关闭
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"全局异常处理器错误: {ex.Message}");
                e.Handled = false;
            }
        }

        /// <summary>
        /// 处理任务未处理异常
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">异常事件参数</param>
        public void HandleTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                LogException(e.Exception, "任务未处理异常");

                // 标记异常已观察，防止应用程序崩溃
                e.SetObserved();

                // 在UI线程上显示错误对话框
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                {
                    ShowErrorDialog(e.Exception, "后台任务遇到错误");
                }));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"全局异常处理器错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录异常信息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="context">异常上下文</param>
        private void LogException(Exception exception, string context)
        {
            try
            {
                var exceptionType = GetExceptionCategory(exception);
                var errorCode = GetErrorCode(exception);

                _logger.LogError(exception,
                    "{Context} - {ExceptionType}: {ErrorCode} - {Message}",
                    context, exceptionType, errorCode, exception.Message);

                // 记录异常详细信息
                if (exception is ProjectDigitizerException pdException && pdException.Details.Count > 0)
                {
                    _logger.LogError("异常详细信息: {@Details}", pdException.Details);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="title">对话框标题</param>
        /// <returns>用户选择结果</returns>
        private MessageBoxResult ShowErrorDialog(Exception exception, string title)
        {
            try
            {
                var message = GetUserFriendlyMessage(exception);
                var buttons = _isDevelopment ? MessageBoxButton.YesNo : MessageBoxButton.OK;
                var buttonText = _isDevelopment ?
                    $"{message}\n\n是否继续运行应用程序？\n\n技术详情：\n{exception}" :
                    message;

                return MessageBox.Show(buttonText, title, buttons, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示错误对话框失败: {ex.Message}");
                return MessageBoxResult.OK;
            }
        }

        /// <summary>
        /// 获取用户友好的错误消息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>用户友好的错误消息</returns>
        private string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                ValidationException => "输入的数据不符合要求，请检查后重试。",
                BusinessRuleViolationException => "操作违反了业务规则，请检查操作是否正确。",
                EntityNotFoundException => "找不到指定的数据项，请刷新后重试。",
                EntityAlreadyExistsException => "数据项已存在，请检查是否重复操作。",
                InvalidDomainOperationException => "当前操作不被允许，请检查操作条件。",
                ServiceException => "服务执行失败，请稍后重试。",
                CoordinationException => "多个服务协调失败，请稍后重试。",
                PermissionException => "没有权限执行此操作，请联系管理员。",
                OperationTimeoutException => "操作超时，请检查网络连接或稍后重试。",
                DataBindingException => "界面数据绑定失败，请刷新页面。",
                NavigationException => "页面导航失败，请重试。",
                CommandExecutionException => "命令执行失败，请重试。",
                RenderingException => "界面渲染失败，请刷新页面。",
                DomainException => "业务操作失败，请检查操作是否正确。",
                Core.Exceptions.ApplicationException => "应用程序服务失败，请稍后重试。",
                UIException => "界面操作失败，请重试。",
                InfrastructureException => "系统服务暂时不可用，请稍后重试。",
                System.IO.FileNotFoundException => "找不到指定的文件，请检查文件路径是否正确。",
                UnauthorizedAccessException => "没有权限执行此操作，请检查文件或文件夹权限。",
                OutOfMemoryException => "系统内存不足，请关闭一些应用程序后重试。",
                ArgumentException => "参数错误，请检查输入的数据。",
                InvalidOperationException => "当前状态下无法执行此操作。",
                NotSupportedException => "不支持此操作。",
                TimeoutException => "操作超时，请检查网络连接或稍后重试。",
                _ => "应用程序遇到未知错误，请联系技术支持。"
            };
        }

        /// <summary>
        /// 获取异常分类
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>异常分类</returns>
        private string GetExceptionCategory(Exception exception)
        {
            return exception switch
            {
                ValidationException => "Validation",
                DomainException => "Domain",
                Core.Exceptions.ApplicationException => "Application",
                UIException => "UI",
                InfrastructureException => "Infrastructure",
                _ => "System"
            };
        }

        /// <summary>
        /// 获取错误代码
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>错误代码</returns>
        private string GetErrorCode(Exception exception)
        {
            if (exception is ProjectDigitizerException pdException && !string.IsNullOrEmpty(pdException.ErrorCode))
            {
                return pdException.ErrorCode;
            }

            return exception.GetType().Name;
        }
    }
}
