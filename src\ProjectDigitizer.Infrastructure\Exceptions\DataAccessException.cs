namespace ProjectDigitizer.Infrastructure.Exceptions;

/// <summary>
/// 数据访问异常
/// </summary>
public class DataAccessException : InfrastructureException
{
    public string? DataSource { get; }

    public DataAccessException(string message) : base(message)
    {
    }

    public DataAccessException(string message, string dataSource) : base(message)
    {
        DataSource = dataSource;
    }

    public DataAccessException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public DataAccessException(string message, string dataSource, Exception innerException) : base(message, innerException)
    {
        DataSource = dataSource;
    }
}
