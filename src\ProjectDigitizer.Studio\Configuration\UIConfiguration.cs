using System.Windows;

using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Configuration
{
    /// <summary>
    /// UI配置管理类
    /// </summary>
    public static class UIConfiguration
    {
        /// <summary>
        /// 默认窗口大小
        /// </summary>
        public static readonly Size DefaultWindowSize = new Size(1024, 768);

        /// <summary>
        /// 最小窗口大小
        /// </summary>
        public static readonly Size MinimumWindowSize = new Size(800, 600);

        /// <summary>
        /// 默认画布缩放级别
        /// </summary>
        public const double DefaultCanvasZoom = 1.0;

        /// <summary>
        /// 最小画布缩放级别
        /// </summary>
        public const double MinimumCanvasZoom = 0.1;

        /// <summary>
        /// 最大画布缩放级别
        /// </summary>
        public const double MaximumCanvasZoom = 5.0;

        /// <summary>
        /// 属性面板默认宽度（更宽以适配复杂属性编辑器，如公式计算）
        /// </summary>
        public const double DefaultPropertyPanelWidth = 520;

        /// <summary>
        /// 工具栏默认高度
        /// </summary>
        public const double DefaultToolbarHeight = 80;

        /// <summary>
        /// 默认连接线样式
        /// </summary>
        public static readonly ConnectionLineStyle DefaultConnectionLineStyle = ConnectionLineStyle.Bezier;

        /// <summary>
        /// 连接线样式配置键名（用于配置文件持久化）
        /// </summary>
        public const string ConnectionLineStyleConfigKey = "ConnectionLineStyle";
    }
}
