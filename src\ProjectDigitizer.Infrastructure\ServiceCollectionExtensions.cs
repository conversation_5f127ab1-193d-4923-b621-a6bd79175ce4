using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using ProjectDigitizer.Application.Extensions;
using ProjectDigitizer.Application.Interfaces;
using ProjectDigitizer.Application.Performance;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Infrastructure.Caching;
using ProjectDigitizer.Infrastructure.Data;
using ProjectDigitizer.Infrastructure.Extensions;
using ProjectDigitizer.Infrastructure.External;
using ProjectDigitizer.Infrastructure.Logging;
using ProjectDigitizer.Infrastructure.Performance;
using ProjectDigitizer.Infrastructure.Plugins;
using ProjectDigitizer.Infrastructure.Repositories;
using ProjectDigitizer.Infrastructure.Services;
using ProjectDigitizer.Infrastructure.UI;

namespace ProjectDigitizer.Infrastructure;

/// <summary>
/// 基础设施层依赖注入扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加基础设施层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册资源管理器
        services.AddSingleton<IResourceManager, ResourceManager>();

        // 注册代码质量验证服务
        services.AddCodeQualityValidation();

        // 使用明确的生命周期注册应用服务实现
        RegisterApplicationServices(services);

        // 注册基础设施服务
        RegisterInfrastructureServices(services);

        // 注册数据访问服务
        RegisterDataServices(services, configuration);

        // 注册外部服务
        RegisterExternalServices(services, configuration);

        // 注册性能管理服务
        RegisterPerformanceServices(services);

        // 插件加载与节点框架注册
        var pluginDir = configuration.GetValue<string>("Plugins:Directory") ?? "Plugins";
        var assemblies = PluginLoader.LoadAssemblies(pluginDir);
        // 提供插件程序集目录给上层（如 Studio ）使用
        services.AddSingleton<ProjectDigitizer.Application.Interfaces.IPluginCatalog>(new ProjectDigitizer.Infrastructure.Plugins.PluginCatalog(assemblies));
        // 注册后端节点（模板/处理器）
        services.AddNodeFramework(assemblies.ToArray());

        return services;
    }

    /// <summary>
    /// 注册应用服务实现
    /// </summary>
    private static void RegisterApplicationServices(IServiceCollection services)
    {
        // 应用服务通常是Scoped，因为它们可能包含请求相关的状态
        services.AddScoped<IProjectService, ProjectService>();
        services.AddScoped<ICanvasService, CanvasService>();
    }

    /// <summary>
    /// 注册基础设施服务
    /// </summary>
    private static void RegisterInfrastructureServices(IServiceCollection services)
    {
        // 文件服务是Scoped，因为可能包含操作上下文
        services.AddScoped<FileService>();

        // 配置服务是Singleton，因为配置通常是全局的
        services.AddSingleton(typeof(ConfigurationService<>));

        // 日志记录服务是Scoped，因为可能包含请求上下文
        services.AddScoped<ILoggingService, LoggingService>();

        // 缓存服务是Singleton，因为缓存应该在应用程序级别共享
        services.AddSingleton<ICacheService, MemoryCacheService>();
    }

    /// <summary>
    /// 注册数据访问服务
    /// </summary>
    private static void RegisterDataServices(IServiceCollection services, IConfiguration configuration)
    {
        // 数据上下文是Scoped，确保在同一请求中使用相同的实例
        services.AddScoped<IDataContext>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<FileDataContext>>();
            var dataDirectory = configuration.GetValue<string>("DataDirectory") ?? "Data";
            return new FileDataContext(logger, provider, dataDirectory);
        });

        // 文件存储库工厂是Singleton，因为它只是创建实例的工厂
        services.AddSingleton<IFileRepositoryFactory, FileRepositoryFactory>();
    }

    /// <summary>
    /// 注册外部服务
    /// </summary>
    private static void RegisterExternalServices(IServiceCollection services, IConfiguration configuration)
    {
        // HTTP客户端配置
        services.AddHttpClient<IExternalServiceClient, HttpExternalServiceClient>(client =>
        {
            var baseUrl = configuration.GetValue<string>("ExternalServices:BaseUrl");
            if (!string.IsNullOrEmpty(baseUrl))
            {
                client.BaseAddress = new Uri(baseUrl);
            }

            client.Timeout = TimeSpan.FromSeconds(configuration.GetValue<int>("ExternalServices:TimeoutSeconds", 30));
        });
    }

    /// <summary>
    /// 注册性能管理服务
    /// </summary>
    private static void RegisterPerformanceServices(IServiceCollection services)
    {
        // 性能管理服务都是Singleton，因为它们需要在应用程序级别维护状态
        services.AddSingleton<IPerformanceOptimizedConnectorManager, PerformanceOptimizedConnectorManager>();
        services.AddSingleton<ICanvasVirtualizationManager, CanvasVirtualizationManager>();
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();

        // Task 8: 新增的内存管理和画布性能优化服务
        services.AddSingleton<IMemoryManagementService, MemoryManagementService>();
        services.AddSingleton<ICanvasPerformanceOptimizer, CanvasPerformanceOptimizer>();

        // Task 9: 新增的响应式UI管理服务
        services.AddSingleton<IReactiveUIManager, ReactiveUIManager>();

        // 性能监控服务实现了IDisposable，会被DI容器自动处理资源释放
    }
}

/// <summary>
/// 文件存储库工厂接口
/// </summary>
public interface IFileRepositoryFactory
{
    /// <summary>
    /// 创建文件存储库
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件存储库实例</returns>
    IRepository<T> CreateFileRepository<T>(string filePath) where T : class;
}

/// <summary>
/// 文件存储库工厂实现
/// </summary>
public class FileRepositoryFactory : IFileRepositoryFactory
{
    private readonly IServiceProvider _serviceProvider;

    public FileRepositoryFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    public IRepository<T> CreateFileRepository<T>(string filePath) where T : class
    {
        var logger = _serviceProvider.GetRequiredService<ILogger<Repository<T>>>();
        return new FileRepository<T>(filePath, logger);
    }
}
