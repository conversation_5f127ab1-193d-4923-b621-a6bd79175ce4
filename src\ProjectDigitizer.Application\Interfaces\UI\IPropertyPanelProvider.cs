using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Application.Interfaces.UI
{
    /// <summary>
    /// 插件声明其为某 ModuleType 提供属性面板的能力。
    /// 为避免 Application 依赖 WPF，仅返回资源键或描述信息，不返回 UI 元素。
    /// </summary>
    public interface IPropertyPanelProvider
    {
        /// <summary>
        /// 该 Provider 适配的模块类型。
        /// </summary>
        ModuleType TargetModuleType { get; }

        /// <summary>
        /// 由插件提供的资源键（例如 DataTemplate 的 x:Key）。
        /// Studio 将在已合并的 ResourceDictionary 中按此键查找并实例化。
        /// </summary>
        string ResourceKey { get; }
    }
}

