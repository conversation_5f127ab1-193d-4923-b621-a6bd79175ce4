using System.Text.RegularExpressions;

using Microsoft.Extensions.Logging;

using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.ValueObjects;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 命名约定服务实现
/// </summary>
public class NamingConventionService : INamingConventionService
{
    private readonly ILogger<NamingConventionService> _logger;

    // 命名约定正则表达式
    // 要求首字母大写，且整体至少包含一个小写字母，避免全大写（如 USERSERVICE）也被视为 PascalCase
    private static readonly Regex PascalCaseRegex = new(@"^(?=.*[a-z])[A-Z][a-zA-Z0-9]*$", RegexOptions.Compiled);
    private static readonly Regex CamelCaseRegex = new(@"^[a-z][a-zA-Z0-9]*$", RegexOptions.Compiled);
    private static readonly Regex InterfaceNameRegex = new(@"^I[A-Z][a-zA-Z0-9]*$", RegexOptions.Compiled);
    private static readonly Regex PrivateFieldRegex = new(@"^_[a-z][a-zA-Z0-9]*$", RegexOptions.Compiled);
    private static readonly Regex ConstantRegex = new(@"^[A-Z][A-Z0-9_]*$", RegexOptions.Compiled);

    public NamingConventionService(ILogger<NamingConventionService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public ValidationResult ValidateClassName(string className, ClassType classType)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(className))
        {
            result.AddError("类名不能为空");
            return result;
        }

        // 基本PascalCase验证
        if (!PascalCaseRegex.IsMatch(className))
        {
            result.AddError($"类名 '{className}' 应使用PascalCase命名约定");
        }

        // 根据类型验证特定后缀
        ValidateClassTypeSuffix(className, classType, result);

        // 验证长度
        if (className.Length > 50)
        {
            result.AddWarning($"类名 '{className}' 过长，建议不超过50个字符");
        }

        _logger.LogDebug("验证类名 {ClassName}，类型 {ClassType}，结果: {IsValid}",
            className, classType, result.IsValid);

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateMethodName(string methodName, MethodType methodType)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(methodName))
        {
            result.AddError("方法名不能为空");
            return result;
        }

        // 根据方法类型验证命名约定
        switch (methodType)
        {
            case MethodType.Public:
            case MethodType.Protected:
                if (!PascalCaseRegex.IsMatch(methodName))
                {
                    result.AddError($"公共/受保护方法名 '{methodName}' 应使用PascalCase命名约定");
                }
                break;

            case MethodType.Private:
                if (!PascalCaseRegex.IsMatch(methodName) && !CamelCaseRegex.IsMatch(methodName))
                {
                    result.AddError($"私有方法名 '{methodName}' 应使用PascalCase或camelCase命名约定");
                }
                break;

            case MethodType.Async:
                if (!methodName.EndsWith("Async"))
                {
                    result.AddError($"异步方法名 '{methodName}' 应以'Async'结尾");
                }
                break;

            case MethodType.EventHandler:
                if (!methodName.StartsWith("On") && !methodName.EndsWith("Handler"))
                {
                    result.AddWarning($"事件处理方法名 '{methodName}' 建议以'On'开头或以'Handler'结尾");
                }
                break;
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidatePropertyName(string propertyName, Core.Interfaces.PropertyType propertyType)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(propertyName))
        {
            result.AddError("属性名不能为空");
            return result;
        }

        switch (propertyType)
        {
            case Core.Interfaces.PropertyType.Public:
            case Core.Interfaces.PropertyType.ReadOnly:
            case Core.Interfaces.PropertyType.Static:
                if (!PascalCaseRegex.IsMatch(propertyName))
                {
                    result.AddError($"公共属性名 '{propertyName}' 应使用PascalCase命名约定");
                }
                break;

            case Core.Interfaces.PropertyType.PrivateField:
                if (!PrivateFieldRegex.IsMatch(propertyName))
                {
                    result.AddError($"私有字段名 '{propertyName}' 应以下划线开头并使用camelCase命名约定");
                }
                break;

            case Core.Interfaces.PropertyType.Constant:
                if (!ConstantRegex.IsMatch(propertyName))
                {
                    result.AddError($"常量名 '{propertyName}' 应使用UPPER_CASE命名约定");
                }
                break;

            case Core.Interfaces.PropertyType.Event:
                if (!propertyName.EndsWith("Event") && !propertyName.EndsWith("Changed"))
                {
                    result.AddWarning($"事件名 '{propertyName}' 建议以'Event'或'Changed'结尾");
                }
                break;
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateNamespace(string namespaceName, ArchitectureLayer expectedLayer)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(namespaceName))
        {
            result.AddError("命名空间不能为空");
            return result;
        }

        // 验证命名空间格式
        var parts = namespaceName.Split('.');
        if (parts.Length < 2)
        {
            result.AddError($"命名空间 '{namespaceName}' 格式不正确，应至少包含项目名称");
            return result;
        }

        // 验证项目根命名空间
        if (!parts[0].Equals("ProjectDigitizer", StringComparison.OrdinalIgnoreCase))
        {
            result.AddError($"命名空间 '{namespaceName}' 应以'ProjectDigitizer'开头");
        }

        // 验证架构层
        ValidateArchitectureLayer(namespaceName, expectedLayer, result);

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateInterfaceName(string interfaceName)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(interfaceName))
        {
            result.AddError("接口名不能为空");
            return result;
        }

        if (!InterfaceNameRegex.IsMatch(interfaceName))
        {
            result.AddError($"接口名 '{interfaceName}' 应以'I'开头并使用PascalCase命名约定");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateFileNameMatch(string fileName, string className)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(fileName) || string.IsNullOrWhiteSpace(className))
        {
            result.AddError("文件名和类名不能为空");
            return result;
        }

        var fileNameWithoutExtension = System.IO.Path.GetFileNameWithoutExtension(fileName);
        if (!fileNameWithoutExtension.Equals(className, StringComparison.OrdinalIgnoreCase))
        {
            result.AddError($"文件名 '{fileName}' 应与类名 '{className}' 匹配");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateProjectStructure(string projectPath)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(projectPath) || !System.IO.Directory.Exists(projectPath))
        {
            result.AddError("项目路径无效");
            return result;
        }

        // 验证基本项目结构
        var expectedFolders = new[] { "src", "tests" };
        foreach (var folder in expectedFolders)
        {
            var folderPath = System.IO.Path.Combine(projectPath, folder);
            if (!System.IO.Directory.Exists(folderPath))
            {
                result.AddWarning($"缺少推荐的文件夹: {folder}");
            }
        }

        return result;
    }

    /// <inheritdoc />
    public string GetRecommendedClassName(string currentName, ClassType classType)
    {
        if (string.IsNullOrWhiteSpace(currentName))
            return string.Empty;

        var baseName = currentName.Trim();

        return classType switch
        {
            ClassType.Service => baseName.EndsWith("Service") ? baseName : $"{baseName}Service",
            ClassType.Controller => baseName.EndsWith("Controller") ? baseName : $"{baseName}Controller",
            ClassType.ViewModel => baseName.EndsWith("ViewModel") ? baseName : $"{baseName}ViewModel",
            ClassType.Dto => baseName.EndsWith("Dto") ? baseName : $"{baseName}Dto",
            ClassType.Exception => baseName.EndsWith("Exception") ? baseName : $"{baseName}Exception",
            ClassType.Extensions => baseName.EndsWith("Extensions") ? baseName : $"{baseName}Extensions",
            ClassType.Test => baseName.EndsWith("Tests") ? baseName : $"{baseName}Tests",
            ClassType.Interface => baseName.StartsWith("I") ? baseName : $"I{baseName}",
            _ => baseName
        };
    }

    /// <inheritdoc />
    public string GetRecommendedNamespace(string filePath, ArchitectureLayer layer)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            return string.Empty;

        var layerName = layer switch
        {
            ArchitectureLayer.Core => "Core",
            ArchitectureLayer.Application => "Application",
            ArchitectureLayer.Infrastructure => "Infrastructure",
            ArchitectureLayer.Presentation => "Studio",
            ArchitectureLayer.Tests => "Tests",
            _ => "Unknown"
        };

        return $"ProjectDigitizer.{layerName}";
    }

    private void ValidateClassTypeSuffix(string className, ClassType classType, ValidationResult result)
    {
        var expectedSuffix = classType switch
        {
            ClassType.Service => "Service",
            ClassType.Controller => "Controller",
            ClassType.ViewModel => "ViewModel",
            ClassType.Dto => "Dto",
            ClassType.Exception => "Exception",
            ClassType.Extensions => "Extensions",
            ClassType.Test => "Tests",
            _ => null
        };

        if (expectedSuffix != null && !className.EndsWith(expectedSuffix, StringComparison.Ordinal))
        {
            // 对需要固定后缀的类型（Service/Controller/ViewModel/Dto/Exception/Extensions/Test）作为错误处理
            result.AddError($"{classType}类型的类名 '{className}' 应以'{expectedSuffix}'结尾");
        }

        if (classType == ClassType.Interface && !className.StartsWith('I'))
        {
            result.AddError($"接口名 '{className}' 应以'I'开头");
        }
    }

    private void ValidateArchitectureLayer(string namespaceName, ArchitectureLayer expectedLayer, ValidationResult result)
    {
        var expectedLayerName = expectedLayer switch
        {
            ArchitectureLayer.Core => "Core",
            ArchitectureLayer.Application => "Application",
            ArchitectureLayer.Infrastructure => "Infrastructure",
            ArchitectureLayer.Presentation => "Studio",
            ArchitectureLayer.Tests => "Tests",
            _ => null
        };

        if (expectedLayerName != null && !namespaceName.Contains($".{expectedLayerName}"))
        {
            result.AddError($"命名空间 '{namespaceName}' 应包含架构层标识 '{expectedLayerName}'");
        }
    }
}
