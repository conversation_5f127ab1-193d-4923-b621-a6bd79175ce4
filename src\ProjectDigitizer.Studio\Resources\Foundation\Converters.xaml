<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters">

    <!-- ========== 转换器定义 ========== -->
    <!-- 从 NodeTemplates.xaml 提取的所有转换器 -->
    
    <!-- 系统内置转换器 -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    
    <!-- 自定义转换器 -->
    <converters:BooleanToAngleConverter x:Key="BooleanToAngleConverter"/>
    <converters:ConnectorDataTypeToColorConverter x:Key="ConnectorDataTypeToColorConverter"/>
    <converters:HasFunctionDisplayItemsConverter x:Key="HasFunctionDisplayItemsConverter"/>
    <converters:FunctionDisplayItemCountConverter x:Key="FunctionDisplayItemCountConverter"/>
    <converters:ConnectorDataTypeToColorSimpleConverter x:Key="ConnectorDataTypeToColorSimpleConverter"/>
    <converters:ModuleTypeToIconConverter x:Key="ModuleTypeToIconConverter"/>

</ResourceDictionary>
