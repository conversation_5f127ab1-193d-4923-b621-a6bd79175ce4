using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;

using ICSharpCode.AvalonEdit.CodeCompletion;
using ICSharpCode.AvalonEdit.Document;
using ICSharpCode.AvalonEdit.Editing;
using ICSharpCode.AvalonEdit.Rendering;

namespace Plugins.DataCalculation.Controls.Functions;

/// <summary>
///     公式编辑器（AvalonEdit 封装）。
///     - 动态从第三方 CalcEngine 读取函数集合，提供中文描述的代码补全。
///     - 根据函数类别（逻辑/数学/文本/统计/其它）进行语法高亮着色。
///     - 在输入括号参数时，底部信息面板展示当前函数的参数签名和示例。
/// </summary>
public partial class AvalonFormulaEditor : UserControl
{
    private static WeakReference<AvalonFormulaEditor>? _lastFocused;

    /// <summary>
    ///     文本依赖属性：与编辑器文本双向绑定。
    /// </summary>
    public static readonly DependencyProperty TextProperty = DependencyProperty.Register(
        nameof(Text), typeof(string), typeof(AvalonFormulaEditor),
        new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
            OnTextChanged));

    private readonly IDictionary<string, CalcEngineFunctionCatalog.FunctionCategory> _funcCategoryMap;
    private readonly IReadOnlyDictionary<string, CalcEngineFunctionCatalog.FunctionInfo> _functionMap;
    private readonly HashSet<string> _functions;

    private CompletionWindow? _completionWindow;

    private bool _isImeComposing;

    /// <summary>
    ///     构造函数：加载函数元数据与高亮器，并初始化编辑器行为。
    /// </summary>
    public AvalonFormulaEditor()
    {
        _functionMap = CalcEngineFunctionCatalog.Load();
        _functions = new HashSet<string>(_functionMap.Keys, StringComparer.OrdinalIgnoreCase);
        _funcCategoryMap =
            _functionMap.ToDictionary(kv => kv.Key, kv => kv.Value.Category, StringComparer.OrdinalIgnoreCase);

        InitializeComponent();
        InitializeEditor();
    }

    /// <summary>
    ///     当前编辑器文本内容。
    /// </summary>
    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    /// <summary>
    ///     依赖属性值变更回调：保持外部绑定与编辑器内容一致。
    /// </summary>
    /// <param name="d">对象</param>
    /// <param name="e">变更事件</param>
    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is AvalonFormulaEditor editor && editor.Editor.Text != (e.NewValue?.ToString() ?? string.Empty))
        {
            editor.Editor.Text = e.NewValue?.ToString() ?? string.Empty;
        }
    }

    /// <summary>
    ///     初始化编辑器事件、补全、文本高亮等。
    /// </summary>
    private void InitializeEditor()
{
    // 简化版：只在 TextArea 上启用 IME，不订阅 TextComposition/IMM，不做候选定位
    try
    {
        InputMethod.SetIsInputMethodEnabled(Editor.TextArea, true);
        InputMethod.SetIsInputMethodSuspended(Editor.TextArea, false);
        InputMethod.SetPreferredImeState(Editor.TextArea, InputMethodState.On);
    }
    catch { }

    // 兼容 AvalonEdit 版本的内置 IME 支持（如存在则开启）
    try
    {
        var opts = Editor.TextArea.Options;
        var p = opts.GetType().GetProperty("EnableImeSupport");
        if (p != null && p.CanWrite) p.SetValue(opts, true);
    }
    catch { }

    // 仅保留必要事件：不再订阅 TextCompositionManager 与候选定位
    Editor.TextArea.TextEntered += TextArea_TextEntered;
    Editor.TextArea.TextEntering += TextArea_TextEntering;
    Editor.TextArea.PreviewKeyDown += TextArea_PreviewKeyDown;
    Editor.TextChanged += Editor_TextChanged;
    Editor.TextArea.GotFocus += (s, e) => _lastFocused = new WeakReference<AvalonFormulaEditor>(this);

    Editor.TextArea.GotKeyboardFocus += (s, e) =>
    {
        // 焦点进入时确保未暂停且开启 IME
        InputMethod.SetIsInputMethodEnabled(Editor.TextArea, true);
        InputMethod.SetIsInputMethodSuspended(Editor.TextArea, false);
        InputMethod.SetPreferredImeState(Editor.TextArea, InputMethodState.On);
    };

    // 语法高亮
    Editor.TextArea.TextView.LineTransformers.Add(new FormulaHighlightColorizer(_funcCategoryMap));
}

private void UpdateImeWindowPosition()
{
    try
    {
        if (!_isImeComposing)
        {
            // 仅在组合期间定位候选，避免干扰 IME 状态机
            return;
        }

        var textArea = Editor?.TextArea;
        if (textArea == null || !textArea.IsKeyboardFocusWithin)
            return;

        var caret = textArea.Caret;
        var textView = textArea.TextView;
        if (textView == null || caret == null)
            return;

        // 获取光标的视觉坐标（DIP）并转换为屏幕像素
        var vpos = textView.GetVisualPosition(caret.Position, ICSharpCode.AvalonEdit.Rendering.VisualYPosition.TextBottom);
        var screenDip = textView.PointToScreen(new System.Windows.Point(vpos.X, vpos.Y));
        var src = System.Windows.Interop.HwndSource.FromVisual(textArea) as System.Windows.Interop.HwndSource;
        if (src == null)
            return;

        var m = src.CompositionTarget?.TransformToDevice ?? System.Windows.Media.Matrix.Identity;
        var px = m.Transform(screenDip);

        // 将屏幕像素转换为窗口客户区坐标（Imm API 期望相对 client 的坐标）
        POINT clientPt = new POINT { x = (int)px.X, y = (int)px.Y };
        ScreenToClient(src.Handle, ref clientPt);
        System.Diagnostics.Debug.WriteLine($"[IME] UpdateImeWindowPosition screen=({px.X:F0},{px.Y:F0}) client=({clientPt.x},{clientPt.y})");

        IntPtr hIMC = ImmGetContext(src.Handle);
        if (hIMC == IntPtr.Zero)
            return;
        try
        {
            var cf = new COMPOSITIONFORM
            {
                // 尽量不强制，给输入法一定的自由度
                dwStyle = CFS_POINT,
                ptCurrentPos = clientPt,
                rcArea = new RECT { left = clientPt.x, top = clientPt.y, right = clientPt.x + 1, bottom = clientPt.y + 1 }
            };
            ImmSetCompositionWindow(hIMC, ref cf);

            var cand = new CANDIDATEFORM
            {
                dwIndex = 0,
                dwStyle = CFS_CANDIDATEPOS,
                ptCurrentPos = clientPt
            };
            ImmSetCandidateWindow(hIMC, ref cand);
        }
        finally
        {
            ImmReleaseContext(src.Handle, hIMC);
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"[IME] UpdateImeWindowPosition exception: {ex.Message}");
    }
}

[System.Runtime.InteropServices.DllImport("User32.dll")]
private static extern bool ScreenToClient(IntPtr hWnd, ref POINT lpPoint);

// Win32 IME 互操作常量与结构
private const int CFS_DEFAULT = 0x0000;
private const int CFS_POINT = 0x0002;
private const int CFS_FORCE_POSITION = 0x0020;
private const int CFS_CANDIDATEPOS = 0x0040;

[System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
private struct POINT { public int x; public int y; }

[System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
private struct RECT { public int left; public int top; public int right; public int bottom; }

[System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
private struct COMPOSITIONFORM
{
    public int dwStyle;
    public POINT ptCurrentPos;
    public RECT rcArea;
}

[System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
private struct CANDIDATEFORM
{
    public int dwIndex;
    public int dwStyle;
    public POINT ptCurrentPos;
    public RECT rcArea;
}

[System.Runtime.InteropServices.DllImport("Imm32.dll")]
private static extern IntPtr ImmGetContext(IntPtr hWnd);

[System.Runtime.InteropServices.DllImport("Imm32.dll")]
private static extern bool ImmReleaseContext(IntPtr hWnd, IntPtr hIMC);

[System.Runtime.InteropServices.DllImport("Imm32.dll")]
private static extern bool ImmSetCompositionWindow(IntPtr hIMC, ref COMPOSITIONFORM lpCompForm);

[System.Runtime.InteropServices.DllImport("Imm32.dll")]
private static extern bool ImmSetCandidateWindow(IntPtr hIMC, ref CANDIDATEFORM lpCandidate);

private void OnPreviewTextInputStart(object sender, TextCompositionEventArgs e)
{
    _isImeComposing = true;
    System.Diagnostics.Debug.WriteLine($"[IME] TextInputStart: comp='{e.TextComposition?.CompositionText}', text='{e.Text}', caret={Editor.CaretOffset}");
    // 组合开始时定位候选窗口
    Dispatcher.BeginInvoke(new Action(UpdateImeWindowPosition), System.Windows.Threading.DispatcherPriority.Background);
}

private void OnPreviewTextInputUpdate(object sender, TextCompositionEventArgs e)
{
    _isImeComposing = true;
    System.Diagnostics.Debug.WriteLine($"[IME] TextInputUpdate: comp='{e.TextComposition?.CompositionText}', text='{e.Text}', caret={Editor.CaretOffset}");
    // 组合更新时跟随光标（部分输入法在更新期变更候选框位置）
    Dispatcher.BeginInvoke(new Action(UpdateImeWindowPosition), System.Windows.Threading.DispatcherPriority.Background);
}

private void OnPreviewTextInputCommit(object sender, TextCompositionEventArgs e)
{
    System.Diagnostics.Debug.WriteLine($"[IME] TextInputCommit: text='{e.Text}', comp='{e.TextComposition?.CompositionText}', caret={Editor.CaretOffset}");
    _isImeComposing = false;
}

    /// <summary>
    ///     获取最近获得焦点的公式编辑器实例（若仍存活）。
    /// </summary>
    public static AvalonFormulaEditor? TryGetLastFocused()
    {
        if (_lastFocused != null && _lastFocused.TryGetTarget(out AvalonFormulaEditor? ed))
        {
            return ed;
        }

        return null;
    }

    /// <summary>
    ///     在光标位置插入文本，并可将光标定位到文本末尾前的偏移处。
    ///     例如插入 "NAME()" 并将光标移到括号内，可传入 caretOffsetFromEnd=1。
    /// </summary>
    public void InsertAtCaret(string text, int caretOffsetFromEnd = 0)
    {
        if (string.IsNullOrEmpty(text))
        {
            return;
        }

        TextDocument? doc = Editor.Document;
        int caret = Editor.CaretOffset;
        doc.Insert(caret, text);
        int newCaret = caret + Math.Max(0, text.Length - Math.Max(0, caretOffsetFromEnd));
        Editor.CaretOffset = newCaret;
        Editor.Focus();
    }

    /// <summary>
    ///     更新签名信息面板：在函数括号内根据光标位置高亮当前参数，并显示示例。
    ///     若无法定位到函数或不在参数区则隐藏面板。
    /// </summary>
    private void UpdateSignatureInfo()
    {
        try
        {
            int caretOffset = Editor.CaretOffset;
            string text = Editor.Text ?? string.Empty;
            if (caretOffset <= 0 || caretOffset > text.Length)
            {
                InfoPanel.Visibility = Visibility.Collapsed;
                return;
            }

            int left = text.LastIndexOf('(', Math.Max(0, caretOffset - 1));
            if (left < 0)
            {
                InfoPanel.Visibility = Visibility.Collapsed;
                return;
            }

            int depth = 1;
            int pos = left - 1;
            while (pos >= 0 && char.IsWhiteSpace(text[pos]))
            {
                pos--;
            }

            int end = pos;
            while (pos >= 0 && char.IsLetter(text[pos]))
            {
                pos--;
            }

            string name = text.Substring(pos + 1, end - pos).Trim();
            if (string.IsNullOrEmpty(name) || !_functions.Contains(name))
            {
                InfoPanel.Visibility = Visibility.Collapsed;
                return;
            }

            int i = left + 1;
            depth = 1;
            int argIndex = 0;
            while (i < text.Length && i < caretOffset && depth > 0)
            {
                char c = text[i];
                if (c == '(')
                {
                    depth++;
                }
                else if (c == ')')
                {
                    depth--;
                }
                else if (c == ',' && depth == 1)
                {
                    argIndex++;
                }

                i++;
            }

            if (_functionMap.TryGetValue(name, out CalcEngineFunctionCatalog.FunctionInfo? info))
            {
                string titleSig = BuildTitleSignature(info);
                InfoTextBlock.Text = titleSig;

                // 参数提示：若有中文参数名则逐项高亮；否则显示参数个数范围。
                if (info.ParamNamesZh is { Length: > 0 })
                {
                    string[]? names = info.ParamNamesZh;
                    int idx = Math.Min(argIndex, Math.Max(0, names.Length - 1));
                    InfoParamsTextBlock.Inlines.Clear();
                    InfoParamsTextBlock.Inlines.Add(new Run("参数: "));
                    for (int pi = 0; pi < names.Length; pi++)
                    {
                        if (pi > 0)
                        {
                            InfoParamsTextBlock.Inlines.Add(new Run("，"));
                        }

                        if (pi == idx)
                        {
                            InfoParamsTextBlock.Inlines.Add(new Bold(new Run(names[pi])));
                        }
                        else
                        {
                            InfoParamsTextBlock.Inlines.Add(new Run(names[pi]));
                        }
                    }
                }
                else
                {
                    InfoParamsTextBlock.Inlines.Clear();
                    InfoParamsTextBlock.Inlines.Add(new Run(BuildParamCount(info)));
                }

                InfoExampleTextBlock.Text = info.ExampleZh ?? string.Empty;
            }
            else
            {
                InfoTextBlock.Text = name;
                InfoParamsTextBlock.Inlines.Clear();
                InfoExampleTextBlock.Text = string.Empty;
            }

            InfoPanel.Visibility = Visibility.Visible;
        }
        catch
        {
            InfoPanel.Visibility = Visibility.Collapsed;
        }
    }

    /// <summary>
    ///     获取光标左侧“当前单词前缀”（仅字母），用于补全过滤和替换。
    /// </summary>
    /// <param name="startOffset">返回前缀在文档中的起始偏移</param>
    /// <returns>当前前缀文本（可能为空）</returns>
    private string GetCurrentPrefix(out int startOffset)
{
    int offset = Editor.CaretOffset;
    startOffset = offset;
    if (offset <= 0)
    {
        System.Diagnostics.Debug.WriteLine($"[IME] GetCurrentPrefix: offset={offset} -> empty");
        return string.Empty;
    }

    TextDocument? doc = Editor.Document;
    int i = offset - 1;
    while (i >= 0)
    {
        char ch = doc.GetCharAt(i);
        if (!char.IsLetter(ch))
        {
            break;
        }

        i--;
    }

    startOffset = i + 1;
    int len = offset - startOffset;
    string result = len > 0 ? doc.GetText(startOffset, len) : string.Empty;
    System.Diagnostics.Debug.WriteLine($"[IME] GetCurrentPrefix: offset={offset}, start={startOffset}, len={len}, result='{result}'");
    return result;
}

    /// <summary>
    ///     根据当前前缀动态刷新补全列表，仅保留匹配的函数项。
    /// </summary>
    private void RefreshCompletionFiltering()
    {
        if (_completionWindow == null)
        {
            return;
        }

        IList<ICompletionData>? data = _completionWindow.CompletionList.CompletionData;
        data.Clear();

        string? prefix = GetCurrentPrefix(out _);
        string kw = (prefix ?? string.Empty).Trim();

        IEnumerable<string> source = _functions;
        if (!string.IsNullOrEmpty(kw))
        {
            // 先按模糊得分排序，再按名称
            var scored = _functions
                .Select(n => new { Name = n, Score = FuzzyScore(n, kw) })
                .Where(x => x.Score > 0);
            source = scored
                .OrderByDescending(x => x.Score)
                .ThenBy(x => x.Name)
                .Select(x => x.Name);
        }
        else
        {
            source = source.OrderBy(n => n);
        }

        foreach (string f in source)
        {
            CalcEngineFunctionCatalog.FunctionInfo? info =
                _functionMap.TryGetValue(f, out CalcEngineFunctionCatalog.FunctionInfo? fi) ? fi : null;
            string desc = info == null || string.IsNullOrWhiteSpace(info.DescriptionZh) ? f : info.DescriptionZh;
            data.Add(new SimpleCompletionData(f, desc));
        }
    }

    private static int FuzzyScore(string text, string pattern)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
        {
            return 0;
        }

        int score = 0;
        int ti = 0;
        foreach (char pc in pattern)
        {
            int found = text.IndexOf(pc.ToString(), ti, StringComparison.OrdinalIgnoreCase);
            if (found < 0)
            {
                return 0;
            }

            // 基础分：匹配到一个字符
            score += 1;
            // 紧邻加权：相邻字符匹配提高分值
            if (found == ti)
            {
                score += 1;
            }

            ti = found + 1;
        }

        // 短名称略微加权，更易命中
        score += Math.Max(0, 4 - text.Length / 8);
        return score;
    }

    private static string BuildTitleSignature(CalcEngineFunctionCatalog.FunctionInfo info)
    {
        // 展示名称 + 简要中文描述
        string sig = info.ParmMin == 0 ? "()" : "(…)";
        string desc = string.IsNullOrWhiteSpace(info.DescriptionZh) ? string.Empty : $"  {info.DescriptionZh}";
        return $"{info.Name}{sig}{desc}";
    }

    private static string BuildParamCount(CalcEngineFunctionCatalog.FunctionInfo info)
    {
        if (info.ParmMin == info.ParmMax)
        {
            return $"参数: {info.ParmMin}";
        }

        string max = info.ParmMax == int.MaxValue ? "N" : info.ParmMax.ToString();
        return $"参数: {info.ParmMin} ~ {max}";
    }

    /// <summary>
    ///     文本变更时将编辑器内容写回依赖属性，以便外部绑定获取。
    /// </summary>
    private void Editor_TextChanged(object? sender, EventArgs e)
    {
        if (Text != Editor.Text)
        {
            Text = Editor.Text;
        }
    }

    /// <summary>
    ///     文本区收到一个“完整字符”输入后触发：
    ///     - 首字母时弹出或刷新补全列表；
    ///     - 更新参数签名提示。
    /// </summary>
    private void TextArea_TextEntered(object? sender, TextCompositionEventArgs e)
{
    if (!string.IsNullOrEmpty(e.Text))
    {
        char ch = e.Text[0];
        // 仅在 ASCII 字母输入时触发/刷新补全；中文不弹出
        if (ch <= 0x7f && char.IsLetter(ch))
        {
            if (_completionWindow == null)
            {
                ShowCompletion();
            }
            else
            {
                RefreshCompletionFiltering();
            }
        }
    }
    UpdateSignatureInfo();
}

    /// <summary>
    ///     预览按键：
    ///     - Ctrl+J 强制触发补全。
    ///     - Escape/Left/Right 关闭补全。
    ///     - BackSpace/Delete 导致文本变化时，刷新补全过滤。
    ///     - 光标移动/删除等情况刷新签名提示。
    /// </summary>
    private void TextArea_PreviewKeyDown(object? sender, KeyEventArgs e)
{
    // Ctrl+J 强制显示补全（避免与 IME 的 Ctrl+Space 冲突）
    if (e.Key == Key.J && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
    {
        ShowCompletion(force: true);
        e.Handled = true;
        return;
    }

    if (_completionWindow != null)
    {
        if (e.Key == Key.Escape)
        {
            _completionWindow.Close();
        }
        else if (e.Key == Key.Left || e.Key == Key.Right)
        {
            _completionWindow.Close();
        }
        else if (e.Key == Key.Back || e.Key == Key.Delete)
        {
            Dispatcher.BeginInvoke(new Action(RefreshCompletionFiltering));
        }
    }

    if (e.Key is Key.Left or Key.Right or Key.Up or Key.Down or Key.Back or Key.Delete or Key.Home or Key.End)
    {
        Dispatcher.BeginInvoke(new Action(UpdateSignatureInfo));
    }
}

    /// <summary>
    ///     文本区“输入进行中”：用于在用户持续输入时异步刷新补全过滤。
    /// </summary>
    private void TextArea_TextEntering(object? sender, TextCompositionEventArgs e)
{
    if (_completionWindow == null)
        return;

    // 组合阶段（空文本）不处理，避免干扰 IME
    if (string.IsNullOrEmpty(e.Text))
        return;

    char ch = e.Text[0];
    // 仅允许 ASCII 字母/数字保留补全；中文/符号关闭补全
    if (!(ch <= 0x7f && char.IsLetterOrDigit(ch)))
    {
        _completionWindow.Close();
        return;
    }

    // 输入 ASCII 字母/数字时异步刷新
    Dispatcher.BeginInvoke(new Action(RefreshCompletionFiltering));
}

    /// <summary>
    ///     打开补全窗口并按当前前缀填充候选项。
    /// </summary>
    /// <param name="force">true=忽略前缀为空的情况也显示所有函数；false=仅在有前缀时过滤显示</param>
    private void ShowCompletion(bool force = false)
{
    try
    {
        System.Diagnostics.Debug.WriteLine($"[IME] ShowCompletion(force={force}) at caret={Editor.CaretOffset}");
        TextArea? textArea = Editor.TextArea;
        _completionWindow?.Close();
        _completionWindow = new CompletionWindow(textArea) { CloseWhenCaretAtBeginning = true };
        IList<ICompletionData>? data = _completionWindow.CompletionList.CompletionData;

        string prefix = GetCurrentPrefix(out _);
        bool hasPrefix = !string.IsNullOrEmpty(prefix);
        System.Diagnostics.Debug.WriteLine($"[IME] ShowCompletion: prefix='{prefix}', hasPrefix={hasPrefix}");

        IEnumerable<string> functionNames = _functions.AsEnumerable();
        if (hasPrefix)
        {
            functionNames = functionNames.Where(n => n.StartsWith(prefix, StringComparison.OrdinalIgnoreCase));
        }

        int count = 0;
        foreach (string name in functionNames.OrderBy(n => n))
        {
            CalcEngineFunctionCatalog.FunctionInfo f = _functionMap[name];
            string desc = string.IsNullOrWhiteSpace(f.DescriptionZh) ? f.Name : f.DescriptionZh;
            data.Add(new SimpleCompletionData(name, desc));
            count++;
        }
        System.Diagnostics.Debug.WriteLine($"[IME] ShowCompletion: items={count}");

        _completionWindow.Show();
        _completionWindow.Closed += (o, args) => _completionWindow = null;
    }
    catch
    {
        _completionWindow = null;
        System.Diagnostics.Debug.WriteLine("[IME] ShowCompletion: exception -> completionWindow reset to null");
    }
}
}

/// <summary>
///     简单补全项：展示“名称 + 描述”，并在选择时用选中项替换“光标左侧前缀”。
/// </summary>
internal class SimpleCompletionData : ICompletionData
{
    public SimpleCompletionData(string text, string? description = null)
    {
        Text = text;
        Description = string.IsNullOrEmpty(description) ? text : description;
    }

    public ImageSource? Image => null;
    public string Text { get; }

    public object Content
    {
        get
        {
            Grid grid = new() { Margin = new Thickness(0) };
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            TextBlock nameTb = new()
            {
                Text = Text,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 12, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(nameTb, 0);
            grid.Children.Add(nameTb);

            string desc = Description?.ToString() ?? string.Empty;
            TextBlock descTb = new()
            {
                Text = desc,
                Foreground = new SolidColorBrush(Color.FromRgb(120, 120, 120)),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap,
                TextTrimming = TextTrimming.CharacterEllipsis
            };
            Grid.SetColumn(descTb, 1);
            grid.Children.Add(descTb);

            return grid;
        }
    }

    public object Description { get; }
    public double Priority => 0;

    /// <summary>
    ///     执行补全：优先查找并替换光标左侧的字母前缀，若没有则按原片段替换。
    /// </summary>
    public void Complete(TextArea textArea, ISegment completionSegment, EventArgs insertionRequestEventArgs)
    {
        // 替换当前光标左侧的字母前缀为所选项，提升前缀匹配体验
        TextDocument? doc = textArea.Document;
        int offset = textArea.Caret.Offset;
        int start = offset;
        while (start > 0)
        {
            char ch = doc.GetCharAt(start - 1);
            if (!char.IsLetter(ch))
            {
                break;
            }

            start--;
        }

        int length = offset - start;
        if (length > 0)
        {
            doc.Replace(start, length, Text);
        }
        else
        {
            doc.Replace(completionSegment, Text);
        }
    }
}

/// <summary>
///     公式高亮器：扫描每一行，将匹配到的“函数名”按类别着色。
/// </summary>
internal class FormulaHighlightColorizer : DocumentColorizingTransformer
{
    private static readonly SolidColorBrush BrushLogical = new(Color.FromRgb(142, 36, 170)); // 紫
    private static readonly SolidColorBrush BrushMath = new(Color.FromRgb(46, 125, 50)); // 绿
    private static readonly SolidColorBrush BrushText = new(Color.FromRgb(21, 101, 192)); // 蓝
    private static readonly SolidColorBrush BrushStat = new(Color.FromRgb(239, 108, 0)); // 橙
    private static readonly SolidColorBrush BrushOther = new(Color.FromRgb(69, 90, 100)); // 灰蓝
    private readonly IDictionary<string, CalcEngineFunctionCatalog.FunctionCategory> _funcCats;

    /// <summary>
    ///     构造高亮器。
    /// </summary>
    /// <param name="funcCats">函数名到类别的映射表</param>
    public FormulaHighlightColorizer(IDictionary<string, CalcEngineFunctionCatalog.FunctionCategory> funcCats)
    {
        _funcCats = funcCats;
        // 冻结画刷，提升性能
        BrushLogical.Freeze();
        BrushMath.Freeze();
        BrushText.Freeze();
        BrushStat.Freeze();
        BrushOther.Freeze();
    }

    /// <summary>
    ///     对一行文本进行着色处理：为花括号包裹的占位符和函数名分别设定颜色。
    /// </summary>
    protected override void ColorizeLine(DocumentLine line)
    {
        string? text = CurrentContext.Document.GetText(line);
        int lineStart = line.Offset;

        for (int i = 0; i < text.Length; i++)
        {
            if (text[i] == '{')
            {
                int j = i + 1;
                while (j < text.Length && text[j] != '}')
                {
                    j++;
                }

                if (j < text.Length && text[j] == '}')
                {
                    ChangeLinePart(lineStart + i, lineStart + j + 1, elem =>
                    {
                        elem.TextRunProperties.SetForegroundBrush(new SolidColorBrush(Color.FromRgb(30, 136, 229)));
                        elem.TextRunProperties.SetTypeface(new Typeface(elem.TextRunProperties.Typeface.FontFamily,
                            FontStyles.Normal, FontWeights.SemiBold, FontStretches.Normal));
                    });
                    i = j;
                }
            }
        }

        int idx = 0;
        while (idx < text.Length)
        {
            if (char.IsLetter(text[idx]))
            {
                int start = idx;
                while (idx < text.Length && char.IsLetter(text[idx]))
                {
                    idx++;
                }

                string word = text.Substring(start, idx - start);
                if (_funcCats.TryGetValue(word, out CalcEngineFunctionCatalog.FunctionCategory cat))
                {
                    ChangeLinePart(lineStart + start, lineStart + idx, elem =>
                    {
                        elem.TextRunProperties.SetForegroundBrush(cat switch
                        {
                            CalcEngineFunctionCatalog.FunctionCategory.Logical => BrushLogical,
                            CalcEngineFunctionCatalog.FunctionCategory.Math => BrushMath,
                            CalcEngineFunctionCatalog.FunctionCategory.Text => BrushText,
                            CalcEngineFunctionCatalog.FunctionCategory.Statistical => BrushStat,
                            _ => BrushOther
                        });
                    });
                }
            }
            else
            {
                idx++;
            }
        }
    }
}
