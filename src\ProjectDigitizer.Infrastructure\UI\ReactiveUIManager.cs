using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Windows.Threading;

namespace ProjectDigitizer.Infrastructure.UI
{
    /// <summary>
    /// 响应式UI管理器实现
    /// </summary>
    public class ReactiveUIManager : IReactiveUIManager
    {
        private readonly DispatcherTimer _batchTimer;
        private readonly DispatcherTimer _performanceTimer;
        private readonly ConcurrentDictionary<INotifyPropertyChanged, ViewModelInfo> _registeredViewModels;
        private readonly ConcurrentQueue<PropertyUpdate> _pendingUpdates;
        private readonly ConcurrentDictionary<string, DateTime> _throttleTimestamps;
        private readonly ConcurrentDictionary<string, object> _debounceTimers;
        private readonly ConcurrentDictionary<string, System.Threading.Timer> _delayedTimers;
        private readonly object _lockObject = new();

        private bool _isActive;
        private bool _disposed;
        private long _totalUpdates;
        private long _batchedUpdates;
        private long _throttledUpdates;
        private long _debouncedUpdates;

        public ReactiveUIConfig CurrentConfig { get; private set; }
        public UIUpdateStats UpdateStats { get; private set; }

        public event Action<UIBatchUpdateEventArgs>? BatchUpdateCompleted;
        public event Action<UIPerformanceWarning>? PerformanceWarning;

        public ReactiveUIManager()
        {
            CurrentConfig = new ReactiveUIConfig();
            UpdateStats = new UIUpdateStats();

            _registeredViewModels = new ConcurrentDictionary<INotifyPropertyChanged, ViewModelInfo>();
            _pendingUpdates = new ConcurrentQueue<PropertyUpdate>();
            _throttleTimestamps = new ConcurrentDictionary<string, DateTime>();
            _debounceTimers = new ConcurrentDictionary<string, object>();
            _delayedTimers = new ConcurrentDictionary<string, System.Threading.Timer>();

            // 批处理定时器
            _batchTimer = new DispatcherTimer(CurrentConfig.UIThreadPriority)
            {
                Interval = TimeSpan.FromMilliseconds(CurrentConfig.BatchDelayMs)
            };
            _batchTimer.Tick += OnBatchTimerTick;

            // 性能监控定时器
            _performanceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // 每5秒检查一次性能
            };
            _performanceTimer.Tick += OnPerformanceTimerTick;
        }

        public void StartReactiveUI()
        {
            if (_disposed) return;

            if (!_isActive)
            {
                _isActive = true;

                if (CurrentConfig.EnableBatchUpdates)
                {
                    _batchTimer.Start();
                }

                if (CurrentConfig.EnablePerformanceMonitoring)
                {
                    _performanceTimer.Start();
                }
            }
        }

        public void StopReactiveUI()
        {
            if (_isActive)
            {
                _isActive = false;
                _batchTimer.Stop();
                _performanceTimer.Stop();

                // 处理所有待处理的更新
                FlushPendingUpdates();
            }
        }

        public void RegisterViewModel(INotifyPropertyChanged viewModel, ViewModelConfig? config = null)
        {
            if (viewModel == null) return;

            var viewModelInfo = new ViewModelInfo
            {
                ViewModel = viewModel,
                Config = config ?? new ViewModelConfig(),
                IsSuspended = false,
                LastUpdateTime = DateTime.Now
            };

            if (_registeredViewModels.TryAdd(viewModel, viewModelInfo))
            {
                // 订阅属性变化事件
                viewModel.PropertyChanged += OnViewModelPropertyChanged;
            }
        }

        public void UnregisterViewModel(INotifyPropertyChanged viewModel)
        {
            if (viewModel == null) return;

            if (_registeredViewModels.TryRemove(viewModel, out var viewModelInfo))
            {
                // 取消订阅属性变化事件
                viewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }
        }

        public void BatchUpdateProperties(IEnumerable<PropertyUpdate> updates)
        {
            if (!_isActive || updates == null) return;

            var updateList = updates.ToList();
            if (updateList.Count == 0) return;

            var stopwatch = Stopwatch.StartNew();

            // 按优先级排序更新
            var sortedUpdates = updateList
                .OrderByDescending(u => u.Priority)
                .ThenBy(u => u.Timestamp);

            var processedViewModels = new HashSet<INotifyPropertyChanged>();

            foreach (var update in sortedUpdates)
            {
                if (_registeredViewModels.TryGetValue(update.ViewModel, out var viewModelInfo) &&
                    !viewModelInfo.IsSuspended)
                {
                    ApplyPropertyUpdate(update);
                    processedViewModels.Add(update.ViewModel);
                }
            }

            stopwatch.Stop();
            _batchedUpdates += updateList.Count;

            // 触发批处理完成事件
            BatchUpdateCompleted?.Invoke(new UIBatchUpdateEventArgs
            {
                UpdateCount = updateList.Count,
                ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                ViewModelCount = processedViewModels.Count,
                BatchType = BatchUpdateType.Manual
            });
        }

        public void DelayedUpdate(Action updateAction, int delay = 100)
        {
            if (updateAction == null) return;

            // 在测试环境中使用System.Threading.Timer，在UI环境中使用DispatcherTimer
            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                var timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(delay)
                };

                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    try
                    {
                        updateAction();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"延迟更新执行失败: {ex.Message}");
                    }
                };

                timer.Start();
            }
            else
            {
                // 测试环境 - 使用System.Threading.Timer，并确保计时器在回调前不被GC回收
                var key = $"delay_{Guid.NewGuid()}";
                var timer = new System.Threading.Timer(_ =>
                {
                    // 回调执行后清理计时器引用
                    _delayedTimers.TryRemove(key, out var t);
                    t?.Dispose();
                    try
                    {
                        updateAction();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"延迟更新执行失败: {ex.Message}");
                    }
                }, null, delay, Timeout.Infinite);

                _delayedTimers[key] = timer;
            }
        }

        public void ThrottledUpdate(string key, Action updateAction, int throttleMs = 100)
        {
            if (string.IsNullOrEmpty(key) || updateAction == null) return;

            var now = DateTime.Now;
            var throttleKey = $"throttle_{key}";

            if (_throttleTimestamps.TryGetValue(throttleKey, out var lastTime))
            {
                var elapsed = (now - lastTime).TotalMilliseconds;
                if (elapsed < throttleMs)
                {
                    _throttledUpdates++;
                    return; // 跳过这次更新
                }
            }

            _throttleTimestamps[throttleKey] = now;

            try
            {
                updateAction();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"节流更新执行失败: {ex.Message}");
            }
        }

        public void DebouncedUpdate(string key, Action updateAction, int debounceMs = 300)
        {
            if (string.IsNullOrEmpty(key) || updateAction == null) return;

            var debounceKey = $"debounce_{key}";

            // 取消之前的防抖定时器
            if (_debounceTimers.TryGetValue(debounceKey, out var existingTimer))
            {
                if (existingTimer is DispatcherTimer dispatcherTimer)
                {
                    dispatcherTimer.Stop();
                }
                else if (existingTimer is System.Threading.Timer threadingTimer)
                {
                    threadingTimer.Dispose();
                }
                _debouncedUpdates++;
            }

            // 在测试环境中使用System.Threading.Timer，在UI环境中使用DispatcherTimer
            if (System.Windows.Application.Current?.Dispatcher != null)
            {
                // 创建新的防抖定时器
                var timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(debounceMs)
                };

                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    _debounceTimers.TryRemove(debounceKey, out _);

                    try
                    {
                        updateAction();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"防抖更新执行失败: {ex.Message}");
                    }
                };

                _debounceTimers[debounceKey] = timer;
                timer.Start();
            }
            else
            {
                // 测试环境 - 使用System.Threading.Timer，需要正确处理防抖
                var timer = new System.Threading.Timer(_ =>
                {
                    _debounceTimers.TryRemove(debounceKey, out _);
                    try
                    {
                        updateAction();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"防抖更新执行失败: {ex.Message}");
                    }
                }, null, debounceMs, Timeout.Infinite);

                _debounceTimers[debounceKey] = timer;
            }
        }

        public void SetPropertyPriority(INotifyPropertyChanged viewModel, string propertyName, UpdatePriority priority)
        {
            if (viewModel == null || string.IsNullOrEmpty(propertyName)) return;

            if (_registeredViewModels.TryGetValue(viewModel, out var viewModelInfo))
            {
                if (!viewModelInfo.Config.PropertyConfigs.ContainsKey(propertyName))
                {
                    viewModelInfo.Config.PropertyConfigs[propertyName] = new PropertyConfig();
                }

                viewModelInfo.Config.PropertyConfigs[propertyName].Priority = priority;
            }
        }

        public void SuspendUpdates(INotifyPropertyChanged viewModel)
        {
            if (viewModel != null && _registeredViewModels.TryGetValue(viewModel, out var viewModelInfo))
            {
                viewModelInfo.IsSuspended = true;
            }
        }

        public void ResumeUpdates(INotifyPropertyChanged viewModel)
        {
            if (viewModel != null && _registeredViewModels.TryGetValue(viewModel, out var viewModelInfo))
            {
                viewModelInfo.IsSuspended = false;
            }
        }

        public void FlushPendingUpdates()
        {
            if (_pendingUpdates.IsEmpty) return;

            var updates = new List<PropertyUpdate>();
            while (_pendingUpdates.TryDequeue(out var update))
            {
                updates.Add(update);
            }

            if (updates.Count > 0)
            {
                BatchUpdateProperties(updates);
            }
        }

        public UIPerformanceMetrics GetPerformanceMetrics()
        {
            var totalRegistered = _registeredViewModels.Count;
            var batchingEfficiency = _totalUpdates > 0 ? (double)_batchedUpdates / _totalUpdates : 1.0;
            var throttlingEfficiency = _totalUpdates > 0 ? (double)_throttledUpdates / _totalUpdates : 0.0;

            return new UIPerformanceMetrics
            {
                UIResponseTimeMs = UpdateStats.AverageUpdateDelayMs,
                UpdateFrequency = CalculateUpdateFrequency(),
                BatchingEfficiency = batchingEfficiency,
                ThrottlingEfficiency = throttlingEfficiency,
                OverallUIPerformanceScore = CalculateOverallScore(batchingEfficiency, throttlingEfficiency),
                RegisteredViewModelsCount = totalRegistered
            };
        }

        public void ApplyConfig(ReactiveUIConfig config)
        {
            CurrentConfig = config ?? throw new ArgumentNullException(nameof(config));

            // 更新定时器间隔
            _batchTimer.Interval = TimeSpan.FromMilliseconds(CurrentConfig.BatchDelayMs);

            // 重启服务以应用新配置
            if (_isActive)
            {
                StopReactiveUI();
                StartReactiveUI();
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (!_isActive || sender is not INotifyPropertyChanged viewModel || string.IsNullOrEmpty(e.PropertyName))
                return;

            if (!_registeredViewModels.TryGetValue(viewModel, out var viewModelInfo) || viewModelInfo.IsSuspended)
                return;

            var priority = GetPropertyPriority(viewModelInfo, e.PropertyName);
            var update = new PropertyUpdate
            {
                ViewModel = viewModel,
                PropertyName = e.PropertyName,
                Priority = priority,
                Timestamp = DateTime.Now
            };

            if (CurrentConfig.EnableBatchUpdates && priority != UpdatePriority.Critical)
            {
                // 添加到批处理队列
                _pendingUpdates.Enqueue(update);

                // 检查是否需要立即处理
                if (_pendingUpdates.Count >= CurrentConfig.BatchSize)
                {
                    ProcessBatchUpdates(BatchUpdateType.SizeBased);
                }
            }
            else
            {
                // 立即处理高优先级更新
                ApplyPropertyUpdate(update);
            }

            _totalUpdates++;
            viewModelInfo.LastUpdateTime = DateTime.Now;
        }

        private void OnBatchTimerTick(object? sender, EventArgs e)
        {
            if (!_pendingUpdates.IsEmpty)
            {
                ProcessBatchUpdates(BatchUpdateType.TimeBased);
            }
        }

        private void OnPerformanceTimerTick(object? sender, EventArgs e)
        {
            UpdatePerformanceStats();
            CheckPerformanceThresholds();
        }

        private void ProcessBatchUpdates(BatchUpdateType batchType)
        {
            var updates = new List<PropertyUpdate>();
            var maxBatchSize = Math.Min(CurrentConfig.BatchSize, _pendingUpdates.Count);

            for (int i = 0; i < maxBatchSize && _pendingUpdates.TryDequeue(out var update); i++)
            {
                updates.Add(update);
            }

            if (updates.Count > 0)
            {
                var stopwatch = Stopwatch.StartNew();

                // 按优先级和时间排序
                var sortedUpdates = updates
                    .OrderByDescending(u => u.Priority)
                    .ThenBy(u => u.Timestamp);

                var processedViewModels = new HashSet<INotifyPropertyChanged>();

                foreach (var update in sortedUpdates)
                {
                    ApplyPropertyUpdate(update);
                    processedViewModels.Add(update.ViewModel);
                }

                stopwatch.Stop();
                _batchedUpdates += updates.Count;

                BatchUpdateCompleted?.Invoke(new UIBatchUpdateEventArgs
                {
                    UpdateCount = updates.Count,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    ViewModelCount = processedViewModels.Count,
                    BatchType = batchType
                });
            }
        }

        private void ApplyPropertyUpdate(PropertyUpdate update)
        {
            try
            {
                // 使用反射设置属性值
                var property = update.ViewModel.GetType().GetProperty(update.PropertyName);
                if (property != null && property.CanWrite)
                {
                    property.SetValue(update.ViewModel, update.NewValue);
                    Debug.WriteLine($"成功应用属性更新: {update.PropertyName} = {update.NewValue}");
                }
                else
                {
                    Debug.WriteLine($"无法找到或设置属性: {update.PropertyName}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用属性更新失败: {ex.Message}");
            }
        }

        private UpdatePriority GetPropertyPriority(ViewModelInfo viewModelInfo, string propertyName)
        {
            if (viewModelInfo.Config.PropertyConfigs.TryGetValue(propertyName, out var propertyConfig))
            {
                return propertyConfig.Priority;
            }

            return viewModelInfo.Config.Priority;
        }

        private void UpdatePerformanceStats()
        {
            UpdateStats.TotalUpdates = _totalUpdates;
            UpdateStats.BatchedUpdates = _batchedUpdates;
            UpdateStats.ThrottledUpdates = _throttledUpdates;
            UpdateStats.DebouncedUpdates = _debouncedUpdates;
            UpdateStats.PendingUpdatesCount = _pendingUpdates.Count;
            UpdateStats.LastUpdated = DateTime.Now;
        }

        private void CheckPerformanceThresholds()
        {
            var pendingCount = _pendingUpdates.Count;

            if (pendingCount > CurrentConfig.MaxPendingUpdates * 0.8)
            {
                var level = pendingCount > CurrentConfig.MaxPendingUpdates ? UIWarningLevel.Critical : UIWarningLevel.Warning;

                PerformanceWarning?.Invoke(new UIPerformanceWarning
                {
                    Level = level,
                    Message = $"待处理UI更新数量过多: {pendingCount}",
                    CurrentMetrics = GetPerformanceMetrics(),
                    SuggestedSolutions = new[]
                    {
                        "增加批处理大小",
                        "减少批处理延迟",
                        "启用更积极的节流策略"
                    }
                });
            }
        }

        private double CalculateUpdateFrequency()
        {
            // 简化的更新频率计算
            return _totalUpdates > 0 ? _totalUpdates / Math.Max(1, (DateTime.Now - UpdateStats.LastUpdated).TotalSeconds) : 0;
        }

        private double CalculateOverallScore(double batchingEfficiency, double throttlingEfficiency)
        {
            var pendingRatio = Math.Min(1.0, (double)_pendingUpdates.Count / CurrentConfig.MaxPendingUpdates);
            var pendingScore = (1.0 - pendingRatio) * 100;
            var batchingScore = batchingEfficiency * 100;
            var throttlingScore = throttlingEfficiency * 50; // 节流不是越多越好

            return (pendingScore + batchingScore + throttlingScore) / 3.0;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopReactiveUI();

                // 清理所有注册的ViewModel
                foreach (var kvp in _registeredViewModels)
                {
                    kvp.Key.PropertyChanged -= OnViewModelPropertyChanged;
                }
                _registeredViewModels.Clear();

                // 清理防抖定时器
                foreach (var timer in _debounceTimers.Values)
                {
                    if (timer is DispatcherTimer dispatcherTimer)
                    {
                        dispatcherTimer.Stop();
                    }
                    else if (timer is System.Threading.Timer threadingTimer)
                    {
                        threadingTimer.Dispose();
                    }
                }
                _debounceTimers.Clear();

                _batchTimer?.Stop();
                _performanceTimer?.Stop();

                _disposed = true;
            }
        }

        /// <summary>
        /// ViewModel信息
        /// </summary>
        private class ViewModelInfo
        {
            public INotifyPropertyChanged ViewModel { get; set; } = null!;
            public ViewModelConfig Config { get; set; } = new();
            public bool IsSuspended { get; set; }
            public DateTime LastUpdateTime { get; set; }
        }
    }
}
