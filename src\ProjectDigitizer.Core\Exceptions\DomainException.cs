namespace ProjectDigitizer.Core.Exceptions;

/// <summary>
/// 领域异常，表示业务规则违反或领域逻辑错误
/// </summary>
public class DomainException : ProjectDigitizerException
{
    public DomainException(string message) : base(message)
    {
    }

    public DomainException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public DomainException(string message, string? errorCode) : base(message, errorCode)
    {
    }

    public DomainException(string message, string? errorCode, Exception innerException) : base(message, errorCode, innerException)
    {
    }
}

/// <summary>
/// 业务规则违反异常
/// </summary>
public class BusinessRuleViolationException : DomainException
{
    public string RuleName { get; }

    public BusinessRuleViolationException(string ruleName, string message) : base(message, "BUSINESS_RULE_VIOLATION")
    {
        RuleName = ruleName;
        WithDetail("RuleName", ruleName);
    }

    public BusinessRuleViolationException(string ruleName, string message, Exception innerException) : base(message, "BUSINESS_RULE_VIOLATION", innerException)
    {
        RuleName = ruleName;
        WithDetail("RuleName", ruleName);
    }
}

/// <summary>
/// 实体未找到异常
/// </summary>
public class EntityNotFoundException : DomainException
{
    public string EntityType { get; }
    public object EntityId { get; }

    public EntityNotFoundException(string entityType, object entityId)
        : base($"Entity of type '{entityType}' with id '{entityId}' was not found.", "ENTITY_NOT_FOUND")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType);
        WithDetail("EntityId", entityId);
    }

    public EntityNotFoundException(string entityType, object entityId, Exception innerException)
        : base($"Entity of type '{entityType}' with id '{entityId}' was not found.", "ENTITY_NOT_FOUND", innerException)
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType);
        WithDetail("EntityId", entityId);
    }
}

/// <summary>
/// 实体已存在异常
/// </summary>
public class EntityAlreadyExistsException : DomainException
{
    public string EntityType { get; }
    public object EntityId { get; }

    public EntityAlreadyExistsException(string entityType, object entityId)
        : base($"Entity of type '{entityType}' with id '{entityId}' already exists.", "ENTITY_ALREADY_EXISTS")
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType);
        WithDetail("EntityId", entityId);
    }

    public EntityAlreadyExistsException(string entityType, object entityId, Exception innerException)
        : base($"Entity of type '{entityType}' with id '{entityId}' already exists.", "ENTITY_ALREADY_EXISTS", innerException)
    {
        EntityType = entityType;
        EntityId = entityId;
        WithDetail("EntityType", entityType);
        WithDetail("EntityId", entityId);
    }
}

/// <summary>
/// 无效操作异常
/// </summary>
public class InvalidDomainOperationException : DomainException
{
    public string Operation { get; }

    public InvalidDomainOperationException(string operation, string message)
        : base(message, "INVALID_DOMAIN_OPERATION")
    {
        Operation = operation;
        WithDetail("Operation", operation);
    }

    public InvalidDomainOperationException(string operation, string message, Exception innerException)
        : base(message, "INVALID_DOMAIN_OPERATION", innerException)
    {
        Operation = operation;
        WithDetail("Operation", operation);
    }
}
