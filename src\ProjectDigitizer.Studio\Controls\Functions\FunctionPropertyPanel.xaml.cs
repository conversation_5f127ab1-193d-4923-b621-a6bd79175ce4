using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

using ProjectDigitizer.Core.Entities;
using ProjectDigitizer.Core.ValueObjects;
using ProjectDigitizer.Studio.Controls.Canvas.Services;
using ProjectDigitizer.Studio.Controls.Properties.Services;
using ProjectDigitizer.Studio.Controls.Properties.Widgets;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls.Functions
{
    /// <summary>
    /// 函数属性面板交互逻辑
    /// </summary>
    public partial class FunctionPropertyPanel : UserControl
    {
        private FunctionNodeConfig _config = null!;
        private FieldReferenceTracker _referenceTracker = null!;
        private ConnectionStyleSyncService _connectionStyleService = null!;
        private bool _isLoadingFunctions = false;

        /// <summary>当前节点依赖属性</summary>
        public static readonly DependencyProperty CurrentNodeProperty = DependencyProperty.Register(
            nameof(CurrentNode), typeof(ModuleNodeViewModel), typeof(FunctionPropertyPanel),
            new PropertyMetadata(null, OnCurrentNodeChanged));

        /// <summary>画布视图模型依赖属性</summary>
        public static readonly DependencyProperty CanvasViewModelProperty = DependencyProperty.Register(
            nameof(CanvasViewModel), typeof(CanvasViewModel), typeof(FunctionPropertyPanel),
            new PropertyMetadata(null, OnCanvasViewModelChanged));

        /// <summary>当前节点</summary>
        public ModuleNodeViewModel? CurrentNode
        {
            get => (ModuleNodeViewModel?)GetValue(CurrentNodeProperty);
            set => SetValue(CurrentNodeProperty, value);
        }

        /// <summary>画布视图模型</summary>
        public CanvasViewModel? CanvasViewModel
        {
            get => (CanvasViewModel?)GetValue(CanvasViewModelProperty);
            set => SetValue(CanvasViewModelProperty, value);
        }

        /// <summary>当前节点变化回调</summary>
        private static void OnCurrentNodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FunctionPropertyPanel panel)
            {
                panel.OnCurrentNodeChanged();
            }
        }

        /// <summary>
        /// 画布视图模型变化回调
        /// </summary>
        private static void OnCanvasViewModelChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FunctionPropertyPanel panel)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] CanvasViewModel changed: {e.NewValue != null}");
                if (e.NewValue is CanvasViewModel canvasViewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] CanvasViewModel nodes count: {canvasViewModel.Nodes?.Count ?? 0}");
                }
                panel.OnCanvasViewModelChanged();
            }
        }

        /// <summary>属性值变化事件</summary>
        public event EventHandler<PropertyValueChangedEventArgs>? PropertyValueChanged;

        /// <summary>可用函数类型</summary>
        public List<FunctionType> AvailableFunctionTypes { get; } = Enum.GetValues<FunctionType>().ToList();

        public FunctionPropertyPanel()
        {
            InitializeComponent();
            InitializeConfig();
            InitializeEventHandlers();
        }

        /// <summary>
        /// 设置当前节点
        /// </summary>
        /// <param name="node">节点视图模型</param>
        public void SetNode(ModuleNodeViewModel? node)
        {
            CurrentNode = node;
        }

        /// <summary>
        /// 初始化配置
        /// </summary>
        private void InitializeConfig()
        {
            _config = new FunctionNodeConfig();
            _referenceTracker = new FieldReferenceTracker();
            _connectionStyleService = new ConnectionStyleSyncService();

            // 设置数据上下文
            DataContext = _config;

            // 建立服务之间的关联
            _connectionStyleService.SetReferenceTracker(_referenceTracker);

            // 订阅引用追踪器事件
            _referenceTracker.FieldReferenceChanged += OnFieldReferenceChanged;
            _referenceTracker.ConnectionStateChanged += OnConnectionStateChanged;

            // 订阅连接线样式变化事件
            _connectionStyleService.ConnectionStyleChanged += OnConnectionStyleChanged;
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            // 数据来源模式切换
            DataSourceModeToggle.Checked += (s, e) => OnDataSourceModeChanged(DataSourceMode.ExternalFile);
            DataSourceModeToggle.Unchecked += (s, e) => OnDataSourceModeChanged(DataSourceMode.NodeOutput);

            // 字段搜索
            FieldSearchBox.TextChanged += OnFieldSearchTextChanged;

            // 节点选择列表双击添加（如果需要的话）
            // AvailableNodesControl.MouseDoubleClick += OnNodeListDoubleClick;

            // 清空已选字段
            ClearSelectedFieldsButton.Click += OnClearSelectedFieldsClick;

            // 添加函数
            AddFunctionButton.Click += OnAddFunctionClick;

            // 导入模板由 XAML 绑定到 ImportTemplateButton_Click

            // 执行所有函数
            ExecuteAllButton.Click += OnExecuteAllClick;

            // 导出结果
            ExportResultsButton.Click += OnExportResultsClick;

            // 监听函数表达式变化
            _config.Functions.CollectionChanged += OnFunctionsCollectionChanged;

            // 为每个现有函数订阅属性变化事件
            foreach (var function in _config.Functions)
            {
                function.PropertyChanged += OnFunctionPropertyChanged;
            }
        }

        /// <summary>
        /// 当前节点变化处理
        /// </summary>
        private void OnCurrentNodeChanged()
        {
            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] OnCurrentNodeChanged called. CurrentNode: {CurrentNode?.Title ?? "null"}");
            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Module Type: {CurrentNode?.Module?.Type.ToString() ?? "null"}");

            if (CurrentNode?.Module == null)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] CurrentNode or Module is null, clearing config");
                ClearConfig();
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Loading node config and fields");
            LoadNodeConfig();
            LoadAvailableFields();
            LoadAvailableSourceNodes();
        }

        /// <summary>
        /// 画布视图模型变化处理
        /// </summary>
        private void OnCanvasViewModelChanged()
        {
            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] OnCanvasViewModelChanged called");
            // 当画布视图模型变化时，重新加载可用的源节点
            LoadAvailableSourceNodes();
        }

        /// <summary>
        /// 加载节点配置
        /// </summary>
        private void LoadNodeConfig()
        {
            if (CurrentNode?.PropertyValues == null)
                return;

            try
            {
                // 从节点属性中加载函数配置
                var configJson = CurrentNode.PropertyValues.GetValue("functionConfig")?.ToString();
                if (!string.IsNullOrEmpty(configJson))
                {
                    // TODO: 实现JSON反序列化加载配置
                    // _config = JsonSerializer.Deserialize<FunctionNodeConfig>(configJson);
                }

                // 加载已选字段
                LoadSelectedFields();

                // 加载函数表达式
                LoadFunctions();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载节点配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载可用字段
        /// </summary>
        private void LoadAvailableFields()
        {
            _config.AvailableFields.Clear();

            if (_config.DataSourceMode == DataSourceMode.NodeOutput)
            {
                LoadFieldsFromConnectedNodes();
            }
            else
            {
                LoadFieldsFromExternalFile();
            }
        }

        /// <summary>
        /// 从连接的节点加载字段
        /// </summary>
        private void LoadFieldsFromConnectedNodes()
        {
            if (CurrentNode?.Inputs == null)
                return;

            foreach (var input in CurrentNode.Inputs)
            {
                if (input.IsConnected)
                {
                    // 获取连接的源节点
                    var sourceNode = GetSourceNode(input);
                    if (sourceNode != null)
                    {
                        var fields = GetNodeOutputFields(sourceNode);
                        foreach (var field in fields)
                        {
                            field.SourceType = "node";
                            field.SourceId = sourceNode.Module?.Id ?? "";
                            _config.AvailableFields.Add(field);
                            _referenceTracker.RegisterField(field);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 从外部文件加载字段
        /// </summary>
        private void LoadFieldsFromExternalFile()
        {
            if (string.IsNullOrEmpty(_config.ExternalFilePath))
                return;

            try
            {
                // TODO: 实现从外部文件读取字段信息
                // var fields = FileFieldReader.ReadFields(_config.ExternalFilePath);
                // foreach (var field in fields)
                // {
                //     field.SourceType = "file";
                //     field.SourceId = _config.ExternalFilePath;
                //     _config.AvailableFields.Add(field);
                //     _referenceTracker.RegisterField(field);
                // }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从外部文件加载字段失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载已选字段
        /// </summary>
        private void LoadSelectedFields()
        {
            // TODO: 从节点属性中加载已选字段
        }

        /// <summary>
        /// 加载函数表达式
        /// </summary>
        private void LoadFunctions()
        {
            _isLoadingFunctions = true;
            try
            {
                _config.Functions.Clear();

                if (CurrentNode?.Module?.Parameters == null)
                {
                    AddDefaultFunction();
                    return;
                }

                try
                {
                    // 从 Module.Parameters 加载函数
                    if (CurrentNode.Module.Parameters.TryGetValue("function_count", out var countObj) &&
                        int.TryParse(countObj.ToString(), out var count) && count > 0)
                    {
                        for (int i = 0; i < count; i++)
                        {
                            var function = new FunctionExpression();

                            if (CurrentNode.Module.Parameters.TryGetValue($"function_{i}_id", out var id))
                                function.Id = id.ToString() ?? Guid.NewGuid().ToString();

                            if (CurrentNode.Module.Parameters.TryGetValue($"function_{i}_name", out var name))
                                function.Name = name.ToString() ?? $"函数{i + 1}";

                            if (CurrentNode.Module.Parameters.TryGetValue($"function_{i}_expression", out var expression))
                                function.Expression = expression.ToString() ?? "";

                            if (CurrentNode.Module.Parameters.TryGetValue($"function_{i}_type", out var type) &&
                                Enum.TryParse<FunctionType>(type.ToString(), out var functionType))
                                function.Type = functionType;

                            if (CurrentNode.Module.Parameters.TryGetValue($"function_{i}_enabled", out var enabled) &&
                                bool.TryParse(enabled.ToString(), out var isEnabled))
                                function.IsEnabled = isEnabled;

                            _config.Functions.Add(function);
                        }
                    }

                    // 如果没有加载到任何函数，添加默认函数
                    if (!_config.Functions.Any())
                    {
                        AddDefaultFunction();
                    }
                    else
                    {
                        // 同步到 ExpandedContent
                        UpdateExpandedContent();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error loading functions: {ex.Message}");
                    AddDefaultFunction();
                }
            }
            finally
            {
                _isLoadingFunctions = false;
            }
        }

        /// <summary>
        /// 添加默认函数
        /// </summary>
        private void AddDefaultFunction()
        {
            var defaultFunction = new FunctionExpression
            {
                Name = "函数1",
                Expression = "",
                Type = FunctionType.Math,
                IsEnabled = true
            };

            _config.Functions.Add(defaultFunction);
        }

        /// <summary>
        /// 清空配置
        /// </summary>
        private void ClearConfig()
        {
            _config.AvailableFields.Clear();
            _config.SelectedFields.Clear();
            _config.Functions.Clear();
            _referenceTracker.Clear();
        }

        /// <summary>
        /// 获取源节点
        /// </summary>
        private ModuleNodeViewModel? GetSourceNode(ConnectorViewModel input)
        {
            // TODO: 实现获取连接的源节点逻辑
            return null;
        }

        /// <summary>
        /// 获取节点输出字段
        /// </summary>
        private List<FieldInfo> GetNodeOutputFields(ModuleNodeViewModel node)
        {
            // TODO: 实现获取节点输出字段逻辑
            return new List<FieldInfo>();
        }

        /// <summary>
        /// 数据来源模式变化处理
        /// </summary>
        private void OnDataSourceModeChanged(DataSourceMode newMode)
        {
            if (_config.DataSourceMode != newMode)
            {
                _config.DataSourceMode = newMode;
                LoadAvailableFields();
                OnPropertyValueChanged("dataSourceMode", newMode);
            }
        }

        /// <summary>
        /// 字段搜索文本变化处理
        /// </summary>
        private void OnFieldSearchTextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = FieldSearchBox.Text?.Trim().ToLower();
            // TODO: 实现字段搜索过滤逻辑
        }

        /// <summary>
        /// 节点列表双击处理（如果需要的话）
        /// </summary>
        private void OnNodeListDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            // TODO: 实现节点双击处理逻辑
            // 可以直接连接到双击的节点
        }

        /// <summary>
        /// 添加已选字段
        /// </summary>
        private void AddSelectedField(FieldInfo field)
        {
            if (!_config.SelectedFields.Contains(field))
            {
                _config.SelectedFields.Add(field);
                OnPropertyValueChanged("selectedFields", _config.SelectedFields.ToList());
            }
        }

        /// <summary>
        /// 清空已选字段处理
        /// </summary>
        private void OnClearSelectedFieldsClick(object sender, RoutedEventArgs e)
        {
            _config.SelectedFields.Clear();
            OnPropertyValueChanged("selectedFields", _config.SelectedFields.ToList());
        }

        /// <summary>
        /// 添加函数处理
        /// </summary>
        private void OnAddFunctionClick(object sender, RoutedEventArgs e)
        {
            var newFunction = new FunctionExpression
            {
                Name = $"函数{_config.Functions.Count + 1}",
                Expression = "",
                Type = FunctionType.Math,
                IsEnabled = true
            };

            _config.Functions.Add(newFunction);
            OnPropertyValueChanged("functions", _config.Functions.ToList());
        }

        // 删除旧的占位导入处理逻辑（由 ImportTemplateButton_Click 实现）

        /// <summary>
        /// 执行所有函数处理
        /// </summary>
        private void OnExecuteAllClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现函数执行逻辑
        }

        /// <summary>
        /// 导出结果处理
        /// </summary>
        private void OnExportResultsClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现结果导出逻辑
        }

        /// <summary>
        /// 函数集合变化处理
        /// </summary>
        private void OnFunctionsCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // 更新表达式引用追踪
            if (e.NewItems != null)
            {
                foreach (FunctionExpression function in e.NewItems)
                {
                    function.PropertyChanged += OnFunctionPropertyChanged;
                }
            }

            if (e.OldItems != null)
            {
                foreach (FunctionExpression function in e.OldItems)
                {
                    function.PropertyChanged -= OnFunctionPropertyChanged;
                    _referenceTracker.RemoveExpressionReferences(function.Id);
                }
            }

            // 只在非加载状态时同步函数变化到节点数据
            if (!_isLoadingFunctions)
            {
                SyncFunctionsToNode();
            }
        }



        /// <summary>
        /// 函数属性变化处理
        /// </summary>
        private void OnFunctionPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is FunctionExpression function)
            {
                if (e.PropertyName == nameof(FunctionExpression.Expression))
                {
                    _referenceTracker.UpdateExpressionReferences(function.Id, function.Expression);
                }

                // 同步到节点
                SyncFunctionsToNode();
            }
        }

        /// <summary>
        /// 字段引用变化处理
        /// </summary>
        private void OnFieldReferenceChanged(object? sender, FieldReferenceChangedEventArgs e)
        {
            // 更新UI中字段的引用状态显示
            var field = _config.AvailableFields.FirstOrDefault(f => f.Name == e.FieldId);
            if (field != null)
            {
                field.IsReferenced = e.IsReferenced;
            }
        }

        /// <summary>
        /// 连接状态变化处理
        /// </summary>
        private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
        {
            // 更新UI中字段的连接状态显示
            var field = _config.AvailableFields.FirstOrDefault(f => f.Name == e.FieldId);
            if (field != null)
            {
                field.IsConnected = e.IsConnected;
            }

            // 注册或注销连接线
            if (e.IsConnected && e.Connection != null)
            {
                _connectionStyleService.RegisterConnection(e.Connection, e.FieldId);
            }
            else
            {
                // 注销连接线（需要连接ID）
                if (e.Connection != null)
                {
                    _connectionStyleService.UnregisterConnection(e.Connection.Id, e.FieldId);
                }
            }
        }

        /// <summary>
        /// 连接线样式变化处理
        /// </summary>
        private void OnConnectionStyleChanged(object? sender, ConnectionStyleChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine(
                $"连接线样式已更新: 字段 {e.FieldId}, 引用状态 {e.IsReferenced}");
        }

        /// <summary>
        /// 触发属性值变化事件
        /// </summary>
        private void OnPropertyValueChanged(string propertyName, object? value)
        {
            PropertyValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(propertyName, null, value));
        }

        /// <summary>
        /// 同步函数到节点
        /// </summary>
        private void SyncFunctionsToNode()
        {
            if (CurrentNode?.Module == null) return;

            try
            {
                // 1. 更新 Module.Parameters（用于数据持久化）
                CurrentNode.Module.Parameters["function_count"] = _config.Functions.Count.ToString();

                for (int i = 0; i < _config.Functions.Count; i++)
                {
                    var function = _config.Functions[i];
                    CurrentNode.Module.Parameters[$"function_{i}_id"] = function.Id;
                    CurrentNode.Module.Parameters[$"function_{i}_name"] = function.Name;
                    CurrentNode.Module.Parameters[$"function_{i}_expression"] = function.Expression;
                    CurrentNode.Module.Parameters[$"function_{i}_type"] = function.Type.ToString();
                    CurrentNode.Module.Parameters[$"function_{i}_enabled"] = function.IsEnabled.ToString();
                }

                // 清理多余的参数
                var keysToRemove = CurrentNode.Module.Parameters.Keys
                    .Where(k => k.StartsWith("function_") &&
                               int.TryParse(k.Split('_')[1], out int index) &&
                               index >= _config.Functions.Count)
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    CurrentNode.Module.Parameters.Remove(key);
                }

                // 2. 更新 ExpandedContent（用于节点显示）
                UpdateExpandedContent();

                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Synced {_config.Functions.Count} functions to node");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error syncing functions: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新展开内容
        /// </summary>
        private void UpdateExpandedContent()
        {
            if (CurrentNode == null) return;

            // 清空现有的函数相关内容
            var functionsToRemove = CurrentNode.ExpandedContent
                .OfType<FunctionDisplayItem>()
                .ToList();

            foreach (var item in functionsToRemove)
            {
                CurrentNode.ExpandedContent.Remove(item);
            }

            // 添加新的函数显示项
            foreach (var function in _config.Functions)
            {
                CurrentNode.ExpandedContent.Add(new FunctionDisplayItem
                {
                    Name = function.Name,
                    Expression = function.Expression,
                    IsEnabled = function.IsEnabled
                });
            }
        }



        /// <summary>
        /// 删除函数按钮点击事件
        /// </summary>
        private void DeleteFunctionButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is FunctionExpression function)
            {
                _config.Functions.Remove(function);
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Removed function: {function.Name}");
            }
        }

        /// <summary>
        /// 连接节点按钮点击事件
        /// </summary>
        private void ConnectNodeButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is SourceNodeInfo sourceNode)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Connect button clicked for {sourceNode.NodeTitle}, IsConnected: {sourceNode.IsConnected}");
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Button Content: {button.Content}, Background: {button.Background}");

                if (sourceNode.IsConnected)
                {
                    // 断开连接
                    DisconnectFromNode(sourceNode);
                }
                else
                {
                    // 建立连接
                    ConnectToNode(sourceNode);
                }

                // 重新加载源节点状态以更新UI
                LoadAvailableSourceNodes();

                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] After operation - {sourceNode.NodeTitle} IsConnected: {sourceNode.IsConnected}");

                // 测试数据绑定：强制触发 PropertyChanged 事件
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Testing data binding - forcing PropertyChanged");
                sourceNode.NodeTitle = sourceNode.NodeTitle; // 这会触发 PropertyChanged
            }
        }

        /// <summary>
        /// 连接到节点
        /// </summary>
        private void ConnectToNode(SourceNodeInfo sourceNode)
        {
            try
            {
                if (CurrentNode == null) return;

                // 查找源节点
                var sourceNodeViewModel = FindNodeById(sourceNode.NodeId);
                if (sourceNodeViewModel == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Source node not found: {sourceNode.NodeId}");
                    return;
                }

                // 创建连接
                var connection = CreateConnection(sourceNodeViewModel, CurrentNode);
                if (connection != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Connected to node: {sourceNode.NodeTitle}");

                    // 更新数据来源
                    UpdateDataSources();

                    // 重新加载可用源节点以更新连接状态
                    LoadAvailableSourceNodes();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error connecting to node: {ex.Message}");
            }
        }

        /// <summary>
        /// 断开与节点的连接
        /// </summary>
        private void DisconnectFromNode(SourceNodeInfo sourceNode)
        {
            try
            {
                if (CurrentNode == null) return;

                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Attempting to disconnect from node: {sourceNode.NodeTitle}");

                // 查找并移除连接
                var connectionsToRemove = FindConnectionsToNode(sourceNode.NodeId);
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Found {connectionsToRemove.Count} connections to remove");

                foreach (var connection in connectionsToRemove)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Removing connection: {connection.Source?.Title} -> {connection.Target?.Title}");
                    RemoveConnection(connection);
                }

                sourceNode.IsConnected = false;
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Disconnected from node: {sourceNode.NodeTitle}");

                // 更新数据来源
                UpdateDataSources();

                // 重新加载可用源节点以更新连接状态
                LoadAvailableSourceNodes();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error disconnecting from node: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据ID查找节点
        /// </summary>
        private ModuleNodeViewModel? FindNodeById(string nodeId)
        {
            if (CanvasViewModel?.Nodes == null || string.IsNullOrEmpty(nodeId))
                return null;

            return CanvasViewModel.Nodes.FirstOrDefault(n => n.Module?.Id == nodeId);
        }

        /// <summary>
        /// 创建连接
        /// </summary>
        private ConnectionViewModel? CreateConnection(ModuleNodeViewModel sourceNode, ModuleNodeViewModel targetNode)
        {
            if (CanvasViewModel == null || sourceNode.Outputs?.Count == 0 || targetNode.Inputs?.Count == 0)
                return null;

            try
            {
                // 找到第一个可用的输出端口
                var outputConnector = sourceNode.Outputs.FirstOrDefault();
                // 找到第一个可用的输入端口
                var inputConnector = targetNode.Inputs.FirstOrDefault();

                if (outputConnector == null || inputConnector == null)
                    return null;

                // 创建连接
                var connection = new ConnectionViewModel
                {
                    Source = outputConnector,
                    Target = inputConnector
                };

                // 添加到画布
                CanvasViewModel.Connections.Add(connection);

                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Created connection from {sourceNode.Title} to {targetNode.Title}");
                return connection;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error creating connection: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查找到指定节点的连接
        /// </summary>
        private List<ConnectionViewModel> FindConnectionsToNode(string sourceNodeId)
        {
            var connections = new List<ConnectionViewModel>();

            if (CanvasViewModel?.Connections == null || CurrentNode == null || string.IsNullOrEmpty(sourceNodeId))
                return connections;

            foreach (var connection in CanvasViewModel.Connections)
            {
                // 检查是否是从指定源节点到当前节点的连接
                if (connection.Source?.Node is ModuleNodeViewModel sourceNode &&
                    sourceNode.Module?.Id == sourceNodeId &&
                    connection.Target?.Node == CurrentNode)
                {
                    connections.Add(connection);
                }
            }

            return connections;
        }

        /// <summary>
        /// 移除连接
        /// </summary>
        private void RemoveConnection(ConnectionViewModel connection)
        {
            if (CanvasViewModel != null && connection != null)
            {
                // 使用 CanvasViewModel 的 RemoveConnection 方法，它会正确更新连接器的连接数
                CanvasViewModel.RemoveConnection(connection);
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Removed connection using CanvasViewModel.RemoveConnection");
            }
        }

        /// <summary>
        /// 更新数据来源
        /// </summary>
        private void UpdateDataSources()
        {
            // TODO: 实现更新数据来源的逻辑
            // 重新加载可用字段等
        }

        /// <summary>
        /// 加载可用的源节点
        /// </summary>
        private void LoadAvailableSourceNodes()
        {
            if (CurrentNode == null) return;

            try
            {
                // 获取画布上所有可以连接到数据计算节点的节点
                var availableNodes = GetAvailableSourceNodesFromCanvas();

                // 创建一个字典来快速查找现有的 SourceNodeInfo
                var existingNodes = _config.AvailableSourceNodes.ToDictionary(n => n.NodeId, n => n);
                var nodesToKeep = new List<SourceNodeInfo>();

                foreach (var node in availableNodes)
                {
                    var nodeId = node.Module?.Id ?? string.Empty;
                    SourceNodeInfo sourceNodeInfo;

                    // 如果已存在相同ID的节点信息，更新它而不是创建新的
                    if (existingNodes.TryGetValue(nodeId, out var existingNode))
                    {
                        sourceNodeInfo = existingNode;
                        // 更新属性
                        sourceNodeInfo.NodeTitle = GetNodeDisplayTitle(node);
                        sourceNodeInfo.NodeType = GetNodeTypeDisplayName(node.Module?.Type);
                        sourceNodeInfo.OutputDataType = GetNodeOutputDataType(node);
                        sourceNodeInfo.OutputCount = node.Outputs?.Count ?? 0;
                        sourceNodeInfo.IsAvailable = CanConnectToNode(node);
                    }
                    else
                    {
                        // 创建新的节点信息
                        sourceNodeInfo = new SourceNodeInfo
                        {
                            NodeId = nodeId,
                            NodeTitle = GetNodeDisplayTitle(node),
                            NodeType = GetNodeTypeDisplayName(node.Module?.Type),
                            OutputDataType = GetNodeOutputDataType(node),
                            OutputCount = node.Outputs?.Count ?? 0,
                            IsAvailable = CanConnectToNode(node)
                        };
                    }

                    // 更新连接状态
                    var isConnected = IsNodeConnectedToCurrentNode(node);
                    sourceNodeInfo.IsConnected = isConnected;
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Node {sourceNodeInfo.NodeTitle} IsConnected: {isConnected}");

                    nodesToKeep.Add(sourceNodeInfo);
                }

                // 移除不再存在的节点
                var nodesToRemove = _config.AvailableSourceNodes.Where(n => !nodesToKeep.Contains(n)).ToList();
                foreach (var nodeToRemove in nodesToRemove)
                {
                    _config.AvailableSourceNodes.Remove(nodeToRemove);
                }

                // 添加新节点
                foreach (var nodeToAdd in nodesToKeep.Where(n => !_config.AvailableSourceNodes.Contains(n)))
                {
                    _config.AvailableSourceNodes.Add(nodeToAdd);
                }

                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Loaded {_config.AvailableSourceNodes.Count} available source nodes");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Error loading source nodes: {ex.Message}");
            }
        }

        /// <summary>
        /// 从画布获取可用的源节点
        /// </summary>
        private List<ModuleNodeViewModel> GetAvailableSourceNodesFromCanvas()
        {
            var availableNodes = new List<ModuleNodeViewModel>();

            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] GetAvailableSourceNodesFromCanvas - CanvasViewModel: {CanvasViewModel != null}");
            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] GetAvailableSourceNodesFromCanvas - Nodes count: {CanvasViewModel?.Nodes?.Count ?? 0}");
            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] GetAvailableSourceNodesFromCanvas - CurrentNode: {CurrentNode != null}");

            if (CanvasViewModel?.Nodes == null || CurrentNode == null)
                return availableNodes;

            foreach (var node in CanvasViewModel.Nodes)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Checking node: {node.Title}, Type: {node.Module?.Type}, Outputs: {node.Outputs?.Count ?? 0}");

                // 排除当前节点自身
                if (node == CurrentNode)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Skipping current node: {node.Title}");
                    continue;
                }

                // 只包含有输出端口的节点
                if (node.Outputs?.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Node has outputs: {node.Title}");

                    // 过滤出适合作为数据来源的节点类型
                    if (IsValidSourceNodeType(node.Module?.Type))
                    {
                        System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Valid source node: {node.Title}");
                        availableNodes.Add(node);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Invalid source node type: {node.Module?.Type}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Node has no outputs: {node.Title}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] Found {availableNodes.Count} available source nodes");
            return availableNodes;
        }

        /// <summary>
        /// 检查是否是有效的源节点类型
        /// </summary>
        private bool IsValidSourceNodeType(ModuleType? moduleType)
        {
            return moduleType switch
            {
                // 输入类：只有输出，作为数据源
                ModuleType.FileInput => true,
                ModuleType.DatabaseInput => true,
                ModuleType.APIInput => true,
                ModuleType.CADInput => true,
                ModuleType.ExcelInput => true,
                ModuleType.CSVInput => true,
                ModuleType.XMLInput => true,
                ModuleType.JSONInput => true,
                ModuleType.ManualDataInput => true,

                // 常规数据类：只有输出，作为数据源
                ModuleType.PipeLine => true,
                ModuleType.RiserPipe => true,
                ModuleType.PressureBox => true,
                ModuleType.Excavation => true,
                ModuleType.Demolition => true,
                ModuleType.AntiCorrosion => true,
                ModuleType.LightningProtection => true,
                ModuleType.WarningBand => true,
                ModuleType.WeldInspection => true,
                ModuleType.InstallationTeam => true,
                ModuleType.Measures => true,

                // 触发器类：有输出
                ModuleType.ClickTrigger => true,
                ModuleType.AssociationTrigger => true,
                ModuleType.TimedTrigger => true,
                ModuleType.FileChangeTrigger => true,
                ModuleType.EnvironmentTrigger => true,

                // 控制类：有输入有输出
                ModuleType.ConditionalBranch => true,
                ModuleType.LoopProcessor => true,
                ModuleType.ErrorHandler => true,
                ModuleType.FlowControl => true,
                ModuleType.ScriptExecutor => true,
                ModuleType.VariableManager => true,
                ModuleType.StateManager => true,
                ModuleType.AIAgent => true,

                // 函数类（数字逻辑类）：有输入有输出
                ModuleType.DataFilter => true,
                ModuleType.TagSearch => true,
                ModuleType.DataCalculation => true,
                ModuleType.DataValidation => true,
                ModuleType.DataTransform => true,
                ModuleType.DataCondition => true,
                ModuleType.ArrayExpansion => true,
                ModuleType.Other => true,

                // 整理类：有输入有输出
                ModuleType.TableManager => true,
                ModuleType.GraphicsAPI => true,
                ModuleType.ExcelCSV => true,
                ModuleType.WordProcessor => true,

                // 输出类：大部分既有输入也有输出（支持链式操作）
                ModuleType.FileGeneration => true,
                ModuleType.ManualLocation => true,
                ModuleType.SpecifiedPath => true,
                ModuleType.ThirdPartyAPI => true,
                ModuleType.CADExport => true,
                ModuleType.ExcelExport => true,
                ModuleType.CSVExport => true,
                ModuleType.WordExport => true,
                ModuleType.PPTExport => true,
                ModuleType.ImageExport => true,
                ModuleType.PublishRelease => true,
                ModuleType.NotificationAlert => true,
                ModuleType.OtherOutput => true,

                // 纯终端操作：只有输入，没有输出（不能作为数据源）
                ModuleType.DialogChat => false,

                _ => false
            };
        }

        /// <summary>
        /// 获取节点显示标题（优先使用用户重命名的标题）
        /// </summary>
        private string GetNodeDisplayTitle(ModuleNodeViewModel node)
        {
            // 优先使用用户重命名的Module.Name，如果为空则使用Title，最后使用默认值
            if (!string.IsNullOrEmpty(node.Module?.Name))
            {
                return node.Module.Name;
            }

            if (!string.IsNullOrEmpty(node.Title))
            {
                return node.Title;
            }

            return "未命名节点";
        }

        /// <summary>
        /// 获取节点类型显示名称
        /// </summary>
        private string GetNodeTypeDisplayName(ModuleType? moduleType)
        {
            return moduleType switch
            {
                ModuleType.FileInput => "文件输入",
                ModuleType.DatabaseInput => "数据库输入",
                ModuleType.APIInput => "API输入",
                ModuleType.DataFilter => "数据过滤",
                ModuleType.DataTransform => "数据转换",
                ModuleType.DataCalculation => "数据计算",
                ModuleType.FileGeneration => "文件生成",
                ModuleType.DatabaseOutput => "数据库输出",
                ModuleType.ManualDataInput => "手动输入",
                ModuleType.ArrayExpansion => "数组展开",
                ModuleType.AIAgent => "AI代理",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取节点输出数据类型
        /// </summary>
        private string GetNodeOutputDataType(ModuleNodeViewModel node)
        {
            // TODO: 实现获取节点输出数据类型的逻辑
            return "Mixed";
        }

        /// <summary>
        /// 检查节点是否已连接到当前节点
        /// </summary>
        private bool IsNodeConnectedToCurrentNode(ModuleNodeViewModel sourceNode)
        {
            if (CanvasViewModel?.Connections == null || CurrentNode == null || sourceNode?.Module?.Id == null)
            {
                System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] IsNodeConnectedToCurrentNode - null check failed");
                return false;
            }

            var isConnected = CanvasViewModel.Connections.Any(c =>
                c.Source?.Node == sourceNode && c.Target?.Node == CurrentNode);

            System.Diagnostics.Debug.WriteLine($"[FunctionPropertyPanel] IsNodeConnectedToCurrentNode - {sourceNode.Title}: {isConnected}");
            return isConnected;
        }

        /// <summary>
        /// 检查是否可以连接到节点
        /// </summary>
        private bool CanConnectToNode(ModuleNodeViewModel sourceNode)
        {
            if (sourceNode?.Outputs?.Count == 0 || CurrentNode?.Inputs?.Count == 0)
                return false;

            // 检查是否已经连接
            if (IsNodeConnectedToCurrentNode(sourceNode))
                return true; // 已连接的节点也显示，但状态不同

            // 检查是否有可用的连接端口
            return sourceNode.Outputs.Any() && CurrentNode.Inputs.Any();
        }

        /// <summary>
        /// 使用公式模板按钮点击事件
        /// </summary>
        private void ImportTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建公式模板选择器
                var templateSelector = new FormulaTemplateSelector();

                // 创建弹出窗口
                var window = new Window
                {
                    Title = "选择公式模板",
                    Content = templateSelector,
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.CanResize
                };

                // 处理公式选择事件
                templateSelector.FormulaSelected += (s, template) =>
                {
                    ApplyFormulaTemplate(template);
                    window.Close();
                };

                // 处理取消选择事件
                templateSelector.SelectionCancelled += (s, args) =>
                {
                    window.Close();
                };

                // 显示窗口
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开公式模板选择器失败: {ex.Message}");
                MessageBox.Show($"打开公式模板选择器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 应用公式模板到当前节点
        /// </summary>
        private void ApplyFormulaTemplate(FormulaTemplate template)
        {
            try
            {
                if (template == null || CurrentNode == null) return;

                // 创建新的函数表达式
                var functionExpression = new FunctionExpression
                {
                    Name = template.Name,
                    Expression = template.Expression,
                    Type = FunctionType.Math,
                    IsEnabled = true
                };

                // 添加到函数列表
                _config.Functions.Add(functionExpression);

                // 刷新UI
                LoadFunctions();

                // 同步到节点
                SyncFunctionsToNode();

                MessageBox.Show($"已成功应用公式模板：{template.Name}", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用公式模板失败: {ex.Message}");
                MessageBox.Show($"应用公式模板失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
