using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;

namespace ProjectDigitizer.Infrastructure.Plugins;

/// <summary>
/// 简单的插件程序集加载器：从指定目录加载所有 .dll。
/// 使用默认上下文加载，避免 WPF 资源找不到；必要时可切换独立 ALC。
/// </summary>
public static class PluginLoader
{
    /// <summary>
    /// 扫描并加载插件程序集。
    /// </summary>
    /// <param name="pluginDirectory">插件目录（相对或绝对路径）</param>
    /// <param name="pattern">文件匹配模式，默认 *.dll</param>
    /// <returns>已加载的程序集列表</returns>
    public static IReadOnlyList<Assembly> LoadAssemblies(string pluginDirectory, string pattern = "*.dll")
    {
        List<Assembly> result = [];

        try
        {
            // 优先以应用程序基目录解析（兼容 dotnet run 与直接双击 exe 的不同工作目录）
            string ResolvePluginsPath()
            {
                try
                {
                    string baseDir = AppContext.BaseDirectory?.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar) ?? Directory.GetCurrentDirectory();
                    string candidate = Path.IsPathRooted(pluginDirectory)
                        ? pluginDirectory
                        : Path.GetFullPath(Path.Combine(baseDir, pluginDirectory));
                    if (Directory.Exists(candidate))
                    {
                        return candidate;
                    }

                    // 回退：按当前工作目录解析
                    string fallback = Path.GetFullPath(pluginDirectory);
                    if (Directory.Exists(fallback))
                    {
                        return fallback;
                    }
                }
                catch
                {
                    // 忽略路径解析异常，最终返回空
                }
                return string.Empty;
            }

            string fullPath = ResolvePluginsPath();
            if (string.IsNullOrEmpty(fullPath) || !Directory.Exists(fullPath))
            {
                return result;
            }

            string[] files = Directory.EnumerateFiles(fullPath, pattern, SearchOption.TopDirectoryOnly)
                // 排除主应用与已知项目自身的 dll
                .Where(p => !Path.GetFileName(p).StartsWith("ProjectDigitizer.", StringComparison.OrdinalIgnoreCase))
                .ToArray();

            foreach (string? file in files)
            {
                try
                {
                    // 默认上下文加载，保证 WPF 资源可解析
                    Assembly asm = AssemblyLoadContext.Default.LoadFromAssemblyPath(file);
                    result.Add(asm);
                }
                catch
                {
                    // 单个加载失败不影响其他插件
                }
            }
        }
        catch
        {
            // 忽略扫描失败
        }

        return result;
    }
}

