using AutoFixture;
using AutoFixture.AutoMoq;

namespace ProjectDigitizer.Core.Tests.Builders;

/// <summary>
/// 测试数据构建器基类
/// 提供通用的测试数据生成功能
/// </summary>
public abstract class TestDataBuilder<T> where T : class
{
    protected readonly IFixture _fixture;

    protected TestDataBuilder()
    {
        _fixture = new Fixture();
        _fixture.Customize(new AutoMoqCustomization());

        // 配置默认行为
        ConfigureFixture(_fixture);
    }

    /// <summary>
    /// 配置Fixture的默认行为
    /// </summary>
    /// <param name="fixture">Fixture实例</param>
    protected virtual void ConfigureFixture(IFixture fixture)
    {
        // 子类可以重写此方法来配置特定的Fixture行为
    }

    /// <summary>
    /// 构建对象实例
    /// </summary>
    /// <returns>构建的对象实例</returns>
    public abstract T Build();

    /// <summary>
    /// 构建多个对象实例
    /// </summary>
    /// <param name="count">实例数量</param>
    /// <returns>对象实例列表</returns>
    public virtual List<T> BuildMany(int count = 3)
    {
        var items = new List<T>();
        for (int i = 0; i < count; i++)
        {
            items.Add(Build());
        }
        return items;
    }

    /// <summary>
    /// 创建随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <returns>随机字符串</returns>
    protected string CreateRandomString(int length = 10)
    {
        return _fixture.Create<string>().Substring(0, Math.Min(length, 10));
    }

    /// <summary>
    /// 创建随机整数
    /// </summary>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>随机整数</returns>
    protected int CreateRandomInt(int min = 1, int max = 100)
    {
        return _fixture.Create<Random>().Next(min, max);
    }

    /// <summary>
    /// 创建随机布尔值
    /// </summary>
    /// <returns>随机布尔值</returns>
    protected bool CreateRandomBool()
    {
        return _fixture.Create<bool>();
    }

    /// <summary>
    /// 创建随机日期时间
    /// </summary>
    /// <returns>随机日期时间</returns>
    protected DateTime CreateRandomDateTime()
    {
        return _fixture.Create<DateTime>();
    }

    /// <summary>
    /// 创建随机GUID
    /// </summary>
    /// <returns>随机GUID</returns>
    protected Guid CreateRandomGuid()
    {
        return _fixture.Create<Guid>();
    }
}
