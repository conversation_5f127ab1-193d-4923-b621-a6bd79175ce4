{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7}}]}, "DataDirectory": "Data", "ExternalServices": {"BaseUrl": "https://api.example.com", "TimeoutSeconds": 30}, "Plugins": {"Directory": "Plugins"}, "Application": {"Name": "ProjectDigitizer Studio", "Version": "1.0.0"}}