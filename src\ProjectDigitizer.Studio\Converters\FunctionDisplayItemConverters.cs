using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

using ProjectDigitizer.Core.Entities;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 检查集合中是否包含 FunctionDisplayItem 的转换器
    /// </summary>
    public class HasFunctionDisplayItemsConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is IEnumerable collection)
            {
                return collection.OfType<FunctionDisplayItem>().Any();
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 计算集合中 FunctionDisplayItem 数量的转换器
    /// </summary>
    public class FunctionDisplayItemCountConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is IEnumerable collection)
            {
                return collection.OfType<FunctionDisplayItem>().Count();
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
