using System.ComponentModel.DataAnnotations;

namespace ProjectDigitizer.Core.Configuration
{
    /// <summary>
    /// UI配置类
    /// </summary>
    public class UISettings
    {
        /// <summary>
        /// 主题设置
        /// </summary>
        public ThemeSettings Theme { get; set; } = new();

        /// <summary>
        /// 窗口设置
        /// </summary>
        public WindowSettings Window { get; set; } = new();

        /// <summary>
        /// 画布设置
        /// </summary>
        public CanvasSettings Canvas { get; set; } = new();

        /// <summary>
        /// 面板设置
        /// </summary>
        public PanelSettings Panels { get; set; } = new();

        /// <summary>
        /// 语言设置
        /// </summary>
        public LanguageSettings Language { get; set; } = new();

        /// <summary>
        /// 性能设置
        /// </summary>
        public UIPerformanceSettings Performance { get; set; } = new();
    }

    /// <summary>
    /// 主题设置
    /// </summary>
    public class ThemeSettings
    {
        /// <summary>
        /// 当前主题名称
        /// </summary>
        [Required]
        public string CurrentTheme { get; set; } = "Light";

        /// <summary>
        /// 是否跟随系统主题
        /// </summary>
        public bool FollowSystemTheme { get; set; } = false;

        /// <summary>
        /// 自定义主题路径
        /// </summary>
        public string CustomThemePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// 窗口设置
    /// </summary>
    public class WindowSettings
    {
        /// <summary>
        /// 默认窗口宽度
        /// </summary>
        [Range(800, 4000)]
        public double DefaultWidth { get; set; } = 1024;

        /// <summary>
        /// 默认窗口高度
        /// </summary>
        [Range(600, 3000)]
        public double DefaultHeight { get; set; } = 768;

        /// <summary>
        /// 最小窗口宽度
        /// </summary>
        [Range(400, 2000)]
        public double MinimumWidth { get; set; } = 800;

        /// <summary>
        /// 最小窗口高度
        /// </summary>
        [Range(300, 1500)]
        public double MinimumHeight { get; set; } = 600;

        /// <summary>
        /// 是否记住窗口位置
        /// </summary>
        public bool RememberWindowPosition { get; set; } = true;

        /// <summary>
        /// 是否记住窗口大小
        /// </summary>
        public bool RememberWindowSize { get; set; } = true;

        /// <summary>
        /// 启动时是否最大化
        /// </summary>
        public bool StartMaximized { get; set; } = false;
    }

    /// <summary>
    /// 画布设置
    /// </summary>
    public class CanvasSettings
    {
        /// <summary>
        /// 默认缩放级别
        /// </summary>
        [Range(0.1, 5.0)]
        public double DefaultZoom { get; set; } = 1.0;

        /// <summary>
        /// 最小缩放级别
        /// </summary>
        [Range(0.01, 1.0)]
        public double MinimumZoom { get; set; } = 0.1;

        /// <summary>
        /// 最大缩放级别
        /// </summary>
        [Range(1.0, 10.0)]
        public double MaximumZoom { get; set; } = 5.0;

        /// <summary>
        /// 缩放步长
        /// </summary>
        [Range(0.01, 1.0)]
        public double ZoomStep { get; set; } = 0.1;

        /// <summary>
        /// 是否显示网格
        /// </summary>
        public bool ShowGrid { get; set; } = true;

        /// <summary>
        /// 网格大小
        /// </summary>
        [Range(5, 100)]
        public double GridSize { get; set; } = 20;

        /// <summary>
        /// 是否启用吸附
        /// </summary>
        public bool EnableSnapping { get; set; } = true;

        /// <summary>
        /// 吸附距离
        /// </summary>
        [Range(1, 50)]
        public double SnapDistance { get; set; } = 10;
    }

    /// <summary>
    /// 面板设置
    /// </summary>
    public class PanelSettings
    {
        /// <summary>
        /// 属性面板默认宽度
        /// </summary>
        [Range(200, 800)]
        public double PropertyPanelWidth { get; set; } = 300;

        /// <summary>
        /// 工具栏默认高度
        /// </summary>
        [Range(50, 200)]
        public double ToolbarHeight { get; set; } = 80;

        /// <summary>
        /// 状态栏默认高度
        /// </summary>
        [Range(20, 100)]
        public double StatusBarHeight { get; set; } = 25;

        /// <summary>
        /// 是否显示属性面板
        /// </summary>
        public bool ShowPropertyPanel { get; set; } = true;

        /// <summary>
        /// 是否显示工具栏
        /// </summary>
        public bool ShowToolbar { get; set; } = true;

        /// <summary>
        /// 是否显示状态栏
        /// </summary>
        public bool ShowStatusBar { get; set; } = true;
    }

    /// <summary>
    /// 语言设置
    /// </summary>
    public class LanguageSettings
    {
        /// <summary>
        /// 当前语言代码
        /// </summary>
        [Required]
        public string CurrentLanguage { get; set; } = "zh-CN";

        /// <summary>
        /// 是否跟随系统语言
        /// </summary>
        public bool FollowSystemLanguage { get; set; } = true;

        /// <summary>
        /// 支持的语言列表
        /// </summary>
        public List<string> SupportedLanguages { get; set; } = new() { "zh-CN", "en-US" };
    }

    /// <summary>
    /// UI性能设置
    /// </summary>
    public class UIPerformanceSettings
    {
        /// <summary>
        /// 是否启用硬件加速
        /// </summary>
        public bool EnableHardwareAcceleration { get; set; } = true;

        /// <summary>
        /// 渲染层级
        /// </summary>
        [Range(0, 2)]
        public int RenderTier { get; set; } = 2;

        /// <summary>
        /// 最大FPS
        /// </summary>
        [Range(30, 120)]
        public int MaxFrameRate { get; set; } = 60;

        /// <summary>
        /// 是否启用虚拟化
        /// </summary>
        public bool EnableVirtualization { get; set; } = true;
    }
}
