using Microsoft.Extensions.DependencyInjection;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 服务生命周期管理器接口
/// </summary>
public interface IServiceLifecycleManager
{
    /// <summary>
    /// 注册服务
    /// </summary>
    /// <typeparam name="TInterface">服务接口类型</typeparam>
    /// <typeparam name="TImplementation">服务实现类型</typeparam>
    /// <param name="lifetime">服务生命周期</param>
    void RegisterService<TInterface, TImplementation>(ServiceLifetime lifetime)
        where TImplementation : class, TInterface
        where TInterface : class;

    /// <summary>
    /// 注册服务实例
    /// </summary>
    /// <typeparam name="TInterface">服务接口类型</typeparam>
    /// <param name="implementationType">实现类型</param>
    /// <param name="lifetime">服务生命周期</param>
    void RegisterService<TInterface>(Type implementationType, ServiceLifetime lifetime)
        where TInterface : class;

    /// <summary>
    /// 验证服务注册
    /// </summary>
    /// <returns>验证结果</returns>
    ServiceRegistrationValidationResult ValidateServiceRegistrations();

    /// <summary>
    /// 获取已注册的服务信息
    /// </summary>
    /// <returns>服务注册信息数组</returns>
    ServiceRegistrationInfo[] GetRegisteredServices();

    /// <summary>
    /// 注册资源释放回调
    /// </summary>
    /// <param name="serviceType">服务类型</param>
    /// <param name="disposeCallback">释放回调</param>
    void RegisterDisposeCallback(Type serviceType, Action<object> disposeCallback);

    /// <summary>
    /// 释放指定类型的所有服务实例
    /// </summary>
    /// <param name="serviceType">服务类型</param>
    void DisposeServices(Type serviceType);

    /// <summary>
    /// 释放所有可释放的服务
    /// </summary>
    void DisposeAllServices();
}

/// <summary>
/// 服务注册信息
/// </summary>
public class ServiceRegistrationInfo
{
    /// <summary>
    /// 服务类型
    /// </summary>
    public Type ServiceType { get; set; } = null!;

    /// <summary>
    /// 实现类型
    /// </summary>
    public Type ImplementationType { get; set; } = null!;

    /// <summary>
    /// 服务生命周期
    /// </summary>
    public ServiceLifetime Lifetime { get; set; }

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisteredAt { get; set; }

    /// <summary>
    /// 是否有自定义释放逻辑
    /// </summary>
    public bool HasCustomDispose { get; set; }
}

/// <summary>
/// 服务注册验证结果
/// </summary>
public class ServiceRegistrationValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<ServiceRegistrationError> Errors { get; set; } = new();

    /// <summary>
    /// 验证警告列表
    /// </summary>
    public List<ServiceRegistrationWarning> Warnings { get; set; } = new();
}

/// <summary>
/// 服务注册错误
/// </summary>
public class ServiceRegistrationError
{
    /// <summary>
    /// 服务类型
    /// </summary>
    public Type ServiceType { get; set; } = null!;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误类型
    /// </summary>
    public ServiceRegistrationErrorType ErrorType { get; set; }
}

/// <summary>
/// 服务注册警告
/// </summary>
public class ServiceRegistrationWarning
{
    /// <summary>
    /// 服务类型
    /// </summary>
    public Type ServiceType { get; set; } = null!;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string WarningMessage { get; set; } = string.Empty;

    /// <summary>
    /// 警告类型
    /// </summary>
    public ServiceRegistrationWarningType WarningType { get; set; }
}

/// <summary>
/// 服务注册错误类型
/// </summary>
public enum ServiceRegistrationErrorType
{
    /// <summary>
    /// 循环依赖
    /// </summary>
    CircularDependency,

    /// <summary>
    /// 缺少依赖
    /// </summary>
    MissingDependency,

    /// <summary>
    /// 重复注册
    /// </summary>
    DuplicateRegistration,

    /// <summary>
    /// 生命周期不匹配
    /// </summary>
    LifetimeMismatch,

    /// <summary>
    /// 无效的服务类型
    /// </summary>
    InvalidServiceType
}

/// <summary>
/// 服务注册警告类型
/// </summary>
public enum ServiceRegistrationWarningType
{
    /// <summary>
    /// 生命周期可能不合适
    /// </summary>
    PotentialLifetimeIssue,

    /// <summary>
    /// 性能影响
    /// </summary>
    PerformanceImpact,

    /// <summary>
    /// 内存泄漏风险
    /// </summary>
    MemoryLeakRisk,

    /// <summary>
    /// 线程安全问题
    /// </summary>
    ThreadSafetyIssue
}
