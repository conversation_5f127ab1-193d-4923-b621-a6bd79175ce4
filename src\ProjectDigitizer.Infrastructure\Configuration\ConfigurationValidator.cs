using System.ComponentModel.DataAnnotations;
using System.IO;

using ProjectDigitizer.Core.Configuration;

namespace ProjectDigitizer.Infrastructure.Configuration
{
    /// <summary>
    /// 配置验证器实现
    /// </summary>
    public class ConfigurationValidator : IConfigurationValidator
    {
        /// <summary>
        /// 验证配置对象
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configuration">配置对象</param>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult Validate<T>(T configuration) where T : class
        {
            var result = new ConfigurationValidationResult { IsValid = true };

            if (configuration == null)
            {
                result.AddError("Configuration", "配置对象不能为空");
                return result;
            }

            var validationContext = new ValidationContext(configuration);
            var validationResults = new List<ValidationResult>();

            bool isValid = Validator.TryValidateObject(configuration, validationContext, validationResults, true);

            if (!isValid)
            {
                foreach (var validationResult in validationResults)
                {
                    var propertyName = validationResult.MemberNames.FirstOrDefault() ?? "Unknown";
                    result.AddError(propertyName, validationResult.ErrorMessage ?? "验证失败");
                }
            }

            // 执行自定义验证逻辑
            PerformCustomValidation(configuration, result);

            return result;
        }

        /// <summary>
        /// 验证应用程序设置
        /// </summary>
        /// <param name="settings">应用程序设置</param>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult ValidateApplicationSettings(ApplicationSettings settings)
        {
            var result = Validate(settings);

            if (settings == null)
                return result;

            // 验证数据目录
            ValidateDataDirectory(settings.DataDirectory, result);

            // 验证数据库设置
            ValidateDatabaseSettings(settings.Database, result);

            // 验证日志设置
            ValidateLoggingSettings(settings.Logging, result);

            // 验证外部服务设置
            ValidateExternalServicesSettings(settings.ExternalServices, result);

            // 验证UI设置
            ValidateUISettings(settings.UI, result);

            return result;
        }

        /// <summary>
        /// 执行自定义验证逻辑
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="configuration">配置对象</param>
        /// <param name="result">验证结果</param>
        private void PerformCustomValidation<T>(T configuration, ConfigurationValidationResult result) where T : class
        {
            switch (configuration)
            {
                case DatabaseSettings dbSettings:
                    ValidateDatabaseSettings(dbSettings, result);
                    break;
                case LoggingSettings logSettings:
                    ValidateLoggingSettings(logSettings, result);
                    break;
                case ExternalServicesSettings extSettings:
                    ValidateExternalServicesSettings(extSettings, result);
                    break;
                case UISettings uiSettings:
                    ValidateUISettings(uiSettings, result);
                    break;
            }
        }

        /// <summary>
        /// 验证数据目录
        /// </summary>
        /// <param name="dataDirectory">数据目录路径</param>
        /// <param name="result">验证结果</param>
        private void ValidateDataDirectory(string dataDirectory, ConfigurationValidationResult result)
        {
            if (string.IsNullOrWhiteSpace(dataDirectory))
            {
                result.AddError(nameof(ApplicationSettings.DataDirectory), "数据目录路径不能为空");
                return;
            }

            try
            {
                var fullPath = Path.GetFullPath(dataDirectory);
                var directory = new DirectoryInfo(fullPath);

                if (!directory.Exists)
                {
                    result.AddWarning(nameof(ApplicationSettings.DataDirectory),
                        $"数据目录不存在，将在首次使用时创建: {fullPath}");
                }
            }
            catch (Exception ex)
            {
                result.AddError(nameof(ApplicationSettings.DataDirectory),
                    $"数据目录路径无效: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证数据库设置
        /// </summary>
        /// <param name="settings">数据库设置</param>
        /// <param name="result">验证结果</param>
        private void ValidateDatabaseSettings(DatabaseSettings settings, ConfigurationValidationResult result)
        {
            if (settings == null)
            {
                result.AddError(nameof(DatabaseSettings), "数据库设置不能为空");
                return;
            }

            // 验证连接字符串
            if (string.IsNullOrWhiteSpace(settings.ConnectionString))
            {
                result.AddError(nameof(DatabaseSettings.ConnectionString), "数据库连接字符串不能为空");
            }

            // 验证SQLite数据库文件路径
            if (settings.Provider == DatabaseProvider.SQLite)
            {
                try
                {
                    var connectionString = settings.ConnectionString;
                    if (connectionString.Contains("Data Source="))
                    {
                        var dataSource = connectionString.Split("Data Source=")[1].Split(';')[0];
                        var directory = Path.GetDirectoryName(dataSource);

                        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                        {
                            result.AddWarning(nameof(DatabaseSettings.ConnectionString),
                                $"SQLite数据库目录不存在，将在首次使用时创建: {directory}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.AddWarning(nameof(DatabaseSettings.ConnectionString),
                        $"无法验证SQLite数据库路径: {ex.Message}");
                }
            }

            // 验证备份设置
            if (settings.Backup.Enabled)
            {
                if (string.IsNullOrWhiteSpace(settings.Backup.BackupDirectory))
                {
                    result.AddError($"{nameof(DatabaseSettings.Backup)}.{nameof(DatabaseBackupSettings.BackupDirectory)}",
                        "启用备份时，备份目录不能为空");
                }
            }
        }

        /// <summary>
        /// 验证日志设置
        /// </summary>
        /// <param name="settings">日志设置</param>
        /// <param name="result">验证结果</param>
        private void ValidateLoggingSettings(LoggingSettings settings, ConfigurationValidationResult result)
        {
            if (settings == null)
            {
                result.AddError(nameof(LoggingSettings), "日志设置不能为空");
                return;
            }

            // 验证日志文件路径
            if (settings.EnableFileLogging && !string.IsNullOrWhiteSpace(settings.LogPath))
            {
                try
                {
                    var directory = Path.GetDirectoryName(settings.LogPath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        result.AddWarning(nameof(LoggingSettings.LogPath),
                            $"日志目录不存在，将在首次使用时创建: {directory}");
                    }
                }
                catch (Exception ex)
                {
                    result.AddError(nameof(LoggingSettings.LogPath),
                        $"日志文件路径无效: {ex.Message}");
                }
            }

            // 验证日志级别覆盖
            if (settings.LogLevelOverrides != null)
            {
                foreach (var kvp in settings.LogLevelOverrides)
                {
                    if (string.IsNullOrWhiteSpace(kvp.Key))
                    {
                        result.AddWarning(nameof(LoggingSettings.LogLevelOverrides),
                            "日志级别覆盖中存在空的命名空间键");
                    }
                }
            }
        }

        /// <summary>
        /// 验证外部服务设置
        /// </summary>
        /// <param name="settings">外部服务设置</param>
        /// <param name="result">验证结果</param>
        private void ValidateExternalServicesSettings(ExternalServicesSettings settings, ConfigurationValidationResult result)
        {
            if (settings == null)
            {
                result.AddError(nameof(ExternalServicesSettings), "外部服务设置不能为空");
                return;
            }

            // 验证BaseUrl
            if (!string.IsNullOrWhiteSpace(settings.BaseUrl))
            {
                if (!Uri.TryCreate(settings.BaseUrl, UriKind.Absolute, out var uri))
                {
                    result.AddError(nameof(ExternalServicesSettings.BaseUrl),
                        "外部服务基础URL格式无效");
                }
                else if (uri.Scheme != "https" && uri.Scheme != "http")
                {
                    result.AddError(nameof(ExternalServicesSettings.BaseUrl),
                        "外部服务基础URL必须使用HTTP或HTTPS协议");
                }
            }

            // 验证重试设置
            if (settings.EnableRetry && settings.MaxRetryAttempts > 0 && settings.RetryDelayMs <= 0)
            {
                result.AddError(nameof(ExternalServicesSettings.RetryDelayMs),
                    "启用重试时，重试延迟必须大于0");
            }
        }

        /// <summary>
        /// 验证UI设置
        /// </summary>
        /// <param name="settings">UI设置</param>
        /// <param name="result">验证结果</param>
        private void ValidateUISettings(UISettings settings, ConfigurationValidationResult result)
        {
            if (settings == null)
            {
                result.AddError(nameof(UISettings), "UI设置不能为空");
                return;
            }

            // 验证窗口设置
            if (settings.Window != null)
            {
                if (settings.Window.DefaultWidth < settings.Window.MinimumWidth)
                {
                    result.AddError($"{nameof(UISettings.Window)}.{nameof(WindowSettings.DefaultWidth)}",
                        "默认窗口宽度不能小于最小窗口宽度");
                }

                if (settings.Window.DefaultHeight < settings.Window.MinimumHeight)
                {
                    result.AddError($"{nameof(UISettings.Window)}.{nameof(WindowSettings.DefaultHeight)}",
                        "默认窗口高度不能小于最小窗口高度");
                }
            }

            // 验证画布设置
            if (settings.Canvas != null)
            {
                if (settings.Canvas.DefaultZoom < settings.Canvas.MinimumZoom ||
                    settings.Canvas.DefaultZoom > settings.Canvas.MaximumZoom)
                {
                    result.AddError($"{nameof(UISettings.Canvas)}.{nameof(CanvasSettings.DefaultZoom)}",
                        "默认缩放级别必须在最小和最大缩放级别之间");
                }

                if (settings.Canvas.MinimumZoom >= settings.Canvas.MaximumZoom)
                {
                    result.AddError($"{nameof(UISettings.Canvas)}.{nameof(CanvasSettings.MinimumZoom)}",
                        "最小缩放级别必须小于最大缩放级别");
                }
            }

            // 验证语言设置
            if (settings.Language != null)
            {
                if (!string.IsNullOrWhiteSpace(settings.Language.CurrentLanguage) &&
                    settings.Language.SupportedLanguages != null &&
                    !settings.Language.SupportedLanguages.Contains(settings.Language.CurrentLanguage))
                {
                    result.AddWarning($"{nameof(UISettings.Language)}.{nameof(LanguageSettings.CurrentLanguage)}",
                        "当前语言不在支持的语言列表中");
                }
            }

            // 验证主题设置
            if (settings.Theme != null && !string.IsNullOrWhiteSpace(settings.Theme.CustomThemePath))
            {
                if (!File.Exists(settings.Theme.CustomThemePath))
                {
                    result.AddWarning($"{nameof(UISettings.Theme)}.{nameof(ThemeSettings.CustomThemePath)}",
                        "自定义主题文件不存在");
                }
            }
        }
    }
}
