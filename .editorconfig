root = true

# 通用设置
[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

# C# 文件格式与风格（供 dotnet format 与分析器读取）
[*.{cs,csx}]
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = true

# 成员与花括号/换行
csharp_new_line_before_open_brace = all
csharp_new_line_between_query_expression_clauses = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true

# 空格偏好
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_before_open_square_brackets = false
csharp_space_between_square_brackets = false

# 代码风格（以 suggestion 等级为主，避免构建失败）
csharp_prefer_braces = true:suggestion
csharp_prefer_simple_using_statement = true:suggestion
csharp_style_var_for_built_in_types = false:suggestion
csharp_style_var_when_type_is_apparent = false:suggestion
csharp_style_var_elsewhere = false:suggestion
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_event = false:suggestion
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion
csharp_style_pattern_local_over_anonymous_function = true:suggestion
csharp_style_prefer_switch_expression = true:suggestion

# XAML/XML
[*.{xaml,xml}]
charset = utf-8
indent_style = space
indent_size = 4
insert_final_newline = true

# JSON/YAML
[*.{json,yml,yaml}]
charset = utf-8
indent_style = space
indent_size = 4
insert_final_newline = true

